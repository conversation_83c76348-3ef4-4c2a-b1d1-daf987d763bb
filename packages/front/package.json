{"name": "@digisac/front", "version": "3.51.3-mr-3105.26", "description": "MandeUmZap", "private": true, "main": "build/server/index.js", "directories": {"test": "tests"}, "workspaces": {"nohoist": ["**"]}, "repository": {"type": "git", "url": "****************************:mandeumzap/mandeumzap-front.git"}, "author": "Ikatec", "license": "UNLICENSED", "scripts": {"node-run": "NODE_OPTIONS=--openssl-legacy-provider node -r @swc-node/register", "build": "yarn node-run ./internal/scripts/build.js --optimize", "build:dev": "yarn node-run ./internal/scripts/build.js", "clean": "yarn node-run ./internal/scripts/clean.js", "develop": "yarn node-run ./internal/scripts/develop.js", "start": "NODE_OPTIONS=--openssl-legacy-provider cross-env NODE_ENV=production node dist/server", "test": "jest", "test:e2e:run": "cross-env NODE_ENV=test yarn node-run ./tests/run.js", "format": "prettier  \"$PWD/src/**/*.{js,ts,jsx,tsx,css,scss,json}\" --write", "format-all": "yarn format \"$PWD/src/**/*.{js,ts,jsx,tsx,css,scss,json}\"", "lint-all": "eslint src"}, "jest": {"testMatch": ["<rootDir>/src/app/__tests__/(unit|integration)/**/?(*.)(spec|test).+(js|ts)?(x)", "<rootDir>/__tests__/(unit|integration)/**/?(*.)(spec|test).+(js|ts)?(x)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}"], "testPathIgnorePatterns": ["<rootDir>/(dist|node_modules|flow-typed|public)/"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/src/app/__tests__/__mocks__/fileMock.js", "\\.(css|less|scss)$": "identity-obj-proxy"}, "transform": {"^.+\\.(tsx|js|ts)$": "babel-jest", "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/tests/jest/fileTransformer.js"}, "clearMocks": true, "setupTestFrameworkScriptFile": "<rootDir>/tests/jest/setup.js", "testEnvironment": "jsdom"}, "dependencies": {"360dialog-connect-button": "^0.9.0", "@elastic/apm-rum": "^5.16.3", "@elastic/apm-rum-react": "^2.0.5", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@fortawesome/fontawesome-svg-core": "~1.2.36", "@fortawesome/free-brands-svg-icons": "~5.12.1", "@fortawesome/free-regular-svg-icons": "~5.12.1", "@fortawesome/free-solid-svg-icons": "~5.12.1", "@fortawesome/react-fontawesome": "~0.1.19", "@hookform/resolvers": "^3.9.1", "@ikatec/nebula-react": "1.0.5", "@ikatec/nebula-tokens": "1.0.5", "@lexical/react": "^0.31.2", "@projectstorm/react-diagrams": "~6.2.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-hook/mouse-position": "^4.1.3", "@react-input/mask": "^1.2.14", "@sentry/browser": "4.5.3", "@sentry/webpack-plugin": "^1.17.1", "@swc-node/register": "^1.9.1", "@swc/cli": "^0.3.12", "@tanstack/react-query": "^5.59.3", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "app-root-dir": "1.0.2", "assign-deep": "^1.0.1", "axios": "1.6.8", "bootstrap": "~4.6.2", "chart.js": "2.8.0", "class-variance-authority": "^0.7.0", "classnames": "2.5.1", "closest": "^0.0.1", "cls-hooked": "^4.2.2", "clsx": "^2.1.1", "compression": "1.7.4", "cookie-parser": "1.4.6", "cors": "2.8.5", "cross-env": "7.0.3", "dagre": "^0.8.5", "date-fns": "2.30.0", "dotenv": "16.4.5", "embla-carousel-react": "^8.3.0", "emoji-mart": "2.11.2", "express": "4.19.2", "fast-deep-equal": "3.1.3", "file-loader": "6.2.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "generate-password-browser": "1.1.0", "glamor": "^2.20.40", "helmet": "~5.1.1", "history": "~4.10.1", "hpp": "0.2.3", "i18next": "^23.11.4", "i18next-browser-languagedetector": "^7.2.1", "i18next-http-backend": "^2.5.1", "ics-to-json": "^1.0.0", "js-file-download": "0.4.12", "jszip": "^3.10.1", "lexical": "^0.31.2", "lodash": "4.17.21", "lucide-react": "^0.454.0", "md5": "2.3.0", "moize": "6.1.6", "moment": "2.30.1", "moment-timezone": "0.5.45", "normalizr": "3.6.2", "papaparse": "5.4.1", "pathfinding": "^0.4.18", "paths-js": "^0.4.11", "polished": "^4.3.1", "postcss": "^8.4.47", "postcss-cssnext": "3.1.1", "postcss-import": "16.1.0", "postcss-loader": "^8.1.1", "postcss-preset-env": "9.5.14", "postcss-simple-vars": "7.0.1", "primereact": "^10.8.3", "prop-types": "15.8.1", "qrcode": "^1.5.3", "qs": "^6.12.1", "quill": "2.0.2", "react": "^18.3.1", "react-async-bootstrapper": "2.1.1", "react-async-component": "2.0.0", "react-autocomplete-input": "^1.0.31", "react-autosize-textarea": "7.1.0", "react-bootstrap-sweetalert": "^5.2.0", "react-chartjs-2": "2.11.2", "react-clipboard.js": "2.0.16", "react-color": "^2.19.3", "react-countdown": "^2.3.5", "react-countdown-hook": "^1.1.3", "react-currency-format": "^1.1.0", "react-datetime": "3.2.0", "react-day-picker": "^9.3.2", "react-dom": "18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "14.2.3", "react-easy-emoji": "1.8.1", "react-flow-renderer": "^8.3.0", "react-google-recaptcha": "1.0.5", "react-gtm-module": "^2.0.11", "react-helmet": "6.1.0", "react-hook-form": "^7.53.2", "react-i18next": "14.1.1", "react-idle-timer": "~5.4.2", "react-image-crop": "^11.0.5", "react-images": "1.1.7", "react-infinite-scroller": "^1.2.6", "react-input-mask": "^2.0.4", "react-linkify": "0.2.2", "react-player": "^2.16.0", "react-redux": "~7.2.9", "react-reformed": "2.0.0", "react-router": "5.3.4", "react-router-dom": "5.3.4", "react-select": "~3.2.0", "react-toastify": "~8.2.0", "react-ultimate-pagination-bootstrap-4": "1.1.0", "react-virtualized": "9.22.5", "react-vis": "^1.12.1", "reactflow": "11.11.3", "reactstrap": "^8.10.1", "recharts": "2.8.0", "redux": "^5.0.1", "redux-act-light": "^1.0.1", "redux-persist-cookie-storage": "~0.4.0", "redux-react-hook": "4.0.3", "redux-saga": "^1.3.0", "reselect": "5.1.0", "resize-observer-polyfill": "^1.5.1", "sass": "1.77.1", "serialize-javascript": "6.0.2", "socket.io-client": "4.7.5", "source-map-support": "0.5.21", "styled-components": "^5.3.11", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "timezones.json": "^1.7.1", "use-stick-to-bottom": "^1.0.43", "uuid": "9.0.1", "validator": "^13.12.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yet-another-react-lightbox": "^3.21.1", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.25.9", "@eslint/eslintrc": "3.1.0", "@eslint/js": "9.4.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.13", "@swc/core": "^1.5.7", "@swc/plugin-styled-components": "^2.0.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/faker": "^6.6.9", "@types/jest": "^29.5.14", "@types/lodash": "4.14.197", "@types/node": "20.12.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-helmet": "^6.1.11", "@types/react-redux": "7.1.26", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-select": "^5.0.1", "@types/reactstrap": "8.7.2", "@types/recompose": "^0.30.15", "@types/redux": "3.6.0", "@types/redux-saga": "0.10.5", "@types/styled-components": "5.1.26", "@types/uuid": "9.0.8", "@typescript-eslint/eslint-plugin": "7.12.0", "@typescript-eslint/parser": "7.12.0", "@welldone-software/why-did-you-render": "7.0.1", "app-root-dir": "1.0.2", "assets-webpack-plugin": "7.1.1", "autoprefixer": "^10.4.20", "chalk": "~4.1.2", "chokidar": "3.5.3", "classnames-loader": "2.1.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "detect-port-alt": "1.1.6", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "1.15.7", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.2", "eslint-plugin-react-hooks": "4.6.2", "faker": "6.6.6", "file-loader": "6.2.0", "glob": "~10.3.4", "hard-source-webpack-plugin": "0.13.1", "html-webpack-plugin": "5.6.0", "identity-obj-proxy": "3.0.0", "jest": "29.6.4", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "2.9.1", "modernizr": "3.13.0", "modernizr-loader": "1.0.1", "node-notifier": "10.0.1", "postcss-nested": "^7.0.2", "prettier": "3.2.5", "react-addons-test-utils": "15.6.2", "react-dev-utils": "10.2.1", "react-refresh": "0.14.2", "react-test-renderer": "16.12.0", "react-testing-library": "8.0.1", "redux-devtools-extension": "2.13.9", "redux-mock-store": "^1.5.4", "redux-saga-test-plan": "3.7.0", "regenerator-runtime": "0.14.1", "rimraf": "5.0.1", "sass-loader": "^16.0.2", "semver": "7.6.2", "shelljs": "0.8.5", "speed-measure-webpack-plugin": "1.5.0", "style-loader": "^4.0.0", "swc-loader": "0.2.6", "tailwindcss": "^3.4.14", "tsc-transpile-only": "0.0.3", "typescript": "5.5.1-rc", "url-loader": "4.1.1", "webpack": "5.91.0", "webpack-bundle-analyzer": "4.10.2", "webpack-dev-middleware": "7.2.1", "webpack-hot-middleware": "2.26.1", "webpack-node-externals": "^3.0.0"}, "resolutions": {"@projectstorm/geometry": "~6.3.0", "@projectstorm/react-canvas-core": "~6.3.0", "@projectstorm/react-diagrams-core": "~6.3.0", "@projectstorm/react-diagrams-defaults": "~6.3.0", "@projectstorm/react-diagrams-routing": "~6.3.0", "@projectstorm/react-diagrams": "~6.2.0", "react": "18.3.1", "react-dom": "18.3.1"}}