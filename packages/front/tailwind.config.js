import { tailwind } from '@ikatec/nebula-react'
import { colors } from '@ikatec/nebula-tokens'

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/**/*.{js,jsx,ts,tsx}',
    './src/components/**/**/*.{js,jsx,ts,tsx}',
    './src/routes/**/**/*.{js,jsx,ts,tsx}',
    './src/layouts/**/**/*.{js,jsx,ts,tsx}',
    'node_modules/@ikatec/nebula-react/dist/**/*.mjs',
  ],
  important: true,
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        ...colors,
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities(
        {
          '.md-block': {
            '@media (min-width: 768px)': {
              display: 'block !important',
            },
          },
          '.md-hidden': {
            '@media (min-width: 768px)': {
              display: 'hidden !important',
            },
          },
          '.md-flex-row': {
            '@media (min-width: 768px)': {
              flexDirection: 'row !important',
            },
          },
          '.md-flex-col': {
            '@media (min-width: 768px)': {
              flexDirection: 'column !important',
            },
          },
          '.md-w-full': {
            '@media (min-width: 768px)': {
              width: '100% !important',
            },
          },
          '.md-w-auto': {
            '@media (min-width: 768px)': {
              width: 'auto !important',
            },
          },
        },
        ['responsive'],
      )
    },
  ],
}
