import {
  Button,
  Checkbox,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { ChevronDown, EllipsisVerticalIcon, EyeIcon, PencilIcon, PlusIcon, Trash2Icon } from 'lucide-react'
import React, { memo, useEffect, useState } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import tagsApi, { tagsQueryKey, type TagsFilters } from '../../api/tags'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import useToggle from '../../app/hooks/useToggle'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { useSearchParams } from '../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { debounceFn } from '../../utils/debouce-fn'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { DeleteManyTagsDialog } from './components/delete-many-tags-dialog'
import { LinkTagsToDepartmentDialog } from './components/link-tags-to-department-dialog'
import { oldColorsToNewColors } from './constants'
import { Title } from '../../components/unconnected/typography'

const ListTags = () => {
  const history = useHistory()

  const { params, setValues, set, hasFilter, clear, searchParams, remove } = useSearchParams<TagsFilters>({
    order: 'ASC',
    sort: 'label',
    page: 1,
  })
  const [selectedTagsIds, setSelectedTagsIds] = useState<Set<string>>(new Set())
  const { isOpen: isOpenDeleteManyTagsDialog, set: setIsOpenDeleteManyTagsDialog } = useToggle(false)
  const { isOpen: isOpenLinkToDepartmentTagsDialog, set: setIsOpenLinkToDepartmentTagsDialog } = useToggle(false)

  const { t } = useTranslation(['tagsPage', 'common'])

  const queryClient = useQueryClient()

  const {
    data: tags,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [tagsQueryKey, 'list', params],
    queryFn: () => tagsApi.getAll({ ...params, customInclude: ['linkedContacts'] }),
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof tags>({ queryKey: [tagsQueryKey, 'list'] }))
    },
  })

  const { currentPage, data = [], limit, total } = tags ?? {}

  const allSelected = selectedTagsIds.size === data?.length

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/tags/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/tags/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/tags/${id}/edit`, search: searchParams }))
  }

  const navigateToDelete = (id: string) => {
    setTimeout(() => history.push({ pathname: `/tags/${id}/delete`, search: searchParams }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'order', 'sort', 'label'])
  }

  const handleSetInputFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      remove(['page', 'order', 'sort'])
      set('label', value)
    })
  }

  const massActionsPermissions = ['tags.update', 'tags.destroy']

  /**
   * Side effect to reset selected tags ids each time the page changes
   */
  useEffect(() => {
    setSelectedTagsIds(new Set())
  }, [params])

  return (
    <>
      <Helmet title={t('tagsPage:TITLE_TAGS')} />

      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Title data-testid="tags-list-heading">{t('tagsPage:TITLE_TAGS')}</Title>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="tags-list-input-filter"
                name="label"
                defaultValue={params.label}
                onChange={(e) => handleSetInputFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              {selectedTagsIds.size > 0 && (
                <IfUserCan any permissions={massActionsPermissions}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="secondary" data-testid="tags-list-button-bulk-actions">
                        {t('BUTTON_BULK_ACTIONS')}
                        <ChevronDown />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <IfUserCan permission="tags.update">
                        <DropdownMenuItem
                          onClick={() => setTimeout(() => setIsOpenLinkToDepartmentTagsDialog(true))}
                          data-testid="tags-list-button-bulk-actions-link-to-department"
                        >
                          {t('DROPDOWN_LINK_DEPARTMENT')}
                        </DropdownMenuItem>
                      </IfUserCan>

                      <IfUserCan permission="tags.destroy">
                        <DropdownMenuItem
                          onClick={() => setTimeout(() => setIsOpenDeleteManyTagsDialog(true))}
                          data-testid="tags-list-button-bulk-actions-delete"
                        >
                          {t('DROPDOWN_DELETE')}
                        </DropdownMenuItem>
                      </IfUserCan>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </IfUserCan>
              )}
              <IfUserCan permission="tags.create">
                <Button size="md" data-testid="tags-list-button-create" onClick={navigateToCreate}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>

        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('tagsPage:NO_TAGS_CREATED')}
              moduleDescription={t('tagsPage:START_CREATE_FIRST_TAG')}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <IfUserCan any permissions={massActionsPermissions}>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        variant={selectedTagsIds.size > 0 && !allSelected ? 'multiselect' : 'default'}
                        checked={selectedTagsIds.size > 0}
                        data-testid="tags-list-checkbox-select-all"
                        onCheckedChange={(isChecked) => {
                          setSelectedTagsIds(isChecked || !allSelected ? new Set(data.map((tag) => tag.id)) : new Set())
                        }}
                      />
                    </TableHead>
                  </IfUserCan>
                  <TableHead className="w-[58px]">{t('tagsPage:COLUMN_COLOR')}</TableHead>
                  <TableHead>
                    <Space size="md" className="justify-start items-center">
                      {t('tagsPage:COLUMN_TABLE_NAME')}
                      <SortTableButton
                        data-testid="tags-list-sort-label"
                        sort="label"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[270px]">{t('tagsPage:COLUMN_LINKED_CONTACTS')}</TableHead>
                  <TableHead className="w-[72px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((tag, index) => {
                  const isChecked = selectedTagsIds.has(tag.id)
                  const tagColor = oldColorsToNewColors.get(tag?.backgroundColor) ?? tag?.backgroundColor
                  return (
                    <TableRow key={tag.id}>
                      <IfUserCan any permissions={massActionsPermissions}>
                        <TableCell>
                          <Checkbox
                            checked={isChecked}
                            onCheckedChange={() => {
                              setSelectedTagsIds((state) => {
                                const newSet = new Set(state)

                                if (isChecked) {
                                  newSet.delete(tag.id)
                                } else {
                                  newSet.add(tag.id)
                                }
                                return newSet
                              })
                            }}
                            data-testid={`tags-list-checkbox-select-${index}`}
                          />
                        </TableCell>
                      </IfUserCan>
                      <TableCell className="flex items-center">
                        <div
                          className={'rounded-[4px] w-4 h-4 border-neutral-400 dark:border-neutral-600 border-dashed'}
                          style={{ backgroundColor: tagColor, borderWidth: tagColor ? 0 : 1 }}
                        />
                      </TableCell>
                      <TableCell>{tag.label}</TableCell>
                      <TableCell>{tag.linkedContacts}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button icon variant="ghost" size="sm" data-testid={`tags-list-button-actions-${index}`}>
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              data-testid={`tags-list-button-actions-${index}-view`}
                              onClick={() => navigateToView(tag.id)}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>

                            <IfUserCan permission="tags.update">
                              <DropdownMenuItem
                                data-testid={`tags-list-button-actions-${index}-edit`}
                                onClick={() => navigateToEdit(tag.id)}
                              >
                                <PencilIcon />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </DropdownMenuItem>
                            </IfUserCan>
                            <IfUserCan permission="tags.destroy">
                              <DropdownMenuItem
                                onClick={() => navigateToDelete(tag.id)}
                                data-testid={`tags-list-button-actions-${index}-delete`}
                              >
                                <Trash2Icon />
                                {t('common:LABEL_DELETE')}
                              </DropdownMenuItem>
                            </IfUserCan>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>

      <DeleteManyTagsDialog
        open={isOpenDeleteManyTagsDialog}
        tagsId={[...selectedTagsIds]}
        onOpenChange={setIsOpenDeleteManyTagsDialog}
        onSuccess={() => {
          setIsOpenDeleteManyTagsDialog(false)
          setSelectedTagsIds(new Set())
        }}
      />

      <LinkTagsToDepartmentDialog
        open={isOpenLinkToDepartmentTagsDialog}
        tagsId={[...selectedTagsIds]}
        onOpenChange={setIsOpenLinkToDepartmentTagsDialog}
        onSuccess={() => {
          setIsOpenLinkToDepartmentTagsDialog(false)
          setSelectedTagsIds(new Set())
        }}
      />
    </>
  )
}

const memoizedListTags = memo(ListTags, () => true)

export { memoizedListTags as ListTags }
