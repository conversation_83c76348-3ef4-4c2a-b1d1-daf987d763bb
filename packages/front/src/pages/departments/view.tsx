import {
  Button,
  <PERSON>alog,
  DialogBody,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Label,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useQuery } from '@tanstack/react-query'
import { ArrowUpRightIcon } from 'lucide-react'
import React, { useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import { Helmet } from 'react-helmet'
import departmentsApi, { departmentsQueryKey } from '../../api/departments'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const ViewDepartments = () => {
  const { t } = useTranslation(['department', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [departmentsQueryKey, id],
    queryFn: () => departmentsApi.getById(id),
    enabled: Boolean(id),
  })

  const handleNavigateToUserListWithDepartmentFilter = useCallback(() => {
    history.push({
      pathname: '/users',
      search: new URLSearchParams({ department: id, 'department-label': data?.name }).toString(),
    })
  }, [history, data])

  useEffect(() => {
    if (isError) {
      toast.error(t('DEPARTMENT_NOT_FOUND'))
      return history.replace('/departments')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${t('department:TITLE_DEPARTMENT_PLURAL')} - ${data?.name}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.replace('/departments')
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isFetching && (
                <div className="w-full flex items-center justify-center">
                  {<Skeleton width="150px" height="24px" />}
                </div>
              )}
              {isSuccess && !isFetching && data.name}
            </DialogTitle>
          </DialogHeader>
          <DialogBody>
            {isFetching && (
              <Space className="w-full items-start" direction="column">
                <Space size="sm">
                  <Label>{t('TABLE_DEPARTMENT_COLUMN_NAME')}:</Label>
                  <Skeleton width="150px" height="24px" />
                </Space>

                <Space size="sm">
                  <Label>Status:</Label>
                  <Skeleton width="100px" height="24px" />
                </Space>

                <Space size="sm">
                  <Label>{t('LINKED_USERS_LABEL')}:</Label>
                  <Skeleton width="100px" height="24px" />
                </Space>
              </Space>
            )}

            {isSuccess && !isFetching && (
              <Space className="w-full items-start" direction="column">
                <Space size="sm" className="items-baseline">
                  <Label>{t('TABLE_DEPARTMENT_COLUMN_NAME')}:</Label>
                  <span>{data.name}</span>
                </Space>
                <Space size="sm" className="items-baseline">
                  <Label>{t('LABEL_STATUS')}:</Label>
                  <span>{data.archivedAt ? t('LABEL_FILED') : t('LABEL_UNARCHIVED')}</span>
                </Space>

                <Space size="sm" className="items-baseline">
                  <Label>{t('LINKED_USERS_LABEL')}:</Label>
                  <span>{data.usersCount}</span>
                  <Button variant="ghost" size="xs" onClick={handleNavigateToUserListWithDepartmentFilter}>
                    {t('common:SEE_lIST')}
                    <ArrowUpRightIcon />
                  </Button>
                </Space>
              </Space>
            )}
          </DialogBody>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                variant="secondary"
                type="button"
                onClick={() => history.replace('/departments')}
                className="w-full"
              >
                {t('common:FORM_ACTION_CLOSE')}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { ViewDepartments }
