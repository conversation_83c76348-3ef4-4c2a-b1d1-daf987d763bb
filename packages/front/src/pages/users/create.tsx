import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError, HttpStatusCode } from 'axios'
import { ChevronLeftIcon } from 'lucide-react'
import React, { useCallback, useRef } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import { Link } from 'react-router-dom'
import usersApi, { type UserBody, usersQueryKey } from '../../api/users'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { UsersForm, type UsersFormRef } from './components/form'
import { LightningLearningBox } from './components/lightning-learning-box'
import { parseUserFormValues } from './utils'
import { Title } from '../../components/unconnected/typography'

const CreateUsers = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()

  const usersFormRef = useRef<UsersFormRef>(null)

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: UserBody) => usersApi.create(values),
    onSuccess: () => {
      toast.success(t('CREATED_USER'))

      queryClient.invalidateQueries({
        queryKey: [usersQueryKey, 'list'],
      })

      history.replace('/users')
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const errorMap = {
        'Recently used password': 'usersPage:RECENTLY_USED_PASSWORD',
        'The given data was invalid.': 'usersPage:EMAIL_ALREADY_EXISTS',
      }

      const errorHandlingMap = {
        [HttpStatusCode.BadRequest]: 'usersPage:CREATE_USER_MESSAGE_ERROR',
        [HttpStatusCode.PaymentRequired]: 'usersPage:CREATE_USER_PLAN_LIMIT_REACHED',
        [HttpStatusCode.InternalServerError]: 'common:MESSAGE_ERROR_SERVER_PROBLEM',
        [HttpStatusCode.Conflict]: 'usersPage:MODAL_DUPLICATE_NAME',
      }

      const { status, data: errorData } = error.response || {}

      const errorMessage = errorMap[errorData?.message] || errorHandlingMap[status]

      toast.error(t(errorMessage))
    },
  })

  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (usersFormRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/users')
  }, [usersFormRef])

  return (
    <Container>
      <Helmet title={`${t('common:LABEL_CREATING')} ${t('GET_TITLE_USER')}`} />
      <ContainerHeader mtSize="sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <Link to="/users">{t('TITLE_USERS')}</Link>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {t('common:LIST_ADD_NEW')} {t('GET_TITLE_USER')}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Space className="w-full items-center" size="sm">
          <Button
            icon
            variant="ghost"
            onClick={handleGoBack}
            aria-label="Go back to users' list"
            data-testid="users-add-button-go-back"
            role="link"
          >
            <ChevronLeftIcon />
          </Button>

          <Title data-testid="users-create-heading">
            {t('common:LIST_ADD_NEW')} {t('usersPage:GET_TITLE_USER')}
          </Title>
        </Space>
      </ContainerHeader>
      <ContainerContent>
        <Space className="gap-6">
          <div className="flex-1">
            <UsersForm
              ref={usersFormRef}
              onSubmit={(values) => mutateAsync(parseUserFormValues(values))}
              onCancel={() => history.replace('/users')}
              isPending={isPending}
            />
          </div>
          <aside className="hidden md-block">
            <LightningLearningBox />
          </aside>
        </Space>
      </ContainerContent>

      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/users')}
      />
    </Container>
  )
}

export { CreateUsers }
