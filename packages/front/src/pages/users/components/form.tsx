import { Box, Button, InputText, Label, Space, Switch } from '@ikatec/nebula-react'
import { Separator } from '@radix-ui/react-separator'
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Copy, LoaderIcon } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import z from 'zod'
import { useSelector } from 'react-redux'
import generatorPassword from 'generate-password-browser'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'
import { cn } from '../../../app/components/common/unconnected/ui/utils'
import { DepartmentsSelect } from '../../../components/connected/departments-select'
import { OrganizationSelect } from '../../../components/connected/organization-select'
import { ResourceSelectValue } from '../../../components/connected/resource-select'
import { RolesSelect } from '../../../components/connected/role-select'
import { TimetableSelect } from '../../../components/connected/timetable-select'
import { PasswordStrengthValidation } from '../../../components/unconnected/password-strength-validation'
import { passwordIsStrong } from '../../../components/unconnected/password-strength-validation/utils'
import { TimeTable } from './time-table'
import { getUserAccount } from '../../../app/modules/auth/selectors'

const resourceSelectSchema = z.object({
  value: z.string().nullish(),
  label: z.string().nullish(),
})

const schema = z
  .object({
    name: z
      .string()
      .trim()
      .nonempty({ message: 'common:REQUIRED_FIELD' })
      .max(255, {
        message: 'usersPage:USER_NAME_MAX_LENGTH',
      })
      .default(''),
    email: z
      .string()
      .trim()
      .nonempty({ message: 'common:REQUIRED_FIELD' })
      .email({ message: 'common:INVALID_EMAIL_FIELD_ERROR' })
      .default(''),
    password: z.string().min(8, { message: 'passwordStrength:INSUFFICIENT_PASSWORD_COMPLEXITY' }).nullish(),
    passwordConfirmation: z.string().optional().nullish(),
    roles: z.array(resourceSelectSchema).optional().nullish().default([]),
    departments: z.array(resourceSelectSchema).optional().nullish().default([]),
    organizations: z.array(resourceSelectSchema).optional().nullish().default([]),
    timetable: resourceSelectSchema.optional().nullish().default(null),
    phoneNumber: z.string().trim().optional().nullish(),
    branch: z.string().trim().optional().nullish(),
    isClientUser: z.boolean().optional().nullish().default(false),
    changePassword: z.boolean().optional().nullish(),
  })
  .superRefine((data, ctx) => {
    const { changePassword, password, passwordConfirmation, departments, roles, organizations, isClientUser } = data

    if (password) {
      if (!passwordIsStrong(password)) {
        ctx.addIssue({
          path: ['password'],
          code: z.ZodIssueCode.custom,
          message: 'passwordStrength:INSUFFICIENT_PASSWORD_COMPLEXITY',
        })
      }
    }

    if (changePassword) {
      if (!password) {
        ctx.addIssue({
          path: ['password'],
          code: z.ZodIssueCode.custom,
          message: 'common:REQUIRED_FIELD',
        })
      }

      if (!passwordConfirmation) {
        ctx.addIssue({
          path: ['passwordConfirmation'],
          code: z.ZodIssueCode.custom,
          message: 'common:REQUIRED_FIELD',
        })
      }

      if (password && passwordConfirmation && password !== passwordConfirmation) {
        ctx.addIssue({
          path: ['passwordConfirmation'],
          code: z.ZodIssueCode.custom,
          message: 'usersPage:CREATE_USER_LABEL_NO_MATCH_PASSWORD',
        })
      }
    }

    if (isClientUser) {
      if (!organizations || organizations?.length === 0) {
        ctx.addIssue({
          path: ['organizations'],
          code: z.ZodIssueCode.custom,
          message: 'common:REQUIRED_FIELD',
        })
      }
      return
    }

    if (!departments || departments?.length === 0) {
      ctx.addIssue({
        path: ['departments'],
        code: z.ZodIssueCode.custom,
        message: 'common:REQUIRED_FIELD',
      })
    }

    if (!roles || roles?.length === 0) {
      ctx.addIssue({
        path: ['roles'],
        code: z.ZodIssueCode.custom,
        message: 'common:REQUIRED_FIELD',
      })
    }
  })

type UserFormValues = Required<
  Omit<z.infer<typeof schema>, 'departments' | 'timetable' | 'roles' | 'organizations'>
> & {
  departments: ResourceSelectValue[] | null
  roles: ResourceSelectValue[] | null
  organizations: ResourceSelectValue[] | null
  timetable: ResourceSelectValue | null
  id?: string
}

interface UsersFormProps {
  onCancel: VoidFunction
  onSubmit: (values: UserFormValues) => void
  defaultValues?: Partial<UserFormValues>
  isPending?: boolean
  isEdit?: boolean
}

type UsersFormRef = { isDirty: boolean }

const UsersForm = forwardRef<UsersFormRef, UsersFormProps>(
  ({ onCancel, onSubmit, defaultValues, isPending, isEdit = false }, ref) => {
    const defaultValuesHasBeenApplied = useRef(false)
    const { t } = useTranslation(['usersPage', 'common'])
    const userAccount = useSelector(getUserAccount)
    const userPasswordCreationMethod = userAccount.settings?.userPasswordCreationMethod

    const formContext = useForm<UserFormValues>({
      defaultValues: {
        name: '',
        email: '',
        branch: null,
        phoneNumber: null,
        roles: [],
        departments: [],
        organizations: [],
        timetable: null,
        isClientUser: false,
        changePassword: !isEdit,
      },
      resetOptions: {
        keepDefaultValues: true,
      },
      resolver: zodResolver(schema),
      criteriaMode: 'all',
      mode: 'all',
      reValidateMode: 'onChange',
    })

    const isAutomaticPasswordGeneration = useMemo(
      () => ['link', 'automatic'].includes(userPasswordCreationMethod),
      [userPasswordCreationMethod],
    )

    const handleSubmit = async (values: UserFormValues) => {
      const { changePassword, password, passwordConfirmation } = values

      onSubmit({
        ...values,
        changePassword: undefined,
        password: changePassword ? password : undefined,
        passwordConfirmation: changePassword ? passwordConfirmation : undefined,
      })
    }

    const {
      watch,
      formState: { isValid, isDirty },
    } = formContext

    const isClientUser = watch('isClientUser')
    const changePassword = watch('changePassword')
    const timeTable = watch('timetable')
    const passwordInputMethod = !isEdit || changePassword ? userPasswordCreationMethod : null

    const automaticPassword = useMemo(
      () =>
        isAutomaticPasswordGeneration
          ? generatorPassword.generate({
              length: 8,
              numbers: true,
              symbols: true,
              lowercase: true,
              uppercase: true,
              excludeSimilarCharacters: true,
              exclude: ',;:"/',
              strict: true,
            })
          : null,
      [isAutomaticPasswordGeneration],
    )

    useEffect(() => {
      const initialValues = {
        changePassword: !isEdit,
        ...(isEdit && { ...defaultValues, changePassword: false }),
        ...(isAutomaticPasswordGeneration && {
          password: automaticPassword,
          passwordConfirmation: automaticPassword,
        }),
      }

      if (defaultValuesHasBeenApplied.current || !Object.keys(initialValues).length) return

      formContext.reset(initialValues)
      defaultValuesHasBeenApplied.current = true

      return () => {
        defaultValuesHasBeenApplied.current = false
      }
    }, [defaultValues, automaticPassword, isAutomaticPasswordGeneration])

    useImperativeHandle(ref, () => ({ isDirty }))

    const EditPasswordField = (
      <FormField
        name="changePassword"
        control={formContext.control}
        render={({ field }) => (
          <FormItem
            className={cn('items-center', {
              'mb-6': field.value,
            })}
          >
            <FormControl>
              <Space className="items-center">
                <Switch
                  id="CREATE_USER_CHANGE_PASSWORD"
                  onCheckedChange={field.onChange}
                  data-testid="users-form-changePassword-toggle"
                  checked={field.value}
                />
                <Label htmlFor="CREATE_USER_CHANGE_PASSWORD">{t('usersPage:CREATE_USER_CHANGE_PASSWORD')}</Label>
              </Space>
            </FormControl>
          </FormItem>
        )}
      />
    )

    const ManualPasswordForm = (
      <Space className="w-full" direction="column" size="lg">
        <div className="w-full">
          <FormField
            name="password"
            control={formContext.control}
            render={({ field, fieldState }) => (
              <Space className="w-full" direction="column" size="xs">
                <FormItem className="w-full">
                  <FormControl>
                    <>
                      <Label htmlFor="password">{t('usersPage:CREATE_USER_LABEL_PASSWORD')}</Label>
                      <InputText
                        {...field}
                        type={!isAutomaticPasswordGeneration ? 'password' : 'text'}
                        id="password"
                        placeholder={t('common:FORM_PLACEHOLDERS#PASSWORD')}
                        data-testid="users-form-input-password"
                        isError={Boolean(fieldState?.error?.message)}
                        autoComplete="new-password"
                        disabled={isAutomaticPasswordGeneration}
                      />
                    </>
                  </FormControl>
                  {!isAutomaticPasswordGeneration && (
                    <>
                      <FormMessage />
                      {!!field.value && <PasswordStrengthValidation password={field.value} />}
                    </>
                  )}
                </FormItem>
              </Space>
            )}
          />
        </div>

        {!isAutomaticPasswordGeneration && (
          <div className="w-full">
            <FormField
              name="passwordConfirmation"
              control={formContext.control}
              render={({ field, fieldState }) => (
                <FormItem className="w-full">
                  <FormControl>
                    <>
                      <Label htmlFor="passwordConfirmation">{t('usersPage:CREATE_USER_LABEL_REPEAT_PASSWORD')}</Label>
                      <InputText
                        {...field}
                        type="password"
                        id="passwordConfirmation"
                        placeholder={t('usersPage:CREATE_USER_LABEL_REPEAT_PASSWORD')}
                        data-testid="users-form-input-passwordConfirmation"
                        isError={Boolean(fieldState?.error?.message)}
                        autoComplete="new-password"
                      />
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </Space>
    )

    const RenderPasswordCreationMethod = () => {
      const components = {
        manual: ManualPasswordForm,
        automatic: (
          <Space size="lg" direction="column">
            <Box className="rounded-3xl bg-neutral-50 dark:bg-neutral-800 dark:text-neutral-200">
              <Space direction="column" size="md">
                <b>{t('usersPage:PASSWORD_AUTOMATIC_MESSAGE#TITLE')}</b>
                <p>
                  {t('usersPage:PASSWORD_AUTOMATIC_MESSAGE#DESCRIPTION_0')}{' '}
                  <b>{t('usersPage:PASSWORD_AUTOMATIC_MESSAGE#COMPANY')}</b>,{' '}
                  {t('usersPage:PASSWORD_AUTOMATIC_MESSAGE#DESCRIPTION_1')}
                </p>
                <p>{t('usersPage:PASSWORD_AUTOMATIC_MESSAGE#LAST_DESCRIPTION')}</p>
              </Space>
            </Box>
            <FormControl>
              <>
                <div className="flex items-end gap-2 w-full">
                  {ManualPasswordForm}
                  <Button
                    variant="ghost"
                    type="button"
                    onClick={() => navigator.clipboard.writeText(automaticPassword)}
                    data-testid="users-form-button-copy-password"
                  >
                    <Copy size={'16px'} />
                    Copiar
                  </Button>
                </div>
              </>
            </FormControl>
          </Space>
        ),
        link: (
          <Box className="rounded-3xl bg-neutral-50 dark:bg-neutral-800 dark:text-neutral-200">
            <Space direction="column" size="md">
              <b>{t('usersPage:PASSWORD_LINK_MESSAGE#TITLE')}</b>
              <p>
                {t('usersPage:PASSWORD_LINK_MESSAGE#DESCRIPTION_0')}{' '}
                <b>{t('usersPage:PASSWORD_LINK_MESSAGE#COMPANY')}</b>,{' '}
                {t('usersPage:PASSWORD_LINK_MESSAGE#DESCRIPTION_1')}
              </p>
            </Space>
          </Box>
        ),
      }

      return components[passwordInputMethod]
    }

    return (
      <Form {...formContext}>
        <form onSubmit={formContext.handleSubmit(handleSubmit)} autoComplete="off">
          <Box border className="mb-6">
            <FormField
              name="isClientUser"
              control={formContext.control}
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <Space className="items-center">
                        <Switch
                          id="CREATE_USER_LABEL_CLIENT_ACCOUNT"
                          onCheckedChange={field.onChange}
                          data-testid="users-form-isClientUser-toggle"
                          checked={field.value}
                        />
                        <Label htmlFor="CREATE_USER_LABEL_CLIENT_ACCOUNT">
                          {t('usersPage:CREATE_USER_LABEL_CLIENT_ACCOUNT')}
                        </Label>
                      </Space>
                    </FormControl>
                  </FormItem>
                )
              }}
            />

            <Separator className="w-full my-6 border-b" />

            <section>
              <p className={'text-neutral-950 dark:text-neutral-100 text-base font-bold mb-5'}>
                {t('common:GENERAL_DATA')}
              </p>

              <Space className="w-full" direction="column" size="lg">
                <div className="w-full">
                  <FormField
                    name="name"
                    control={formContext.control}
                    render={({ field, fieldState }) => {
                      return (
                        <FormItem>
                          <FormControl>
                            <>
                              <Label htmlFor="name">{t('usersPage:CREATE_USER_LABEL_NAME')}</Label>
                              <InputText
                                {...field}
                                type="text"
                                id="name"
                                data-testid="users-form-input-name"
                                isError={Boolean(fieldState?.error?.message)}
                                autoComplete="off"
                              />
                            </>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>

                <div className="w-full">
                  <FormField
                    name="email"
                    control={formContext.control}
                    render={({ field, fieldState }) => {
                      return (
                        <FormItem>
                          <FormControl>
                            <>
                              <Label htmlFor="name">{t('common:EMAIL_LABEL')}</Label>
                              <InputText
                                {...field}
                                type="text"
                                id="email"
                                placeholder={t('common:FORM_PLACEHOLDERS#EMAIL')}
                                data-testid="users-form-input-email"
                                isError={Boolean(fieldState?.error?.message)}
                                autoComplete="off"
                              />
                            </>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>

                {!isClientUser && (
                  <>
                    <Space className="w-full flex-col md-flex-row" size="lg">
                      <div className="w-full">
                        <FormField
                          name="phoneNumber"
                          control={formContext.control}
                          render={({ field, fieldState }) => {
                            return (
                              <FormItem>
                                <FormControl>
                                  <>
                                    <Label htmlFor="phoneNumber">
                                      {t('common:PHONE')} ({t('common:OPTIONAL_LABEL')})
                                    </Label>
                                    <InputText
                                      type="text"
                                      id="phoneNumber"
                                      placeholder={t('common:FORM_PLACEHOLDERS#PHONE')}
                                      data-testid="users-form-input-phoneNumber"
                                      isError={Boolean(fieldState?.error?.message)}
                                      {...field}
                                      autoComplete="off"
                                      inputMode="numeric"
                                      onChange={(e) => {
                                        const input = e.target.value
                                        const regex = /^[0-9()\s-]*$/

                                        if (regex.test(input)) {
                                          field.onChange(e)
                                        }
                                      }}
                                    />
                                  </>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )
                          }}
                        />
                      </div>

                      <div className="w-full">
                        <FormField
                          name="branch"
                          control={formContext.control}
                          render={({ field, fieldState }) => {
                            return (
                              <FormItem>
                                <FormControl>
                                  <>
                                    <Label htmlFor="name">
                                      {t('usersPage:CREATE_USER_LABEL_BRANCH')} ({t('common:OPTIONAL_LABEL')})
                                    </Label>
                                    <InputText
                                      type="text"
                                      id="branch"
                                      data-testid="users-form-input-branch"
                                      isError={Boolean(fieldState?.error?.message)}
                                      {...field}
                                    />
                                  </>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )
                          }}
                        />
                      </div>
                    </Space>

                    <div className="w-full">
                      <FormField
                        name="roles"
                        control={formContext.control}
                        render={({ field, fieldState }) => {
                          return (
                            <FormItem>
                              <FormControl>
                                <>
                                  <Label htmlFor="roles">{t('usersPage:TABLE_COLUMN_POSTS')}</Label>
                                  <RolesSelect
                                    {...field}
                                    id="roles"
                                    placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                                    data-testid="users-form-input-roles"
                                    isPaged={false}
                                    isClearable
                                    isMulti
                                    isError={Boolean(fieldState?.error?.message)}
                                  />
                                </>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </div>

                    <div className="w-full">
                      <FormField
                        name="departments"
                        control={formContext.control}
                        render={({ field, fieldState }) => {
                          return (
                            <FormItem>
                              <FormControl>
                                <>
                                  <Label htmlFor="departments">{t('usersPage:TABLE_COLUMN_DEPARTMENTS')}</Label>
                                  <DepartmentsSelect
                                    {...field}
                                    id="departments"
                                    isMulti
                                    placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                                    data-testid="users-form-input-departments"
                                    isPaged={false}
                                    isClearable
                                    isError={Boolean(fieldState?.error?.message)}
                                  />
                                </>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </div>

                    <div className="w-full">
                      <FormField
                        name="timetable"
                        control={formContext.control}
                        render={({ field, fieldState }) => {
                          return (
                            <FormItem>
                              <FormControl>
                                <>
                                  <Label htmlFor="timetable">
                                    {t('usersPage:TABLE_COLUMN_TIME_SCHEDULE')} ({t('common:OPTIONAL_LABEL')})
                                  </Label>
                                  <TimetableSelect
                                    {...field}
                                    id="timetable"
                                    placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                                    data-testid="users-form-input-timetable"
                                    isError={Boolean(fieldState?.error?.message)}
                                    isPaged={false}
                                    isClearable
                                  />
                                </>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </div>
                  </>
                )}
                {isClientUser && (
                  <div className="w-full">
                    <FormField
                      name="organizations"
                      control={formContext.control}
                      render={({ field, fieldState }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <>
                                <Label htmlFor="organizations">{t('usersPage:CREATE_USER_LABEL_ORGANIZATION')}</Label>
                                <OrganizationSelect
                                  {...field}
                                  id="organizations"
                                  isMulti
                                  placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                                  data-testid="users-form-input-organizations"
                                  isError={Boolean(fieldState?.error?.message)}
                                  isPaged={false}
                                  isClearable
                                />
                              </>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                )}
                {timeTable && <TimeTable timeTable={timeTable} />}
              </Space>
            </section>

            <Separator className="w-full my-6 border-b" />

            <section>
              <p className="text-neutral-950 dark:text-neutral-100 text-base font-bold mb-5">
                {t('common:SYSTEM_ACCESS')}
              </p>
              {isEdit && EditPasswordField}
              {RenderPasswordCreationMethod()}
            </section>
          </Box>

          <Space className="w-full">
            <Button variant="secondary" type="button" onClick={onCancel} data-testid="users-form-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={isPending || !isValid}
              data-testid="users-form-button-save"
            >
              {isPending ? <LoaderIcon className="animate-spin" /> : null}
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </Space>
        </form>
      </Form>
    )
  },
)

export { UsersForm, type UserFormValues, type UsersFormRef }
