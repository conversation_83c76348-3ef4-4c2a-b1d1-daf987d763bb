import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
  Button,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError, HttpStatusCode } from 'axios'
import { ChevronLeftIcon } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import { Link } from 'react-router-dom'
import usersApi, { type UserBody, usersQueryKey } from '../../api/users'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { UsersForm, UsersFormRef } from './components/form'
import { LightningLearningBox } from './components/lightning-learning-box'
import { parseUserFormValues } from './utils'
import { Title } from '../../components/unconnected/typography'
import { FormPageSkeleton } from '../../components/unconnected/skeletons/form-page-skeleton'

const EditUsers = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: Boolean(id),
  })
  const history = useHistory()
  const usersFormRef = useRef<UsersFormRef>(null)

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: UserBody) => usersApi.update(id, values),
    onSuccess: async () => {
      toast.success(t('usersPage:USER_UPDATED_SUCCESSFULLY'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [usersQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [usersQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])

      history.replace('/users')
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const errorMap = {
        'Recently used password': 'usersPage:RECENTLY_USED_PASSWORD',
      }

      const errorHandlingMap = {
        [HttpStatusCode.BadRequest]: 'usersPage:RECENTLY_USED_PASSWORD',
        [HttpStatusCode.PaymentRequired]: 'usersPage:CREATE_USER_PLAN_LIMIT_REACHED',
        [HttpStatusCode.InternalServerError]: 'common:MESSAGE_ERROR_SERVER_PROBLEM',
        [HttpStatusCode.Conflict]: 'usersPage:MODAL_DUPLICATE_NAME',
      }

      const { status, data: errorData } = error.response || {}

      const errorMessage = errorMap[errorData?.message] || errorHandlingMap[status]

      return toast.error(t(errorMessage))
    },
  })

  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (usersFormRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/users')
  }, [usersFormRef])

  const defaultValues = useMemo(
    () => ({
      ...data,
      departments: data?.departments?.map((department) => ({
        label: department.name,
        value: department.id,
      })),
      roles: data?.roles?.map((role) => ({
        label: role.displayName,
        value: role.id,
      })),
      organizations: data?.organizations?.map((organization) => ({
        value: organization.id,
        label: organization.name,
      })),
      ...(data?.timetableId && { timetable: { value: data.timetableId } }),
    }),
    [data],
  )

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/users')
    }
  }, [isError])

  if (isFetching) {
    return <FormPageSkeleton />
  }

  return (
    <Container>
      <Helmet title={`${t('common:LABEL_EDITING_STAGE')} ${t('GET_TITLE_USER')}`} />
      <ContainerHeader mtSize="sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <Link to="/users">{t('TITLE_USERS')}</Link>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {t('common:LABEL_EDITING_STAGE')} {t('GET_TITLE_USER')}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Space className="w-full items-center" size="sm">
          <Button
            icon
            variant="ghost"
            onClick={handleGoBack}
            aria-label="Go back to users' list"
            data-testid="users-edit-button-go-back"
            role="link"
          >
            <ChevronLeftIcon />
          </Button>

          <Title data-testid="users-edit-heading">
            {t('common:LABEL_EDITING_STAGE')} {t('usersPage:GET_TITLE_USER')}
          </Title>
        </Space>
      </ContainerHeader>
      <ContainerContent>
        <Space className="gap-6">
          <div className="flex-1">
            <UsersForm
              ref={usersFormRef}
              onSubmit={(values) => mutateAsync(parseUserFormValues(values))}
              onCancel={() => history.replace('/users')}
              isPending={isPending}
              defaultValues={defaultValues}
              isEdit
            />
          </div>

          <aside className="hidden md-block">
            <LightningLearningBox />
          </aside>
        </Space>
      </ContainerContent>

      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/users')}
      />
    </Container>
  )
}

export { EditUsers }
