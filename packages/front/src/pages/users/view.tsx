import {
  <PERSON>ton,
  <PERSON>alog,
  DialogBody,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Label,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import usersApi, { usersQueryKey } from '../../api/users'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const ViewUsers = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: <PERSON><PERSON><PERSON>(id),
  })

  const userToString = (users) => users && users.map((user) => user.name || user.displayName).join(', ')

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/users')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <DialogContent>
        <DialogHeader className="flex items-center">
          <DialogTitle className="break-words max-w-[500px] text-center">
            {isFetching && <Skeleton width="150px" height="24px" />}
            {isSuccess && !isFetching && data.name}
          </DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && (
            <Space className="w-full items-start" direction="column">
              <Space size="sm">
                <Label>{t('TABLE_COLUMN_NAME')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('TABLE_COLUMN_EMAIL')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('CREATE_USER_LABEL_PHONE')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('CREATE_USER_LABEL_BRANCH')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('TABLE_COLUMN_POSTS')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('TABLE_COLUMN_DEPARTMENTS')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Label>{t('TABLE_COLUMN_TIME_SCHEDULE')}:</Label>
                <Skeleton width="150px" height="24px" />
              </Space>
            </Space>
          )}

          {isSuccess && !isFetching && (
            <Space className="items-start w-150 text-neutral-1000 dark:text-neutral-200" direction="column">
              <div className="flex items-baseline gap-1">
                <span className="font-bold">{t('TABLE_COLUMN_NAME')}:</span>{' '}
                <p className="break-words max-w-[552px]">{data.name || '-'}</p>
              </div>
              <div>
                <span className="font-bold">{t('TABLE_COLUMN_EMAIL')}:</span> <span>{data.email || '-'}</span>
              </div>
              <div>
                <span className="font-bold">{t('CREATE_USER_LABEL_PHONE')}:</span>{' '}
                <span>{data?.phoneNumber || '-'}</span>
              </div>
              <div>
                <span className="font-bold">{t('CREATE_USER_LABEL_BRANCH')}:</span> <span>{data?.branch || '-'}</span>
              </div>
              <div>
                <span className="font-bold">{t('TABLE_COLUMN_POSTS')}:</span>{' '}
                <span>{userToString(data?.roles) || '-'}</span>
              </div>
              <div>
                <span className="font-bold">{t('TABLE_COLUMN_DEPARTMENTS')}:</span>{' '}
                <span>{userToString(data?.departments) || '-'}</span>
              </div>
              <div>
                <span className="font-bold">{t('TABLE_COLUMN_TIME_SCHEDULE')}:</span>{' '}
                <span>{data?.timetable?.name || '-'}</span>
              </div>
            </Space>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="users-view-button-close">
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ViewUsers }
