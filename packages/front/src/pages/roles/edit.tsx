import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef } from 'react'
import { RolesForm, RolesFormRef, RolesFormValues } from './components/form'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import {
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
  Breadcrumb,
  BreadcrumbItem,
  Space,
  Button,
  toast,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { LightningLearningBox } from './components/lightning-learning-box'
import { Link, useHistory, useParams } from 'react-router-dom'
import { Title } from '../../components/unconnected/typography'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import rolesApi, { RolesData, rolesQueryKey } from '../../api/roles'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { ChevronLeftIcon } from 'lucide-react'
import { PermissionsModel } from '../../api/permissions'
import { AxiosError, HttpStatusCode } from 'axios'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { FormPageSkeleton } from '../../components/unconnected/skeletons/form-page-skeleton'
import { Helmet } from 'react-helmet'

// don't forget to implements skeleton screen
const RolesEdit = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['rolesPage', 'common'])

  const { data, isLoading, isError, isSuccess } = useQuery({
    queryKey: [rolesQueryKey, id],
    queryFn: () => rolesApi.getById(id, { include: ['permissions'] }),
    enabled: !!id,
  })
  const history = useHistory()
  const queryClient = useQueryClient()
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: RolesData) => rolesApi.update(id, values),
    async onSuccess() {
      toast.success(t('EDIT_ROLES_SUCCESS'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, id] }),
      ])
      history.replace('/roles')
    },
    onError(error: AxiosError) {
      const errorHandlingMap = {
        [HttpStatusCode.Unauthorized]: 'rolesPage:LABEL_ERROR_MESSAGE_DISPLAY',
        [HttpStatusCode.Conflict]: 'rolesPage:MODAL_DUPLICATE_NAME',
      }

      return toast.error(t(errorHandlingMap[error.response?.status] || 'common:MESSAGE_ERROR_SERVER_PROBLEM'))
    },
  })

  const handleMutate = (formValues: RolesFormValues) => {
    mutateAsync({
      displayName: formValues.name,
      permissions: [...formValues.permissions].map((v) => ({ id: v })) as PermissionsModel[],
    })
  }

  const defaultValues = useMemo<RolesFormValues>(
    () => ({
      name: data?.displayName,
      permissions: new Set(data?.permissions?.flatMap((p) => p.id) ?? []),
    }),
    [data],
  )

  const formRef = useRef<RolesFormRef>(null)
  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (formRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/roles')
  }, [])

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/roles')
    }
  }, [isError])

  if (isLoading) {
    return (
      <FormPageSkeleton>
        <Skeleton className="w-full h-80" />
      </FormPageSkeleton>
    )
  }
  return (
    <>
      <Helmet title={`${t('common:ACTIONS_SUBMENU_EDIT')} ${t('TITLE_ROLES').toLowerCase()}`} />
      <Container>
        <ContainerHeader mtSize="sm">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <Link to="/roles" data-testid={'roles-edit-breadcrumb-link-to-roles-list'}>
                  {t('TITLE_ROLES_PLURAL')}
                </Link>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>
                  {t('common:ACTIONS_SUBMENU_EDIT')} {t('TITLE_ROLES').toLowerCase()}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <Space className="w-full items-center" size="sm">
            <Button
              icon
              variant="ghost"
              onClick={handleGoBack}
              aria-label="Go back to roles' list"
              data-testid="roles-edit-button-go-back"
              role="link"
            >
              <ChevronLeftIcon />
            </Button>
            <Title data-testid="roles-edit-heading">
              {t('common:ACTIONS_SUBMENU_EDIT')} {t('rolesPage:TITLE_ROLES').toLowerCase()}
            </Title>
          </Space>
        </ContainerHeader>
        <ContainerContent>
          {isSuccess && (
            <Space className="gap-6">
              <div className="flex-1">
                <RolesForm onSubmit={handleMutate} isPending={isPending} defaultValues={defaultValues} ref={formRef} />
              </div>
              <aside className="hidden md-block">
                <LightningLearningBox />
              </aside>
            </Space>
          )}
        </ContainerContent>
      </Container>
      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/roles')}
      />
    </>
  )
}

export { RolesEdit }
