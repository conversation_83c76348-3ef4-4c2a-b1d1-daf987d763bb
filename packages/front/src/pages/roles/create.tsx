import React, { use<PERSON><PERSON>back, useMemo, useRef } from 'react'
import { RolesForm, RolesFormRef, RolesFormValues } from './components/form'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { Title } from '../../components/unconnected/typography'
import { useTranslation } from 'react-i18next'
import { Link, useHistory, useLocation } from 'react-router-dom'
import { LightningLearningBox } from './components/lightning-learning-box'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import rolesApi, { rolesQueryKey, type RolesData } from '../../api/roles'
import { PermissionsModel } from '../../api/permissions'
import useToggle from '../../app/hooks/useToggle'
import { ChevronLeftIcon } from 'lucide-react'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { AxiosError, HttpStatusCode } from 'axios'
import { Helmet } from 'react-helmet'

// don't forget to implements skeleton screen
const RolesCreate = () => {
  const location = useLocation<{ duplicateRoleId?: string }>()
  const { duplicateRoleId } = location.state || {}
  const history = useHistory()
  const formRef = useRef<RolesFormRef>(null)
  const { t } = useTranslation(['rolesPage', 'common'])
  const queryClient = useQueryClient()
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: RolesData) => rolesApi.create(values),
    onSuccess() {
      toast.success(t('ADD_ROLES_SUCCESS'))
      queryClient.invalidateQueries({ queryKey: [rolesQueryKey, 'list'] })
      history.replace('/roles')
    },
    onError(error: AxiosError) {
      const errorHandlingMap = {
        [HttpStatusCode.Unauthorized]: 'rolesPage:LABEL_ERROR_MESSAGE_DISPLAY',
        [HttpStatusCode.Conflict]: 'rolesPage:MODAL_DUPLICATE_NAME',
      }

      toast.error(t(errorHandlingMap[error.response?.status] || 'common:MESSAGE_ERROR_SERVER_PROBLEM'))
    },
  })

  const handleMutate = (formValues: RolesFormValues) => {
    mutateAsync({
      displayName: formValues.name,
      permissions: [...formValues.permissions].map((v) => ({ id: v })) as PermissionsModel[],
    })
  }

  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const defaultValues = useMemo(() => ({ duplicateRoleId: duplicateRoleId || null }), [duplicateRoleId])

  const handleGoBack = useCallback(() => {
    if (formRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/roles')
  }, [])

  return (
    <>
      <Helmet title={`${t('common:LIST_ADD_NEW')} ${t('TITLE_ROLES').toLowerCase()}`} />
      <Container>
        <ContainerHeader mtSize="sm">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <Link to="/roles" data-testid="roles-create-breadcrumb-link-to-roles-list">
                  {t('TITLE_ROLES_PLURAL')}
                </Link>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>
                  {t('common:LIST_ADD_NEW')} {t('TITLE_ROLES').toLowerCase()}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <Space className="w-full items-center" size="sm">
            <Button
              icon
              variant="ghost"
              onClick={handleGoBack}
              aria-label="Go back to roles' list"
              data-testid="roles-create-button-go-back"
              role="link"
            >
              <ChevronLeftIcon />
            </Button>
            <Title data-testid="roles-create-heading">
              {t('common:LIST_ADD_NEW')} {t('rolesPage:TITLE_ROLES').toLowerCase()}
            </Title>
          </Space>
        </ContainerHeader>
        <ContainerContent>
          <Space className="gap-6">
            <div className="flex-1">
              <RolesForm onSubmit={handleMutate} isPending={isPending} ref={formRef} defaultValues={defaultValues} />
            </div>
            <aside className="hidden md-block">
              <LightningLearningBox />
            </aside>
          </Space>
        </ContainerContent>
      </Container>
      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/roles')}
      />
    </>
  )
}

export { RolesCreate }
