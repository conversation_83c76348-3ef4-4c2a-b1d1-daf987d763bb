import { Space, Box } from '@ikatec/nebula-react'
import { ZapIcon } from 'lucide-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

const LightningLearningBox = () => {
  const { t } = useTranslation(['rolesPage', 'usersPage', 'common'])
  return (
    <Box variant="secondary" border>
      <Space className="text-primary-800 dark:text-primary-300 font-bold text-sm items-center mb-4" size="sm">
        <ZapIcon width={24} height={24} />
        <p className="font-bold text-base">{t('common:LIGHTNING_LEARNING')}</p>
      </Space>

      <div className="pl-8 text-neutral-950 dark:text-neutral-300 text-sm">
        <div className="font-bold mb-2">
          {t('rolesPage:TITLE_ROLES_PLURAL')} {t('common:AND')} {t('rolesPage:Permissões').toLocaleLowerCase()}
        </div>
        <div className="w-72">{t('usersPage:USER_PERMISSIONS_DESCRIPTION')}</div>
      </div>
    </Box>
  )
}

export { LightningLearningBox }
