import { zodResolver } from '@hookform/resolvers/zod'

import {
  Accordion,
  AccordionContent,
  AccordionDescription,
  AccordionItem,
  AccordionProps,
  AccordionTrigger,
  Badge,
  Box,
  Button,
  Checkbox,
  CheckboxProps,
  InputText,
  Label,
  Space,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Tooltip,
} from '@ikatec/nebula-react'
import { CheckedState } from '@radix-ui/react-checkbox'
import { useQuery } from '@tanstack/react-query'
import { HelpCircleIcon, LoaderIcon, SearchIcon } from 'lucide-react'
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import z from 'zod'
import permissionsApi, {
  PermissionsByTabEnum,
  permissionsQueryKey,
  PermissionsTypeEnum,
} from '../../../api/permissions'
import { NO_PAGINATED_REQUEST } from '../../../api/types'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'
import { RolesSelect } from '../../../components/connected/role-select'
import { SubTitle } from '../../../components/unconnected/typography/sub-title'
import { GroupedPermissionByType, groupPermissionsByTab, groupPermissionsByType } from '../../../utils/permissions'
import { useHistory } from 'react-router'
import rolesApi, { RolesModel, rolesQueryKey } from '../../../api/roles'
import { DuplicateRolesPermissionConfirmationDialog } from './duplicate-roles-permission-confirmation-dialog'
import { HighlightText } from '../../../components/unconnected/highlight-text'
import { debounceFn } from '../../../utils/debouce-fn'
import { ResultFeedback } from '../../../components/unconnected/result-feedback'
import { cn } from '../../../app/components/common/unconnected/ui/utils'

const formSchema = z
  .object({
    name: z
      .string()
      .trim()
      .nonempty({ message: 'common:REQUIRED_FIELD' })
      .max(255, { message: 'common:INPUT_FORM_MESSAGES#MAX_SIZE' })
      .default(''),
    permissions: z
      .set(z.string({ message: 'common:REQUIRED_FIELD' }))
      .min(1, { message: 'common:REQUIRED_FIELD' })
      .default(new Set()),
    duplicateRoleId: z.string().nullish().optional().default(null),
  })
  .refine(
    ({ permissions }) => {
      return permissions.size > 0
    },
    { message: "'common:REQUIRED_FIELD" },
  )

type RolesFormValues = z.infer<typeof formSchema>

interface RolesFormProps {
  isPending?: boolean
  onSubmit(values: RolesFormValues): void
  defaultValues?: RolesFormValues
  disabled?: boolean
}
type RolesFormRef = { isDirty: boolean }

const permissionsWithCustomRules = [PermissionsTypeEnum.MESSAGES, PermissionsTypeEnum.TICKETS]

const uniquePermissionsByType = {
  [PermissionsTypeEnum.MESSAGES]: [
    'messages.view.all',
    'messages.view.department',
    'messages.view.owned',
    'messages.view.ticket.mine.current',
    'messages.view.ticket.department.current',
    'messages.view.ticket.mine',
    'messages.view.ticket.department',
    'messages.view.transfer.department',
    'messages.view.transfer.mine',
  ],
  [PermissionsTypeEnum.TICKETS]: ['tickets.view.all', 'tickets.view.all.departments'],
}

const permissionOnAllSelectedByType = {
  [PermissionsTypeEnum.MESSAGES]: 'messages.view.all',
  [PermissionsTypeEnum.TICKETS]: 'tickets.view.all',
}

const initialValues = { name: '', permissions: new Set<string>(), duplicateRoleId: null }

interface FilterPermissionsInputProps {
  onChange(filter: string): void
}
interface FilterPermissionsInputRef {
  reset: VoidFunction
}
const FilterPermissionsInput = memo(
  forwardRef<FilterPermissionsInputRef, FilterPermissionsInputProps>(
    ({ onChange }: FilterPermissionsInputProps, ref) => {
      const { t } = useTranslation(['rolesPage'])
      const [searchText, setSearchText] = useState('')

      const inputRef = useRef<HTMLInputElement>(null)
      useEffect(() => {
        debounceFn(() => {
          onChange(searchText)
        }, 10)
      }, [searchText])

      useImperativeHandle(ref, () => ({
        reset() {
          inputRef.current?.focus()
          setSearchText('')
        },
      }))

      return (
        <div className="my-4">
          <InputText
            icon={<SearchIcon />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder={t('rolesPage:FORM_PERMISSIONS_INPUT_SEARCH_PLACEHOLDER')}
            onClean={() => setSearchText('')}
            ref={inputRef}
          />
        </div>
      )
    },
  ),
  () => true,
)

const permissionsWithTooltip = [
  'messages.destroy',
  'messages.view.all',
  'messages.view.out.queue',
  'messages.view.ticket.mine',
  'messages.view.ticket.mine.current',
  'messages.view.transfer.mine',
  'messages.view.ticket.department',
  'messages.view.ticket.department.current',
  'messages.view.transfer.department',
]

const stopPropagation = (e) => {
  e.stopPropagation()
}

interface PermissionAccordionProps {
  value: string[]
  onValueChange: (value: string[]) => void
  hideAccordion?: (type: PermissionsTypeEnum) => boolean
  groupedPermissionsByTab: GroupedPermissionByType
  permissionCheckboxVariant: (type: PermissionsTypeEnum) => { checked: boolean; variant: 'multiselect' | 'default' }
  tab: PermissionsByTabEnum
  disabled: boolean
  handleCheckAllByType: (type: PermissionsTypeEnum, checked: boolean) => void
  handleCheckboxPermissionChange: (id: string, checked: boolean) => void
  searchText?: string
  selectedPermissions: Set<string>
}
const PermissionAccordion = ({
  value,
  onValueChange,
  groupedPermissionsByTab,
  permissionCheckboxVariant,
  hideAccordion,
  tab,
  disabled,
  handleCheckAllByType,
  searchText,
  selectedPermissions,
  handleCheckboxPermissionChange,
}: PermissionAccordionProps) => {
  const { t } = useTranslation(['permissions', 'rolesPage', 'common'])
  return (
    <Accordion
      className="w-full bg-transparent px-0 first:pt-4"
      type="multiple"
      value={value}
      onValueChange={onValueChange}
    >
      {Array.from(groupedPermissionsByTab.get(tab))?.map?.(([type, permissionsList], permissionAccordionIndex) => {
        if (hideAccordion?.(type)) return null
        const { checked: typeChecked, variant: typeVariant } = permissionCheckboxVariant(type)
        return (
          <AccordionItem key={type} value={type} className="last:pb-0 last:mb-0 pb-4 mb-4">
            <AccordionTrigger className="data-[state=open]:mb-4">
              <Space className="items-center">
                <div tabIndex={-1} role="button" onClick={stopPropagation} onKeyDown={stopPropagation}>
                  <Checkbox
                    id={type}
                    variant={typeVariant}
                    onCheckedChange={(c) => handleCheckAllByType(type, Boolean(c))}
                    checked={typeChecked}
                    disabled={disabled}
                    data-testid={`roles-form-accordions-checkbox-accordionTrigger-${permissionAccordionIndex}-selectAllPermissionGroup`}
                  />
                </div>

                <AccordionDescription className="m-0 font-bold">
                  <HighlightText
                    highlightText={searchText}
                    text={t(`permissions:GROUP_NAME#${type}`)}
                    className="leading-[24px] font-semibold"
                  />
                </AccordionDescription>
              </Space>
            </AccordionTrigger>
            <AccordionContent className="px-6">
              <div className="grid grid-cols-2 gap-4">
                {permissionsList?.map?.((permission, accordionContent) => (
                  <Space size="md" className="items-top">
                    <Space className="items-center" key={permission.id}>
                      <Checkbox
                        id={permission.id}
                        checked={selectedPermissions.has(permission.id)}
                        disabled={disabled}
                        onCheckedChange={(c) => handleCheckboxPermissionChange(permission.id, Boolean(c))}
                        data-testid={`roles-form-accordions-checkbox-accordionTrigger-${permissionAccordionIndex}-accordionContent-checkbox-${accordionContent}`}
                      />
                      <Label htmlFor={permission.id}>
                        <HighlightText
                          highlightText={searchText}
                          text={t(`permissions:DISPLAY_NAME#${permission.name}`)}
                          className="leading-[21px] font-normal"
                        />
                      </Label>
                    </Space>

                    {permissionsWithTooltip.includes(permission.name) && (
                      <Tooltip
                        content={t(`permissions:PERMISSIONS_INFO#${permission.name}`)}
                        onClick={(e) => e.preventDefault()}
                      >
                        <div>
                          <HelpCircleIcon size={18} className="dark:text-neutral-500 text-neutral-400" />
                        </div>
                      </Tooltip>
                    )}
                  </Space>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        )
      })}
    </Accordion>
  )
}

const RolesForm = forwardRef<RolesFormRef, RolesFormProps>(
  ({ isPending, onSubmit, defaultValues, disabled }: RolesFormProps, ref) => {
    const defaultValuesHasBeenApplied = useRef(false)
    const [openDuplicateConfirmationDialog, setOpenDuplicateConfirmationDialog] = useState(false)
    const [openAccordions, setOpenAccordions] = useState<Set<PermissionsTypeEnum>>(new Set())
    const history = useHistory()
    const { t } = useTranslation(['permissions', 'rolesPage', 'common'])
    const { data: permissions = [], isSuccess } = useQuery({
      queryKey: [permissionsQueryKey, 'list', NO_PAGINATED_REQUEST],
      queryFn: () => permissionsApi.getNoPaginatedAll(),
    })

    const groupedPermissionsByType = useMemo(() => {
      return groupPermissionsByType(permissions)
    }, [permissions])

    const groupedPermissionsByTab = useMemo(() => {
      return groupPermissionsByTab(groupedPermissionsByType)
    }, [groupedPermissionsByType])

    const permissionTabs = useMemo(() => Object.values(PermissionsByTabEnum), [])

    const form = useForm<RolesFormValues>({
      defaultValues: initialValues,
      resetOptions: {
        keepDefaultValues: true,
      },
      resolver: zodResolver(formSchema),
      mode: 'all',
      reValidateMode: 'onChange',
      criteriaMode: 'all',
    })

    const selectedPermissions = form.watch('permissions')
    const duplicateRoleId = form.watch('duplicateRoleId')

    const { data: roleData } = useQuery({
      queryKey: [rolesQueryKey, duplicateRoleId],
      queryFn: () => rolesApi.getById(duplicateRoleId, { include: ['permissions'] }),
      enabled: !!duplicateRoleId,
    })

    useEffect(() => {
      if (!defaultValues || defaultValuesHasBeenApplied.current) return

      form.reset({ ...initialValues, ...defaultValues })
      defaultValuesHasBeenApplied.current = true

      return () => {
        defaultValuesHasBeenApplied.current = false
      }
    }, [defaultValues, initialValues])

    const { isValid, isDirty } = form.formState

    const handleSelectAll = (checked: boolean) => {
      const newSelectedPermissions = new Set<string>()
      if (checked) {
        permissions.forEach((permission) => {
          const permissionObject = permissions.find(({ id }) => id === permission.id)
          const hasCustomRule = permissionsWithCustomRules.includes(permissionObject.type)
          const permissionShouldBeUnique = (uniquePermissionsByType[permissionObject.type] as string[])?.includes(
            permissionObject.name,
          )
          const isDefault = permissionOnAllSelectedByType[permissionObject.type] === permissionObject.name

          if (hasCustomRule && permissionShouldBeUnique && !isDefault) {
            return
          }
          newSelectedPermissions.add(permission.id)
        })
      }

      if (!newSelectedPermissions.size) {
        form.resetField('duplicateRoleId')
      }
      return newSelectedPermissions
    }

    const handleCheckboxPermissionChange = (permissionId: string, checked: CheckedState) => {
      const permissionObject = permissions.find(({ id }) => id === permissionId)
      const hasCustomRule = permissionsWithCustomRules.includes(permissionObject.type)
      const permissionShouldBeUnique = (uniquePermissionsByType[permissionObject.type] as string[])?.includes(
        permissionObject.name,
      )

      const newSelectedPermissions = new Set(selectedPermissions)

      if (checked) {
        newSelectedPermissions.add(permissionId)
      } else {
        newSelectedPermissions.delete(permissionId)
      }

      if (hasCustomRule && permissionShouldBeUnique) {
        const uniquePermissions = (uniquePermissionsByType[permissionObject.type] ?? []) as string[]

        uniquePermissions
          .filter((p) => p !== permissionObject.name)
          .forEach((uniquePermission) => {
            const permission = permissions.find(({ name }) => name === uniquePermission)

            if (!permission) return

            newSelectedPermissions.delete(permission?.id)
          })
      }

      return newSelectedPermissions
    }

    const handleCheckAllByType = (permissionType: PermissionsTypeEnum, checked: CheckedState) => {
      const newSet = new Set<string>(selectedPermissions)

      groupedPermissionsByType.get(permissionType).forEach(({ id }) => {
        const permissionObject = permissions.find(({ id: pId }) => pId === id)
        const hasCustomRule = permissionsWithCustomRules.includes(permissionObject.type)
        const permissionShouldBeUnique = (uniquePermissionsByType[permissionObject.type] as string[])?.includes(
          permissionObject.name,
        )
        const isDefault = permissionOnAllSelectedByType[permissionObject.type] === permissionObject.name

        if ((hasCustomRule && permissionShouldBeUnique && !isDefault) || !checked) {
          return newSet.delete(id)
        }
        newSet.add(id)
      })

      return newSet
    }

    const permissionCheckboxVariant = (permissionType: PermissionsTypeEnum) => {
      let variant: CheckboxProps['variant'] = 'default'

      const permissionsList = groupedPermissionsByType.get(permissionType) ?? []
      const totalPermissions = permissionsList.length ?? 0

      const selectedPermissionByType = [...selectedPermissions].filter((p) =>
        permissionsList.some(({ id }) => id === p),
      )

      if (selectedPermissionByType.length > 0 && selectedPermissionByType.length < totalPermissions) {
        variant = 'multiselect'
      }

      return { checked: selectedPermissionByType.length > 0, variant }
    }

    const selectedAllCheckboxType = useMemo<CheckboxProps['variant']>(() => {
      if (!selectedPermissions) return 'default'
      if (selectedPermissions.size === 0) {
        return 'default'
      }

      if (selectedPermissions.size === permissions.length) {
        return 'default'
      }

      return 'multiselect'
    }, [selectedPermissions, permissions.length])

    const getSelectedPermissionsByTab = (tabType: PermissionsByTabEnum): number => {
      const tabPermissions = groupedPermissionsByTab?.get?.(tabType)

      return (
        [...tabPermissions].reduce((prev, acc) => {
          const [, permissionList] = acc
          const permissionCount = permissionList?.reduce?.((perAcc, perCurr) => {
            let sum = 0
            if (selectedPermissions.has(perCurr.id)) sum = 1
            return perAcc + sum
          }, 0)
          return prev + permissionCount
        }, 0) || null
      )
    }

    const selectedPermissionsType = useMemo(
      () =>
        Array.from(groupedPermissionsByType)?.reduce?.((acc, [type, permissionsList]) => {
          if (
            permissionsList.reduce((isChecked, permissionModel) => {
              if (isChecked) return true

              return selectedPermissions.has(permissionModel.id)
            }, false)
          ) {
            acc.add(type)
          }
          return acc
        }, new Set<PermissionsTypeEnum>()),

      [groupedPermissionsByType, selectedPermissions],
    )

    const handleDuplicateRolesPermissions = (role: RolesModel) => {
      form.setValue('permissions', new Set((role?.permissions || []).flatMap((p) => p.id)), {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      })
    }

    useEffect(() => {
      if (selectedPermissions.size || !roleData?.id || roleData?.permissions.length === 0) return

      handleDuplicateRolesPermissions(roleData)
    }, [roleData, selectedPermissions])

    useImperativeHandle(ref, () => ({ isDirty }))

    const [lastDuplicateRoleId, setLastDuplicateRoleId] = useState(initialValues.duplicateRoleId)
    const [searchText, setSearchText] = useState('')
    const [selectedTab, setSelectedTab] = useState<PermissionsByTabEnum>(permissionTabs[0])
    const inputSearchRef = useRef<FilterPermissionsInputRef>({ reset: () => {} })

    const [filteredAccordions, filteredTabs] = useMemo(() => {
      const regex = new RegExp(searchText, 'gi')
      return [...groupedPermissionsByTab]
        .map((v) => v)
        .reduce<[accordions?: PermissionsTypeEnum[], tabs?: PermissionsByTabEnum[]]>(
          (acc, currentValue) => {
            const [tab] = currentValue

            const accordions = [...groupedPermissionsByTab.get(tab)]
              .map((v) => v)
              .reduce<PermissionsTypeEnum[]>((previousValue, curr) => {
                const [accordionName, permissionsList] = curr

                if (!permissionsList) return

                const translatedAccordionTitle = t(`permissions:GROUP_NAME#${accordionName}`)

                const matchFilter = permissionsList.some((permission) => {
                  const translatedPermissionTitle = t(`permissions:DISPLAY_NAME#${permission.name}`)

                  return translatedPermissionTitle.match(regex)
                })

                if (matchFilter || translatedAccordionTitle.match(regex)) {
                  previousValue.push(accordionName)
                }

                return previousValue
              }, [])

            if (accordions?.length) {
              acc[0].push(...accordions)
              acc[1].push(tab)
            }

            return acc
          },
          [[], []],
        )
    }, [groupedPermissionsByTab, searchText, selectedTab, t])

    useEffect(() => {
      if (!searchText) return setOpenAccordions(() => new Set([]))
      setOpenAccordions(() => new Set(filteredAccordions))
    }, [filteredAccordions, searchText])
    return (
      <>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} autoComplete="off" className="grid gap-6">
            <Box border>
              <SubTitle className="mb-4">{t('common:DETAILS_LABEL')}</SubTitle>

              <Space className="w-full" direction="column" size="lg">
                <div className="w-full">
                  <FormField
                    name="name"
                    control={form.control}
                    render={({ field, fieldState }) => {
                      return (
                        <FormItem>
                          <FormControl>
                            <fieldset>
                              <Label>{t('rolesPage:TABLE_ROLES_COLUMN_NAME')}</Label>
                              <InputText
                                {...field}
                                isError={!!fieldState.error?.message}
                                disabled={disabled}
                                readOnly={disabled}
                                autoFocus={!!defaultValues?.duplicateRoleId}
                                data-testid="roles-form-input-name"
                              />
                            </fieldset>
                          </FormControl>
                          <FormMessage messageParams={{ size: 255 }} />
                        </FormItem>
                      )
                    }}
                  />
                </div>

                {!defaultValues?.name && (
                  <div className="w-full">
                    <FormField
                      name="duplicateRoleId"
                      control={form.control}
                      render={({ field }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <fieldset>
                                <Label>{t('rolesPage:DUPLICATE_ROLE_LABEL')}</Label>
                                <RolesSelect
                                  {...field}
                                  onChange={(newValue: { value: string }) => {
                                    setLastDuplicateRoleId(field.value)
                                    field.onChange(newValue?.value)

                                    if (selectedPermissions.size) {
                                      return setOpenDuplicateConfirmationDialog(true)
                                    }

                                    if (!newValue?.value) {
                                      field.onChange(null)
                                      return handleDuplicateRolesPermissions({ permissions: [] } as RolesModel)
                                    }
                                  }}
                                  value={field.value ? { value: field.value } : null}
                                  isMulti={false}
                                  isPaged={false}
                                  placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                                  isClearable
                                  data-testid="roles-form-input-duplicate-role"
                                />
                              </fieldset>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                )}
              </Space>
            </Box>

            <Box className="pb-0" border>
              <div className="flex items-center gap-2 mb-4 justify-between">
                <Space className="items-center">
                  <SubTitle>{t('rolesPage:CREATE_ROLES_PERMISSIONS')}</SubTitle>

                  <Label>
                    {selectedPermissions.size}{' '}
                    {selectedPermissions.size === 1 ? t('common:SELECTED_LABEL') : t('common:SELECTED_LABEL_PLURAL')}
                  </Label>
                </Space>

                {!disabled && !searchText && (
                  <Space className="items-center ">
                    <Checkbox
                      variant={selectedAllCheckboxType}
                      id="select-all"
                      onCheckedChange={(c) =>
                        form.setValue('permissions', handleSelectAll(Boolean(c)), {
                          shouldDirty: true,
                          shouldTouch: true,
                          shouldValidate: true,
                        })
                      }
                      checked={selectedPermissions.size > 0}
                      data-testid="roles-form-checkbox-selectAllPermission"
                    />
                    <Label htmlFor="select-all">{t('common:LABEL_SELECT_ALL')}</Label>
                  </Space>
                )}
              </div>

              <FilterPermissionsInput onChange={setSearchText} ref={inputSearchRef} />

              <FormField
                name="permissions"
                control={form.control}
                render={({ field }) => {
                  return (
                    <div className="w-full">
                      {searchText && (
                        <div className="w-full">
                          {isSuccess && (
                            <>
                              {permissionTabs
                                .filter((tab) => filteredTabs.includes(tab))
                                .map((tab) => (
                                  <div className="pt-4 pb-6">
                                    <h4 className="uppercase text-[10px] text-neutral-900 dark:text-neutral-700">
                                      {t(`permissions:TAB_TITLE#${tab}`)}
                                    </h4>
                                    <div key={`tag-content-${tab}`}>
                                      <PermissionAccordion
                                        disabled={disabled}
                                        groupedPermissionsByTab={groupedPermissionsByTab}
                                        handleCheckAllByType={(type, checked) => {
                                          field.onChange(handleCheckAllByType(type, checked))
                                        }}
                                        handleCheckboxPermissionChange={(id, checked) => {
                                          field.onChange(handleCheckboxPermissionChange(id, checked))
                                        }}
                                        value={[...selectedPermissionsType, ...openAccordions]}
                                        onValueChange={(v) => setOpenAccordions(new Set(v as PermissionsTypeEnum[]))}
                                        permissionCheckboxVariant={permissionCheckboxVariant}
                                        selectedPermissions={selectedPermissions}
                                        tab={tab}
                                        hideAccordion={(type) => !filteredAccordions.includes(type)}
                                        searchText={searchText}
                                      />
                                    </div>
                                  </div>
                                ))}
                            </>
                          )}
                        </div>
                      )}

                      {searchText && !filteredAccordions?.length && (
                        <div>
                          <ResultFeedback
                            title={t('common:MESSAGE_NOT_FOUND_ELEMENTS')}
                            description={t('common:TRY_CLEAR_FILTERS_LABEL')}
                            icon={<SearchIcon />}
                            actions={
                              <Button
                                type="button"
                                variant="secondary"
                                size="sm"
                                onClick={() => {
                                  inputSearchRef.current?.reset()
                                }}
                              >
                                {t('common:CLEAR_SEARCH')}
                              </Button>
                            }
                          />
                        </div>
                      )}

                      {!searchText && (
                        <Tabs
                          defaultValue={selectedTab}
                          className="w-full"
                          value={selectedTab}
                          onValueChange={(newTabValue) => setSelectedTab(newTabValue as PermissionsByTabEnum)}
                        >
                          <TabsList className="tailwind">
                            {permissionTabs.map((tab, i) => {
                              const selectedPermissionCount = getSelectedPermissionsByTab(tab)
                              return (
                                <TabsTrigger
                                  key={`tag-trigger-${tab}`}
                                  value={tab}
                                  className={cn('min-w-[none]')}
                                  data-testid={`roles-form-tabs-tabTrigger-${i}`}
                                >
                                  {t(`permissions:TAB_TITLE#${tab}`)}

                                  {selectedPermissionCount && (
                                    <Badge size="sm" variant="secondary">
                                      {selectedPermissionCount}
                                    </Badge>
                                  )}
                                </TabsTrigger>
                              )
                            })}
                          </TabsList>
                          {isSuccess && (
                            <div className="pt-4 pb-6">
                              {permissionTabs.map((tab) => (
                                <TabsContent key={`tag-content-${tab}`} value={tab}>
                                  <PermissionAccordion
                                    disabled={disabled}
                                    groupedPermissionsByTab={groupedPermissionsByTab}
                                    handleCheckAllByType={(type, checked) => {
                                      field.onChange(handleCheckAllByType(type, checked))
                                    }}
                                    handleCheckboxPermissionChange={(id, checked) => {
                                      field.onChange(handleCheckboxPermissionChange(id, checked))
                                    }}
                                    value={[...selectedPermissionsType, ...openAccordions]}
                                    onValueChange={(v) => setOpenAccordions(new Set(v as PermissionsTypeEnum[]))}
                                    permissionCheckboxVariant={permissionCheckboxVariant}
                                    selectedPermissions={selectedPermissions}
                                    tab={tab}
                                    hideAccordion={(type) => false}
                                    searchText={searchText}
                                  />
                                </TabsContent>
                              ))}
                            </div>
                          )}
                        </Tabs>
                      )}
                    </div>
                  )
                }}
              />
            </Box>

            {!disabled && (
              <Space className="w-full">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={() => history.replace('/roles')}
                  data-testid="roles-form-button-cancel"
                >
                  {t('common:FORM_ACTION_CANCEL')}
                </Button>

                <Button
                  variant="primary"
                  type="submit"
                  disabled={!isValid || isPending}
                  data-testid="roles-form-button-submit"
                >
                  {isPending ? <LoaderIcon className="animate-spin" /> : null}
                  {t('common:FORM_ACTION_SAVE')}
                </Button>
              </Space>
            )}
          </form>
        </Form>

        <DuplicateRolesPermissionConfirmationDialog
          open={openDuplicateConfirmationDialog}
          onOpenChange={setOpenDuplicateConfirmationDialog}
          onConfirm={() => handleDuplicateRolesPermissions(roleData)}
          onCancel={() => {
            form.setValue('duplicateRoleId', lastDuplicateRoleId)
          }}
        />
      </>
    )
  },
)

export { RolesForm, type RolesFormValues, type RolesFormRef }
