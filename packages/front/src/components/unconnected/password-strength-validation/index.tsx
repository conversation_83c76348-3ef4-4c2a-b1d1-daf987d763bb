import { CheckIcon, XIcon } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { cn } from '../../../app/components/common/unconnected/ui/utils'
import zxcvbnHelper from '../../../app/utils/zxcvbnHelper'
import { passwordValidators } from './utils'

interface PasswordStrengthValidationProps {
  password?: string
}

const PasswordStrengthValidation = ({ password }: PasswordStrengthValidationProps) => {
  const { t } = useTranslation(['passwordStrength'])
  const [passwordStrength, setPasswordStrength] = useState({ score: 0 })

  const scoreName = useMemo(
    () =>
      ({
        0: t('PASSWORD_SCORE_0'),
        1: t('PASSWORD_SCORE_1'),
        2: t('PASSWORD_SCORE_2'),
        3: t('PASSWORD_SCORE_3'),
        4: t('PASSWORD_SCORE_4'),
      })[passwordStrength?.score],
    [passwordStrength?.score],
  )

  const getScorePasswordDebounce = useCallback(zxcvbnHelper.getPasswordStrengthDebounced, [])

  useEffect(() => {
    getScorePasswordDebounce(password, null, setPasswordStrength)
  }, [password])

  if (!password) return null

  return (
    <div className="w-full">
      <div className="w-full flex gap-x-2 items-center mb-4">
        <div className="whitespace-nowrap text-sm text-neutral-600 font-medium">{scoreName}</div>
        <div className="relative h-1 rounded-full w-full bg-neutral-200 dark:bg-neutral-800">
          <div
            className={cn('h-1 rounded-full left-0 top-0 transition-all', {
              hidden: !password.length,
              'bg-red-700 dark:bg-red-300 w-1/4': passwordStrength?.score <= 1,
              'bg-yellow-700 dark:bg-yellow-300 w-2/4': passwordStrength?.score === 2,
              'bg-green-700 dark:bg-green-300 w-3/4': passwordStrength?.score === 3,
              'bg-blue-700 dark:bg-blue-300 w-full': passwordStrength?.score === 4,
            })}
          />
        </div>
      </div>
      <div>
        <p className="text-sm text-neutral-1000 font-medium mb-1 dark:text-neutral-200">{t('THE_NEW_PASSWORD_MUST')}</p>
        <ul className="gap-y-1 grid">
          {Object.entries(passwordValidators).map(([key, { message, validate }]) => {
            const isValid = validate(password, passwordStrength)
            const [messageKey, params] = message
            return (
              <li
                className={cn('flex gap-x-1 text-xs items-center', {
                  'text-red-600 dark:text-red-300': !isValid,
                  'text-green-600 dark:text-green-300': isValid,
                })}
              >
                {isValid ? (
                  <CheckIcon className="text-inherit" width={16} height={16} />
                ) : (
                  <XIcon className="text-inherit" width={16} height={16} />
                )}
                <p className="text-sm font-medium">{t(messageKey, params)}</p>
              </li>
            )
          })}
        </ul>
      </div>
    </div>
  )
}

export { PasswordStrengthValidation }
