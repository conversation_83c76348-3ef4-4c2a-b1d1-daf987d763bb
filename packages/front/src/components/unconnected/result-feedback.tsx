import React from 'react'

interface ResultsProps {
  icon?: React.ReactNode
  title?: string
  description?: string
  actions?: React.ReactNode
}

const ResultFeedback = ({ icon, title, description, actions }: ResultsProps) => {
  return (
    <div className="flex flex-col items-center py-6">
      <div className="mb-4">
        <div
          className={`
            w-12
            h-12
            flex
            justify-center
            items-center
            rounded-full
            bg-primary-100
            dark:bg-neutral-800
            [&>svg]:text-primary-700
            [&>svg]:dark:text-neutral-200
          `}
        >
          {icon}
        </div>
      </div>
      {title && (
        <h3
          className={`
            text-center
            font-semibold
            text-neutral-950
            text-md
            dark:text-neutral-200
            whitespace-pre-line
            mb-1
          `}
        >
          {title}
        </h3>
      )}
      {description && (
        <p
          className={`
            text-center
            font-normal
            text-neutral-600
            text-md
            dark:text-neutral-400
            whitespace-pre-line
          `}
        >
          {description}
        </p>
      )}
      {actions && <div className="mt-4">{actions}</div>}
    </div>
  )
}

export { ResultFeedback }
