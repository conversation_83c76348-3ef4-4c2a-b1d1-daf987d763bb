import React from 'react'
import { But<PERSON> } from '@ikatec/nebula-react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useMemo } from 'react'
import { type BaseFilters } from '../../../api/types'

interface SortTableButtonProps {
  defaultSort?: string
  sort: string
  currentSort: string
  order?: BaseFilters['order']
  onChange: (head?: string, order?: BaseFilters['order']) => void
}

const SortTableButton = ({ sort, currentSort, order = 'ASC', onChange, ...rest }: SortTableButtonProps) => {
  const isActiveSort = useMemo(() => currentSort === sort, [currentSort, sort])
  const nextOrder = useMemo(() => (order === 'ASC' ? 'DESC' : 'ASC'), [order])

  const getIconColor = (criteria: SortTableButtonProps['order']) => {
    if (!isActiveSort || criteria !== order) return 'text-neutral-400 dark:text-neutral-600'
    return 'text-primary-800 dark:text-primary-300'
  }

  return (
    <Button
      icon
      variant="ghost"
      size="sm"
      onClick={() => {
        onChange(sort, nextOrder)
      }}
      {...rest}
    >
      <div className="grid">
        <ChevronUp className={`w-4 h-4 translate-y-1 ${getIconColor('ASC')}`} />
        <ChevronDown className={`w-4 h-4 -translate-y-1 ${getIconColor('DESC')}`} />
      </div>
    </Button>
  )
}

export { SortTableButton }
