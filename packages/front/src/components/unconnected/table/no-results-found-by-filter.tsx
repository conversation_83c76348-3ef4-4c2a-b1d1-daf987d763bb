import React from 'react'
import { Box, But<PERSON> } from '@ikatec/nebula-react'
import { SearchIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { ResultFeedback } from '../result-feedback'

interface NoResultsFoundByFilterProps {
  onClearFilter: VoidFunction
}

const NoResultsFoundByFilter = ({ onClearFilter }: NoResultsFoundByFilterProps) => {
  const { t } = useTranslation(['common'])
  return (
    <Box variant="primary" border paddingSize="xl">
      <ResultFeedback
        icon={<SearchIcon />}
        title={t('LIST_NO_RESULTS_FOUND_WITH_FILTER')}
        description={t('LIST_NO_RESULTS_FOUND_WITH_FILTER_DESCRIPTION')}
        actions={
          <Button type="button" variant="secondary" size="sm" onClick={onClearFilter}>
            {t('LIST_CLEAR_ALL_FILTERS')}
          </Button>
        }
      />
    </Box>
  )
}

export { NoResultsFoundByFilter }
