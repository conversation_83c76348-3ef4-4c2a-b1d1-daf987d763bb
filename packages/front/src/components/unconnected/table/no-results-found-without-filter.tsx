import React from 'react'
import { <PERSON>, Button } from '@ikatec/nebula-react'
import { BuildingIcon, PlusIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { ResultFeedback } from '../result-feedback'

interface NoResultsFoundWithoutFilterProps {
  onAdd: VoidFunction
  moduleTitle: string
  moduleDescription: string
}

const NoResultsFoundWithoutFilter = ({ onAdd, moduleTitle, moduleDescription }: NoResultsFoundWithoutFilterProps) => {
  const { t } = useTranslation(['common'])
  return (
    <Box variant="primary" border paddingSize="xl">
      <ResultFeedback
        icon={<BuildingIcon />}
        title={t('LIST_NO_DATA_CREATED', { appModule: moduleTitle })}
        description={t('LIST_NO_RESULTS_FOUND_DESCRIPTION', {
          appModule: moduleDescription,
        })}
        actions={
          <Button type="button" variant="secondary" size="sm" onClick={onAdd}>
            <PlusIcon />
            {t('LIST_ADD_NEW')}
          </Button>
        }
      />
    </Box>
  )
}

export { NoResultsFoundWithoutFilter }
