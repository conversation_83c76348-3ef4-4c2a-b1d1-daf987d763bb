import React from 'react'
import { Box, Button } from '@ikatec/nebula-react'
import { GlobeIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { ResultFeedback } from '../result-feedback'

interface NoPossibleLoadDataProps {
  onRefresh: VoidFunction
}

const NoPossibleLoadData = ({ onRefresh }: NoPossibleLoadDataProps) => {
  const { t } = useTranslation(['common'])
  return (
    <Box variant="primary" border paddingSize="xl">
      <ResultFeedback
        icon={<GlobeIcon />}
        title={t('LIST_IS_ERROR')}
        description={t('LIST_IS_ERROR_DESCRIPTION')}
        actions={
          <Button type="button" variant="secondary" size="sm" onClick={onRefresh}>
            {t('REFRESH_PAGE')}
          </Button>
        }
      />
    </Box>
  )
}

export { NoPossibleLoadData }
