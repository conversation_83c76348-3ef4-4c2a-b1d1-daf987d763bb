/* eslint-disable func-names */
import { put, takeLatest, takeEvery, call, select, fork, take, cancelled, delay } from 'redux-saga/effects'
import pick from 'lodash/pick'
import CookieStorage from 'redux-persist-cookie-storage'
import * as Sentry from '@sentry/browser'
import * as actions from './actions'
import * as selectors from './selectors'
import { actions as appActions } from '../app'
import authApi from './services/authApiAuthedSaga'
import config, { set as configSet } from '../../../../config'
import * as apiResource from '../../utils/apiResource'
import putAndWait from '../../utils/sagas/putAndWait'
import { reportErrorSaga } from '../app/sagas'
import toast from '../../utils/toast'
import { createPersistStateSaga, createRehydrateStateSaga } from '../../utils/reduxSagaPersistor'
import { KEY } from './reducers'
import * as impersonateSelectors from '../admin/modules/users/modules/impersonate/selectors'
import createSocket from '../../utils/createSocket'
import socketToChannel from '../../utils/socketToChannel'
import { actions as servicesAllActions } from '../services/modules/all'
import { selectors as chatSelectors } from '../chat'
import { normalizeList } from '../../resources/user/schema'
import { actions as entitiesActions } from '../entities'
import i18n from '../../../client/i18n'
import { detectMobile } from '../../utils/validator'
import { mapMessages } from '../../components/App/Dashboard/terms/Constants'
import { differenceInDays } from 'date-fns'
import { AxiosError } from 'axios'

const parseTokenData = (data) => ({
  accessToken: data.access_token,
  refreshToken: data.refresh_token,
  expiresIn: data.expires_in,
  tokenType: data.token_type,
})

// ------------------------------------
// Sub-routines
// ------------------------------------

function* listenSocketSaga() {
  const TOOK_OVER = 'took_over'
  const CONNECT = 'connect'

  const isImpersonating = yield select(impersonateSelectors.getIsImpersonating)

  if (isImpersonating) return

  const socket = yield call(createSocket, 'me')
  const channel = yield call(socketToChannel, socket, [TOOK_OVER, CONNECT])

  yield fork(function* emitter() {
    while (true) {
      const actionPayload = yield take(actions.emitSocketEvent)
      const { event, data } = actionPayload.payload
      socket.emit(event, data)
    }
  })

  try {
    while (true) {
      const payload = yield take(channel)

      const { event } = payload

      if (event === TOOK_OVER) {
        yield put(actions.tookOver())
        yield put(actions.updateUserStatus({ status: 'offline' }))
      }

      if (event === CONNECT) {
        yield put(actions.updateSocketUiState())
      }
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

export function* emitUiStateSaga() {
  const chatFilters = yield select(chatSelectors.getFilters)
  const currentContactId = yield select(chatSelectors.getCurrentContactId)
  const data = {
    chat: {
      ...pick(chatFilters, ['view']),
      currentContactId,
    },
  }
  yield put(actions.emitSocketEvent({ event: 'state', data }))
}

export function* fetchTokenSaga(action) {
  const { accountAlias, username, password, scope = '*' } = action.payload

  try {
    yield put(actions.fetchTokenStarted())

    const grantData = {
      grant_type: 'password',
      client_id: config('clientId'),
      client_secret: config('clientSecret'),
      username,
      password,
      scope,
      account_alias: accountAlias,
    }

    // TODO race timeout
    const res = yield call(authApi.fetchToken, grantData)

    const tokenData = parseTokenData(res)

    yield put(actions.fetchTokenSuccess(tokenData))
  } catch (error) {
    yield put(actions.fetchTokenFailed({ error, action }))
  }
}

export function* refreshTokenSaga(action) {
  try {
    yield put(actions.fetchTokenStarted())

    const refreshToken = yield select(selectors.getRefreshToken)

    if (!refreshToken) {
      throw new Error('No refresh token stored.')
    }

    const grantData = {
      grant_type: 'refresh_token',
      client_id: config('clientId'),
      client_secret: config('clientSecret'),
      refresh_token: refreshToken,
    }

    // TODO race timeout
    const res = yield call(authApi.fetchToken, grantData)

    const tokenData = {
      accessToken: res.access_token,
      refreshToken: res.refresh_token,
      expiresIn: res.expires_in,
      tokenType: res.token_type,
    }

    yield put(actions.fetchTokenSuccess(tokenData))
  } catch (error) {
    yield put(actions.fetchTokenFailed({ error, action }))
  }
}

export function* fetchUserSaga(action) {
  try {
    yield put(actions.fetchUserStarted())

    const res = yield call(authApi.fetchUser, { include: ['permissions', 'departments', 'timetable'] })

    const account = yield call(authApi.fetchUserAccount, {
      where: {
        id: res.data.accountId,
      },
      include: ['defaultDepartment', 'integrations'],
    })

    const apiAddress = res.headers['x-api-address']
    const socketAddress = res.headers['x-socket-address'] || apiAddress
    const frontAddress = res.headers['x-front-address']

    if (apiAddress) {
      apiResource.setBaseUrl(`${apiAddress}/v1`)
      configSet('socketUrl', socketAddress)
    }

    const user = { ...res.data, account: account.data }

    if (user.account.expiresAt && new Date(user.account.expiresAt).getTime() < new Date().getTime()) {
      yield put(actions.accountExpired())
    }

    const normalized = normalizeList([user])
    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.fetchUserSuccess({ user, apiAddress, frontAddress }))

    const gtmLayer = window.dataLayer || []

    const userRolesGtm = user?.roles || []
    const adminRole = userRolesGtm.find((role) => role?.isAdmin)
    const isAdmimGtm = adminRole ? adminRole?.isAdmin : false

    gtmLayer.push({
      event: 'loadPage',
      page: document.title,
      accountId: user?.accountId,
      userId: user?.id,
      userName: user?.name,
      userEmail: user?.email,
      userIsAdmin: isAdmimGtm,
    })

    const getCookie = (name) => {
      const value = `; ${document.cookie}`
      const parts = value.split(`; ${name}=`)
      if (parts.length === 2) return parts.pop().split(';').shift()
      return null
    }

    const currentPageFrontAddress = decodeURIComponent(getCookie('frontAddress'))
    if (frontAddress && currentPageFrontAddress && currentPageFrontAddress !== frontAddress) {
      window.location.reload()
    }
  } catch (error) {
    if (error?.response?.status == 401) {
      document.cookie = 'reduxSagaPersistor=;'
      document.cookie = 'auth=;'
      document.location.reload()
    }
    yield put(actions.fetchUserFailed({ error, action }))
  }
}

export function* takeoverSaga(action) {
  try {
    yield put(actions.takeoverStarted())

    yield call(authApi.takeover)

    window.location.reload()

    yield put(actions.takeoverSuccess())
  } catch (error) {
    yield put(actions.takeoverFailed({ error, action }))
    document.cookie = 'reduxSagaPersistor=;'
    document.cookie = 'auth=;'
    document.location.reload()
  }
}

export function* updateUserSaga(action) {
  try {
    const { payload } = action
    yield put(actions.updateUserStarted())

    const localUser = yield select(selectors.getUser)

    const userDataAttempt = pick(
      {
        ...payload,
      },
      ['name', 'email', 'password', 'language', 'data', 'otpAuthActive', 'preferences'],
    )

    const serverUser = yield call(authApi.updateUser, userDataAttempt)

    const mergedUser = { ...localUser, ...serverUser }

    const user = {
      ...mergedUser,
      account: {
        ...mergedUser.account,
        integrations: mergedUser.account.integrations || localUser.account.integrations,
      },
    }

    const normalized = normalizeList([user])
    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.updateUserSuccess({ user }))
  } catch (error) {
    yield put(actions.updateUserFailed({ error, action }))
  }
}

export function* updateUserStatusSaga(action) {
  try {
    yield put(actions.updateUserStatusStarted())

    const localUser = yield select(selectors.getUser)

    const isMobile = detectMobile()

    const data = {
      userId: localUser.id,
      client: isMobile ? 'app' : 'web',
      status: action.payload.status,
    }

    const serverUser = yield call(authApi.updateUserStatus, data)

    const mergedUser = { ...localUser, ...serverUser }

    const user = {
      ...mergedUser,
      account: {
        ...mergedUser.account,
        integrations: mergedUser.account.integrations || localUser.account.integrations,
      },
    }

    const normalized = normalizeList([user])

    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.updateUserStatusSuccess({ user }))
  } catch (error) {
    yield put(actions.updateUserStatusFailed({ error, action }))
  }
}

export function* attemptLoginSaga(action) {
  try {
    const { payload } = action

    if (!payload.username || !payload.password) return

    yield put(actions.attemptLoginStarted(payload))

    yield call(putAndWait, actions.fetchToken(payload), actions.fetchTokenSuccess, actions.fetchTokenFailed)

    const fetchUserSuccessPayload = yield call(
      putAndWait,
      actions.fetchUser(payload),
      actions.fetchUserSuccess,
      actions.fetchUserFailed,
    )

    const passwordExpiresAt = fetchUserSuccessPayload.user.passwordExpiresAt

    if (passwordExpiresAt && differenceInDays(new Date(passwordExpiresAt), new Date()) < 0) {
      yield put(actions.passwordExpired())
    } else {
      yield put(actions.attemptLoginSuccess(fetchUserSuccessPayload))
    }
  } catch (error) {
    yield put(actions.attemptLoginFailed({ error, action }))
  }
}

const invalidCredentials = (error: AxiosError) =>
  function* () {
    const remainingAttempts = error.response.headers['x-ratelimit-remaining']

    yield call(
      toast.error,
      typeof remainingAttempts === 'undefined'
        ? i18n.t('login:LABEL_INVALID_CREDENTIALS')
        : i18n.t('login:LABEL_INVALID_CREDENTIALS_WITH_ATTEMPTS', {
            attempts: remainingAttempts,
          }),
    )
  }

function* blockedLogin() {
  yield put(actions.showTwoFactorAuthTokenModal({ show: false }))
  yield call(toast.error, i18n.t('login:LABEL_BLOCKED_LOGIN'))
}

export function* hydrateLoginSaga(action) {
  try {
    const { payload } = action
    yield put(actions.hydrateLoginStarted(payload))

    const tokenData = parseTokenData(payload)

    yield put(actions.fetchTokenSuccess(tokenData))

    const fetchUserSuccessPayload = yield call(
      putAndWait,
      actions.fetchUser(payload),
      actions.fetchUserSuccess,
      actions.fetchUserFailed,
    )

    yield put(actions.hydrateLoginSuccess(fetchUserSuccessPayload))
  } catch (error) {
    yield put(actions.hydrateLoginFailed({ error, action }))
  }
}

export function* attemptLoginFailedSaga({ payload }) {
  if (payload.error.response?.data?.message === 'termsNeedsAdminUser') {
    yield call(toast.warn, mapMessages[payload.error.response?.data?.message])
    return
  }

  // repass any error that is not 400 to app global error handler
  if (payload.error.response && payload.error.response.status !== 400) {
    if (payload.error.response.status === 429) {
      return yield call(blockedLogin)
    }

    if (payload.error.response.status === 401 && !payload.error.response.data.message.match(/^otp token.*required/i)) {
      return yield call(invalidCredentials(payload.error))
    }

    yield put(appActions.error(payload))
    return
  }

  yield call(invalidCredentials)
}

export function* fetchUserSuccessSaga({ payload }) {
  yield put(actions.loggedIn())

  const sentryUser = {
    ...pick(payload.user, ['id', 'username', 'email', 'accountId']),
    accountName: payload.user.account.name,
  }

  Sentry.configureScope((scope) => {
    scope.setUser(sentryUser)
  })
}

export function* logoutSaga() {
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    const localUser = yield select(selectors.getUser)

    const user = { ...localUser, status: 'offline' }

    const normalized = normalizeList([user])
    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.updateUserSuccess({ user }))

    // criado action clean pois a do logout ta entrando em loop. Mantida ela caso esteja usando em outros logares
    yield put(entitiesActions.clean())
    yield put(actions.clean())

    // O reload é necessário para que o cliente se deslogue do socket. Caso contrário ele continuará ouvindo.
    yield delay(200)
    document.cookie = 'reduxSagaPersistor=;'
    document.cookie = 'auth=;'
    document.location.reload(true)
  }
}

export function* reportLoginSaga(action) {
  const isImpersonating = yield select(impersonateSelectors.getIsImpersonating)
  const user = yield select(selectors.getUser)

  if (!isImpersonating && user.isFirstLogin) {
    yield call(authApi.reportLogin)
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default (store) =>
  function* () {
    const persistConfig = {
      key: KEY,
      whitelist: ['accessToken', 'refreshToken', 'apiAddress', 'frontAddress'],
      storage: new CookieStorage({
        indexKey: 'reduxSagaPersistor',
        cookies: store.custom.cookies,
        expiration: { default: 365 * 86400 },
      }),
      selector: selectors.getStateSlice,
    }
    yield takeEvery(Object.values(actions), createPersistStateSaga(persistConfig))
    yield takeLatest(appActions.startRehydration, function* () {
      yield fork(createRehydrateStateSaga(persistConfig))
    })

    yield takeLatest(actions.attemptLogin, attemptLoginSaga)
    yield takeLatest(actions.hydrateLogin, hydrateLoginSaga)
    yield takeLatest(actions.takeover, takeoverSaga)
    yield takeEvery(actions.attemptLoginSuccess, reportLoginSaga)
    yield takeLatest(actions.fetchToken, fetchTokenSaga)
    yield takeLatest(actions.fetchUser, fetchUserSaga)
    yield takeLatest(actions.attemptLoginFailed, attemptLoginFailedSaga)
    yield takeLatest(actions.updateUser, updateUserSaga)
    yield takeLatest(actions.updateUserStatus, updateUserStatusSaga)
    yield takeLatest(actions.refreshToken, refreshTokenSaga)
    yield takeLatest(actions.fetchUserSuccess, fetchUserSuccessSaga)
    yield takeLatest(actions.logout, logoutSaga)
    yield takeEvery(actions.updateUserFailed, reportErrorSaga)
    yield takeEvery(actions.fetchUserFailed, reportErrorSaga)
    yield takeLatest(actions.updateSocketUiState, emitUiStateSaga)
    yield takeLatest(servicesAllActions.fetchAllSuccess, listenSocketSaga)
  }
