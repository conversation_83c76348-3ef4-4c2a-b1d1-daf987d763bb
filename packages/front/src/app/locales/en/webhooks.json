{"TYPE_OPTIONS": {"GENERAL": "General", "BY_SERVICE": "Per connection", "WHATSAPP_LOW_LEVEL": "WhatsApp (API low level)"}, "GROUP_NAMES": {"bot": "Bot", "contact": "Contacts", "department": "Departments", "message": "Messages", "people": "People", "organization": "Organizations", "user": "Users", "ticketTopics": "Ticket Issues", "role": "Roles", "company": "Company", "quickReplies": "Quick Answers", "tag": "Tags", "campaign": "Campaigns", "ticket": "Tickets", "service": "Connections", "whatsapp": "WhatsApp (API low level)", "pipeline": "Sales Funnel"}, "DISPLAY_NAMES_BY_SERVICE": {"message.created": "When creating", "message.updated": "When changing", "ticket.created": "When creating", "ticket.updated": "When changing", "service.created": "When creating", "service.updated": "When changing", "contact.created": "When creating", "contact.updated": "When changing", "contact.destroyed": "When deleting", "bot.command": "When sending command"}, "DISPLAY_NAMES_WHATSAPP_LOW_LEVEL": {"whatsapp.message.created": "When creating message", "whatsapp.message.updated": "When changing message", "whatsapp.contact.updated": "When changing contact", "service.created": "When creating", "service.updated": "When changing", "bot.command": "When sending command"}, "GENERAL_DISPLAY_NAMES": {"message.created": "When creating", "message.updated": "When changing", "ticket.created": "When creating", "ticket.updated": "When changing", "service.created": "When creating", "service.updated": "When changing", "service.destroyed": "When deleting", "contact.created": "When creating", "contact.updated": "When changing", "contact.destroyed": "When deleting", "people.created": "When creating", "people.updated": "When changing", "people.destroyed": "When deleting", "organization.created": "When creating", "organization.updated": "When changing", "organization.destroyed": "When deleting", "user.created": "When creating", "user.updated": "When changing", "user.destroyed": "When deleting", "department.created": "When creating", "department.updated": "When changing", "department.destroyed": "When deleting", "ticketTopics.created": "When creating", "ticketTopics.updated": "When changing", "ticketTopics.destroyed": "When deleting", "role.created": "When creating", "role.updated": "When changing", "role.destroyed": "When deleting", "bot.created": "When creating", "bot.updated": "When changing", "bot.destroyed": "When deleting", "bot.command": "When sending command", "quickReplies.created": "When creating", "quickReplies.updated": "When changing", "quickReplies.destroyed": "When deleting", "tag.created": "When creating", "tag.updated": "When changing", "tag.destroyed": "When deleting", "campaign.created": "When creating", "campaign.updated": "When changing", "campaign.destroyed": "When deleting", "whatsapp.message.created": "When creating message", "whatsapp.message.updated": "When changing message", "whatsapp.contact.updated": "When changing contact", "pipeline.created": "When creating", "pipeline.updated": "When changing"}}