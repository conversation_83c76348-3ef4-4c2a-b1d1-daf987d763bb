{"GROUP_NAME": {"evaluation": "Evaluations", "bots": "<PERSON><PERSON>", "chat": "Cha<PERSON>", "miscellaneous": "Miscellaneous", "contacts": "Contacts", "departments": "Departments", "messages": "Messages", "myAccount": "Company", "overview": "Statistics", "quickReplies": "Quick responses", "pipelines": "Sales funnel", "roles": "Roles", "services": "Connections", "settingsApi": "API", "authHistory": "Security actions", "tags": "Tags", "holidays": "Holidays", "stickers": "Stickers", "tickets": "Tickets", "users": "Users", "ticketTopics": "Ticket Issues", "distribution": "Call Distribution", "people": "People", "organizations": "Organizations", "campaigns": "Campaigns", "hsm": "Template", "interactive-messages": "Interactive messages", "customFields": "Custom fields", "schedule": "Schedules", "notification": "Notifications", "timetables": "Timetable", "integrations": "Integration", "acceptanceTerms": "Acceptance terms", "groups": "Whatsapp Groups", "download": "Media, links and documents", "aIConsumption": "AI Consumption", "knowledgeBase": "Knowledge base"}, "DISPLAY_NAME": {"users.view": "View users", "users.create": "Create users", "users.update": "Edit users", "users.archive": "Archive users", "roles.view": "View positions", "roles.create": "Create positions", "roles.update": "Edit positions", "roles.destroy": "Delete positions", "services.view": "View connection", "services.create": "Create connection", "services.update": "Edit connection", "services.destroy": "Delete connection", "services.operate": "Handle connection", "services.disabled": "Disable connection", "campaigns.view": "View campaigns", "campaigns.create": "Create campaigns", "campaigns.update": "Edit campaigns", "campaigns.destroy": "Delete campaigns", "campaigns.autoPause": "Automatically pause campaigns", "tags.view": "View tags", "tags.create": "Create tags", "tags.update": "Edit tags", "tags.destroy": "Delete tags", "holidays.view": "View holidays", "holidays.create": "Create holidays", "holidays.update": "Edit holidays", "holidays.destroy": "Delete holidays", "stickers.management": "Add and remove stickers", "stickers.send": "Send stickers", "tickets.transfer.all": "Transfer any ticket", "tickets.transfer.me": "Transfer own tickets", "tickets.transfer.all.bulk": "Transfer tickets in bulk", "tickets.transfer.departments": "Transfer department tickets", "tickets.close.all": "Close any ticket", "tickets.close.me": "Close own tickets", "tickets.update": "Edit ticket", "tickets.open": "Open ticket", "tickets.view.all.departments": "View all department tickets", "tickets.view.all": "View all tickets", "tickets.comment": "Add comment on tickets belonging to another user", "departments.view": "View departments", "departments.create": "Create departments", "departments.update": "Edit departments", "departments.archive": "Archive departments", "bots.view": "View bots", "bots.create": "Create bots", "bots.update": "Edit bots", "bots.destroy": "Delete bots", "evaluation.view": "View evaluations", "evaluation.create": "Create evaluations", "evaluation.update": "Edit evaluations", "evaluation.destroy": "Delete evaluations", "contacts.view.number": "View contact numbers", "contacts.view": "View contacts", "contacts.create": "Create contacts", "contacts.update": "Edit contacts", "contacts.destroy": "Delete contacts", "contacts.export": "Export contacts", "contacts.pin": "Pin contacts", "contacts.block": "Block and unblock contacts", "quickReplies.view": "View quick responses", "quickReplies.create": "Create quick responses", "quickReplies.update": "Edit quick responses", "quickReplies.destroy": "Delete quick responses", "pipelines.view": "Sales Funnel", "pipelines.create": "Create and Duplicate Sales Funnel", "pipelines.update": "Edit sales Funnel", "pipelines.destroy": "Delete sales Funnel", "myAccount.view": "View business data", "myAccount.update": "Edit business data", "myAccount.myPlan": "View My Plan", "myAccount.absence": "Use absence control", "chat": "Using Chat", "chat.view.contacts": "View 3rd row of contacts", "settingsApi.update": "Update API", "authHistory.view": "View security actions", "ignore-ip-restriction": "Ignore IP restriction", "overview.now.view": "View data in real time", "overview.history.view": "View ticket history", "overview.absence.view": "View absence control", "overview.hsm.statistic.view": "View template statistics", "overview.evaluation.view": "View statistics evaluation", "messages.view.all": "View all messages", "messages.view.department": "View the conversations of the departments themselves", "messages.view.owned": "View only the conversation itself", "messages.destroy": "Delete messages", "messages.view.ticket.mine.current": "View messages of tickets the user currently participates in", "messages.view.ticket.department.current": "View messages of tickets that user departments currently participate", "messages.view.ticket.mine": "View messages of tickets that the user participates", "messages.view.ticket.department": "View ticket messages from your department(s)", "messages.view.transfer.mine": "View messages of transfers that user participates", "messages.view.transfer.department": "View transfer messages from your department(s)", "messages.view.out.queue": "Hide messages from service queue and all contacts", "ticketTopics.view": "View ticket subjects", "ticketTopics.create": "Create ticket subjects", "ticketTopics.update": "Edit ticket subjects", "ticketTopics.destroy": "Delete ticket subjects", "ticketTopics.archive": "Archive ticket subjects", "distribution.view": "View Distribution", "distribution.create": "Create Distribution", "distribution.update": "Edit Distribution", "distribution.destroy": "Delete Distribution", "people.view": "View people", "people.create": "Create people", "people.update": "Edit people", "people.destroy": "Delete people", "acceptanceTerms.view": "View acceptance terms", "acceptanceTerms.create": "Create acceptance terms", "acceptanceTerms.update": "Edit acceptance terms", "acceptanceTerms.destroy": "Delete acceptance terms", "organizations.view": "View organizations", "organizations.create": "Create organizations", "organizations.update": "Edit organizations", "organizations.destroy": "Delete organizations", "interactive-messages.view": "View interactive messages", "interactive-messages.create": "Create interactive messages", "interactive-messages.management.all.departments": "Manage interactive messages from other departments", "interactive-messages.update": "Edit interactive messages", "interactive-messages.destroy": "Delete interactive messages", "interactive-messages.send": "Submit interactive message", "hsm.view": "View template", "hsm.create": "Create template", "hsm.update": "Edit template", "hsm.destroy": "Delete template", "hsm.send.before.expire": "Submit template before 24 Hours", "hsm.send": "Submit template", "integrations.view": "View integrations", "integrations.create": "Create integrations", "integrations.update": "Edit integrations", "integrations.destroy": "Delete integrations", "customFields.view": "View custom fields", "customFields.create": "Create custom fields", "customFields.update": "Edit custom fields", "customFields.destroy": "Delete custom fields", "schedule.view": "View schedules", "schedule.create": "Create schedules", "schedule.update": "Edit schedules", "schedule.destroy": "Delete schedules", "schedule.create.all": "Create schedules for another user", "notification.view.all": "View all notifications", "timetables.view": "View timetable", "timetables.create": "Create timetable", "timetables.update": "Edit timetable", "timetables.destroy": "Delete timetable", "groups.create": "Create group", "groups.view": "View group", "groups.update": "Edit group", "groups.add.members": "Add members", "groups.remove.members": "Remove members", "groups.promote.admin": "Promote members to admin", "groups.demote.admin": "Remove admin permission", "groups.leave": "Leave group", "download.audio": "Download audios on tickets", "download.image": "Download images on tickets", "download.video": "Download videos on tickets", "aIConsumption.view": "View AI consumption", "knowledgeBase.view": "View knowledge base", "knowledgeBase.create": "Create knowledge base", "knowledgeBase.update": "Edit knowledge base", "knowledgeBase.destroy": "Delete knowledge base"}, "PERMISSIONS_INFO": {"messages.destroy": "Allows the deletion of messages sent on the platform.", "messages.view.all": "Displays all messages from the tabs of open tickets, ticket queues, and contacts.", "messages.view.out.queue": "No messages will be displayed. This setting must be combined with other permissions for the agent to view messages.", "messages.view.ticket.mine": "Displays all messages from tickets in which the operator is or has been involved, regardless of restrictions in other departments.", "messages.view.ticket.mine.current": "Displays all messages from tickets currently under the user's responsibility, regardless of department permissions.", "messages.view.transfer.mine": "Displays only the messages exchanged during the period the user was responsible for the ticket. Other messages remain hidden.", "messages.view.ticket.department": "Displays all messages from tickets that have passed through or are currently in departments the user is part of.", "messages.view.ticket.department.current": "Displays all messages from tickets that are currently in the user's departments, regardless of assigned permissions.", "messages.view.transfer.department": "Displays all messages exchanged in tickets from your department. If transferred to a department without permission, messages from that new stage will not be displayed."}, "permissions.info.title": "Behavior information", "TAB_TITLE": {"SERVICE": "Service", "MARKETING": "Marketing", "AI": "AI", "MANAGEMENT": "Management"}}