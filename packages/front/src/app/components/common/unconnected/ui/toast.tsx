import React from 'react'
import styled, { keyframes } from 'styled-components'
import { X } from 'lucide-react'
import * as ToastPrimitives from '@radix-ui/react-toast'

type ToastVariant = 'default' | 'destructive' | 'success' | 'warn' | 'neutral'

const slideInFromTop = keyframes`
  from {
    transform: translateY(-100%);
    opacity: 0.5;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`

const slideOutToRight = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`

const ToastProvider = ToastPrimitives.Provider

const StyledToastViewport = styled(ToastPrimitives.Viewport)`
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9999999;
  display: flex;
  flex-direction: column;
  padding: 16px;
  max-width: 440px;
  width: 100%;
  gap: 16px;
`

const StyledToastClose = styled(ToastPrimitives.Close)`
  position: absolute;
  right: 24px;
  top: 24px;
  padding: 0;
  background-color: transparent;
  border: none;
  color: white;

  &:hover {
    opacity: 0.8;
  }
`

const StyledToast = styled(ToastPrimitives.Root)<{ variant?: ToastVariant }>`
  position: relative;
  pointer-events: auto;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  overflow: hidden;
  border-radius: 16px;
  padding: 24px;
  padding-right: 64px;
  box-shadow:
    0px 10px 15px -3px rgba(0, 0, 0, 0.1),
    0px 4px 6px -2px rgba(0, 0, 0, 0.05);

  background-color: ${({ variant }) => {
    const variantColors: Record<ToastVariant, string> = {
      success: '#068506',
      destructive: '#B81D1D',
      warn: '#9C630C',
      default: '#3c66b9',
      neutral: '#ffffff',
    }

    return variantColors[variant] ?? '#3c66b9'
  }};

  color: ${({ variant }) => {
    const colorByVariant: Record<ToastVariant, string> = {
      success: '#ffffff',
      destructive: '#ffffff',
      warn: '#ffffff',
      default: '#ffffff',
      neutral: '#000000',
    }

    return colorByVariant[variant] ?? '#ffffff'
  }};

  ${StyledToastClose} {
    color: ${({ variant }) => {
      const colorByVariant: Record<ToastVariant, string> = {
        success: '#ffffff',
        destructive: '#ffffff',
        warn: '#ffffff',
        default: '#ffffff',
        neutral: '#000000',
      }

      return colorByVariant[variant] ?? '#ffffff'
    }};
  }

  &[data-swipe='cancel'] {
    transform: translateX(0) 300ms ease-in-out;
  }

  &[data-swipe='end'] {
    animation: ${slideOutToRight} 300ms forwards;
  }

  &[data-swipe='move'] {
    transition: transform 300ms ease-in-out;
  }

  &[data-state='open'] {
    animation: ${slideInFromTop} 300ms forwards;
  }

  &[data-state='closed'] {
    animation: ${slideOutToRight} 300ms forwards;
  }
`

interface ToastProps extends React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> {
  variant?: ToastVariant
}

const Toast = React.forwardRef<React.ElementRef<typeof ToastPrimitives.Root>, ToastProps>(
  ({ variant = 'default', ...props }, ref) => <StyledToast ref={ref} variant={variant} {...props} />,
)
Toast.displayName = ToastPrimitives.Root.displayName

const StyledToastTitle = styled(ToastPrimitives.Title)`
  font-size: 1rem;
  font-weight: 500;
`

const StyledToastDescription = styled(ToastPrimitives.Description)`
  font-size: 0.875rem;
  opacity: 0.9;
  color: white;
`

const StyledToastAction = styled(ToastPrimitives.Action)`
  font-weight: bold;
  text-decoration: underline;
  color: white;
  cursor: pointer;
  background-color: transparent;
  border: none;
  padding: 0;
  text-align: left;
  margin-top: 8px;

  &:hover {
    opacity: 0.8;
  }
`

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>((props, ref) => <StyledToastAction ref={ref} {...props} />)
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>((props, ref) => (
  <StyledToastClose ref={ref} {...props}>
    <X width="24" height="24" />
  </StyledToastClose>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  Toast,
  StyledToastTitle as ToastTitle,
  StyledToastDescription as ToastDescription,
  ToastClose,
  ToastAction,
  StyledToastViewport as ToastViewport,
}
