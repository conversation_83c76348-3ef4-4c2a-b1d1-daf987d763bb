import React, { useCallback, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import Qs from 'qs'
import InputGroup from '../../common/unconnected/InputGroup'
import useEventCallback from '../../../hooks/useEventCallback'
import usePrevious from '../../../hooks/usePrevious'

interface AccountSelectProps {
  validation: any
  value: string
  onChange: (value: string) => void
  inputProps?: Partial<React.InputHTMLAttributes<HTMLInputElement>>
}

const AccountSelect: React.FC<AccountSelectProps> = ({ validation, value, onChange, inputProps = {} }) => {
  const { t } = useTranslation(['login', 'common'])
  const location = useLocation()
  const [isAccountSaved, setIsAccountSaved] = useState(false)
  const previousValue = usePrevious(value)

  const saveAccountAlias = useCallback((accountAlias: string) => {
    const trimmedAccount = accountAlias?.trim?.()
    if (!trimmedAccount) return

    window.localStorage.setItem('accountAlias', trimmedAccount)
    setIsAccountSaved(true)
  }, [])

  const getInitialAccountAlias = () => {
    const query = location.search && Qs.parse(location.search.substring(1))
    const accountFromQuery = query?.account as string
    const accountFromStorage = window.localStorage.getItem('accountAlias')
    return accountFromQuery || accountFromStorage
  }

  useEffect(() => {
    // handle race condition with parents useEffect setModel
    if (value === undefined || previousValue !== undefined) return

    const initialAccount = getInitialAccountAlias()

    if (initialAccount) {
      saveAccountAlias(initialAccount)
      onChange(initialAccount)
    }
  }, [value])

  const handleChangeAccount = useEventCallback((event: React.MouseEvent) => {
    event.preventDefault()
    setIsAccountSaved(false)
    onChange('')
  })

  const handleAccountAliasBlur = useEventCallback(() => saveAccountAlias(value))

  const handleChange = useEventCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  })

  if (isAccountSaved && value) {
    return (
      <p>
        {t('LABEL_ACCOUNT_LOGGING_ON')} "<b>{value}</b>"{' '}
        <a href="#" className="ml-2" onClick={handleChangeAccount}>
          {t('LABEL_ACCOUNT_CHANGE')}
        </a>
      </p>
    )
  }

  return (
    <InputGroup
      id="accountAlias"
      label={t('LABEL_ACCOUNT_INPUT')}
      autoComplete="accountAlias"
      validation={validation}
      value={value}
      onChange={handleChange}
      onBlur={handleAccountAliasBlur}
      {...inputProps}
    />
  )
}

export default AccountSelect
