import React, { useEffect, useMemo, useState } from 'react'
import sum from 'lodash/sum'
import { Pie } from 'react-chartjs-2'
import { useTranslation } from 'react-i18next'
import { Col, Row, Table } from 'reactstrap'
import { Popover, PopoverHeader, PopoverBody } from 'reactstrap'
import './style.css'
import Icon from '../../../../../common/unconnected/Icon'

const legendOpts = {
  display: false,
  position: false,
  fullWidth: true,
  reverse: false,
}

const left = {
  textAlign: 'left',
}

const colors = {
  fullySatisfied: '#0183FF',
  satisfied: '#52658C',
  neutral: '#939292',
  unsatisfied: '#E89996',
  fullyUnsatisfied: '#DD4B44',
  invalid: '#CFCFCF',
  noCredits: '#F7F794',
}

const chartStyles = {
  backgroundColor: Object.values(colors),
  borderWidth: 0,
}

const option = {
  tooltips: {
    callbacks: {
      label(tooltipItem, data) {
        const dataset = data.datasets[tooltipItem.datasetIndex]
        const meta = dataset._meta[Object.keys(dataset._meta)[0]]
        const { total } = meta
        const currentValue = dataset.data[tooltipItem.index]
        const percentage = parseFloat(((currentValue / total) * 100).toFixed(1))
        return `${currentValue} (${percentage}%)`
      },
      title(tooltipItem, data) {
        return data.labels[tooltipItem[0].index]
      },
    },
  },
}

const IACSAT = ({ data = [], grouping }) => {
  const [fullySatisfied, setFullySatisfied] = useState(true)
  const [fullyUnsatisfied, setFullyUnsatisfied] = useState(true)
  const [neutral, setNeutral] = useState(true)
  const [satisfied, setSatisfied] = useState(true)
  const [unsatisfied, setUnsatisfied] = useState(true)
  const [invalid, setInvalid] = useState(true)
  const [noCredits, setNoCredits] = useState(true)
  const { t } = useTranslation(['evaluationStats', 'common'])
  const [storage, setStorage] = useState({
    dataFullySatisfied: 0,
    dataFullyUnsatisfied: 0,
    dataNeutral: 0,
    dataSatisfied: 0,
    dataUnsatisfied: 0,
    dataInvalid: 0,
    dataNoCredits: 0,
  })

  useEffect(() => {
    setStorage({
      dataFullySatisfied: data.fullySatisfied,
      dataSatisfied: data.satisfied,
      dataNeutral: data.neutral,
      dataUnsatisfied: data.unsatisfied,
      dataFullyUnsatisfied: data.fullyUnsatisfied,
      dataInvalid: data.invalid,
      dataNoCredits: data.noCredits,
    })
  }, [data])

  const buttonFullySatisfied = () => {
    setFullySatisfied(!fullySatisfied)
    if (!fullySatisfied) {
      setStorage({ ...storage, dataFullySatisfied: data.fullySatisfied })
      return
    }
    setStorage({ ...storage, dataFullySatisfied: 0 })
  }

  const buttonSatisfied = () => {
    setSatisfied(!satisfied)
    if (!satisfied) {
      setStorage({ ...storage, dataSatisfied: data.satisfied })
      return
    }
    setStorage({ ...storage, dataSatisfied: 0 })
  }

  const buttonNeutral = () => {
    setNeutral(!neutral)
    if (!neutral) {
      setStorage({ ...storage, dataNeutral: data.neutral })
      return
    }
    setStorage({ ...storage, dataNeutral: 0 })
  }

  const buttonUnsatisfied = () => {
    setUnsatisfied(!unsatisfied)
    if (!unsatisfied) {
      setStorage({ ...storage, dataUnsatisfied: data.unsatisfied })
      return
    }
    setStorage({ ...storage, dataUnsatisfied: 0 })
  }

  const buttonFullyUnsatisfied = () => {
    setFullyUnsatisfied(!fullyUnsatisfied)
    if (!fullyUnsatisfied) {
      setStorage({ ...storage, dataFullyUnsatisfied: data.fullyUnsatisfied })
      return
    }
    setStorage({ ...storage, dataFullyUnsatisfied: 0 })
  }

  const buttonInvalid = () => {
    setInvalid(!invalid)
    if (!invalid) {
      setStorage({ ...storage, dataInvalid: data.invalid })
      return
    }
    setStorage({ ...storage, dataInvalid: 0 })
  }

  const buttonNoCredits = () => {
    setNoCredits(!noCredits)
    if (!noCredits) {
      setStorage({ ...storage, dataNoCredits: data.noCredits })
      return
    }
    setStorage({ ...storage, dataNoCredits: 0 })
  }

  const getPercent = (qty) => {
    if (!qty) return '0%'
    const addition = sum(Object.values(storage))
    const percentage = (qty / addition) * 100
    return `${percentage.toFixed(2)} %`
  }

  const dataValues = useMemo(
    () => ({
      labels: [
        t('CREATE_STATS_EVALUATION_LABEL_FULLY_SATISFIED'),
        t('CREATE_STATS_EVALUATION_LABEL_SATISFIED'),
        t('CREATE_STATS_EVALUATION_LABEL_NEUTRAL'),
        t('CREATE_STATS_EVALUATION_LABEL_UNSATISFIED'),
        t('CREATE_STATS_EVALUATION_LABEL_FULLY_UNSATISFIED'),
        t('CREATE_STATS_EVALUATION_LABEL_INVALID'),
      ],
      datasets: [
        {
          ...chartStyles,
          label: t('CREATE_STATS_EVALUATION_LABEL_ALL_ACCOUNT_CALLS'),
          data: Object.values(storage),
        },
      ],
    }),
    [data, grouping, storage],
  )

  const [popoverOpen, setPopoverOpen] = useState(false)

  const togglePopover = () => setPopoverOpen(!popoverOpen)

  return (
    <>
      <Row>
        <Col md={12}>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'baseline' }}>
            <h4 color="#0A1221">{t('AI_CSAT_EVALUATE_TITLE')}</h4>
            <Icon
              id="info-icon"
              size="lg"
              fixedWidth
              onClick={togglePopover}
              name="info-circle"
              color="#DDDDDD"
              style={{
                cursor: 'pointer',
              }}
            />

            <Popover placement="top" isOpen={popoverOpen} target={'info-icon'} toggle={togglePopover}>
              <PopoverHeader color="#0A1221" style={{ fontWeight: '600', fontSize: '14px' }}>
                {t('AI_CSAT_TOOLTIP_TITLE')}
              </PopoverHeader>
              <PopoverBody color="#0A1221" style={{ fontWeight: '500', fontSize: '12px' }}>
                {t('AI_CSAT_TOOLTIP_CONTENT')}
              </PopoverBody>
            </Popover>
          </div>
        </Col>
      </Row>
      <Row>
        <Col md={7}>
          <Pie legend={legendOpts} data={dataValues} options={option} />
          <br />
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <div>
              <Icon name="square" color={colors.fullySatisfied} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-fully-satisfied"
                onClick={buttonFullySatisfied}
                style={{
                  textDecoration: fullySatisfied ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_FULLY_SATISFIED')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.satisfied} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-satisfied"
                onClick={buttonSatisfied}
                style={{
                  textDecoration: satisfied ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_SATISFIED')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.neutral} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-neutral"
                onClick={buttonNeutral}
                style={{
                  textDecoration: neutral ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_NEUTRAL')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.unsatisfied} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-unsatisfied"
                onClick={buttonUnsatisfied}
                style={{
                  textDecoration: unsatisfied ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_UNSATISFIED')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.fullyUnsatisfied} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-fullyUnsatisfied"
                onClick={buttonFullyUnsatisfied}
                style={{
                  textDecoration: fullyUnsatisfied ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_FULLY_UNSATISFIED')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.invalid} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-invalid"
                onClick={buttonInvalid}
                style={{
                  textDecoration: invalid ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_INVALID')}
              </button>
            </div>

            <div>
              <Icon name="square" color={colors.noCredits} fixedWidth />
              <button
                data-testid="stats-Evaluation-button-no-credits"
                onClick={buttonNoCredits}
                style={{
                  textDecoration: noCredits ? 'none' : 'line-through',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                }}
              >
                {t('CREATE_STATS_EVALUATION_LABEL_NO_CREDITS')}
              </button>
            </div>
          </div>
        </Col>
        <Col md={5}>
          <Table
            style={{
              textAlign: 'center',
            }}
          >
            <tr>
              <th style={left}>{t('CREATE_STATS_EVALUATION_LABEL_TYPE')}</th>
              <th>{t('CREATE_STATS_EVALUATION_LABEL_GRADES')}</th>
              <th>{t('CREATE_STATS_EVALUATION_LABEL_QTD')}</th>
              <th>{t('CREATE_STATS_EVALUATION_LABEL_PORCENT')}</th>
            </tr>
            {fullySatisfied ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.fullySatisfied} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_FULLY_SATISFIED')}
                </td>
                <td>5</td>
                <td>{storage.dataFullySatisfied}</td>
                <td>{getPercent(storage.dataFullySatisfied)}</td>
              </tr>
            ) : (
              ''
            )}
            {satisfied ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.satisfied} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_SATISFIED')}
                </td>
                <td>4</td>
                <td>{storage.dataSatisfied}</td>
                <td>{getPercent(storage.dataSatisfied)}</td>
              </tr>
            ) : (
              ''
            )}
            {neutral ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.neutral} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_NEUTRAL')}
                </td>
                <td>3</td>
                <td>{storage.dataNeutral}</td>
                <td>{getPercent(storage.dataNeutral)}</td>
              </tr>
            ) : (
              ''
            )}
            {unsatisfied ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.unsatisfied} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_UNSATISFIED')}
                </td>
                <td>2</td>
                <td>{storage.dataUnsatisfied}</td>
                <td>{getPercent(storage.dataUnsatisfied)}</td>
              </tr>
            ) : (
              ''
            )}
            {fullyUnsatisfied ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.fullyUnsatisfied} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_FULLY_UNSATISFIED')}
                </td>
                <td>1</td>
                <td>{storage.dataFullyUnsatisfied}</td>
                <td>{getPercent(storage.dataFullyUnsatisfied)}</td>
              </tr>
            ) : (
              ''
            )}
            {invalid ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.invalid} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_INVALID')}
                </td>
                <td>&nbsp;</td>
                <td>{storage.dataInvalid}</td>
                <td>{getPercent(storage.dataInvalid)}</td>
              </tr>
            ) : (
              ''
            )}
            {noCredits ? (
              <tr>
                <td style={left}>
                  <Icon name="square" color={colors.noCredits} fixedWidth />
                  {t('CREATE_STATS_EVALUATION_LABEL_NO_CREDITS')}
                </td>
                <td>&nbsp;</td>
                <td>{storage.dataNoCredits}</td>
                <td>{getPercent(storage.dataNoCredits)}</td>
              </tr>
            ) : (
              ''
            )}
          </Table>
        </Col>
      </Row>
    </>
  )
}

export default IACSAT
