import React, { Component } from 'react'
import { Col } from 'reactstrap'
import { debounce } from 'lodash'
import pickBy from 'lodash/pickBy'
import identity from 'lodash/identity'
import { withTranslation } from 'react-i18next'
import LoadingSpinner from '../../../../../common/unconnected/LoadingSpinner'
import DepartmentCard from './DepartmentCard'
import Filters from './Filters'
import TablePagination from '../../../../../common/unconnected/TablePagination'
import * as S from '../styles'
import { CardFilters } from '../../../../styles/table'

class DepartmentTab extends Component {
  constructor(props) {
    super(props)

    this.state = {
      poolingInterval: null,
      filters: {
        name: '',
      },
      pagination: {
        page: 1,
        perPage: 15,
        archive: true,
      },
    }

    this.handleFilterChange = this.handleFilterChange.bind(this)
    this.handlePaginationChange = this.handlePaginationChange.bind(this)
    this.fetch = debounce(this.fetch.bind(this), 400)
  }

  handlePaginationChange(key, value) {
    this.setState(
      (prevState) => ({
        pagination: {
          ...prevState.pagination,
          [key]: value,
        },
      }),
      this.fetch,
    )
  }

  componentDidMount() {
    this.fetch()
  }

  fetch() {
    const { fetchManyDepartments } = this.props
    const { pagination, filters } = this.state

    const query = pickBy(
      {
        where: pickBy(
          {
            name: filters.name && { $iLike: `%${filters.name}%` },
          },
          identity,
        ),
        page: pagination.page,
        perPage: pagination.perPage,
        archive: true,
        order: [['name', 'ASC']],
      },
      identity,
    )

    fetchManyDepartments({ query })
  }

  handleFilterChange(key, value) {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
        pagination: 1,
      }),
      this.fetch,
    )
  }

  handlePaginationChange(key, value) {
    this.setState(
      (prevState) => ({
        pagination: {
          ...prevState.pagination,
          [key]: value,
        },
      }),
      this.fetch,
    )
  }

  render() {
    const { departments, isRefreshing, isFiltersShowing, pagination, isLoading, t } = this.props

    const { filters, pagination: localPagination } = this.state

    return (
      <div>
        {isFiltersShowing && (
          <CardFilters className="mb-4">
            <Filters filters={filters} handleFilterChange={this.handleFilterChange} />
          </CardFilters>
        )}

        {!isLoading && departments.length < 1 ? (
          <S.NowWrapperContent>
            <p className="text-center text-secondary py-4">{t('CREATE_STATS_NOW_LABEL_NOT_FOUND_DEPARTMENT')}</p>
          </S.NowWrapperContent>
        ) : (
          <S.NowWrapperContent>
            {departments.map((department) => (
              <Col md={6} lg={4} key={department.id} className="mb-4">
                <DepartmentCard department={department} isRefreshing={isRefreshing} />
              </Col>
            ))}
          </S.NowWrapperContent>
        )}
        <TablePagination
          pagination={pagination}
          localPagination={this.state.pagination}
          handlePaginationChange={this.handlePaginationChange}
          perPageOptions={[15]}
        />

        {departments.length === 0 && isLoading && <LoadingSpinner isLoading />}
      </div>
    )
  }
}

export default withTranslation(['now', 'common'])(DepartmentTab)
