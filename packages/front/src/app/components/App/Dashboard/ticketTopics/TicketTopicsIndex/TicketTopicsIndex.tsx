import React, { useState } from 'react'

import Helmet from 'react-helmet'
import { <PERSON>, Switch, withRouter } from 'react-router-dom'

import PropTypes from 'prop-types'
import { pickBy, identity } from 'lodash'

import { useTranslation } from 'react-i18next'
import CardLoading from '../../../../common/unconnected/CardLoading'
import IfUserCan from '../../../../common/connected/IfUserCan'
import IfUserCanRoute from '../../../../common/connected/IfUserCanRoute'
import TablePagination from '../../../../common/unconnected/TablePagination'
import Toggler from '../../../../common/unconnected/Toggler'
import Filters from './Filters'
import Button from '../../../../common/unconnected/Button'
import {
  Table,
  TableHead,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  TableOptions,
  ButtonRefresh,
  ButtonDropdownActions,
  DropdownItemActions,
  DropdownMenuActions,
  CardFilters,
} from '../../../styles/table'

import {
  IconTicketSubject,
  IconSearch,
  IconOptions,
  IconEdit,
  IconInactivate,
  IconShowPassword as IconEyes,
} from '../../../../common/unconnected/IconDigisac'

import { PrimaryColor, TextColor } from '../../../styles/colors'
import Container from '../../../styles/container'

// unconnected
import SweetModal from '../../../../common/unconnected/SweetModal'
import useConfirmation from '../../../../../hooks/useConfirmation'
import Icon from '../../../../common/unconnected/Icon'

// resource
import { useFetchManyTicketTopics, useArchive } from '../../../../../resources/ticketTopic/requests'

// hooks
import useIndexController from '../../../../../hooks/crud/useIndexController'

// components
import ticketopicDelete from '../TicketTopicsDelete'
import ticketopicForm from '../TicketTopicsForm'
import ticketopicShow from '../TicketTopicsShow'

const buildQuery = ({ filters, localPagination }) => ({
  query: JSON.stringify({
    where: {
      ...pickBy(
        {
          name: filters.name && { $iLike: `%${filters.name}%` },
        },
        identity,
      ),
      ...(filters.archivedAt &&
        filters.archivedAt.value !== 'all' && {
          archivedAt: { archived: { $ne: null }, unarchived: { $eq: null } }[filters.archivedAt.value],
        }),
    },
    order: [['name', 'ASC']],
    page: localPagination.page,
    perPage: localPagination.perPage,
  }),
})

const TicketTopicsIndex = ({ match }) => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])

  const initialFilters = {
    name: '',
    archivedAt: {
      value: 'unarchived',
      label: t('LABEL_UNARCHIVED'),
    },
  }

  const {
    models: ticketTopics,
    pagination,
    isLoading,
    fetch,
    localPagination,
    handleLocalPaginationChange,
    handleFilterChange,
    filters,
    toggleFilters,
    isFiltersShowing,
  } = useIndexController({
    buildQuery,
    initialFilters,
    useFetchMany: useFetchManyTicketTopics,
  })

  const messageModalError = t('MESSAGE_MODAL_ERROR')
  const [Modal, confirmArchive] = useConfirmation()
  const [showModalError, setShowModalError] = useState(false)
  const dispatchModal = (isModalOpen) => setShowModalError(!isModalOpen)

  const [, archive] = useArchive()
  async function handleArchive(id, archivedAt) {
    try {
      const confirmed = await confirmArchive({
        type: 'warning',
        title: archivedAt ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE') + t('LABEL_SUBJECT'),
        body: `${t('ARE_YOU_SURE_YOU_WANT_TO')} ${archivedAt ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE')} ${t(
          'THIS_SUBJECT',
        )}`,
        confirmBtnText: archivedAt ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE'),
        cancelBtnText: t('common:FORM_ACTION_CANCEL'),
      })

      if (!confirmed) return

      await archive(id, { archive: archivedAt })
      await fetch()
    } catch (error) {
      if (error.response?.status === 403) return dispatchModal(showModalError)
    }
  }

  return (
    <>
      <Modal />
      <SweetModal
        type="warning"
        title={t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED')}
        confirmBtnText="Ok"
        show={showModalError}
        showCancel={false}
        onConfirm={() => dispatchModal(showModalError)}
      >
        {messageModalError}
      </SweetModal>
      <div>
        <Helmet title={t('TITLE_TICKET_TOPICS')} />

        <Container>
          <div className="d-flex align-itens-center justify-content-between">
            <div className="title-page">
              <h2 data-testid="ticket-topics-title-page">{t('TITLE_TICKET_TOPICS')}</h2>
            </div>

            <div className="d-flex align-items-center">
              <IfUserCan permission="ticketTopics.create">
                <Button background={PrimaryColor} size="xll" className="mr-2">
                  <Link data-testid="create-button-ticket-topics" to="/ticket-topics/create">
                    <IconTicketSubject fill="white" width="25" height="25" />
                    {t('common:BUTTON_NEW_ITEM', {
                      item: `${t('TITLE_TICKET_TOPICS')}`,
                    })}
                  </Link>
                </Button>
              </IfUserCan>
              <Button
                size="xl"
                background={PrimaryColor}
                onClick={toggleFilters}
                className="mr-2"
                data-testid="ticket-topics-button-show_filters"
              >
                <IconSearch fill="white" width="25" height="25" />
                {isFiltersShowing ? `${t('common:BUTTON_TEXT_HIDDEN')} ` : `${t('common:BUTTON_TEXT_SHOW')} `}
                {t('common:BUTTON_TEXT_FILTERS')}
              </Button>
              <ButtonRefresh
                loadIcon="sync-alt"
                isLoading={isLoading}
                onClick={fetch}
                data-testid="refresh-ticketopics-button"
              >
                <Icon name="sync-alt" fixedWidth />
              </ButtonRefresh>
            </div>
          </div>

          <CardLoading isLoading={isLoading} />

          {isFiltersShowing && (
            <CardFilters>
              <Filters filters={filters} handleFilterChange={handleFilterChange} />
            </CardFilters>
          )}

          <Table className="mb-0" hover data-testid="ticket-topics-table">
            <TableHead data-testid="ticket-topics-card-header" columns={3}>
              <TableColumn>{t('TABLE_TITLE_TICKET_COLUMN_NAME')}</TableColumn>
              <TableColumn>{t('TABLE_TITLE_TICKET_COLUMN_STATUS')}</TableColumn>
              <TableColumn data-testid="action-tittle-ticket-topics" className="text-right justify-content-end pr-4">
                {t('common:ACTIONS_SUBMENU_TITLE')}
              </TableColumn>
            </TableHead>
            <TableBody>
              {ticketTopics &&
                ticketTopics.map((ticketTopic) => (
                  <TableRow cells={3} key={ticketTopic.id}>
                    <TableCell>
                      <Link to={`/ticket-topics/${ticketTopic.id}`}>{ticketTopic.name}</Link>
                    </TableCell>
                    <TableCell className="break-long-texts">
                      {!ticketTopic.archivedAt ? t('common:LABEL_UNARCHIVED') : t('common:LABEL_ARCHIVED')}
                    </TableCell>
                    <TableCell actions>
                      <Toggler
                        render={({ active, toggle }) => (
                          <ButtonDropdownActions group={false} isOpen={active} toggle={toggle}>
                            <TableOptions size="sm" color="primary" data-testid="ticket-topics-button-action">
                              <IconOptions className="icon-options" fill={PrimaryColor} />
                            </TableOptions>
                            <DropdownMenuActions>
                              <DropdownItemActions header>{t('common:ACTIONS_SUBMENU_TITLE')}</DropdownItemActions>
                              <Link
                                to={`/ticket-topics/${ticketTopic.id}`}
                                className="dropdown-item"
                                data-testid="view-create-ticket-topics"
                              >
                                <IconEyes fill={TextColor} width="29" height="29" />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </Link>

                              <IfUserCan permission="ticketTopics.update">
                                <Link
                                  to={`/ticket-topics/${ticketTopic.id}/edit`}
                                  className="dropdown-item"
                                  data-testid="edit-button-ticket-topics"
                                >
                                  <IconEdit fill={TextColor} width="28" height="28" />
                                  {t('common:ACTIONS_SUBMENU_EDIT')}
                                </Link>
                              </IfUserCan>

                              <IfUserCan permission="ticketTopics.archive">
                                <div
                                  onClick={() => handleArchive(ticketTopic.id, ticketTopic.archivedAt === null)}
                                  className="dropdown-item"
                                  data-testid="archived-button-ticket-topics"
                                >
                                  <IconInactivate fill={TextColor} width="28" height="28" />
                                  {ticketTopic.archivedAt ? t('LABEL_UNARCHIVE') : t('LABEL_TO_FILE')}
                                </div>
                              </IfUserCan>
                            </DropdownMenuActions>
                          </ButtonDropdownActions>
                        )}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              {ticketTopics.length < 1 && (
                <tr>
                  <td colSpan={6} className="text-center">
                    {t('common:NO_RESULTS_FOUND')}
                  </td>
                </tr>
              )}
            </TableBody>
          </Table>

          <TablePagination
            pagination={pagination}
            localPagination={localPagination}
            handlePaginationChange={handleLocalPaginationChange}
            data-testid="pagination-contacts"
          />
        </Container>

        <Switch>
          <IfUserCanRoute
            permission="ticketTopics.create"
            exact
            path={`${match.url}/create`}
            component={ticketopicForm}
          />
          <IfUserCanRoute
            permission="ticketTopics.update"
            exact
            path={`${match.url}/:id/edit`}
            component={ticketopicForm}
          />
          <IfUserCanRoute
            permission="ticketTopics.destroy"
            exact
            path={`${match.url}/:id/delete`}
            component={ticketopicDelete}
          />
          <IfUserCanRoute permission="ticketTopics.view" exact path={`${match.url}/:id`} component={ticketopicShow} />
        </Switch>
      </div>
    </>
  )
}

TicketTopicsIndex.propTypes = {
  match: PropTypes.object,
}

export default withRouter(TicketTopicsIndex)
