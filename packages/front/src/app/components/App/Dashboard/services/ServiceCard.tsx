import React, { useEffect, useCallback, memo, useState } from 'react'
import { Link } from 'react-router-dom'
import { Button, Col, DropdownItem, Modal as Modal<PERSON><PERSON>, <PERSON>dalBody, ModalHeader, <PERSON> } from 'reactstrap'
import { useMappedState } from 'redux-react-hook'
import { useSelector } from 'react-redux'
import { Trans, useTranslation } from 'react-i18next'
import serviceApi from '../../../../resources/service/api'
import useConfirmation from '../../../../hooks/useConfirmation'
import config from '../../../../../../config'
import Toggler from '../../../common/unconnected/Toggler'
import SweetModal from '../../../common/unconnected/SweetModal'
import IfUserCan from '../../../common/connected/IfUserCan/IfUserCanContainer'
import Icon from '../../../common/unconnected/Icon'
import CardLoading from '../../../common/unconnected/CardLoading'
import { useRequest } from '../../../../hooks/useRequest'
import useToggle from '../../../../hooks/useToggle'
import usePrevious from '../../../../hooks/usePrevious'
import * as impersonateModule from '../../../../modules/admin/modules/users/modules/impersonate'
import ServiceCardStatus from './ServiceCardStatus'
import { useArchive } from '../../../../resources/service/requests'
import * as S from './styles'
import {
  WhatsAppColor,
  WhatsAppBusinessColor,
  InstagramColor,
  MessengerColor,
  TelegramColor,
  GoogleBusinessMessageColor,
  ReclameAquiColor,
  SmsColor,
  WebChatColor,
  EmailColor,
  TextColor,
} from '../../styles/colors'
import {
  IconWhatsapp,
  IconWidget,
  IconTelegram,
  IconSms,
  IconOptions,
  IconInstagram,
  IconMessenger,
  IconGoogleBusinessMessage,
  IconTrash,
  IconEdit,
  IconShowPassword as IconEyes,
  IconRestart,
  IconLogout,
  IconInactivate,
  IconScript,
  IconCheck,
  IconHistory,
  IconWABAHealth,
  IconEnvelope,
  IconWebhook,
  IconMegaphone,
  IconCircleCheck,
  IconTriangleAlert,
  IconOctagonX,
} from '../../../common/unconnected/IconDigisac'
import { ButtonDropdownActions, DropdownItemActions, DropdownMenuActions, TableOptions } from '../../styles/table'
import { ModalDigisac, ModalFooter } from '../../styles/common'

const ScreenshotModal = memo(({ service, isOpen, toggle }) => {
  const { t } = useTranslation(['servicesPage', 'common'])
  const [{ response }, exec] = useRequest(serviceApi.screenshotById, service.id)
  const previousResponse = usePrevious(response)

  useEffect(() => {
    exec()
    const timeout = setInterval(() => exec(), 3000)
    return () => clearInterval(timeout)
  }, [])

  const src = response || previousResponse

  return (
    <ModalMain isOpen={isOpen} toggle={toggle} size="lg">
      <ModalHeader toggle={toggle}>{t('MODAL_HEADER_MONITOR_UPDATED')}</ModalHeader>
      <ModalBody>{src ? <img src={src} alt="Screenshot" className="img-fluid" /> : t('common:LOADING')}</ModalBody>
    </ModalMain>
  )
})

function ServiceCard({ service, hideActionButtons = false, fetch, isDialog }) {
  const { t } = useTranslation(['servicesPage', 'common'])
  const mapState = useCallback(
    (state) => ({
      isImpersonating: impersonateModule.selectors.getIsImpersonating(state),
    }),
    [],
  )
  const { isImpersonating } = useMappedState(mapState)

  const [{ response: signInGmailResponse }, signInGmail] = useRequest(serviceApi.gmailSignIn)

  useEffect(() => {
    if (signInGmailResponse) {
      window.open(signInGmailResponse, '_blank', 'height=570,width=720,left=520')
    }
  }, [signInGmailResponse])

  const [isOpenTicketsModal, setIsOpenTicketsModal] = useState(false)
  const [isOpenCampaignsModal, setIsOpenCampaignModal] = useState(false)
  const toggleTicketsModal = () => setIsOpenTicketsModal(!isOpenTicketsModal)
  const toggleCampaignsModal = () => setIsOpenCampaignModal(!isOpenCampaignsModal)
  const [countOpenTickets, setCountOpenTickets] = useState(0)
  const [countActiveCampaigns, setCountActiveCampaigns] = useState(0)

  const [{ isLoading: isStartLoading }, start] = useRequest(serviceApi.startById, service.id)
  const [{ isLoading: isShutdownLoading }, shutdown] = useRequest(serviceApi.shutdownById, service.id)
  const [{ isLoading: isRestartLoading }, restart] = useRequest(serviceApi.restartById, service.id)
  const [{ isLoading: isTakeoverLoading }, takeover] = useRequest(serviceApi.takeoverById, service.id)
  const [{ isLoading: isLogoutLoading }, logout] = useRequest(serviceApi.logoutById, service.id)
  const [{ response: ticketsResponse }, fetchTickets] = useRequest(serviceApi.getActiveTicketsCount, service.id)
  const [{ response: campaignsResponse }, fetchCampaigns] = useRequest(serviceApi.getReadyCampaignsCount, service.id)

  const isAnyActionLoading =
    isStartLoading || isShutdownLoading || isRestartLoading || isTakeoverLoading || isLogoutLoading

  const { isOpen: isScreenshotModalOpen, toggle: toggleScreenshotModal } = useToggle(false)
  const [Modal, confirmArchive] = useConfirmation()

  const [{ response }, archive] = useArchive()

  const handleArchiveConnection = async (id, archivedAt, type) => {
    const countOpenTickets = await fetchTickets(id)
    const countActiveCampaigns = await fetchCampaigns(id)
    setCountOpenTickets(countOpenTickets)
    setCountActiveCampaigns(countActiveCampaigns)

    if (countOpenTickets > 0) {
      toggleTicketsModal()
    } else if (countActiveCampaigns > 0) {
      toggleCampaignsModal()
    } else {
      handleArchiveGeneral(id, archivedAt, type)
    }
  }

  async function handleArchiveGeneral(id, archivedAt, type) {
    const confirmed = await confirmArchive({
      type: 'warning',
      title: `${t('TITLE_ARCHIVE_SERVICE', {
        item: archivedAt ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE'),
      })}`,
      body: archivedAt && t('BODY_TO_FILE_SERVICE'),
      confirmBtnText: archivedAt ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE'),
      cancelBtnText: t('common:FORM_ACTION_CANCEL'),
    })

    if (!confirmed) return

    try {
      if (!user.isSuperAdmin && !isImpersonating && !archivedAt) {
        return dispatchModalAuthorized(showModalAuthorization)
      }
      if (service?.data?.isConnected) {
        await logout()
      }
      await archive(id, { archive: archivedAt, type })
      await fetch()
    } catch (error) {
      if ([400, 402].includes(error.response?.status)) return dispatchModal(showModalError)
    }
  }

  async function handleArchiveWithCampaign(id, archivedAt, type) {
    await archive(id, { archive: archivedAt, type })
    toggleCampaignsModal()
    await fetch()
  }

  const [showModalError, setShowModalError] = useState(false)
  const [showModalAuthorization, setShowModalAuthorization] = useState(false)

  const user = useSelector((state) => state.auth.user)

  const messageModalError = t('ERROR_LIMIT_TYPE_CONNECTION')
  const messageModalAuthorization = `${t('MESSAGE_MODAL_AUTHORIZATION')} ${config('whitelabel.appName')}.`
  const showInvoluntaryDisconnectionBorder =
    !service.data?.status?.isConnected &&
    service.data?.isManuallyDisconnected === false &&
    service?.archivedAt === null &&
    service?.data?.syncCount

  const dispatchModal = (isModalOpen) => setShowModalError(!isModalOpen)
  const dispatchModalAuthorized = (isModalOpen) => setShowModalAuthorization(!isModalOpen)

  const IconsService = (serviceType) => {
    const iconsDigisac = {
      whatsapp: <IconWhatsapp fill={WhatsAppColor} width="25" height="25" />,
      'whatsapp-business': <IconWhatsapp fill={WhatsAppBusinessColor} width="25" height="25" />,
      webchat: <IconWidget fill={WebChatColor} width="25" height="25" />,
      telegram: <IconTelegram fill={TelegramColor} width="25" height="25" />,
      'sms-wavy': <IconSms fill={SmsColor} width="25" height="25" />,
      email: <IconEnvelope fill={EmailColor} width="28" height="28" />,
      instagram: <IconInstagram fill={InstagramColor} width="25" height="25" />,
      'facebook-messenger': <IconMessenger fill={MessengerColor} width="25" height="25" />,
      'google-business-message': <IconGoogleBusinessMessage fill={GoogleBusinessMessageColor} width="25" height="25" />,
      'reclame-aqui': <IconMegaphone fill={ReclameAquiColor} width="25" height="25" />,
    }
    return iconsDigisac[serviceType]
  }

  const processService = (service, message) => {
    return {
      ...service,
      health: {
        info:
          service?.health?.status && service?.health?.status !== 'AVAILABLE'
            ? message
            : t('MESSAGE_HEALTH_INFO_DESCRIPTION'),
        status: service?.health?.status ?? 'AVAILABLE',
      },
    }
  }

  const [processedService, setProcessedService] = useState({})

  if (!service) return

  const [{ response: signInOutlookResponse }, signInOutlook] = useRequest(serviceApi.outlookSignin)

  useEffect(() => {
    if (signInOutlookResponse) {
      window.open(signInOutlookResponse, 'popup', 'status=1, height=500, width=500, toolbar=0,resizable=0')
    }
  }, signInOutlookResponse)

  useEffect(() => {
    setProcessedService(processService(service, t('MESSAGE_INFO_DESCRIPTION')))
  }, [service])

  return (
    <>
      <Modal />
      <ModalDigisac size="md" isOpen={isOpenTicketsModal} toggle={toggleTicketsModal}>
        <ModalHeader className="text-center">
          <IconTriangleAlert width="50" />
          <br />
          <br />
          {t('ARCHIVE_CONNECTION_MODAL_TICKETS_TITLE')}
        </ModalHeader>
        <ModalBody>
          <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION', { countOpenTickets })}</Trans>
          <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP1')}</Trans>
          <Link to="/ticket-history" target="_blank">
            <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP1_LINK')}</Trans>
            <Icon name="external-link-alt" fixedWidth className="mr-1" />;
          </Link>
          <br />
          <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP2')}</Trans>
          <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP3')}</Trans>
        </ModalBody>
        <br />
        <ModalFooter>
          <Button color="secondary" onClick={toggleTicketsModal}>
            {t('ARCHIVE_CONNECTION_MODAL_BUTTON_CANCEL')}
          </Button>
          <Link to="/ticket-history">
            <Button color="primary" onClick={toggleTicketsModal}>
              {t('ARCHIVE_CONNECTION_MODAL_TICKETS_BUTTON_TICKETS_HISTORY')}
            </Button>
          </Link>
        </ModalFooter>
      </ModalDigisac>
      <ModalDigisac size="md" isOpen={isOpenCampaignsModal} toggle={toggleCampaignsModal}>
        <ModalHeader className="text-center">
          <IconTriangleAlert width="50" />
          <br />
          <br />
          <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_CAMPAIGNS_TITLE')}</Trans>
        </ModalHeader>
        <ModalBody>
          <Trans i18nKey="services">
            {t('ARCHIVE_CONNECTION_MODAL_CAMPAIGNS_DESCRIPTION', { countActiveCampaigns })}
          </Trans>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={toggleCampaignsModal}>
            <Trans i18nKey="services">{t('ARCHIVE_CONNECTION_MODAL_BUTTON_CANCEL')}</Trans>
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleArchiveWithCampaign(service.id, service.archivedAt === null, service.type)
            }}
          >
            <Trans i18nKey="services">{t('LABEL_TO_FILE')}</Trans>
          </Button>
        </ModalFooter>
      </ModalDigisac>
      <SweetModal
        type="warning"
        title={t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED')}
        confirmBtnText="Ok"
        show={showModalError}
        showCancel={false}
        onConfirm={() => dispatchModal(showModalError)}
        onCancel={() => dispatchModal(showModalError)}
      >
        {messageModalError}
      </SweetModal>
      <SweetModal
        type="warning"
        title={t('common:LABEL_UNARCHIVED_CONNECTION')}
        confirmBtnText="Ok"
        show={showModalAuthorization}
        showCancel={false}
        onConfirm={() => dispatchModalAuthorized(showModalAuthorization)}
        onCancel={() => dispatchModalAuthorized(showModalAuthorization)}
      >
        {messageModalAuthorization}
      </SweetModal>
      <S.CardService
        isArchived={service.archivedAt}
        color={!service?.archivedAt ? (service.data?.status?.isConnected ? 'success' : 'danger') : 'secondary'}
        style={{
          height: '100%',
          border: showInvoluntaryDisconnectionBorder && '3px solid #324B7D',
        }}
        className="mt-2"
      >
        <CardLoading isLoading={isAnyActionLoading} />

        <Row data-testid="connections-body-card">
          <Col md={9}>
            <S.CardServiceName data-testid="connections-label-card_name">
              <span>{IconsService(service.type)}</span>
              {service.name}
            </S.CardServiceName>
          </Col>
          {!hideActionButtons && (
            <Col md={3}>
              <div>
                <Toggler
                  render={({ active, toggle }) => (
                    <ButtonDropdownActions
                      data-testid="connections-button-actions"
                      className="float-right"
                      group={false}
                      isOpen={active}
                      toggle={toggle}
                    >
                      <TableOptions size="sm" outline>
                        <IconOptions className="icon-options" fill={TextColor} />
                      </TableOptions>
                      <DropdownMenuActions>
                        <DropdownItemActions header data-testid="connections-label-actions">
                          {t('common:ACTIONS_SUBMENU_TITLE')}
                        </DropdownItemActions>
                        {service.type === 'webchat' &&
                          service.data?.webchat?.id &&
                          service.data?.status?.isConnected && (
                            <Link
                              to={`/services/${service.id}/script`}
                              className="dropdown-item"
                              data-testid="open-toview-accounts"
                              onClick={toggle}
                            >
                              <IconScript fill={TextColor} width="29" height="29" />
                              Script
                            </Link>
                          )}
                        {service.type === 'google-business-message' && service.data?.status.isConnected && (
                          <Link
                            to={`/services/${service.id}/webhook`}
                            className="dropdown-item"
                            data-testid="open-toview-accounts"
                            onClick={toggle}
                          >
                            <IconWebhook className="ml-1" fill={TextColor} width="23" height="23" />
                            {t('LABEL_WEBHOOK_LINK')}
                          </Link>
                        )}
                        <Link
                          to={`/services/${service.id}`}
                          className="dropdown-item"
                          data-testid="connections-button-view_connection"
                          onClick={toggle}
                        >
                          <IconEyes fill={TextColor} width="29" height="29" />
                          {t('common:ACTIONS_SUBMENU_VIEW')}
                        </Link>
                        {service.type === 'whatsapp-business' &&
                          service.data?.status?.isStarted &&
                          service.data?.status?.isConnected && (
                            <Link
                              to={`/services/waba/${service.id}`}
                              className="dropdown-item"
                              data-testid="connection-button-event_waba"
                              onClick={toggle}
                            >
                              <IconWABAHealth fill={TextColor} />
                              {t('SERVICE_CONNECTION_HEALTH')}
                            </Link>
                          )}
                        <Link
                          to={`/services/history/${service.id}`}
                          className="dropdown-item"
                          data-testid="connection-button-event_history"
                          onClick={toggle}
                        >
                          <IconHistory fill={TextColor} width="29" height="29" />
                          {t('SERVICE_EVENT_HISTORY')}
                        </Link>

                        <IfUserCan permission="services.disabled">
                          <Button
                            onClick={() =>
                              handleArchiveConnection(service.id, service.archivedAt === null, service.type)
                            }
                            className="dropdown-item"
                            data-testid="connections-button-archive_unarchive"
                          >
                            <IconInactivate fill={TextColor} width="28" height="28" />
                            {service.archivedAt === null ? t('LABEL_TO_FILE') : t('LABEL_UNARCHIVE')}
                          </Button>
                        </IfUserCan>

                        <IfUserCan permission="services.update">
                          <Link
                            to={
                              service.type === 'webchat'
                                ? `/services/${service.id}/edit/webchat`
                                : `/services/${service.id}/edit`
                            }
                            className="dropdown-item"
                            data-testid="connections-button-edit_connection"
                            onClick={toggle}
                          >
                            <IconEdit fill={TextColor} width="28" height="28" />
                            {t('common:ACTIONS_SUBMENU_EDIT')}
                          </Link>
                        </IfUserCan>

                        <IfUserCan permission="services.destroy">
                          <Link
                            to={`/services/${service.id}/delete`}
                            className="dropdown-item"
                            data-testid="connection-button-delete_connection"
                            onClick={toggle}
                          >
                            <IconTrash fill={TextColor} width="28" height="28" />
                            {t('common:MODAL_DELETE_BUTTON_CONFIRM')}
                          </Link>
                        </IfUserCan>

                        <IfUserCan permission="services.operate">
                          {!service.data?.status?.isStarted &&
                            service.type === 'email' &&
                            service.data.service === 'Gmail' && (
                              <Button
                                className="dropdown-item"
                                data-testid="connections-button-auth_gmail"
                                onClick={signInGmail}
                              >
                                <Icon name="user-shield" fixedWidth className="mr-1" />
                                {t('AUTHENTICATE_BUTTON')}
                              </Button>
                            )}

                          {!service.data?.status?.isStarted &&
                            service.type === 'email' &&
                            service.data.service === 'Hotmail' && (
                              <>
                                <DropdownItem divider />
                                <Button
                                  onClick={() => signInOutlook(service.id)}
                                  className="dropdown-item"
                                  data-testid="connections-button-auth_outlook"
                                >
                                  <Icon name="user-shield" fixedWidth className="mr-2 ml-2" />
                                  {t('AUTHENTICATE_BUTTON')}
                                </Button>
                              </>
                            )}

                          {(service.data?.status?.isStarting || service.data?.status?.isStarted) &&
                          service.archivedAt === null ? (
                            <>
                              <DropdownItem divider />
                              <Button
                                onClick={() => restart()}
                                className="dropdown-item"
                                data-testid="connections-button-reset_connection"
                              >
                                <IconRestart fill={TextColor} width="26" height="26" />
                                {t('LABEL_RESTART')}
                              </Button>
                              {!['facebook-messenger', 'instagram', 'reclame-aqui'].includes(service.type) &&
                                service.data?.providerType !== 'meta' && (
                                  <Button
                                    onClick={() => shutdown()}
                                    className="dropdown-item ml-1"
                                    data-testid="button-shutdown-connection"
                                  >
                                    <Icon name="power-off" fixedWidth className="mr-2" />
                                    {t('LABEL_TO_SWITCH_OFF')}
                                  </Button>
                                )}
                              {(![
                                'telegram',
                                'google-business-message',
                                'sms-wavy',
                                'webchat',
                                'whatsapp-business',
                                'reclame-aqui',
                              ].includes(service.type) ||
                                service.data?.providerType === 'meta' ||
                                (service.type === 'email' &&
                                  (service.data.service === 'Gmail'
                                    ? service.data.tokens
                                    : service.data.microsoft))) && (
                                <Button
                                  onClick={() => logout()}
                                  className="dropdown-item"
                                  data-testid="connection-button-logout_connection"
                                >
                                  <IconLogout fill={TextColor} width="26" height="26" />
                                  {t('LABEL_LOGOUT')}
                                </Button>
                              )}
                              {isImpersonating && ['whatsapp', 'whatsapp-remote-pod'].includes(service.type) && (
                                <>
                                  <DropdownItem divider />
                                  <Button
                                    onClick={toggleScreenshotModal}
                                    className="dropdown-item"
                                    data-testid="connection-button-monitor"
                                  >
                                    <Icon name="desktop" fixedWidth className="mr-1" />
                                    {t('LABEL_MONITOR')}
                                  </Button>
                                </>
                              )}
                            </>
                          ) : (
                            !['facebook-messenger', 'instagram'].includes(service.type) &&
                            service.data.providerType !== 'meta' &&
                            service.archivedAt === null &&
                            !service.data?.status?.isConnected && (
                              <>
                                <Button
                                  onClick={() => start()}
                                  className="dropdown-item"
                                  data-testid="connections-button-start_connection"
                                >
                                  <IconCheck fill={TextColor} width="28" height="28" />
                                  {t('LABEL_START')}
                                </Button>
                              </>
                            )
                          )}
                        </IfUserCan>
                      </DropdownMenuActions>
                    </ButtonDropdownActions>
                  )}
                />
              </div>
            </Col>
          )}
        </Row>

        <ServiceCardStatus {...{ service, takeover, start }} />

        {isScreenshotModalOpen && (
          <ScreenshotModal service={service} isOpen={isScreenshotModalOpen} toggle={toggleScreenshotModal} />
        )}
        {service.type === 'whatsapp-business' &&
          service.data?.status?.isStarted &&
          service.data?.status?.isConnected && (
            <div style={{ height: '1rem', marginTop: isDialog ? '16px' : '8px' }}>
              <S.CardServiceFooter isDialog={isDialog}>
                <div style={{ alignContent: 'center', paddingLeft: '12px' }}>
                  <IconWABAHealth className="mx-1" fill="#324B7D" width="16" height="16" />
                  {t('SERVICE_CONNECTION_HEALTH')}
                </div>
                <S.DivBadge>
                  <Link to={`/services/waba/${service.id}`}>
                    <div
                      className={
                        processedService?.health?.status == 'LIMITED'
                          ? 'badge badge-warning-waba'
                          : processedService?.health?.status == 'AVAILABLE'
                            ? 'badge badge-success-waba'
                            : 'badge badge-error-waba'
                      }
                    >
                      {processedService?.health?.status == 'LIMITED' ? (
                        <IconTriangleAlert width="16" height="16" />
                      ) : processedService?.health?.status == 'AVAILABLE' ? (
                        <IconCircleCheck width="16" height="16" />
                      ) : (
                        <IconOctagonX width="16" height="16" />
                      )}{' '}
                      {processedService?.health?.status == 'LIMITED'
                        ? t('STATUS_WABA_LIMITED')
                        : processedService?.health?.status == 'AVAILABLE'
                          ? t('STATUS_WABA_AVAILABLE')
                          : t('STATUS_WABA_BLOCKED')}
                    </div>
                  </Link>
                </S.DivBadge>
              </S.CardServiceFooter>
            </div>
          )}
      </S.CardService>
    </>
  )
}

export default memo(ServiceCard)
