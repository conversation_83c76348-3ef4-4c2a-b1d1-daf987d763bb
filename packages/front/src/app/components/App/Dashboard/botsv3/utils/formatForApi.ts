import { Edge as ReactFlowEdge, getInco<PERSON>, getOutgoers } from 'reactflow'
import {
  ConditionNodeType,
  MessageNodeType,
  Node,
  nodeActionsMap,
  SendHolidayMessageNodeType,
  SendTemplateMessageNodeType,
  TriggerNodeType,
  AiNodeType,
} from '../Flow'
import type { ConditionData, ConditionNodeData } from '../nodes/condition/types'
import { DEFAULT_ACTIONS, DEFAULT_TRIGGERS } from '../constants'
import { PartialBy } from './types'
import { makeAnswerOutputHandle } from '../nodes/sendMessage/MessageNode'
import { ButtonsData, SimpleListData } from '../nodes/sendTemplateMessage/types'
import { makeActionOutputHandle } from '../nodes/ai/AiNode'

type Edge = ReactFlowEdge & { targetNode?: Node; sourceNode?: Node }

type FlowData = {
  nodes: Node[]
  edges: Edge[]
}

type Action = {
  type: PartialBy<(typeof DEFAULT_ACTIONS)[keyof typeof DEFAULT_ACTIONS], 'name'>
  data: any
}

// the attributes are actually actually required here but its optional in ConditionNodeData
type Condition = {
  variable?: PartialBy<ConditionData['variable'], 'name' | 'type'>
  operator?: PartialBy<ConditionData['operator'], 'name' | 'type'>
  target?: ConditionData['target']
}

type Rule = {
  title?: string
  actions: Action[]
  data?: {}
  fallbackActions?: Action[]
  conditions: Condition[] | ConditionNodeData
  triggerNodeId?: string
}

type Trigger = {
  trigger: PartialBy<(typeof DEFAULT_TRIGGERS)[keyof typeof DEFAULT_TRIGGERS], 'name'>
  rules: Rule[]
}

export type Context = {
  id: '@INIT' | '@EVERY' | '@FALLBACK' | string
  name?: string
  triggers: Trigger[]
}

type BotContexts = Context[]

const getContextId = (node?: Node) => {
  if (node?.type === 'START_NODE') return '@INIT'
  if (node?.type === 'PERSISTENT_START_NODE') return '@EVERY'
  if (node?.type === 'TRIGGER_NODE') return null // trigger do not have contexts
  return node?.id
}

// triggers do not have its own contexts, so we get from its parents
const getTriggerContextIds = (node: Node, nodes: Node[], edges: Edge[]): string[] => {
  const incomers = getIncomers(node, nodes, edges) as Node[]
  return incomers.map(getContextId)
}

const getNodeById = (nodes: Node[], id: string) => nodes.find((n) => n.id === id)

// may not be created yet! (TODO: check this)
const getNodeContext = (contexts: BotContexts, node: Node) => contexts.find((c) => c.id === getContextId(node))

// always annex to the source context
const buildTriggerNode = (contexts: BotContexts, { nodes, edges }: FlowData, node: TriggerNodeType) => {
  const sourceNodes = getIncomers(node, nodes, edges) as Node[]

  const targetNodes = getOutgoers(node, nodes, edges) as Node[]
  const targetNode = targetNodes?.[0] // trigger has only one handle

  const targetContext = getContextId(targetNode)

  sourceNodes.forEach((n) => {
    if (!node?.data?.trigger?.type) return

    const context = getNodeContext(contexts, n)

    if (!context) return

    let trigger: Trigger = context.triggers.find((t: Trigger) => t.trigger.type === node.data.trigger.type)

    // if trigger still not exists in the context, we create it
    if (!trigger) {
      trigger = {
        trigger: {
          type: node.data.trigger.type,
        },
        rules: [],
      }

      context.triggers.push(trigger)
    }

    const { inactivityTime, timeUnit } = node.data

    trigger.rules.push({
      triggerNodeId: node.id,
      actions: [
        targetContext && {
          data: {
            context: targetContext,
          },
          type: {
            value: 'SET_CONTEXT' as const,
          },
        },
      ].filter(Boolean),
      data: {
        ...(node.data.trigger.type === 'BOT_INACTIVE' &&
          inactivityTime &&
          timeUnit && {
            inactivityTime,
            timeUnit,
          }),
      },
      conditions: [],
      fallbackActions: [],
    })
  })
}

const buildConditionNode = (contexts: BotContexts, { edges }: FlowData, node: ConditionNodeType) => {
  const targetEdges = edges.filter((e) => e.source === node.id)

  const truthyEdge = targetEdges.find((e) => e.sourceHandle === 'OUTPUT_TRUE')
  const falsyEdge = targetEdges.find((e) => e.sourceHandle === 'OUTPUT_FALSE')

  const targetTruthyContext = getContextId(truthyEdge?.targetNode)
  const targetFalsyContext = getContextId(falsyEdge?.targetNode)

  contexts.push({
    id: getContextId(node),
    name: node.id,
    triggers: [
      {
        trigger: {
          type: 'ENTER_CONTEXT' as const,
        },
        rules: [
          {
            actions: [
              targetTruthyContext && {
                data: {
                  context: targetTruthyContext,
                },
                type: {
                  value: 'SET_CONTEXT' as const,
                },
              },
            ].filter(Boolean),
            // @ts-ignore
            conditions: node.data?.conditionType ? node.data : [node.data],
            fallbackActions: [
              targetFalsyContext && {
                data: {
                  context: targetFalsyContext,
                },
                type: {
                  value: 'SET_CONTEXT' as const,
                },
              },
            ].filter(Boolean),
          },
        ],
      },
    ],
  })
}

const buildMessageNode = (contexts: BotContexts, { edges }: FlowData, node: MessageNodeType) => {
  const targetFallbackEdge = edges.find((e) => e.source === node.id && e.sourceHandle === 'OUTPUT_FALLBACK')
  const targetFallbackContext = getContextId(targetFallbackEdge?.targetNode)

  const messageReceivedTrigger: Trigger = node.data?.answers?.length && {
    trigger: {
      type: 'MESSAGE_RECEIVED',
    },
    rules: [
      ...node.data.answers.map((answer): { actions: Action[]; conditions: Condition[] } => {
        const targetEdge = edges.find((e) => e.source === node.id && e.sourceHandle === makeAnswerOutputHandle(answer))
        const targetContext = getContextId(targetEdge?.targetNode)

        return {
          actions: [
            targetContext && {
              data: {
                context: targetContext,
              },
              type: {
                value: 'SET_CONTEXT' as const,
              },
            },
          ].filter(Boolean),
          conditions: [
            {
              variable: { value: 'message_text' as const },
              operator: { value: '$eq' as const },
              target: answer.text,
            },
          ],
        }
      }),
      targetFallbackContext && {
        actions: [
          {
            data: {
              context: targetFallbackContext,
            },
            type: {
              value: 'SET_CONTEXT' as const,
            },
          },
        ].filter(Boolean),
        conditions: [
          {
            variable: { value: 'message_text' as const },
            operator: { value: '$nin' as const },
            target: node.data.answers.map((a) => a.text),
          },
        ],
      },
    ].filter(Boolean),
  }

  const actionTargetEdge = edges.find((e) => e.source === node.id)
  const actionTargetContext = getContextId(actionTargetEdge?.targetNode)

  contexts.push({
    id: getContextId(node),
    name: node.id,
    triggers: [
      {
        trigger: {
          type: 'ENTER_CONTEXT' as const,
        },
        rules: [
          {
            actions: [
              {
                data: node.data,
                type: {
                  value: 'SEND_MESSAGE' as const,
                },
              },
              // if not has answers
              !node.data?.answers?.length &&
                actionTargetContext && {
                  data: {
                    context: actionTargetContext,
                  },
                  type: { value: 'SET_CONTEXT' as const },
                },
            ].filter(Boolean),
            conditions: [],
          },
        ],
      },
      // if has answers
      messageReceivedTrigger,
    ].filter(Boolean),
  })
}

// we just make it compatible with buildMessageNode to reuse code
const buildTemplateMessageNode = (contexts: BotContexts, flowData: FlowData, node: SendTemplateMessageNodeType) => {
  type Button = ButtonsData['action']['buttons'][number]
  type ListItem = SimpleListData['action']['sections'][number]['rows'][number]

  const buttons: Button[] = (node.data?.interactive as ButtonsData)?.action?.buttons || []
  const rows: ListItem[] =
    (node.data?.interactive as SimpleListData)?.action?.sections?.reduce((list, s) => [...list, ...s.rows], []) || []

  const answersCompat = [
    ...buttons.map((button) => ({
      id: button.reply.id,
      text: button.reply.title,
    })),
    ...rows.map((row) => ({
      id: row.id,
      text: row.title,
    })),
  ]

  const nodeCompat: MessageNodeType = {
    ...node,
    type: 'MESSAGE_NODE',
    data: {
      ...node.data,
      answers: answersCompat,
    },
  }

  buildMessageNode(contexts, flowData, nodeCompat)
}

const buildSendHolidayMessageNode = (contexts: BotContexts, { edges }: FlowData, node: SendHolidayMessageNodeType) => {
  const targetEdge = edges.find((e) => e.source === node.id)
  const targetContext = getContextId(targetEdge?.targetNode)

  contexts.push({
    id: getContextId(node),
    name: node.id,
    triggers: [
      {
        trigger: {
          type: 'ENTER_CONTEXT' as const,
        },
        rules: [
          {
            actions: [
              {
                data: {
                  text: '@botOnHoliday',
                },
                type: {
                  value: 'SEND_MESSAGE' as const,
                },
              },
              targetContext && {
                data: {
                  context: targetContext,
                },
                type: {
                  value: 'SET_CONTEXT' as const,
                },
              },
            ].filter(Boolean),
            conditions: [],
          },
        ],
      },
    ].filter(Boolean),
  })
}

const buildCommonActionNode = (contexts: BotContexts, { edges }: FlowData, node: Node) => {
  const targetEdge = edges.find((e) => e.source === node.id)
  const targetContext = getContextId(targetEdge?.targetNode)

  const actionType = nodeActionsMap[node.type]

  contexts.push({
    id: getContextId(node),
    name: node.id,
    triggers: [
      {
        trigger: {
          type: 'ENTER_CONTEXT',
        },
        rules: [
          {
            actions: [
              actionType && {
                data: node.data,
                type: {
                  value: actionType,
                },
              },
              targetContext && {
                data: {
                  context: targetContext,
                },
                type: {
                  value: 'SET_CONTEXT' as const,
                },
              },
            ].filter(Boolean),
            conditions: [],
          },
        ],
      },
    ],
  })
}

const buildAiNode = (contexts: BotContexts, { edges }: FlowData, node: AiNodeType) => {
  const rules: Rule[] = [
    {
      actions: [
        {
          data: {
            agentId: node.data.agentId,
            // knowledgeBase: node.data.knowledgeBase,
            voiceTone: node.data.voiceTone,
            languageType: node.data.languageType,
            function: node.data.function,
            companySegment: node.data.companySegment,
            companySubject: node.data.companySubject,
            companyServices: node.data.companyServices,
            prompt: node.data.prompt,
            maxAttempts: node.data.maxAttempts,
            actions: node.data.actions?.map((action) => {
              const targetEdge = edges.find(
                (e) => e.source === node.id && e.sourceHandle === makeActionOutputHandle(node.id, action),
              )
              const targetContext = getContextId(targetEdge?.targetNode)

              return {
                context: targetContext,
                action,
              }
            }),
          },
          type: {
            value: 'AI' as const,
          },
        },
      ],
      conditions: [],
    },
  ]

  contexts.push({
    id: getContextId(node),
    name: node.id,
    triggers: [
      {
        trigger: {
          type: 'ENTER_CONTEXT' as const,
        },
        rules,
      },
      {
        trigger: {
          type: 'MESSAGE_RECEIVED' as const,
        },
        rules,
      },
    ].filter(Boolean),
  })
}

export default ({ nodes, edges }: { nodes: Node[]; edges: ReactFlowEdge[] }): BotContexts => {
  const contexts: BotContexts = [
    {
      id: '@INIT',
      name: 'Contexto inicial',
      triggers: [],
    },
    {
      id: '@EVERY',
      name: 'Contexto persistente',
      triggers: [],
    },
    {
      id: '@FALLBACK',
      name: 'Contexto de contingência',
      triggers: [],
    },
  ]

  const data: FlowData = {
    nodes,
    edges: edges.map((e) => ({
      ...e,
      sourceNode: getNodeById(nodes, e.source),
      targetNode: getNodeById(nodes, e.target),
    })),
  }

  data.nodes.forEach((node: Node) => {
    if (node.type === 'START_NODE') {
      // already at contexts
    } else if (node.type === 'PERSISTENT_START_NODE') {
      // already at contexts
    } else if (node.type === 'TRIGGER_NODE') {
      buildTriggerNode(contexts, data, node)
    } else if (node.type === 'CONDITION_NODE') {
      buildConditionNode(contexts, data, node)
    } else if (node.type === 'MESSAGE_NODE') {
      buildMessageNode(contexts, data, node)
    } else if (node.type === 'TEMPLATE_MESSAGE_NODE') {
      buildTemplateMessageNode(contexts, data, node)
    } else if (node.type === 'SEND_HOLIDAY_MESSAGE_NODE') {
      buildSendHolidayMessageNode(contexts, data, node)
    } else if (node.type === 'AI_NODE') {
      buildAiNode(contexts, data, node)
    } else {
      buildCommonActionNode(contexts, data, node)
    }
  })

  return contexts
}
