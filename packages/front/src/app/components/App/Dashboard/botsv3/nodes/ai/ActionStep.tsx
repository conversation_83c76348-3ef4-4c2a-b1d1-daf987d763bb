import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Collapsible, CollapsibleContent } from '../../../../../common/unconnected/ui/collapsible'
import { Input } from '../../../../../common/unconnected/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../common/unconnected/ui/select'
import { FormStepProps } from './AiEdit'

const attemptsOptions = [
  { value: '3', label: '3 tentativas' },
  { value: '4', label: '4 tentativas' },
  { value: '5', label: '5 tentativas' },
]

export const ActionStep = ({ formik, node, userCanUpdateBots }: FormStepProps) => {
  const firstActionId = formik.values.actions?.[0]?.id

  const [openCollapsibles, setOpenCollapsibles] = useState<Record<string, boolean>>({
    ...(firstActionId && { [firstActionId]: true }),
  })

  const toggleCollapsible = (id: string) => {
    setOpenCollapsibles((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  return (
    <div style={{ padding: '16px' }}>
      <Label style={{ fontWeight: 600, marginBottom: '16px' }}>Comandos</Label>

      <p style={{ color: '#586171', fontSize: '14px', marginBottom: '16px' }}>
        Defina os comandos para o robô realizar
      </p>

      {formik.values.actions.map((item, i) => (
        <Collapsible
          key={item.id}
          style={{
            border: '1px solid #D7DBE0',
            borderRadius: '24px',
            marginBottom: '16px',
          }}
          open={openCollapsibles[item.id] ?? false}
          onOpenChange={() => toggleCollapsible(item.id)}
          data-testid={`bot_v3_edit_sidebar_${node.type}-collapsible-${i}`}
        >
          <button
            onClick={() => toggleCollapsible(item.id)}
            type="button"
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: 'none',
              background: 'none',
              height: '72px',
              width: '100%',
              padding: '24px',
            }}
            data-testid={`bot_v3_edit_sidebar_${node.type}-collapsible-trigger_${i}`}
          >
            <Label style={{ fontWeight: 600 }}>{item.name}</Label>
            {openCollapsibles[item.id] ? <ChevronUp color="#24272D" /> : <ChevronDown color="#24272D" />}
          </button>

          <CollapsibleContent>
            <div style={{ padding: '0px 24px 24px 24px' }}>
              <Input
                id={item?.name.toLowerCase().replace(/\s+/g, '-')}
                disabled
                value={item.description}
                data-testid={`bot_v3_edit_sidebar_${node.type}-input-name_${i}`}
              />
              {item.type === 'agent-cannot-answer' && (
                <>
                  <Label style={{ fontWeight: 500, marginTop: '16px' }}>Transferir após</Label>
                  <Select
                    disabled={!userCanUpdateBots}
                    defaultValue={formik.values.maxAttempts.toString()}
                    onValueChange={(value) => formik.setFieldValue('maxAttempts', parseInt(value, 10))}
                  >
                    <SelectTrigger data-testid={`bot_v3_edit_sidebar_${node.type}-select-attempts_trigger`}>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                    <SelectContent>
                      {attemptsOptions.map((option) => (
                        <SelectItem key={option.label} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      ))}
    </div>
  )
}
