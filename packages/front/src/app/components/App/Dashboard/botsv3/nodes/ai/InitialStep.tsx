import React from 'react'
import { Label } from '../../../../../common/unconnected/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../common/unconnected/ui/select'
import { Textarea } from '../../../../../common/unconnected/ui/textarea'
import { FormStepProps } from './AiEdit'
import { CharacterLimit } from '../../../interactiveMessages/InteractiveMessagesForm/InteractiveMessagesForm'

const labelStyle = { color: '#6E7A89', marginBottom: '4px' }
const sectionTitleStyle = { fontWeight: 600, marginBottom: '24px' }
const formFieldStyle = { marginBottom: '24px' }

const VOICE_TONE_OPTIONS = [
  { value: 'formal', label: 'Formal' },
  { value: 'neutral', label: 'Neutro' },
  { value: 'informal', label: 'Informal' },
]

const LANGUAGE_TYPE_OPTIONS = [
  { value: 'simple', label: 'Simples' },
  { value: 'neutral', label: 'Neutro' },
  { value: 'technical', label: 'Técnica' },
]

const FUNCTION_OPTIONS = [
  { value: 'triage', label: 'Fazer triagem' },
  { value: 'sales', label: 'Realizar vendas' },
  { value: 'answer-questions', label: 'Responder dúvidas' },
  { value: 'support', label: 'Dar suporte' },
]

export const InitialStep = ({ formik, node, userCanUpdateBots }: FormStepProps) => {
  const { values } = formik

  return (
    <div>
      <div style={{ padding: '16px', borderBottom: '1px solid #e0e0e0' }}>
        <Label style={sectionTitleStyle}>Configurações iniciais</Label>

        {/* <Label style={labelStyle}>Bases de conhecimento</Label>
        <Select
          disabled={!userCanUpdateBots || true}
          defaultValue={values.knowledgeBase}
          onValueChange={(value) => formik.setFieldValue('knowledgeBase', value)}
        >
          <SelectTrigger
            style={formFieldStyle}
            data-testid={`bot_v3_edit_sidebar_${node.type}-select-knowledge_base_trigger`}
          >
            <SelectValue placeholder="Selecione" />
          </SelectTrigger>
          <SelectContent>
            {[].map(
              (item) =>
                item.name && (
                  <SelectItem key={item.name} value={item.name}>
                    {item.name}
                  </SelectItem>
                ),
            )}
          </SelectContent>
        </Select> */}

        <Label style={labelStyle}>Tom de voz</Label>
        <Select
          disabled={!userCanUpdateBots}
          defaultValue={values.voiceTone}
          onValueChange={(value) => formik.setFieldValue('voiceTone', value)}
        >
          <SelectTrigger
            style={formFieldStyle}
            data-testid={`bot_v3_edit_sidebar_${node.type}-select-voice_tone_trigger`}
          >
            <SelectValue placeholder="Selecione" />
          </SelectTrigger>
          <SelectContent>
            {VOICE_TONE_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label style={labelStyle}>Tipo de linguagem</Label>
        <Select
          disabled={!userCanUpdateBots}
          defaultValue={values.languageType}
          onValueChange={(value) => formik.setFieldValue('languageType', value)}
        >
          <SelectTrigger data-testid={`bot_v3_edit_sidebar_${node.type}-select-language_type_trigger`}>
            <SelectValue placeholder="Selecione" />
          </SelectTrigger>
          <SelectContent>
            {LANGUAGE_TYPE_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div style={{ padding: '16px' }}>
        <Label style={sectionTitleStyle}>Informações da sua empresa</Label>

        <Label style={labelStyle}>Função do agente inteligente</Label>
        <Select
          disabled={!userCanUpdateBots}
          defaultValue={values.function}
          onValueChange={(value) => formik.setFieldValue('function', value)}
        >
          <SelectTrigger
            style={formFieldStyle}
            data-testid={`bot_v3_edit_sidebar_${node.type}-select-function_trigger`}
          >
            <SelectValue placeholder="Selecione" />
          </SelectTrigger>
          <SelectContent>
            {FUNCTION_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label style={labelStyle}>Qual é o seguimento da sua empresa?</Label>
        <Textarea
          id="companySegment"
          disabled={!userCanUpdateBots}
          maxLength={70}
          value={values.companySegment || ''}
          onChange={formik.handleChange}
          style={{ minHeight: '56px', maxHeight: '100px' }}
          placeholder="Exemplo: Imobiliária de alto padrão"
          data-testid={`bot_v3_edit_sidebar_${node.type}-textarea-company_segment`}
        />
        <CharacterLimit
          message={values?.companySegment || ''}
          limit={70}
          label="caracteres"
          style={{
            margin: '4px 0px 12px 0px',
            color: formik.errors?.companySegment ? 'red' : 'gray',
          }}
        />

        <Label style={{ marginTop: '24px', ...labelStyle }}>Qual é o nome do seu produto ou empresa?</Label>
        <Textarea
          id="companySubject"
          disabled={!userCanUpdateBots}
          maxLength={70}
          value={values?.companySubject || ''}
          onChange={formik.handleChange}
          style={{ minHeight: '56px', maxHeight: '100px' }}
          placeholder="Exemplo: Imobiliária Doze Irmãos"
          data-testid={`bot_v3_edit_sidebar_${node.type}-textarea-company_subject`}
        />
        <CharacterLimit
          message={values?.companySubject || ''}
          limit={70}
          label="caracteres"
          style={{
            margin: '4px 0px 12px 0px',
            color: formik.errors?.companySubject ? 'red' : 'gray',
          }}
        />

        <Label style={{ marginTop: '24px', ...labelStyle }}>Quais serviços e/ou produtos você oferece?</Label>
        <Textarea
          id="companyServices"
          disabled={!userCanUpdateBots}
          maxLength={200}
          value={values?.companyServices || ''}
          onChange={formik.handleChange}
          style={{ minHeight: '80px', maxHeight: '200px' }}
          placeholder="Exemplo: Compra, venda e aluguel de imóveis de alto padrão."
          data-testid={`bot_v3_edit_sidebar_${node.type}-textarea-company_services`}
        />
        <CharacterLimit
          message={values?.companyServices || ''}
          limit={200}
          label="caracteres"
          style={{
            margin: '4px 0px 12px 0px',
            color: formik.errors?.companyServices ? 'red' : 'gray',
          }}
        />
      </div>
    </div>
  )
}
