import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { v4 as uuid } from 'uuid'
import { useTranslation } from 'react-i18next'
import { HelpCircle, X } from 'lucide-react'
import { Node as ReactFlowNode, Edge, useReactFlow } from 'reactflow'
import { AiNodeData } from './types'
import { ConfirmUnsavedChangesDialog } from '../../EditSidebar/ConfirmUnsavedChangesDialog'
import { Button } from '../../../../../common/unconnected/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../../../../common/unconnected/ui/tooltip'
import { InitialStep } from './InitialStep'
import { PromptStep } from './PromptStep'
import { ActionStep } from './ActionStep'
import { defaultEdgeOptions, TransferTicketNodeType } from '../../Flow'

export interface FormStepProps {
  formik: ReturnType<typeof useFormik<FormData>>
  userCanUpdateBots: boolean
  node: {
    id: string
    type: string
    data: AiNodeData
  }
}

// Fields are optional to allow for partial saves
const FormSchema = Yup.object().shape({
  agentId: Yup.string().optional(),
  // knowledgeBase: Yup.array().of(Yup.string()).optional(),
  voiceTone: Yup.string().optional(),
  languageType: Yup.string().optional(),
  function: Yup.string().optional(),
  companySegment: Yup.string().max(70).optional(),
  companySubject: Yup.string().max(70).optional(),
  companyServices: Yup.string().max(200).optional(),
  prompt: Yup.string().max(1600).optional(),
  maxAttempts: Yup.string().optional(),
  actions: Yup.array().of(
    Yup.object().shape({
      id: Yup.string().optional(),
      name: Yup.string().optional(),
      description: Yup.string().optional(),
    }),
  ),
})

type FormData = AiNodeData

type AiEditProps = {
  data: FormData
  onChange: (value: FormData) => void
  onChangeHasUnsavedChanges: (value: boolean) => void
  onClose: (wasSubmitted?: boolean) => void
  isOpenHasUnsavedChangesDialog: boolean
  setIsOpenHasUnsavedChangesDialog: (value: boolean) => void
  userCanUpdateBots: boolean
  node: {
    id: string
    type: string
    data: AiNodeData
    position: { x: number; y: number }
  }
}

const HEADER_TEXT = {
  1: 'Escolha as configurações iniciais do agente inteligente',
  2: 'Criamos uma sugestão de instrução com base nas informações da sua empresa e produto',
  3: 'Defina os comandos que vão guiar o agente inteligente.',
}

export const transferTicketTypes = [
  { suffix: 'customer-requested-agent', yOffset: -150 },
  { suffix: 'agent-cannot-answer', yOffset: 0 },
  { suffix: 'agent-out-of-credits', yOffset: +150 },
]

function AiEdit({
  data,
  node,
  onChange,
  onChangeHasUnsavedChanges,
  onClose,
  isOpenHasUnsavedChangesDialog,
  setIsOpenHasUnsavedChangesDialog,
  userCanUpdateBots,
}: AiEditProps) {
  const [formStep, setFormStep] = useState(1)
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false)

  const { t } = useTranslation('botsPage')
  const { getEdges, setNodes, setEdges } = useReactFlow()

  const initialValues = useMemo<FormData>(
    () => ({
      // knowledgeBase: [],
      voiceTone: '',
      languageType: '',
      function: '',
      companySegment: '',
      companySubject: '',
      companyServices: '',
      prompt: '',
      maxAttempts: 3,
      actions: [
        {
          id: uuid(),
          name: 'Cliente solicitou falar com atendente',
          description: 'Transferir para atendimento humano',
          type: 'customer-requested-agent',
        },
        {
          id: uuid(),
          name: 'Agente não soube responder',
          description: 'Transferir para atendimento humano',
          type: 'agent-cannot-answer',
        },
        {
          id: uuid(),
          name: 'Agente está sem créditos',
          description: 'Transferir para atendimento humano',
          type: 'agent-out-of-credits',
        },
      ],
      ...data,
    }),
    [data],
  )

  const formik = useFormik<FormData>({
    initialValues,
    validationSchema: FormSchema,
    onSubmit: onChange,
  })

  const isStep1ButtonDisabled = useMemo(() => {
    return (
      !formik.values.voiceTone ||
      !formik.values.languageType ||
      !formik.values.function ||
      !formik.values.companySegment ||
      !formik.values.companySubject ||
      !formik.values.companyServices
    )
  }, [
    formik.values.voiceTone,
    formik.values.languageType,
    formik.values.function,
    formik.values.companySegment,
    formik.values.companySubject,
    formik.values.companyServices,
  ])

  const isStep2ButtonDisabled = useMemo(() => {
    return !formik.values.prompt || isGeneratingPrompt
  }, [formik.values.prompt, isGeneratingPrompt])

  const isStep3ButtonDisabled = useMemo(() => {
    return !formik.dirty || !formik.isValid
  }, [formik])

  const isButtonDisabled = useMemo(() => {
    if (!userCanUpdateBots) return formStep === 3

    if (formStep === 1) return isStep1ButtonDisabled
    if (formStep === 2) return isStep2ButtonDisabled
    if (formStep === 3) return isStep3ButtonDisabled

    return false
  }, [formStep, isStep1ButtonDisabled, isStep2ButtonDisabled, isStep3ButtonDisabled])

  const addTransferTicketNodesToAiNode = useCallback(() => {
    const newNodes: ReactFlowNode[] = []
    const newEdges: Edge[] = []

    transferTicketTypes.forEach(({ suffix, yOffset }) => {
      const nodeId = `TRANSFER_TICKET_${uuid()}`

      const newNode: TransferTicketNodeType = {
        id: nodeId,
        type: 'TRANSFER_TICKET_NODE',
        position: {
          x: node.position.x + 450,
          y: node.position.y + yOffset,
        },
        data: {},
        deletable: false,
      }

      const newEdge: Edge = {
        ...defaultEdgeOptions,
        source: node.id,
        sourceHandle: `OUTPUT_${node.id}_${suffix}`,
        target: nodeId,
        targetHandle: 'INPUT',
        id: `reactflow__edge-${node.id}OUTPUT-${nodeId}INPUT`,
        updatable: false,
        deletable: false,
      }

      newNodes.push(newNode)
      newEdges.push(newEdge)
    })

    setNodes((nds) => [...nds, ...newNodes])
    setEdges((eds) => [...eds, ...newEdges])
  }, [setNodes, setEdges])

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    const connectedEdges = getEdges().filter((edge) => edge.source === node.id || edge.target === node.id)
    if (connectedEdges.length === 0) {
      addTransferTicketNodesToAiNode()
    }

    onChangeHasUnsavedChanges(false)
    onClose(true)
    formik.handleSubmit(e)
  }

  const nextFormStep = async (e) => {
    e.preventDefault()

    if (formStep === 3) {
      return handleSubmit(e)
    }
    setFormStep((prev) => prev + 1)
  }

  useEffect(() => {
    formik.setValues(initialValues)
  }, [initialValues])

  useEffect(() => {
    onChangeHasUnsavedChanges(formik.dirty)
  }, [formik.dirty])

  return (
    <form onSubmit={handleSubmit}>
      <header
        style={{
          padding: '16px',
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onClose()}
            data-testid={`bot_v3_edit_sidebar_${node.type}-button-close`}
          >
            <X
              color="#24272D"
              style={{
                width: '24px',
                height: '24px',
              }}
            />
          </Button>
          <h2
            style={{
              color: '#24272D',
              fontSize: '16px',
              fontWeight: 600,
              marginBottom: '0px',
            }}
          >
            Agente inteligente
          </h2>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <a
                  href="https://digisac.gitbook.io/manual-digisac-2-0"
                  target="_blank"
                  rel="noreferrer"
                  data-testid={`bot_v3_edit_sidebar_${node.type}-link-help`}
                >
                  <Button size="icon" variant="ghost">
                    <HelpCircle
                      color="#324B7D"
                      style={{
                        width: '24px',
                        height: '24px',
                      }}
                    />
                  </Button>
                </a>
              </TooltipTrigger>
              <TooltipContent side="bottom" sideOffset={4} collisionPadding={16}>
                {t('HELP_MATERIAL_TEXT')}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <p
          style={{
            margin: '0px',
            color: '#24272D',
            fontSize: '14px',
            fontWeight: 400,
            marginTop: '8px',
          }}
        >
          {HEADER_TEXT[formStep]}
        </p>
      </header>

      <main style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
        {formStep === 1 && <InitialStep formik={formik} node={node} userCanUpdateBots={userCanUpdateBots} />}

        {formStep === 2 && (
          <PromptStep
            formik={formik}
            node={node}
            userCanUpdateBots={userCanUpdateBots}
            isGeneratingPrompt={isGeneratingPrompt}
            setIsGeneratingPrompt={setIsGeneratingPrompt}
          />
        )}

        {formStep === 3 && <ActionStep formik={formik} node={node} userCanUpdateBots={userCanUpdateBots} />}
      </main>

      <div style={{ borderTop: '1px solid #e0e0e0', display: 'flex', gap: '8px', padding: '16px' }}>
        <Button
          style={{ width: '100%' }}
          disabled={isGeneratingPrompt}
          variant="outline"
          title="Cancelar alterações no elemento"
          onClick={() => (formStep > 1 ? setFormStep((prev) => prev - 1) : onClose(false))}
          data-testid={`bot_v3_edit_sidebar_${node.type}-button-cancel_changes`}
        >
          {formStep > 1 ? 'Voltar' : 'Cancelar'}
        </Button>
        <Button
          style={{ width: '100%' }}
          disabled={isButtonDisabled}
          type={formStep === 3 && formik.isValid ? 'submit' : 'button'}
          title={formStep === 3 ? 'Aplicar alterações no elemento' : 'Avançar para o próximo passo'}
          onClick={nextFormStep}
          data-testid={`bot_v3_edit_sidebar_${node.type}-button-${formStep === 3 ? 'apply_changes' : 'next_step'}`}
        >
          {formStep === 3 ? 'Aplicar' : 'Avançar'}
        </Button>
      </div>

      <ConfirmUnsavedChangesDialog
        isOpenHasUnsavedChangesDialog={isOpenHasUnsavedChangesDialog}
        setIsOpenHasUnsavedChangesDialog={setIsOpenHasUnsavedChangesDialog}
        onChangeHasUnsavedChanges={onChangeHasUnsavedChanges}
        onClose={onClose}
      />
    </form>
  )
}

export default React.memo(AiEdit)
