export type LocalFile = {
  file: File
  base64Url: string
  mimetype: string
  fileName: string
}

export type ApiFile = {
  id: string
  url: string
  publicFilename: string
  mimetype: string
  fileName: string
}

export type MessageNodeData = {
  text?: string
  answers?: {
    id: string
    text: string
    fallback?: boolean
  }[]
  file?: LocalFile | ApiFile
}

export type BaseInteractiveMessage = {
  header: {
    type: 'text'
    text?: string
  }
  body: {
    text?: string
  }
  footer: {
    text?: string
  }
}

export type ButtonsData = BaseInteractiveMessage & {
  type: 'button'
  action: {
    buttons: {
      reply: { id: string; title: string }
      type: 'reply'
    }[]
  }
}

export type SimpleListData = BaseInteractiveMessage & {
  type: 'list'
  action: {
    button: string
    sections: {
      title: string
      rows: { id: string; title: string }[]
    }[]
  }
}

export type SendTemplateMessageNodeData = {
  interactive?: ButtonsData | SimpleListData
  file?: LocalFile | ApiFile
}
