import React from 'react'
import { Position } from 'reactflow'
import styled from 'styled-components'
import { Cpu } from 'lucide-react'
import BaseNode, { <PERSON><PERSON>, HeaderContainer, InputHandle, Label, OutputHandle } from '../BaseNode'
import { AiNodeData } from './types'
import { Separator } from '../../../../../common/unconnected/ui/separator'
import { NodeActions } from '../NodeActions'
import { Node } from '../../Flow'
import { truncateText } from '../../../../../../utils/truncate-text'

export const makeActionOutputHandle = (nodeId: string, action: AiNodeData['actions'][number]) =>
  `OUTPUT_${nodeId}_${action?.type}`

const Ai = styled.div`
  white-space: pre-line;
  overflow-wrap: break-word;
  margin: 0;
  border-radius: 7px;
  padding: 12px;
`

const ActionsContainer = styled.div`
  margin-top: 10px;
`

const ActionContainer = styled.div`
  position: relative;
  flex-grow: 1;
  text-align: left;
`

const Action = styled(Ai)`
  background-color: #ececec;
  margin: 10px 0 0 0;
`

const ActionOutputHandle = styled(OutputHandle)`
  right: -23px;
`

export const AiHeader = React.memo((props: { small?: boolean; isCollapsed?: boolean; showBadgeNew?: boolean }) => (
  <Header
    iconBoxBackgroundColor="#E1EDF8"
    customIcon={<Cpu size={16} color="#4679CA" />}
    title={props.isCollapsed ? '' : 'Agente inteligente'}
    showBadgeNew={props.showBadgeNew}
    {...props}
  />
))

const targetBlacklistInput: Node['type'][] = ['START_NODE', 'PERSISTENT_START_NODE']
const targetBlacklistOutput: Node['type'][] = ['TRIGGER_NODE']

interface AiNodeProps {
  id: string
  data: AiNodeData
  isConnectable: boolean
  selected: boolean
}

function AiNode({ id, data, isConnectable, selected }: AiNodeProps) {
  const hasActions = !!data.actions?.length

  return (
    <BaseNode selected={selected} data-testid={`bot_v3-node-${id}`}>
      <HeaderContainer>
        <AiHeader />
        <NodeActions id={id} />
      </HeaderContainer>
      <Separator
        style={{
          margin: '12px 0',
        }}
      />
      <InputHandle
        id="INPUT"
        position={Position.Left}
        isConnectable={isConnectable}
        targetBlacklist={targetBlacklistInput}
        nodeId={id}
      />

      {hasActions ? (
        <ActionsContainer>
          <Label>Caminhos</Label>

          {data.actions.map((action) => (
            <ActionContainer key={action.id}>
              <Action>{truncateText(action.name)}</Action>
              <ActionOutputHandle
                position={Position.Right}
                id={makeActionOutputHandle(id, action)}
                nodeId={id}
                targetBlacklist={targetBlacklistOutput}
              />
            </ActionContainer>
          ))}
        </ActionsContainer>
      ) : (
        <ActionContainer>
          <Action />
        </ActionContainer>
      )}
    </BaseNode>
  )
}

export default React.memo(AiNode)
