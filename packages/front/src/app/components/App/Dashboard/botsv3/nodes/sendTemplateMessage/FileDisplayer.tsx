import React, { useMemo } from 'react'
import { ApiFile, LocalFile, MessageNodeData } from './types'
import isImage from '../../../../../common/unconnected/isImage'
import { CardImg } from 'reactstrap'
import Icon from '../../../../../common/unconnected/Icon'

type Props = {
  file: MessageNodeData['file']
}

function FileDisplayer(props: Props) {
  const { file } = props

  const url = (file as ApiFile)?.url || (file as LocalFile)?.base64Url
  const fileName = (file as ApiFile)?.publicFilename || (file as LocalFile)?.fileName

  const displayFileName = useMemo(
    () => (fileName?.length > 35 ? `${fileName?.replace(fileName?.substring(27), '')}...` : fileName),
    [fileName],
  )

  return (
    <>
      {isImage(file?.mimetype) ? (
        <CardImg width="270%" src={url} alt={displayFileName} />
      ) : (
        <h4 className="pl-4 pt-4">
          <a href={url} download={displayFileName}>
            <Icon name="file" fixedWidth className="mr-1" />
            {displayFileName}
          </a>
        </h4>
      )}
    </>
  )
}

export default React.memo(FileDisplayer)
