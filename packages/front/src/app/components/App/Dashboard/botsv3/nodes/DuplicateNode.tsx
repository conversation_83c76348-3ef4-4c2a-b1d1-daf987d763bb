import React from 'react'
import { Copy } from 'lucide-react'
import { Edge, Node, useReactFlow } from 'reactflow'
import { DropdownMenuItem } from '../../../../common/unconnected/ui/dropdown-menu'
import { v4 as uuid } from 'uuid'
import { transferTicketTypes } from './ai/AiEdit'
import { defaultEdgeOptions, TransferTicketNodeType } from '../Flow'

interface DuplicateNodeProps {
  id: string
}

export function DuplicateNode({ id }: DuplicateNodeProps) {
  const { getNodes, setNodes, setEdges } = useReactFlow()

  function duplicateNode(e: React.MouseEvent) {
    e.stopPropagation()

    const nodes = getNodes()
    const currentNode = nodes.find((node) => node.id === id)
    if (!currentNode) return

    // Posição inicial por uma variavel
    let newX = currentNode.position.x + 50
    let newY = currentNode.position.y + 50

    // Verifica se já existe alguma duplicação no local
    let positionOccupied = true

    while (positionOccupied) {
      positionOccupied = nodes.some(
        (node) => Math.abs(node.position.x - newX) < 10 && Math.abs(node.position.y - newY) < 10,
      )

      if (positionOccupied) {
        newX += 50
        newY += 50
      }
    }

    const newNode = {
      ...currentNode,
      id: `${currentNode.type}_${uuid()}`,
      position: {
        x: newX,
        y: newY,
      },
      data: { ...currentNode.data },
      selected: false,
      deletable: true,
    }

    if (newNode.id.startsWith('AI_NODE')) {
      delete newNode.data.agentId // Remove agentId to create a new agent for this node during bot publish
    }

    setNodes((nds) => [...nds, newNode])

    if (newNode.id.startsWith('AI_NODE') && newNode.data.actions?.length > 0) {
      setTimeout(() => {
        const newNodes: Node[] = []
        const newEdges: Edge[] = []

        transferTicketTypes.forEach(({ suffix, yOffset }) => {
          const nodeId = `TRANSFER_TICKET_${uuid()}`

          const newTransferNode: TransferTicketNodeType = {
            id: nodeId,
            type: 'TRANSFER_TICKET_NODE',
            position: {
              x: newNode.position.x + 450,
              y: newNode.position.y + yOffset,
            },
            data: {},
            deletable: false,
          }

          const newEdge: Edge = {
            ...defaultEdgeOptions,
            source: newNode.id,
            sourceHandle: `OUTPUT_${newNode.id}_${suffix}`,
            target: newTransferNode.id,
            targetHandle: 'INPUT',
            id: `reactflow__edge-${newNode.id}OUTPUT-${newTransferNode.id}INPUT`,
            updatable: false,
            deletable: false,
          }

          newNodes.push(newTransferNode)
          newEdges.push(newEdge)
        })

        setNodes((nds) => [...nds, ...newNodes])
        setEdges((eds) => [...eds, ...newEdges])
      }, 500)
    }
  }

  return (
    <DropdownMenuItem onClick={duplicateNode} data-testid="bot_v3_duplicate_node-dropdown_menu_item-duplicate">
      <Copy size={16} style={{ marginRight: '8px', color: '#586171' }} />
      Duplicar bloco
    </DropdownMenuItem>
  )
}
