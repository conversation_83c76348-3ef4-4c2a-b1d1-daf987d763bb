import React, { useEffect } from 'react'
import { WandSparkles } from 'lucide-react'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Textarea } from '../../../../../common/unconnected/ui/textarea'
import { Button } from '../../../../../common/unconnected/ui/button'
import { FormStepProps } from './AiEdit'
import { GeneratePromptDialog } from './GeneratePromptDialog'
import botsApi from '../../../../../../resources/bot/api'
import { useToast } from '../../../../../../hooks/useToast'
import useToggle from '../../../../../../hooks/useToggle'
import { CharacterLimit } from '../../../interactiveMessages/InteractiveMessagesForm/InteractiveMessagesForm'

interface PromptStepProps extends FormStepProps {
  isGeneratingPrompt: boolean
  setIsGeneratingPrompt: (value: boolean) => void
}

export const PromptStep = ({
  formik,
  node,
  userCanUpdateBots,
  isGeneratingPrompt,
  setIsGeneratingPrompt,
}: PromptStepProps) => {
  const { toast } = useToast()
  const { isOpen, close, open } = useToggle()

  const controllerRef = React.useRef<AbortController>(null)

  const handleSuggestPrompt = async () => {
    if (controllerRef.current) {
      controllerRef.current.abort()
      controllerRef.current = null
    }

    setIsGeneratingPrompt(true)
    controllerRef.current = new AbortController()

    if (isOpen) close()

    try {
      const { data } = await botsApi.suggestAgentPrompt(
        {
          voiceTone: formik.values.voiceTone,
          languageType: formik.values.languageType,
          function: formik.values.function,
          companySegment: formik.values.companySegment,
          companySubject: formik.values.companySubject,
          companyServices: formik.values.companyServices,
        },
        {
          signal: controllerRef.current?.signal,
        },
      )

      formik.setFieldValue('prompt', data)
    } catch (error) {
      if (error.message === 'canceled') {
        toast({
          title: 'Geração de sugestão de instruções do robô cancelada pelo usuário.',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: 'Erro ao gerar sugestão de instruções do robô.',
        variant: 'destructive',
      })
    } finally {
      setIsGeneratingPrompt(false)
    }
  }

  const generatingPromptPlaceholder = 'Gerando sugestão de instruções do robô...'

  useEffect(() => {
    if (!formik.values.prompt) {
      handleSuggestPrompt()
    }

    return () => {
      // Cancel the request if the component unmounts (eg: user closes the edit sidebar)
      if (controllerRef.current) {
        controllerRef.current.abort()
        controllerRef.current = null
      }
    }
  }, [])

  return (
    <div style={{ padding: '16px' }}>
      <Label style={{ marginBottom: '4px' }}>Instruções do robô</Label>
      <Textarea
        id="prompt"
        disabled={!userCanUpdateBots || isGeneratingPrompt}
        maxLength={1600}
        value={isGeneratingPrompt ? generatingPromptPlaceholder : formik.values.prompt}
        onChange={formik.handleChange}
        style={{ minHeight: '498px', maxHeight: '498px' }}
        placeholder={isGeneratingPrompt ? generatingPromptPlaceholder : 'Digite aqui'}
        data-testid={`bot_v3_edit_sidebar_${node.type}-textarea_prompt`}
      />
      <CharacterLimit
        message={formik.values.prompt || ''}
        limit={1600}
        label="caracteres"
        style={{
          margin: '4px 0px 12px 0px',
          color: formik.errors?.prompt ? 'red' : 'gray',
        }}
      />

      <p style={{ color: '#586171', marginTop: '4px', fontSize: '12px', fontWeight: 500 }}>
        Edite esse comando que ditará o objetivo e ações do robô.
      </p>
      <Button
        variant="ghost"
        onClick={open}
        disabled={!userCanUpdateBots || isGeneratingPrompt}
        style={{ width: '100%' }}
        data-testid={`bot_v3_edit_sidebar_${node.type}-button-generate_prompt`}
      >
        <WandSparkles size={16} style={{ marginRight: '8px' }} />
        Gerar sugestão de instruções
      </Button>

      <GeneratePromptDialog isOpen={isOpen} toggle={close} handleSuggestPrompt={handleSuggestPrompt} />
    </div>
  )
}
