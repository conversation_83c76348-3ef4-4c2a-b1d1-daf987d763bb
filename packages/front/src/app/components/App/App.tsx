import React from 'react'
import { Switch, Route } from 'react-router-dom'
// eslint-disable-next-line import/no-extraneous-dependencies
import TagManager from 'react-gtm-module'
import { ToastContainer } from 'react-toastify'
import { Helmet } from 'react-helmet'
import 'react-toastify/dist/ReactToastify.css'
import '@fortawesome/fontawesome-svg-core/styles.css'
import config from '../../../../config'
import PrivateRoute from '../common/connected/privateRoute/PrivateRoute'
import Error404 from './Error404'
import DashboardRoute from './Dashboard'
import AccountExpired from './Dashboard/AccountExpired'
import LoginRoute from './Login'
import Wizard from './Wizard'
import ForgotPasswordRequest from './ForgotPassword/ForgotPasswordRequest'
import ForgotPasswordUpdate from './ForgotPassword/ForgotPasswordUpdate'
import ClientTicketHistory from './ClientUser/TicketHistory'
import Integration from './integration'
import { ErrorHandlerProvider } from '../../hooks/useErrorHandler'
import SetupSecret from './TwoFactorAuthentication/SetupSecret'
import { Toaster } from '../common/unconnected/ui/toaster'
import { Toaster as NebulaToaster } from '@ikatec/nebula-react'
import './../../../styles/global.css'
import './../../../styles/nebula-tokens.css'

const refresh = (e) => {
  e.preventDefault()
  window.location.reload()
}
const Fallback = () => (
  <div className="container d-flex align-items-center justify-content-center full-height">
    <div>
      <h3 className="mb-4">Uh, Houston, we've had a problem.</h3>
      <p>
        Ocorreu uma falha crítica na aplicação ao tentar executar a ação desejada. <br />
      </p>
      <p>
        Por enquanto você pode{' '}
        <a onClick={refresh} href="#">
          recarregar
        </a>{' '}
        a página e tentar novamente.
      </p>
    </div>
  </div>
)

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  render() {
    if (this.state.hasError) return <Fallback />
    return this.props.children
  }
}

if (process.env.BUILD_FLAG_IS_DEV !== 'true') {
  const gtmId = config('googleAnalyticsTag')
  TagManager.initialize({ gtmId })
}

export default () => (
  <>
    <Helmet titleTemplate={config('htmlPage.titleTemplate')} defaultTitle={config('htmlPage.defaultTitle')}>
      <html lang="pt-BR" />
      <meta charSet="utf-8" />
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="application-name" content={config('htmlPage.defaultTitle')} />
      <meta name="description" content={config('htmlPage.description')} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="msapplication-TileColor" content={config('whitelabel.primaryColor')} />
      <meta name="theme-color" content={config('whitelabel.primaryColor')} />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <link rel="mask-icon" href="/safari-pinned-tab.svg" color={config('whitelabel.primaryColor')} />
      <link rel="manifest" href="/manifest.json" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet" />
    </Helmet>
    <ErrorBoundary>
      <ErrorHandlerProvider>
        <Switch>
          <Route exact path="/expired-account" component={AccountExpired} />
          <Route exact path="/login" component={LoginRoute} />
          <Route exact path="/reset-password" component={ForgotPasswordRequest} />
          <Route exact path="/reset-password/:token" component={ForgotPasswordUpdate} />
          <Route exact path="/reset-password/:token/:accountId" component={ForgotPasswordUpdate} />
          {!config('whitelabel.hideTutorials') && <PrivateRoute exact path="/wizard" component={Wizard} />}
          <PrivateRoute path="/client/ticket-history" component={ClientTicketHistory} />
          <PrivateRoute path="/two-factor-auth/setup" component={SetupSecret} />
          <PrivateRoute path="/" component={DashboardRoute} />
          <Route component={Error404} />
        </Switch>
      </ErrorHandlerProvider>
    </ErrorBoundary>
    <ToastContainer />
    <Toaster />
    <div className="tailwind">
      <NebulaToaster />
    </div>
    <Integration />
  </>
)
