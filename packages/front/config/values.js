import { ContextReplacementPlugin } from 'webpack'
import EnvVars from '../internal/utils/envVars'
import fs from 'fs'
import path from 'path'

const getDevVendorDllInclude = () =>
  Object.keys(JSON.parse(fs.readFileSync('./package.json')).dependencies).filter(
    (d) => !['react-hot-loader'].includes(d),
  )

const mandeumzapWhiteLabel = {
  appName: 'MandeUmZap',
  primaryColor: '#009947',
  secondaryColor: '#353535',
  disabledColor: '#A6A8AC',
  darkColor: '#3C4965',
  lightColor: '#F9FBFF',
  textColor: '#868FA1',
  dangerColor: '#FF4651',
  borderColor: 'rgba(82, 101, 140, 0.15)',
  logo: 'mandeumzap',
  hideTutorials: false,
  logoStyle: {
    height: '45px',
    margin: '-5px 0 -15px -5px',
  },
  supportEmail: '<EMAIL>',
  supportWhatsapp: '5514981535744',
  supportPhone: '14 3103-7800',
  supportAILimitUrl: 'https://tinyurl.com/aumentarlimite-IA',
  apiDocsUrl: 'https://documenter.getpostman.com/view/24605757/2sA3BhfaDg',
  termsOfUseUrl: 'https://docs.wixstatic.com/ugd/72c045_4986cf557b7b4909946680ae209c5f32.pdf',
  publicAssetsPath: './public/mandeumzap',
}

const digisacWhiteLabel = {
  appName: 'Digisac',
  primaryColor: '#52658C',
  secondaryColor: '#353535',
  disabledColor: '#A6A8AC',
  darkColor: '#3C4965',
  lightColor: '#F9FBFF',
  textColor: '#515151',
  dangerColor: '#FF4651',
  borderColor: 'rgba(82, 101, 140, 0.15)',
  hideTutorials: false,
  logo: 'digisac',
  logoStyle: {
    height: '32px',
    margin: '-18px 0px -15px 0px',
  },
  supportEmail: '<EMAIL>',
  supportWhatsapp: '5514981535744',
  supportPhone: '14 3103-7800',
  supportAILimitUrl: 'https://tinyurl.com/aumentarlimite-IA',
  termsOfUseUrl: 'https://digisac.com.br/termos-de-uso',
  apiDocsUrl: 'https://documenter.getpostman.com/view/24605757/2sA3BhfaDg',
  privacyPolicyUrl: 'https://digisac.com.br/politicasdeprivacidade.html',
  publicAssetsPath: './public/digisac',
}

const hublxWhiteLabel = {
  appName: 'HUBLX',
  primaryColor: '#535353',
  secondaryColor: '#353535',
  disabledColor: '#A6A8AC',
  darkColor: '#3C4965',
  lightColor: '#F9FBFF',
  textColor: '#868FA1',
  dangerColor: '#FF4651',
  borderColor: 'rgba(82, 101, 140, 0.15)',
  hideTutorials: true,
  logo: 'hublx',
  logoStyle: { height: '38px', width: '100%' },
  supportEmail: '<EMAIL>',
  supportWhatsapp: '5511953042429',
  supportPhone: '(11) 95304-2429',
  supportAILimitUrl: 'https://tinyurl.com/aumentarlimite-IA',
  apiDocsUrl: 'https://documenter.getpostman.com/view/24605757/2sA3BhfaDg',
  termsOfUseUrl: 'https://hublx.com.br/termosdeuso.html',
  privacyPolicyUrl: 'https://hublx.com.br/politicasdeprivacidade.html',
  publicAssetsPath: './public/hublx',
}

const whiteLabelMap = {
  'digisac.app': digisacWhiteLabel,
  'digisac.chat': digisacWhiteLabel,
  'digisac.me': digisacWhiteLabel,
  'digisac.co': digisacWhiteLabel,
  'hublx.app': hublxWhiteLabel,
  'mandeumzap.app': mandeumzapWhiteLabel,
  'mandeumzap.com.br': mandeumzapWhiteLabel,
}

const domain = EnvVars.string('DOMAIN', 'mandeumzap.com.br')
const currentWhiteLabel = whiteLabelMap[domain] || whiteLabelMap['digisac.app']

const values = {
  clientConfigFilter: {
    serviceWorker: {
      enabled: true,
    },
    domain: true,
    whitelabel: true,
    htmlPage: true,
    apiUrl: true,
    frontUrl: true,
    socketUrl: true,
    webchatUrl: true,
    myplanUrl: true,
    internalChatUrl: true,
    remotePodSocketUrl: true,
    clientId: true,
    clientSecret: true,
    sentryDsn: true,
    isBeta: true,
    enableReduxLogger: true,
    googleApiKey: true,
    googleRecaptchaSiteKey: true,
    googleAnalyticsTag: true,
    version: true,
    microsoft: true,
    facebook: true,
    updateFetchCountDebounce: true,
    hub360PartnerId: true,
    isOnClusterMode: true,
    privateApiUrl: true,
    elasticApmServerUrl: true,
    elasticApmServiceName: true,
    elasticApmEnvironment: true,
    elasticApmActive: true,
    marketPlaceUrl: true,
    creditPurchaseUrl: true,
  },
  isOnClusterMode: EnvVars.bool('CLUSTER_MODE', false),
  apiUrl: EnvVars.string('API_URL', 'http://localhost:8080/v1'),
  privateApiUrl: EnvVars.string('INTERNAL_API_URL', 'http://app-api:8080/v1'),
  frontUrl: EnvVars.string('FRONT_URL', 'http://127.0.0.1:1337'),
  socketUrl: EnvVars.string('SOCKET_URL', 'http://localhost:8080'),
  webchatUrl: EnvVars.string('WEBCHAT_URL', 'http://localhost:4404'),
  myplanUrl: EnvVars.string('MYPLAN_URL', 'https://myplan.agnus.app'),
  internalChatUrl: EnvVars.string('INTERNAL_CHAT_URL', 'https://internalchat.digisac.io'),
  remotePodSocketUrl: EnvVars.string('REMOTE_POD_SOCKET_URL', `${EnvVars.string('API_URL').replace('/v1', '')}`),
  marketPlaceUrl: EnvVars.string('MARKETPLACE_URL', ''),
  clientId: EnvVars.string('CLIENT_ID', 'api'),
  clientSecret: EnvVars.string('CLIENT_SECRET', 'secret'),
  googleApiKey: EnvVars.string('GOOGLE_API_KEY', 'AIzaSyD58OtLDYOHh2NtpfMz3ojC1lcx2B9IBjI'),
  googleRecaptchaSiteKey: EnvVars.string('GOOGLE_RECAPTCHA_SITE_KEY', '6LcNVI0UAAAAADpAsyAj2g57xOMKSm0baKkibi-m'),
  googleAnalyticsTag: EnvVars.string('GOOGLE_ANALYTICS_TAG', 'GTM-WC224LN9'),
  updateFetchCountDebounce: EnvVars.number('UPDATE_FETCH_COUNT_DEBOUNCE', 3 * 1000),
  version: JSON.parse(fs.readFileSync(path.resolve(__dirname, '..', 'package.json'))).version || '0',
  hub360PartnerId: EnvVars.string('HUB360_API_PARTNER_ID', '5urmEBPA'),
  host: EnvVars.string('HOST', '127.0.0.1'),
  port: EnvVars.number('PORT', 1337),
  baseUrl: EnvVars.string('BASE_URL', ''),
  clientDevServerPort: EnvVars.number('CLIENT_DEV_PORT', 7331),
  sentryDsn: EnvVars.string('SENTRY_DSN', ''),
  elasticApmServerUrl: EnvVars.string('ELASTIC_APM_SERVER_URL', 'https://apm-digisac.ikatec.cloud'),
  elasticApmServiceName: EnvVars.string('ELASTIC_APM_SERVICE_NAME', 'digisac-frontend'),
  elasticApmEnvironment: EnvVars.string('ELASTIC_APM_ENVIRONMENT', 'ikatec'),
  elasticApmActive: EnvVars.bool('ELASTIC_APM_ACTIVE', true),
  isBeta: EnvVars.bool('BETA', false),
  enableReduxLogger: EnvVars.bool('ENABLE_REDUX_LOGGER', false),
  disableSSR: EnvVars.bool('DISABLE_SSR', true),
  browserCacheMaxAge: '365d',
  domain,
  whitelabel: currentWhiteLabel,
  microsoft: {
    clientId: EnvVars.string('MICROSOFT_CLIENT_ID', 'clientId'),
    redirectUri: EnvVars.string('MICROSOFT_REDIRECT_URI', 'redirectUri'),
  },
  facebook: {
    appId: EnvVars.string('FACEBOOK_APP_ID', ''),
  },
  htmlPage: {
    titleTemplate: `${currentWhiteLabel.appName} - %s`,
    defaultTitle: currentWhiteLabel.appName,
    description: 'Marketing Empresarial Multicanal',
  },
  cspExtensions: {
    workerSrc: ["'self'"],
    connectSrc: [...EnvVars.string('MEDIA_HOST').split(','), 'blob:', '*'],
    defaultSrc: [],
    fontSrc: ['fonts.googleapis.com/css', 'fonts.gstatic.com'],
    imgSrc: [
      ...EnvVars.string('MEDIA_HOST', '').split(','),
      'unpkg.com',
      'cdnjs.cloudflare.com',
      'twemoji.maxcdn.com',
      'data:',
      'blob:',
      'maps.googleapis.com',
      '*.google.com',
      '*',
    ],
    mediaSrc: [...EnvVars.string('MEDIA_HOST').split(','), 'blob:', '*'],
    manifestSrc: ["'self'"],
    objectSrc: [...EnvVars.string('MEDIA_HOST').split(','), 'blob:', '*'],
    scriptSrc: [
      '*.google.com',
      'cdn.polyfill.io',
      "'unsafe-eval'",
      '*.youtube.com',
      's.ytimg.com',
      'sentry.digisac.app',
      'https://connect.facebook.net/en_US/sdk.js',
      'dev.visualwebsiteoptimizer.com',
      '*.userguiding.com',
      'gtm.js',
      '*.hotjar.com',
      'us-js.zonka.co',
      '*.announcekit.app',
      'announcekit.co',
      '*.kompassify.com',
      '*.kompassify.app',
    ],
    styleSrc: ['fonts.googleapis.com', '*.google.com'],
    frameSrc: [...EnvVars.string('MEDIA_HOST').split(','), 'blob:', '*'],
  },
  publicAssetsPath: currentWhiteLabel.publicAssetsPath,
  buildOutputPath: './dist',
  includeSourceMapsForOptimisedClientBundle: false,
  bundleSrcTypes: ['js', 'jsx', 'json', 'ts', 'tsx'],
  bundleAssetsFileName: 'assets.json',
  nodeExternalsFileTypeWhitelist: [
    /\.(eot|woff|woff2|ttf|otf)$/,
    /\.(svg|png|jpg|jpeg|gif|ico)$/,
    /\.(mp4|mp3|ogg|swf|webp)$/,
    /\.(css|scss|sass|sss|less)$/,
  ],
  serviceWorker: {
    enabled: false,
    fileName: 'sw.js',
    includePublicAssets: ['./**/*'],
    offlinePageFileName: 'offline.html',
  },

  bundles: {
    client: {
      srcEntryFile: './src/client/index.js',
      srcPaths: ['./src', './internal'],
      outputPath: './dist/client',
      webPath: '/client/',
      devVendorDLL: {
        enabled: true,
        include: getDevVendorDllInclude(),
        name: '__dev_vendor_dll__',
      },
    },

    server: {
      srcEntryFile: './src/server/index.js',
      srcPaths: ['./src', './internal'],
      outputPath: './dist/server',
      webPath: '/server/',
    },
  },
  plugins: {
    babelConfig: (babelConfig, buildOptions) => {
      const { target, mode } = buildOptions

      return babelConfig
    },
    webpackConfig: (webpackConfig, buildOptions) => {
      const { target, mode } = buildOptions

      // webpackConfig.plugins.push(new BundleAnalyzerPlugin({ analyzerMode: 'static' }))
      webpackConfig.plugins.push(new ContextReplacementPlugin(/moment[/\\]locale$/, /pt-BR/))

      return webpackConfig
    },
  },
  creditPurchaseUrl: EnvVars.string('CREDIT_PURCHASE_URL', ''),
}

if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
  throw new Error(
    "You shouldn't be importing the `/config/values.js` directly into code that will be included in your 'client' bundle as the configuration object will be sent to user's browsers. This could be a security risk! Instead, use the `config` helper function located at `/config/index.js`.",
  )
}

export default values
