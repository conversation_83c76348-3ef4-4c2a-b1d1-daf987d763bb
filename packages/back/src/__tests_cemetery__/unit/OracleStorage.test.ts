import { S3 } from 'aws-sdk'
import OracleStorage from '../../core/services/storage/OracleStorage' // Adjust the import path as necessary
import { Container } from 'typedi'
import Config from '../../core/services/config/Config'
import Logger from '../../core/services/logs/Logger'
import retry from '../../core/utils/retry'

jest.mock('aws-sdk', () => {
  const mockS3Instance = {
    upload: jest.fn().mockReturnThis(),
    promise: jest.fn(),
  }
  return {
    S3: jest.fn(() => mockS3Instance),
  }
})

jest.mock('../../../../../core/utils/retry', () => {
  return jest.fn().mockImplementation(jest.requireActual('../../../../../core/utils/retry'))
})

describe('OracleStorage', () => {
  let oracleStorage: OracleStorage
  let mockS3: jest.Mocked<S3>
  let mockConfig: jest.Mocked<Config>
  let mockLogger: jest.Mocked<Logger>

  beforeEach(() => {
    jest.clearAllMocks()

    mockConfig = {
      get: jest.fn((key: string) => {
        switch (key) {
          case 'oracleEndpoint':
            return 'http://localhost:4566'
          case 'oracleRegion':
            return 'us-east-1'
          case 'oracleAccessKeyId':
            return 'test-access-key-id'
          case 'oracleSecretAccessKey':
            return 'test-secret-access-key'
          case 'oracleBucketName':
            return 'primary-bucket'
          case 'oracleBucketNameFallback':
            return 'fallback-bucket'
          default:
            return null
        }
      }),
    } as any

    mockLogger = {
      log: jest.fn(),
    } as any

    oracleStorage = new OracleStorage(mockConfig)

    mockS3 = new S3() as any
  })

  describe('write method', () => {
    it('should successfully upload data to the primary bucket', async () => {
      const filename = 'test.txt'
      const data = 'Hello, world!'
      const uploadResponse = { Location: 'http://localhost:4566/primary-bucket/test.txt' }

      mockS3.upload().promise.mockResolvedValue(uploadResponse)
      ;(retry as jest.Mock).mockImplementation((operation) => operation())

      const result = await oracleStorage.write(filename, data)

      expect(result).toEqual(uploadResponse)
      expect(mockS3.upload).toHaveBeenCalledWith({ Body: data, Key: filename })
      expect(mockS3.promise).toHaveBeenCalledTimes(1)
    })

    it('should retry with the fallback bucket on initial failure', async () => {
      const filename = 'retry.txt'
      const data = 'Retry this upload'
      const temporaryError = new Error('Temporary S3 Error')
      const successResponse = { Location: 'http://localhost:4566/fallback-bucket/retry.txt' }

      mockS3.upload().promise.mockRejectedValueOnce(temporaryError).mockResolvedValueOnce(successResponse)
      ;(retry as jest.Mock).mockImplementation(async (operation) => {
        try {
          return await operation(0)
        } catch {
          return await operation(1)
        }
      })

      const result = await oracleStorage.write(filename, data)
      expect(result).toEqual(successResponse)
      expect(mockS3.upload).toHaveBeenCalledTimes(3)
      expect(mockS3.promise).toHaveBeenCalledTimes(2)
    })
  })
})
