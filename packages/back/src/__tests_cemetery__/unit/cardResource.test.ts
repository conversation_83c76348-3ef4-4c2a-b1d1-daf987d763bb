import cardResource from '../../core/resources/cardResource'
import cardRepository from '../../core/dbSequelize/repositories/cardRepository'

jest.mock('../../../../core/dbSequelize/repositories/cardProductRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/cardCommentRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/cardMovementRepository', () => jest.fn())
jest.mock('../../../../core/resources/BaseResource', () => jest.fn())
jest.mock('../../../../core/resources/cardMovementResource', () => jest.fn())
jest.mock('../../../../core/resources/contactResource', () => jest.fn())
jest.mock('../../../../core/resources/pipelineResource', () => ({
  findById: jest.fn(),
  emit: jest.fn(),
  update: jest.fn(),
}))
jest.mock('../../../../core/services/pipeline/PipelineService', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => ({ define: jest.fn() }))

jest.mock('../../../../core/services/logs/Logger', () => {
  return {
    __esModule: true,
    default: {
      log: jest.fn(),
    },
  }
})

jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../../core/services/logs/Logger').default) {
          return { log: jest.fn() }
        }
        return {}
      }),
    },
    Service: () => () => {},
    Inject: () => () => {},
  }
})

jest.mock('../../../../core/dbSequelize/repositories/cardRepository', () => ({
  findById: () => ({ id: 2, order: 2, pipelineId: 2, pipelineStageId: 2, accountId: 2 }),
  findOne: () => ({ id: 1, order: 1, pipelineId: 1, pipelineStageId: 1, accountId: 1 }),
  update: jest.fn(),
  updateOrder: jest.fn(),
}))

describe('cardResource', () => {
  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should update card order', async () => {
    const mockCard = { id: 1, order: 1, pipelineId: 1, pipelineStageId: 1, accountId: 1 }
    jest.spyOn(cardRepository, 'findById').mockResolvedValue(mockCard)

    await cardResource.updateOrder('1', { order: 1 })

    expect(cardRepository.update).toHaveBeenCalledWith(mockCard, { order: 1 })
    expect(cardRepository.updateOrder).toHaveBeenCalledWith(1, 1, 1, 1, 1)
  })
})
