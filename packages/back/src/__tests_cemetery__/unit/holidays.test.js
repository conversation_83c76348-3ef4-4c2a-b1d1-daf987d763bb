import { validateCreate, validateUpdate } from '../../microServices/api/routes/holiday'

jest.mock('../../../core/dbSequelize/repositories/holidayRepository', () => ({
  existsById: (id) => Promise.resolve(!!id),
}))
jest.mock('../../../core/resources/BaseResource', () => jest.fn())

const mockRes = { locals: { user: {} } }

describe('holiday', () => {
  describe('validation', () => {
    // create
    describe('create', () => {
      it('test rules with empty input', async () => {
        const mockReq = { body: {} }
        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['required'],
            }),
            from: expect.objectContaining({
              types: ['required'],
            }),
            to: expect.objectContaining({
              types: ['required'],
            }),
            message: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with invalid types (name, from, to, message)', async () => {
        const mockReq = {
          body: {
            name: 2,
            from: 15,
            to: 16,
            message: 3,
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['string'],
            }),
            from: expect.objectContaining({
              types: ['date'],
            }),
            to: expect.objectContaining({
              types: ['date'],
            }),
            message: expect.objectContaining({
              types: ['string'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with text overflow of 300 characters', async () => {
        const mockReq = {
          body: {
            name: 'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
            message:
              'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
            from: expect.objectContaining({
              types: ['required'],
            }),
            to: expect.objectContaining({
              types: ['required'],
            }),
            message: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            name: 'A valid name',
            from: 'Wed May 04 2022 17:31:06 GMT-0300 (Horário Padrão de Brasília)',
            to: 'Wed May 04 2022 17:31:06 GMT-0300 (Horário Padrão de Brasília)',
            message: 'A valid message',
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        expect(mockNext).toBeCalledWith()
      })
    })

    // update
    describe('update', () => {
      it('test rules with empty input', async () => {
        const mockReq = { body: {} }
        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['required'],
            }),
            from: expect.objectContaining({
              types: ['required'],
            }),
            to: expect.objectContaining({
              types: ['required'],
            }),
            message: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with invalid types (name, from, to, message)', async () => {
        const mockReq = {
          body: {
            name: 2,
            from: 15,
            to: 16,
            message: 3,
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['string'],
            }),
            from: expect.objectContaining({
              types: ['date'],
            }),
            to: expect.objectContaining({
              types: ['date'],
            }),
            message: expect.objectContaining({
              types: ['string'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with text overflow of 300 characters', async () => {
        const mockReq = {
          body: {
            name: 'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
            message:
              'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
            from: expect.objectContaining({
              types: ['required'],
            }),
            to: expect.objectContaining({
              types: ['required'],
            }),
            message: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            name: 'A valid name',
            from: 'Wed May 04 2022 17:31:06 GMT-0300 (Horário Padrão de Brasília)',
            to: 'Wed May 04 2022 17:31:06 GMT-0300 (Horário Padrão de Brasília)',
            message: 'A valid message',
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        expect(mockNext).toBeCalledWith()
      })
    })
  })
})
