import { validateCreate, validateUpdate } from '../../microServices/api/routes/departments'

jest.mock('../../../core/dbSequelize/repositories/serviceRepository', () => ({
  existsById: (id) => Promise.resolve(!!id),
}))

const mockRes = { locals: { user: {} } }

describe('department', () => {
  describe('validation', () => {
    describe('create', () => {
      it('test rules with empty input', async () => {
        const mockReq = {}
        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })
    })

    describe('update', () => {
      it('test rules with empty input', async () => {
        const mockReq = {}
        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })
    })
  })
})
