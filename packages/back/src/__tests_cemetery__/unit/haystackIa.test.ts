import messageResource from '../../core/resources/messageResource'
import HaystackIaApi from '../../core/services/haystackIa'
import Axios from 'axios'
import config from '../../core/configValues'

jest.mock('../../../../core/resources/messageResource', () => ({
  __esModule: true,
  default: {
    getMessagesForSummary: jest.fn().mockResolvedValue([]),
    updateById: jest.fn(),
    findById: jest.fn(),
    decryptMessageText: jest.fn(),
    findMany: jest.fn(),
  },
}))

jest.mock('axios')
jest.mock('../../../../core/configValues', () => ({
  haystackIa: {
    username: 'test-username',
    password: 'test-password',
    url: 'http://test-url',
  },
}))

jest.mock('../../../../core/resources/creditMovementResource', () => ({
  __esModule: true, // Garante que o Jest está lidando corretamente com módulos ES
  default: {
    createDebit: jest.fn(),
  },
}))

describe('HaystackIaApi', () => {
  let haystackIaApi: HaystackIaApi

  beforeEach(() => {
    haystackIaApi = new HaystackIaApi()
  })

  afterEach(() => {
    jest.clearAllMocks()
    Axios.post.mockReset()
    Axios.delete.mockReset()
  })

  test('getBearerToken should return token', async () => {
    const mockToken = 'test-token'
    Axios.post.mockResolvedValue({ data: { access_token: mockToken } })

    const token = await haystackIaApi.getBearerToken()
    expect(token).toBe(mockToken)
    expect(Axios.post).toHaveBeenCalledWith(`${config.haystackIa.url}/v1/auth/token`, expect.any(String))
  })

  test('getBearerToken should handle expired token', async () => {
    Axios.post.mockRejectedValue({ response: { status: 401, data: { message: 'Token expired' } } })
    await expect(haystackIaApi.getBearerToken()).rejects.toThrow('Token expired')
  })

  test('getConfig should return config with token', async () => {
    const mockToken = 'test-token'
    jest.spyOn(haystackIaApi, 'getBearerToken').mockResolvedValue(mockToken)

    const config = await haystackIaApi.getConfig()
    expect(config).toEqual({
      headers: {
        Authorization: `Bearer ${mockToken}`,
        'Content-Type': 'application/json',
      },
    })
  })

  test('createKnowledge should return docIds and sourceId', async () => {
    const mockResponse = {
      data: {
        details: {
          message: {
            doc_ids: ['doc1', 'doc2'],
            source_id: 'source1',
          },
        },
      },
    }
    Axios.post.mockResolvedValue(mockResponse)

    const result = await haystackIaApi.createKnowledge('source', { accountId: 'account1', type: 'messages' })
    expect(result).toEqual({ docIds: ['doc1', 'doc2'], sourceId: 'source1' })
  })

  test('createKnowledge should throw error on failure', async () => {
    Axios.post.mockRejectedValue(new Error('Create Error'))

    await expect(haystackIaApi.createKnowledge('source', { accountId: 'account1', type: 'messages' })).rejects.toThrow(
      'Failed to create knowledge: Failed to get config: Failed to get bearer token: Create Error',
    )
  })

  test('findKnowledge should return documents', async () => {
    const mockResponse = {
      data: [{ id: 'doc1', content: 'content1', meta: {}, blob: {} }],
    }
    Axios.post.mockResolvedValue(mockResponse)

    const result = await haystackIaApi.findKnowledge(['doc1'], 'account1')
    expect(result).toEqual([{ id: 'doc1', content: 'content1', meta: {}, blob: {} }])
  })

  test('findKnowledge should throw error on failure', async () => {
    Axios.post.mockRejectedValue(new Error('Find Error'))

    await expect(haystackIaApi.findKnowledge(['doc1'], 'account1')).rejects.toThrow(
      'Failed to search for knowledge: Failed to get config: Failed to get bearer token: Find Error',
    )
  })

  test('deleteKnowledgeDocs should delete documents', async () => {
    const mockResponse = { data: { message: 'success' } }
    Axios.delete.mockResolvedValue(mockResponse)
    Axios.post.mockResolvedValue({})

    await haystackIaApi.deleteKnowledgeDocs(['doc1'], 'account1')
    expect(Axios.delete).toHaveBeenCalledWith(
      `${config.haystackIa.url}/v1/vectorstore/collections/account1/documents/delete-messages`,
      expect.objectContaining({ data: { document_ids: ['doc1'] } }),
    )
  })

  test('deleteKnowledgeDocs should return empty value', async () => {
    const mockResponse = { data: {} }
    Axios.delete.mockResolvedValue(mockResponse)
    Axios.post.mockResolvedValue({})

    await expect(haystackIaApi.deleteKnowledgeDocs(['doc1'], 'account1')).rejects.toThrow(
      'Failed to delete documents of knowledge: Failed to delete documents of knowledge. DocumentIds: doc1',
    )
  })

  test('deleteKnowledgeDocs should throw error on failure', async () => {
    Axios.delete.mockRejectedValue(new Error('Delete Error'))
    Axios.post.mockResolvedValue({})

    await expect(haystackIaApi.deleteKnowledgeDocs(['doc1'], 'account1')).rejects.toThrow(
      'Failed to delete documents of knowledge: Delete Error',
    )
  })

  test('suggestResponseByMessage should return message', async () => {
    const mockResponse = { data: { message: 'response message' } }
    Axios.post.mockResolvedValue(mockResponse)
    messageResource.findById.mockResolvedValue({ dataValues: { text: 'test message' } })
    messageResource.decryptMessageText.mockResolvedValue('decrypted message')

    const result = await haystackIaApi.suggestResponseByMessage('message1', 'account1')
    expect(result).toEqual({ message: 'response message' })
  })

  test('suggestResponseByMessage should throw error on failure', async () => {
    Axios.post.mockRejectedValue(new Error('Suggest Error'))

    await expect(haystackIaApi.suggestResponseByMessage('message1', 'account1')).rejects.toThrow(
      'Failed to suggest a response: Failed to get config: Failed to get bearer token: Suggest Error',
    )
  })

  test('suggestResponseByMessage should return empty message', async () => {
    Axios.post.mockResolvedValue({ data: {} })
    messageResource.findById.mockResolvedValue({ dataValues: { text: 'test message' } })
    messageResource.decryptMessageText.mockResolvedValue('decrypted message')

    await expect(haystackIaApi.suggestResponseByMessage('message1', 'account1')).rejects.toThrow(
      'Failed to suggest a response: Failed to suggest a response. MessageId: message1',
    )
  })

  test('suggestResponseByTicket should return message', async () => {
    const mockResponse = { data: { message: 'response message' } }
    Axios.post.mockResolvedValue(mockResponse)
    messageResource.findMany.mockResolvedValue([{ dataValues: { text: 'test message' } }])
    messageResource.decryptMessageText.mockResolvedValue('decrypted message')

    const result = await haystackIaApi.suggestResponseByTicket('ticket1', 'account1')
    expect(result).toEqual({ message: 'response message' })
  })

  test('suggestResponseByTicket should return empty message', async () => {
    Axios.post.mockResolvedValue({ data: {} })
    messageResource.findMany.mockResolvedValue([{ text: 'test message' }])
    messageResource.decryptMessageText.mockResolvedValue('decrypted message')

    await expect(haystackIaApi.suggestResponseByTicket('ticket1', 'account1')).rejects.toThrow(
      'Failed to suggest a response: Failed to suggest a response. ticketId: ticket1',
    )
  })

  test('suggestResponseByTicket should throw error on failure', async () => {
    Axios.post.mockRejectedValue(new Error('Suggest Error'))

    await expect(haystackIaApi.suggestResponseByTicket('ticket1', 'account1')).rejects.toThrow(
      'Failed to suggest a response: Failed to get config: Failed to get bearer token: Suggest Error',
    )
  })

  test('should return FormData for urls', () => {
    const source = 'http://example.com'
    const data = { type: 'urls', name: 'example', accountId: 'account1' }
    const payload = haystackIaApi.getPayload(source, data)
    expect(payload.getBuffer().toString()).toContain('http://example.com')
    expect(payload.getBuffer().toString()).toContain('example')
  })

  test('should return FormData for files', () => {
    const source = Buffer.from('file content')
    const data = { type: 'files', name: 'example.txt', accountId: 'account1' }
    const payload = haystackIaApi.getPayload(source, data)
    expect(payload.getBuffer().toString()).toContain('file content')
    expect(payload.getBuffer().toString()).toContain('example.txt')
  })

  test('should return Request for messages', () => {
    const source = 'message content'
    const data = { type: 'messages', name: 'example', accountId: 'account1' }
    const payload = haystackIaApi.getPayload(source, data)
    expect(payload).toEqual({
      documents: [{ text: 'message content', meta: { name: 'example', data: expect.any(Date) } }],
    })
  })

  test('should throw error for unknown type', () => {
    const source = 'unknown content'
    const data = { type: 'unknown', name: 'example', accountId: 'account1' }
    expect(() => haystackIaApi.getPayload(source, data)).toThrow('Unknown type: unknown')
  })
})
