import {
  dedupeInclude,
  expandInclude,
  parseInclude,
  prepareInclude,
} from '../../core/dbSequelize/repositories/BaseSequelizeRepository'
import models from '../../core/dbSequelize/models'
import { initAssociations } from '../../core/services/db/setupSequelize'

initAssociations()

test('parseInclude singular', () => {
  const include = {
    model: 'service',
  }

  const actual = parseInclude(models.Contact, include)
  const expected = {
    model: models.Service,
    as: 'service',
  }

  expect(actual).toMatchObject(expected)
})

test('parseInclude plural', () => {
  const include = {
    model: 'tags',
  }

  const actual = parseInclude(models.Contact, include)
  const expected = {
    model: models.Tag,
    as: 'tags',
  }

  expect(actual).toMatchObject(expected)
})

test('parseInclude string', () => {
  const include = 'service'

  const actual = parseInclude(models.Contact, include)
  const expected = {
    model: models.Service,
    as: 'service',
  }

  expect(actual).toMatchObject(expected)
})

test('parseInclude nested with string', () => {
  const include = {
    model: 'service',
    include: ['account'],
  }

  const actual = parseInclude(models.Contact, include)
  const expected = {
    model: models.Service,
    as: 'service',
    include: [
      {
        model: models.Account,
        as: 'account',
      },
    ],
  }

  expect(actual).toMatchObject(expected)
})

test('parseInclude nested with dot string', () => {
  const include = 'messages.file'

  const actual = parseInclude(models.Campaign, include)
  const expected = {
    model: models.CampaignMessage,
    as: 'messages',
    include: [
      {
        model: models.File,
        as: 'file',
      },
    ],
  }

  expect(actual).toMatchObject(expected)
})

test('parseInclude nested with dot string 3 levels', () => {
  const include = 'user.account.cluster'

  const actual = parseInclude(models.OAuthAccessToken, include)
  const expected = {
    model: models.User,
    as: 'user',
    include: [
      {
        model: models.Account,
        as: 'account',
        include: [
          {
            model: models.Cluster,
            as: 'cluster',
          },
        ],
      },
    ],
  }

  expect(actual).toMatchObject(expected)
})

test('expandInclude nested', () => {
  const rawInclude = ['ticket', 'ticket.department', 'ticket.user']

  const expandedInclude = expandInclude(rawInclude)

  expect(expandedInclude).toMatchInlineSnapshot(`
    Array [
      Object {
        "as": "ticket",
        "model": "ticket",
      },
      Object {
        "as": "ticket",
        "include": Array [
          Object {
            "as": "department",
            "model": "department",
          },
        ],
        "model": "ticket",
      },
      Object {
        "as": "ticket",
        "include": Array [
          Object {
            "as": "user",
            "model": "user",
          },
        ],
        "model": "ticket",
      },
    ]
  `)
})

test('prepareInclude nested', () => {
  const rawInclude = ['ticket', 'ticket.department', 'ticket.user']

  const include = prepareInclude(rawInclude)

  expect(include).toMatchInlineSnapshot(`
    Array [
      Object {
        "as": "ticket",
        "include": Array [
          Object {
            "as": "department",
            "model": "department",
          },
          Object {
            "as": "user",
            "model": "user",
          },
        ],
        "model": "ticket",
      },
    ]
  `)
})
