import pipelineResource from '../../core/resources/pipelineResource'
import pipelineAutomations from '../../core/dbSequelize/repositories/pipelineAutomationsRepository'

jest.mock('../../../../core/services/logs/Logger', () => jest.fn())
jest.mock('../../../../core/services/config/Config', () => jest.fn())
jest.mock('../../../../core/config', () => jest.fn())

jest.mock('typedi', () => {
  const mockOracleStorage = {
    setBucket: (key) => jest.fn(),
  }

  const mockConfig = {
    get: jest.fn((key) => {
      if (key === 'buckets') return 'bucket1,bucket2'
    }),
  }

  return {
    __esModule: true,
    default: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),
        captureError: jest.fn(),
        dispatch: jest.fn(),
      }),
    },
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../../core/services/config/Config')) return mockConfig
        else if (service === require('../../../../core/services/storage/OracleStorage').default)
          return mockOracleStorage
        return {}
      }),
    },
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

jest.mock('../../../../core/services/db/sequelize', () => ({
  __esModule: true,
  default: {
    define: jest.fn(() => ({
      prototype: {
        hasPermission: jest.fn(),
      },
      associate: jest.fn(),
      belongsTo: jest.fn(),
    })),
  },
}))

jest.mock('../../../../core/dbSequelize/repositories/pipelineRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineStageRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineStageStatusRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineNotificationsRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineAutomationsRepository', () => {
  return {
    create: jest.fn((body) => body),
    update: jest.fn((id, body) => ({ id, ...body })),
    destroy: jest.fn((id) => ({ id, deleted: true })),
    findById: jest.fn((id) => ({ id })),
  }
})
jest.mock('../../../../core/dbSequelize/repositories/pipelineNotificationsRepository', () => jest.fn())
jest.mock('../../../../core/resources/departmentResource', () => jest.fn())
jest.mock('../../../../core/resources/cardResource', () => jest.fn())
jest.mock('../../../../core/resources/BaseResource', () => jest.fn())

describe('pipelineResouce', () => {
  it('should show correct values for potential revenue', async () => {
    jest.resetModules()

    jest.mock('../../../../core/resources/pipelineResource', () => ({
      __esModule: true,
      default: {
        findManyWithTotals: jest.fn(async () => [
          {
            id: 'pipeline-1',
            name: 'Pipeline 1',
            totals: {
              total: 100,
              totalSuccess: 200,
              totalFailed: 300,
            },
          },
          {
            id: 'pipeline-2',
            name: 'Pipeline 2',
            totals: {
              total: 50,
              totalSuccess: 90,
              totalFailed: 120,
            },
          },
        ]),
        createAutomation: jest.fn(),
        updateAutomation: jest.fn(),
        destroyAutomation: jest.fn(),
      },
    }))

    const { default: pipelineResource } = await import('../../core/resources/pipelineResource')

    const result = await pipelineResource.findManyWithTotals({})

    expect(result).toEqual([
      {
        id: 'pipeline-1',
        name: 'Pipeline 1',
        totals: {
          total: 100,
          totalSuccess: 200,
          totalFailed: 300,
        },
      },
      {
        id: 'pipeline-2',
        name: 'Pipeline 2',
        totals: {
          total: 50,
          totalSuccess: 90,
          totalFailed: 120,
        },
      },
    ])
  })

  it('should create automation successfully with valid input', async () => {
    const body = {
      pipelineId: 'd6aab144-7983-4a63-9065-4a2ca2044cbc',
      type: 'notification',
    }
    return await pipelineResource.createAutomation(body).then((result) => {
      expect(result).toEqual(body)
      expect(pipelineAutomations.create).toHaveBeenCalled()
    })
  })

  it('should update automation successfully with valid input', async () => {
    const id = 'd6aab144-7983-4a63-9065-4a2ca2044cbc'
    const body = {
      id,
      type: 'updated-notification',
    }
    return pipelineResource.updateAutomation(id, body).then((result) => {
      expect(result).toEqual(body)
      expect(pipelineAutomations.update).toHaveBeenCalled()
    })
  })

  it('should destroy automation successfully with valid input', async () => {
    const id = 'd6aab144-7983-4a63-9065-4a2ca2044cbc'
    return pipelineResource.destroyAutomation(id).then((result) => {
      expect(pipelineAutomations.destroy).toHaveBeenCalled()
    })
  })
})
