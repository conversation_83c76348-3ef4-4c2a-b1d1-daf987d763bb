import { v4 as uuid } from 'uuid'
import { createRules } from '../../microServices/api/routes/messages'
import validate from '../../core/middlewares/validate'

jest.mock('../../../core/dbSequelize/repositories/contactRepository', () => ({
  existsById: () => Promise.resolve(true),
}))

const toObj = (err) =>
  err && {
    status: err.status,
    message: err.message,
    errors: err.errors,
  }

describe('messages', () => {
  describe('validation', () => {
    describe('create', () => {
      const mockRes = { locals: { user: {} } }

      it('test rules with empty input', async () => {
        const mockReq = { body: {} }
        const mockNext = jest.fn()
        await validate(createRules)(mockReq, mockRes, (error) => mockNext(toObj(error)))

        const errors = {
          body: {
            contactId: expect.objectContaining({
              types: ['requiredIfOtherNotPresent'],
            }),
            text: expect.objectContaining({
              types: ['requiredIfOtherNotPresent'],
            }),
            file: expect.objectContaining({
              types: ['requiredIfOtherNotPresent'],
            }),
          },
        }
        expect(mockNext).toHaveBeenCalledWith({
          status: 400,
          message: 'The given data was invalid.',
          errors,
        })
      })

      it('test rules with text without contactId', async () => {
        const mockReq = {
          body: {
            text: 'test message',
          },
        }
        const mockNext = jest.fn()
        await validate(createRules)(mockReq, mockRes, (error) => mockNext(toObj(error)))

        const errors = {
          body: {
            contactId: expect.objectContaining({
              types: ['requiredIfOtherNotPresent'],
            }),
          },
        }
        expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({ errors }))
      })

      it('test rules with text and contactId', async () => {
        const mockReq = {
          body: {
            text: 'test message here',
            contactId: uuid(),
          },
        }
        const mockNext = jest.fn()
        await validate(createRules)(mockReq, mockRes, (error) => mockNext(toObj(error)))

        expect(mockNext).toHaveBeenCalledWith(undefined)
      })

      it('test rules with file', async () => {
        const mockReq = {
          body: {
            file: {
              base64: 'base64here',
            },
          },
        }
        const mockNext = jest.fn()
        await validate(createRules)(mockReq, mockRes, (error) => mockNext(toObj(error)))

        const errors = {
          body: {
            contactId: expect.objectContaining({
              types: ['requiredIfOtherNotPresent'],
            }),
          },
        }
        expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({ errors }))
      })
    })
  })
})
