import botRealFlowTransformer, { FlowConfig } from '../../core/utils/bot/botRealFlowTransformer'

describe('BotRealFlowTransformer', () => {
  const menuText = 'Seleciona uma das opções abaixo: \n 1 - Financeiro\n2 - Comercial\n 3 - Outros\nVoltar'
  const menuOption1Text = 'Estou te direcionando para o departamento Financeiro!'
  const menuOption2Text = 'Estou te direcionando para o departamento Comercial!'
  const menuOption3Text = 'Estou te direcionando para um especialista!'

  const expected = {
    '@INIT': {
      name: 'Contexto inicial',
      triggers: {
        MESSAGE_RECEIVED: [
          {
            title: '',
            actions: [
              {
                data: {
                  text: 'Bem vindo a digisac',
                },
                name: 'Enviar mensagem',
                value: 'SEND_MESSAGE',
              },
              {
                data: {
                  context: '361b1f71-691b-40e5-9094-419e4aa9d5ff',
                },
                name: 'Definir contexto',
                value: 'SET_CONTEXT',
              },
            ],
            conditions: [],
          },
        ],
      },
    },
    '@EVERY': {
      name: 'Contexto persistente',
      triggers: {},
    },
    '@FALLBACK': {
      name: 'Contexto de contingência',
      triggers: {},
    },
    '361b1f71-691b-40e5-9094-419e4aa9d5ff': {
      name: 'Menu',
      triggers: {
        ENTER_CONTEXT: [
          {
            title: '',
            actions: [
              {
                data: {
                  text: menuText,
                },
                name: 'Enviar mensagem',
                value: 'SEND_MESSAGE',
              },
            ],
            conditions: [],
          },
        ],
        MESSAGE_RECEIVED: [
          {
            title: '',
            actions: [
              {
                data: {
                  text: menuOption1Text,
                },
                name: 'Enviar mensagem',
                value: 'SEND_MESSAGE',
              },
            ],
            conditions: [
              {
                '{{message_text}}': {
                  $eq: '1',
                },
              },
            ],
          },
          {
            title: '',
            actions: [
              {
                data: {
                  text: menuOption2Text,
                },
                name: 'Enviar mensagem',
                value: 'SEND_MESSAGE',
              },
            ],
            conditions: [
              {
                '{{message_text}}': {
                  $eq: '2',
                },
              },
            ],
          },
          {
            title: '',
            actions: [
              {
                data: {
                  text: menuOption3Text,
                },
                name: 'Enviar mensagem',
                value: 'SEND_MESSAGE',
              },
            ],
            conditions: [
              {
                '{{message_text}}': {
                  $eq: '3',
                },
              },
            ],
          },
        ],
      },
    },
  }

  const initialNodes = [
    {
      id: 'init-0',
      type: 'triggerNode',
      position: { x: -100, y: 0 },
      data: {
        trigger: 'MESSAGE_RECEIVED',
      },
    },
    {
      id: 'node-0',
      type: 'menuNode',
      position: { x: 0, y: -200 },
      data: {
        text: 'Bem vindo a digisac',
      },
    },
    {
      id: 'node-1',
      type: 'menuNode',
      position: { x: 0, y: 0 },
      data: {
        text: menuText,
        answers: [
          { id: 'answer-1', text: '1' },
          { id: 'answer-2', text: '2' },
          { id: 'answer-3', text: '3' },
          { id: 'answer-fallback', text: 'Demais respostas', fallback: true },
        ],
      },
    },
    {
      id: 'node-2',
      type: 'menuNode',
      position: { x: 400, y: 100 },
      data: { text: menuOption1Text },
    },
    {
      id: 'node-3',
      type: 'menuNode',
      position: { x: 400, y: 250 },
      data: { text: menuOption2Text },
    },
    {
      id: 'node-4',
      type: 'menuNode',
      position: { x: 400, y: 400 },
      data: { text: menuOption3Text },
    },
    {
      id: 'node-5',
      type: 'menuNode',
      position: { x: 400, y: 550 },
      data: { text: 'Desculpe, não entendi' },
    },
  ]

  const initialEdges = [
    {
      id: 'edge-init-0',
      source: 'init-0',
      target: 'node-0',
      sourceHandle: 'self-0',
    },
    {
      id: 'edge-0',
      source: 'node-0',
      target: 'node-1',
      sourceHandle: 'self-0',
    },
    {
      id: 'edge-1',
      source: 'node-1',
      target: 'node-2',
      sourceHandle: 'answer-1',
    },
    {
      id: 'edge-2',
      source: 'node-1',
      target: 'node-3',
      sourceHandle: 'answer-2',
    },
    {
      id: 'edge-3',
      source: 'node-1',
      target: 'node-4',
      sourceHandle: 'answer-3',
    },
    {
      id: 'edge-4',
      source: 'node-1',
      target: 'node-5',
      sourceHandle: 'answer-fallback',
    },
    {
      id: 'edge-4',
      source: 'node-5',
      target: 'node-1',
      sourceHandle: 'self-0',
    },
  ]

  test('Should return correct values', () => {
    const config = { nodes: initialNodes, edges: initialEdges }
    const actual = botRealFlowTransformer(config as FlowConfig)

    console.log(JSON.stringify(actual, null, 2))

    expect(actual).toMatchObject(expected)
  })
})
