import { validateCreate, validateUpdate } from '../../microServices/api/routes/roles'

const mockRes = { locals: { user: {} } }

describe('roles', () => {
  describe('validation', () => {
    describe('create', () => {
      it('test rules with empty input', async () => {
        const mockReq = { body: {} }
        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            displayName: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with invalid type for all inputs', async () => {
        const mockReq = {
          body: {
            name: 1,
            displayName: 1,
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['string'],
            }),
            displayName: expect.objectContaining({
              types: ['string'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with text overflow of 300 characters for "name", "displayName"', async () => {
        const mockReq = {
          body: {
            name: 'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
            displayName:
              'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
            displayName: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            name: 'valid name',
            displayName: 'valid displayName',
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        expect(mockNext).toBeCalledWith()
      })
    })

    describe('update', () => {
      it('test rules with empty input', async () => {
        const mockReq = { body: {} }
        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            displayName: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with invalid type for all inputs', async () => {
        const mockReq = {
          body: {
            name: 1,
            displayName: 1,
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['string'],
            }),
            displayName: expect.objectContaining({
              types: ['string'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with text overflow of 300 characters for "name", "displayName"', async () => {
        const mockReq = {
          body: {
            name: 'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
            displayName:
              'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa',
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            name: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
            displayName: expect.objectContaining({
              types: ['hasLengthLesserThanOrEqual'],
            }),
          },
        }

        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            name: 'valid name',
            displayName: 'valid displayName',
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        expect(mockNext).toBeCalledWith()
      })
    })
  })
})
