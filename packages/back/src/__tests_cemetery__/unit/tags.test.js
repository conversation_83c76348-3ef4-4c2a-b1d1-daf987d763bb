// importa funções a serem testadas
import { validateCreate, validateUpdate } from '../../microServices/api/routes/tags'

const mockRes = { locals: { user: {} } }

describe('tags', () => {
  describe('validation', () => {
    // create
    describe('create', () => {
      // Teste de envio com corpo em branco
      // a fim de receber o erro de validação do label required.
      it('test rules with empty input', async () => {
        // objeto Request a ser validado
        const mockReq = { body: {} }

        // Declarando uma mock function do "next" para poder
        // saber se essa função é chamada ou não com os erros de validação.
        // @see https://facebook.github.io/jest/docs/en/mock-functions.html
        // Lembrando que o middleware chama o "next" (terceiro parâmetro passado ao middleware)
        // com o objeto do Erro quando uma validação falha.
        const mockNext = jest.fn()

        // Executa o middleware passando os paramentros Request, Response e uma function (a "next")
        await validateCreate(mockReq, mockRes, mockNext)

        // Os erros que espera que o middleware retorne
        const expectedErrors = {
          body: {
            label: expect.objectContaining({
              types: ['required'],
            }),
          },
        }

        // neste formato se acessa os parametros recebecidos pela mock function
        // [0][0] refere-se ao primeiro paramtro recebebido na primeira vez que a function é chamada
        // @see https://facebook.github.io/jest/docs/en/mock-functions.html#mock-property
        const actualErrors = mockNext.mock.calls[0][0].errors

        // A assertion, ta verificando se o erro passado para a function "next"
        // possui os erros que a gente esperava
        // @see https://facebook.github.io/jest/docs/en/expect.html
        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with invalid type for "label"', async () => {
        const mockReq = {
          body: {
            label: 1, // deveria ser uma string, vai ser passado um número
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            label: expect.objectContaining({
              // erro de validação esperado
              types: ['string'],
            }),
          },
        }
        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            label: 'Um label válido',
          },
        }

        const mockNext = jest.fn()

        await validateCreate(mockReq, mockRes, mockNext)

        // se o next foi chamado com undefined é porque não houve erros de validação
        expect(mockNext).toBeCalledWith()
      })
    })
    // create
    // update
    describe('update', () => {
      it('test rules with invalid type for "label"', async () => {
        const mockReq = {
          body: {
            label: 1,
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        const expectedErrors = {
          body: {
            label: expect.objectContaining({
              types: ['string'],
            }),
          },
        }
        const actualErrors = mockNext.mock.calls[0][0].errors

        expect(actualErrors).toEqual(expectedErrors)
      })

      it('test rules with valid input', async () => {
        const mockReq = {
          body: {
            label: 'Um label válido',
          },
        }

        const mockNext = jest.fn()

        await validateUpdate(mockReq, mockRes, mockNext)

        expect(mockNext).toBeCalledWith()
      })
    })
    // update
  })
})
