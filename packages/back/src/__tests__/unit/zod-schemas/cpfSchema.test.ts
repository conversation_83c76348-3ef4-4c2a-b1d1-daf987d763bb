import { ZodSchema } from 'zod'
import { cpfSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation CPF Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: cpfSchema,
  })

  it('should be able return success with a valid cpf', () => {
    const { sut } = makeSut()
    const validCpf = '56383091018'

    expect(sut.safeParse(validCpf).success).toBeTruthy()
  })

  it('should be able return error with an invalid cpf', () => {
    const { sut } = makeSut()
    const invalidCpf = '56383091011'

    expect(sut.safeParse(invalidCpf).success).toBeFalsy()
  })

  it('should be able return error with an invalid cpf (less than 11 chars)', () => {
    const { sut } = makeSut()
    const invalidCpf = '5638309101'

    expect(sut.safeParse(invalidCpf).success).toBeFalsy()
  })

  it('should be able return error with an invalid cpf (more than 11 chars)', () => {
    const { sut } = makeSut()
    const invalidCpf = '563830910111'

    expect(sut.safeParse(invalidCpf).success).toBeFalsy()
  })

  it('should be able return error with an invalid cpf (number)', () => {
    const { sut } = makeSut()
    const invalidCpf = 56383091011

    expect(sut.safeParse(invalidCpf).success).toBeFalsy()
  })
})
