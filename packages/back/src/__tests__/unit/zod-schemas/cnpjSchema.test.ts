import { ZodSchema } from 'zod'
import { cnpjSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation CNPJ Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: cnpjSchema,
  })

  it('should be able return success with a valid Cnpj', () => {
    const { sut } = makeSut()
    const validCnpj = '99666774000113'

    expect(sut.safeParse(validCnpj).success).toBeTruthy()
  })

  it('should be able return error with an invalid Cnpj', () => {
    const { sut } = makeSut()
    const invalidCnpj = '99666774000111'

    expect(sut.safeParse(invalidCnpj).success).toBeFalsy()
  })

  it('should be able return error with an invalid cpf (less than 14 chars)', () => {
    const { sut } = makeSut()
    const invalidCnpj = '9966677400011'

    expect(sut.safeParse(invalidCnpj).success).toBeFalsy()
  })

  it('should be able return error with an invalid cpf (more than 14 chars)', () => {
    const { sut } = makeSut()
    const invalidCnpj = '996667740001110'

    expect(sut.safeParse(invalidCnpj).success).toBeFalsy()
  })

  it('should be able return error with an invalid Cnpj (number)', () => {
    const { sut } = makeSut()
    const invalidCnpj = 99666774000111

    expect(sut.safeParse(invalidCnpj).success).toBeFalsy()
  })
})
