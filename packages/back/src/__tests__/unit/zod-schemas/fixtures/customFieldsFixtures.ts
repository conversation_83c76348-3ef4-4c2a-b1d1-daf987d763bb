type Range =
  | {
      start: string
      end: string
    }
  | string

export const cpfFixture = (required: boolean, value: string) => ({
  type: 'cpf',
  value,
  required,
})

export const cnpjFixture = (required: boolean, value: string) => ({
  type: 'cnpj',
  value,
  required,
})

export const dateRangeFixture = (required: boolean, value: Range) => ({
  type: 'date-range',
  value,
  required,
})

export const hourRangeFixture = (required: boolean, value: Range) => ({
  type: 'hour-range',
  value,
  required,
})
