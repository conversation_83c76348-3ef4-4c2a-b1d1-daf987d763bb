import { ZodSchema } from 'zod'
import { dateSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation DATE Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: dateSchema,
  })

  it('should be able return success with a valid YYYY-MM-DD', () => {
    const { sut } = makeSut()
    const validDate = '2025-01-01'

    expect(sut.safeParse(validDate).success).toBeTruthy()
  })

  it('should be able return error with a valid date but not right format', () => {
    const { sut } = makeSut()
    const invalidDate = new Date()

    expect(sut.safeParse(invalidDate).success).toBeFalsy()
  })

  it('should be able return error with an valid date but not right format (ISO)', () => {
    const { sut } = makeSut()
    const invalidDate = new Date().toISOString()

    expect(sut.safeParse(invalidDate).success).toBeFalsy()
  })

  it('should be able return error with an unix date (number)', () => {
    const { sut } = makeSut()
    const invalidDate = 1731354667
    expect(sut.safeParse(invalidDate).success).toBeFalsy()
  })
})
