import { ZodSchema } from 'zod'
import { hourRangeSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation HOUR INTERVAL Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: hourRangeSchema,
  })

  it('should be able return success with a valid interval hour', () => {
    const { sut } = makeSut()
    const validIntervalHour = {
      start: '20:00',
      end: '20:01',
    }

    expect(sut.safeParse(validIntervalHour).success).toBeTruthy()
  })

  it('should be able return error with an invalid interval hour (with HHhMM pattern)', () => {
    const { sut } = makeSut()
    const invalidIntervalHour = {
      start: '20h00',
      end: '21h00',
    }

    expect(sut.safeParse(invalidIntervalHour).success).toBeFalsy()
  })

  it('should be able return error with an invalid interval hour (with HH:MM:SS pattern)', () => {
    const { sut } = makeSut()
    const invalidIntervalHour = {
      start: '20:00:00',
      end: '20:00:01',
    }

    expect(sut.safeParse(invalidIntervalHour).success).toBeFalsy()
  })
})
