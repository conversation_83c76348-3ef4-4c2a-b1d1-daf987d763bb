import { ZodSchema } from 'zod'
import { hourSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation HOUR Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: hourSchema,
  })

  it('should be able return success with a valid hour', () => {
    const { sut } = makeSut()
    const validHour = '20:00'

    expect(sut.safeParse(validHour).success).toBeTruthy()
  })

  it('should be able return error with an invalid hour (with seconds)', () => {
    const { sut } = makeSut()
    const invalidHour = '20:00:01'

    expect(sut.safeParse(invalidHour).success).toBeFalsy()
  })

  it('should be able return error with an invalid hour (with HHhMM pattern)', () => {
    const { sut } = makeSut()
    const invalidHour = '20h00'

    expect(sut.safeParse(invalidHour).success).toBeFalsy()
  })
})
