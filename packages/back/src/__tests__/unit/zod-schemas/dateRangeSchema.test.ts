import { ZodSchema } from 'zod'
import { dateRangeSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation DATE INTERVAL Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: dateRangeSchema,
  })

  it('should be able return success with a valid interval date', () => {
    const { sut } = makeSut()
    const validIntervalHour = {
      start: '2024-01-01',
      end: '2024-01-02',
    }

    expect(sut.safeParse(validIntervalHour).success).toBeTruthy()
  })

  it('should be able return error with an invalid interval date (invalid interval - end less than start)', () => {
    const { sut } = makeSut()
    const invalidIntervalHour = {
      start: '2024-01-02',
      end: '2024-01-01',
    }

    expect(sut.safeParse(invalidIntervalHour).success).toBeFalsy()
  })
})
