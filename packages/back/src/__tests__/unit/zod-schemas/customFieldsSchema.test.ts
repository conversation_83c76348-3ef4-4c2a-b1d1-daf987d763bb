import { ZodSchema } from 'zod'
import { customFieldsSchema } from '../../../core/zod-schemas'
import { cpfFixture, cnpjFixture, dateRangeFixture, hourRangeFixture } from './fixtures'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation CUSTOM FIELDS Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: customFieldsSchema,
  })

  it('should be able return error value is required (CNPJ)', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(true, ''),
        dateRangeFixture(true, { start: '2024-01-01', end: '2024-01-02' }),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const lastIssue = result.error.issues[result.error.issues.length - 1].message
    expect(lastIssue).toBe('Value is required')
  })

  it('should be able return success if value is not required (CNPJ)', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(false, ''),
        dateRangeFixture(true, { start: '2024-01-01', end: '2024-01-02' }),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const isSuccess = result.success
    expect(isSuccess).toBeTruthy()
  })

  it('should be able return success if value is not required (Complex Objects like date-range)', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(true, '79952674000146'),
        dateRangeFixture(false, ''),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const isSuccess = result.success
    expect(isSuccess).toBeTruthy()
  })

  it('should be able return success if value is not required but was sent (CNPJ)', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(false, '79952674000146'),
        dateRangeFixture(true, { start: '2024-01-01', end: '2024-01-02' }),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const isSuccess = result.success
    expect(isSuccess).toBeTruthy()
  })

  it('should be able return success if value is not required but was sent (Complex Objects)', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(true, '79952674000146'),
        dateRangeFixture(false, { start: '2024-01-01', end: '2024-01-02' }),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const isSuccess = result.success
    expect(isSuccess).toBeTruthy()
  })

  it('should be able return error if value is Complex Object is required but value is empty string', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(true, '71753018048'),
        cnpjFixture(true, '79952674000146'),
        dateRangeFixture(true, ''),
        hourRangeFixture(true, { start: '20:00', end: '21:00' }),
      ],
    })

    const lastIssue = result.error.issues[result.error.issues.length - 1].message
    expect(lastIssue).toBe('Value is required')
  })

  it('should be able return success if all values is not required', () => {
    const { sut } = makeSut()

    const result = sut.safeParse({
      customFields: [
        cpfFixture(false, ''),
        cnpjFixture(false, ''),
        dateRangeFixture(false, ''),
        hourRangeFixture(false, ''),
      ],
    })

    const isSuccess = result.success
    expect(isSuccess).toBeTruthy()
  })
})
