import { ZodSchema } from 'zod'
import { shortTextSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation SHORT TEXT Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: shortTextSchema,
  })

  it('should be able return success with a valid short text', () => {
    const { sut } = makeSut()
    const validShortText = 'Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur'

    expect(sut.safeParse(validShortText).success).toBeTruthy()
  })

  it('should be able return error with an invalid short text (number)', () => {
    const { sut } = makeSut()
    const invalidShortText = 12345678

    expect(sut.safeParse(invalidShortText).success).toBeFalsy()
  })
})
