import { ZodSchema } from 'zod'
import { emailSchema } from '../../../core/zod-schemas'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation EMAIL Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: emailSchema,
  })

  it('should be able return success with a valid email', () => {
    const { sut } = makeSut()
    const validEmail = '<EMAIL>'

    expect(sut.safeParse(validEmail).success).toBeTruthy()
  })

  it('should be able return error with an invalid email (without Domain)', () => {
    const { sut } = makeSut()
    const invalidEmail = 'software-engineer@ikatec'

    expect(sut.safeParse(invalidEmail).success).toBeFalsy()
  })

  it('should be able return error with an invalid email (no @ symbol)', () => {
    const { sut } = makeSut()
    const invalidEmail = 'software-engineer#ikatec.com.br'

    expect(sut.safeParse(invalidEmail).success).toBeFalsy()
  })
})
