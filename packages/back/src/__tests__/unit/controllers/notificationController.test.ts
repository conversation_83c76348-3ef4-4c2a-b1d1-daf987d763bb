import { Request, Response } from 'express'

import NotificationController from '../../../microServices/api/controllers/NotificationController'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'

jest.mock('../../../core/resources/notificationResource', () => jest.fn())
jest.mock('../../../core/resources/notificationReadResource', () => jest.fn())
jest.mock('../../../core/utils/array/queuedAsyncMap', () => jest.fn())
jest.mock('../../../core/utils/hasPermission', () => jest.fn())
jest.mock('../../../core/services/notifications', () => jest.fn())

jest.mock('../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../core/dbSequelize/models/Notification', () => jest.fn())
jest.mock('../../../core/transformers/notificationTransformer', () => jest.fn())
jest.mock('../../../core/services/logs/reportError', () => jest.fn())
jest.mock('../../../core/services/logs/Logger', () => jest.fn())

jest.mock('typedi', () => {
  return {
    get: jest.fn().mockReturnValue({
      log: jest.fn(),
    }),
  }
})

describe('NotificationController', () => {
  let notificationController: NotificationController

  beforeEach(() => {
    jest.clearAllMocks()
    notificationController = new NotificationController()
  })

  afterEach(() => {
    // restore the spy created with spyOn
    jest.restoreAllMocks()
  })

  describe('sendAll', () => {
    it('should throw BadRequestHttpError when text is missing', async () => {
      const req = {
        body: {
          image: 'image.png',
          to: '',
        },
      } as unknown as Request

      const res = {
        locals: {
          accountId: '123',
        },
      } as unknown as Response

      await expect(notificationController.sendAll(req, res)).rejects.toThrow(
        new BadRequestHttpError('Erro ao criar notificações'),
      )
    })
  })

  describe('read', () => {
    it('should throw BadRequestHttpError where id is missing', async () => {
      const req = {
        params: {},
      } as unknown as Request

      const res = {
        locals: {
          accountId: '123',
        },
      } as unknown as Response

      await expect(notificationController.read(req, res)).rejects.toThrow(
        new BadRequestHttpError('NotificationId is required and must be a string.'),
      )
    })
  })
})
