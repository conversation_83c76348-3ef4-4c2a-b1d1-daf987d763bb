import * as BaseFunctions from '../../../../../../microServices/api/controllers/dashboard/service/overview/baseFunctions'
import {
  getOpenedTicketsCount,
  getClosedTicketsCount,
  getTicketTopicsCount,
  getSentMessagesCount,
  getReceivedMessagesCount,
  getWaitingTime,
  getTicketTime,
  getCountContacts,
} from '../../../../../../microServices/api/controllers/dashboard/service/overview/byDepartment'
import { dashboardOverviewTestCases } from './testCases'

jest.mock('../../../../../../core/services/db/sequelize', () => ({
  sequelize: jest.fn(),
}))

const doQuerySpy = jest.spyOn(BaseFunctions, 'doQuery').mockResolvedValue(0)

describe('Calls doQuery with correct parameters', () => {
  const defaultQuery = {
    accountId: '123',
    startPeriod: '123',
    endPeriod: '123',
  }

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getClosedTicketsCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getOpenedTicketsCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getCountContacts({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getTicketTopicsCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getSentMessagesCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getReceivedMessagesCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getWaitingTime({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name', ({ query, expected }) => {
    getTicketTime({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('', expect.stringContaining(expected))
  })
})
