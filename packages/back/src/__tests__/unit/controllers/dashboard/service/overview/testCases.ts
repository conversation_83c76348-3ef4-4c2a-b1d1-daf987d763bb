export const dashboardOverviewTestCases = [
  {
    name: 'with multiple department IDs and last department participation',
    query: { departmentId: ['234', '345', '456'], departmentParticipation: 'last' },
    expected: "AND t.\"departmentId\" IN ('234', '345', '456')",
  },
  {
    name: 'with a single department ID  last department participation',
    query: { departmentId: '123', departmentParticipation: 'last' },
    expected: 'AND t."departmentId" = \'123\'',
  },
  {
    name: 'with multiple department IDs and middle department participation',
    query: { departmentId: ['234', '345', '456'], departmentParticipation: 'middle' },
    expected: "AND tt.\"toDepartmentId\" IN ('234', '345', '456')",
  },
  {
    name: 'with a single department ID and middle department participation',
    query: { departmentId: '123', departmentParticipation: 'middle' },
    expected: 'AND tt."toDepartmentId" = \'123\'',
  },
  {
    name: 'with multiple user IDs and last participation',
    query: { userId: ['234', '345', '456'], userParticipation: 'last' },
    expected: "AND t.\"userId\" IN ('234', '345', '456')",
  },
  {
    name: 'with a single user ID and last participation',
    query: { userId: '123', userParticipation: 'last' },
    expected: 'AND t."userId" = \'123\'',
  },
  {
    name: 'with multiple user IDs and middle participation',
    query: { userId: ['234', '345', '456'], userParticipation: 'middle' },
    expected: "AND t.\"userId\" IN ('234', '345', '456')",
  },
  {
    name: 'with a single user ID and middle participation',
    query: { userId: '123', userParticipation: 'middle' },
    expected: 'AND t."userId" = \'123\'',
  },
]
