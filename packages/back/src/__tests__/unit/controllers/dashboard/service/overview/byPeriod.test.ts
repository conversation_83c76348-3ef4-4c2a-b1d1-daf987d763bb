import * as BaseFunctions from '../../../../../../microServices/api/controllers/dashboard/service/overview/baseFunctions'
import {
  getSentMessagesCount,
  getReceivedMessagesCount,
  getWaitingTime,
  getTicketTime,
  getWaitingTimeAfterBot,
  getWaitingTimeAvg,
  getContactCount,
  getMessagingTime,
} from '../../../../../../microServices/api/controllers/dashboard/service/overview/byPeriod'
import { dashboardOverviewTestCases } from './testCases'

jest.mock('../../../../../../core/services/db/sequelize', () => ({
  sequelize: jest.fn(),
}))

const doQuerySpy = jest.spyOn(BaseFunctions, 'doQuery2').mockResolvedValue([])

describe('Calls doQuery with correct parameters', () => {
  const defaultQuery = {
    accountId: '123',
    startPeriod: '123',
    endPeriod: '123',
  }

  it.each(dashboardOverviewTestCases)('$name in getContactCount', async ({ query, expected }) => {
    await getContactCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('count', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getWaitingTimeAfterBot', async ({ query, expected }) => {
    await getWaitingTimeAfterBot({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('avg', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getSentMessagesCount', async ({ query, expected }) => {
    await getSentMessagesCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('count', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getReceivedMessagesCount', async ({ query, expected }) => {
    await getReceivedMessagesCount({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('count', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getTicketTime', async ({ query, expected }) => {
    await getTicketTime({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('avg', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getWaitingTimeAvg', async ({ query, expected }) => {
    await getWaitingTimeAvg({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('avg', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getMessagingTime', async ({ query, expected }) => {
    await getMessagingTime({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('avg', expect.stringContaining(expected))
  })

  it.each(dashboardOverviewTestCases)('$name in getWaitingTime', async ({ query, expected }) => {
    await getWaitingTime({ ...defaultQuery, ...query })
    expect(doQuerySpy).toHaveBeenCalledWith('avg', expect.stringContaining(expected))
  })
})
