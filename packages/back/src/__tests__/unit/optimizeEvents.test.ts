import EventEmitter from 'events'
import optimizeEvents from '../../core/resources/utils/optimizeEvents'

test('optimizeEvents', async () => {
  const emitter1 = new EventEmitter()
  const model1: object = { id: 1 }

  const emitter2 = new EventEmitter()
  const model2: object = { id: 2 }

  const eventTuplesInput = [
    [emitter1, 'updated', model1],
    [emitter1, 'created', model1],
    [emitter1, 'updated', model1],
    [emitter2, 'updated', model2],
  ]

  // @ts-ignore
  const eventTuplesOutput = optimizeEvents(eventTuplesInput)

  expect(eventTuplesOutput).toMatchObject([
    [emitter1, 'created', model1],
    [emitter1, 'updated', model1],
    [emitter2, 'updated', model2],
  ])
})
