import { v4 as uuid } from 'uuid'
import async<PERSON><PERSON><PERSON> from '../../core/utils/asyncSingleton'

test('basic', async () => {
  const res = await asyncSingleton('1', async () => 1)

  expect(res).toEqual(1)
})

test('equals on second call', async () => {
  const KEY = uuid()

  const res1 = await asyncSingleton(KEY, async () => uuid())
  const res2 = await asyncSingleton(KEY, async () => uuid())

  expect(res1).toEqual(res2)
})
