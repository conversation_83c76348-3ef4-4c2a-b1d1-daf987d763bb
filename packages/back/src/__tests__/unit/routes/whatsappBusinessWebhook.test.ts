import { getContactIdAndType } from '../../../microServices/api/routes/whatsappBusinessWebhooks'
import { ServiceInstance } from '../../../core/dbSequelize/models/Service'

jest.mock('../../../core/services/logs/Logger', () => {
  return {
    __esModule: true,
    default: {
      log: jest.fn(),
    },
  }
})

jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../core/services/logs/Logger').default) {
          return { log: jest.fn() }
        }
        return {}
      }),
    },
    Service: () => () => {},
    Inject: () => () => {},
  }
})

jest.mock('../../../core/resources/serviceResource', () => jest.fn())
jest.mock('../../../core/services/jobs/queue/QueueJobsDispatcher', () => jest.fn())

describe('getContactIdAndType', () => {
  let service: ServiceInstance

  beforeEach(() => {
    service = {
      id: '1',
      accountId: '1',
      type: 'whatsapp-business',
      data: {},
      settings: {},
    } as ServiceInstance
  })

  it('should return correct id and type for gupshup message', () => {
    service.data.providerType = 'gupshup'
    const body = {
      type: 'message',
      payload: {
        source: '*************',
      },
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '*************' })
  })

  it('should return correct id and type for gupshup message-event', () => {
    service.data.providerType = 'gupshup'
    const body = {
      type: 'message-event',
      payload: {
        destination: '*************',
      },
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'statuses', id: '*************' })
  })

  it('should return correct id and type for 360Dialog cloud message', () => {
    service.data.providerType = '360Dialog'
    service.settings.cloudApi = true
    const body = {
      object: 'whatsapp_business_account',
      entry: [
        {
          changes: [
            {
              value: {
                messages: [
                  {
                    from: '*************',
                  },
                ],
              },
            },
          ],
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '*************' })
  })

  it('should return correct id and type for 360Dialog cloud statuses', () => {
    service.data.providerType = '360Dialog'
    service.settings.cloudApi = true
    const body = {
      object: 'whatsapp_business_account',
      entry: [
        {
          changes: [
            {
              value: {
                statuses: [
                  {
                    recipient_id: '*************',
                  },
                ],
              },
            },
          ],
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'statuses', id: '*************' })
  })

  it('should return correct id and type for 360Dialog on-premise message', () => {
    service.data.providerType = '360Dialog'
    service.settings.cloudApi = false
    const body = {
      messages: [{}],
      contacts: [
        {
          wa_id: '*************',
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '*************' })
  })

  it('should return correct id and type for 360Dialog on-premise statuses', () => {
    service.data.providerType = '360Dialog'
    service.settings.cloudApi = false
    const body = {
      statuses: [
        {
          recipient_id: '*************',
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'statuses', id: '*************' })
  })

  it('should return correct id and type for meta message', () => {
    service.data.providerType = 'meta'
    const body = {
      entry: [
        {
          changes: [
            {
              value: {
                messages: [
                  {
                    from: '*************',
                  },
                ],
              },
            },
          ],
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '*************' })
  })

  it('should return correct id and type for meta statuses', () => {
    service.data.providerType = 'meta'
    const body = {
      entry: [
        {
          changes: [
            {
              value: {
                statuses: [
                  {
                    recipient_id: '*************',
                  },
                ],
              },
            },
          ],
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'statuses', id: '*************' })
  })

  it('should return correct id and type for positus message', () => {
    service.data.providerType = 'positus'
    const body = {
      messages: [
        {
          from: {
            id: '*************',
          },
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '*************' })
  })

  it('should return correct id and type for positus statuses', () => {
    service.data.providerType = 'positus'
    const body = {
      statuses: [
        {
          recipient_id: '*************',
        },
      ],
    }
    expect(getContactIdAndType(service, body)).toEqual({ type: 'statuses', id: '*************' })
  })

  it('should return default message type and empty id if no match', () => {
    ;(service.data.providerType as 'unknown') = 'unknown'
    const body = {}
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '' })
  })

  it('should return default message type and empty id if no match payload gupshup', () => {
    service.data.providerType = 'gupshup'
    const body = {}
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '' })
  })

  it('should return default message type and empty id if no match payload 360Dialog', () => {
    service.data.providerType = '360Dialog'
    const body = {}
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '' })
  })

  it('should return default message type and empty id if no match payload meta', () => {
    service.data.providerType = 'meta'
    const body = {}
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '' })
  })

  it('should return default message type and empty id if no match payload positus', () => {
    service.data.providerType = 'positus'
    const body = {}
    expect(getContactIdAndType(service, body)).toEqual({ type: 'message', id: '' })
  })
})
