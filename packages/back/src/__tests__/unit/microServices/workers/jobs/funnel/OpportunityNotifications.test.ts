import 'reflect-metadata'
import moment from 'moment'
import OpportunityNotifications from '../../../../../../microServices/workers/jobs/funnel/OpportunityNotifications'
import accountResource from '../../../../../../core/resources/accountResource'
import pipelineResource from '../../../../../../core/resources/pipelineResource'
import notificationResource from '../../../../../../core/resources/notificationResource'
import cardMovementRepository from '../../../../../../core/dbSequelize/repositories/cardMovementRepository'
import userResource from '../../../../../../core/resources/userResource'

// Mocks
jest.mock('../../../../../../core/services/logs/Logger', () => {
  return { log: jest.fn() }
})
jest.mock('../../../../../../core/resources/accountResource', () => {
  return { findMany: jest.fn() }
})
jest.mock('../../../../../../core/resources/pipelineResource', () => {
  return { findMany: jest.fn(), createNotification: jest.fn() }
})
jest.mock('../../../../../../core/resources/userResource', () => {
  return { findMany: jest.fn() }
})
jest.mock('../../../../../../core/resources/notificationResource', () => {
  return { create: jest.fn() }
})
jest.mock('../../../../../../core/dbSequelize/repositories/accountRepository', () => jest.fn())
jest.mock('../../../../../../core/dbSequelize/repositories/cardMovementRepository', () => {
  return { getAllStoppedCards: jest.fn() }
})
jest.mock('../../../../../../core/utils/array/queuedAsyncMap', () => jest.fn((arr, fn) => Promise.all(arr.map(fn))))
jest.mock('../../../../../../core/config', () => jest.fn())
jest.mock('typedi', () => {
  return {
    __esModule: true,
    default: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),
        captureError: jest.fn(),
        dispatch: jest.fn(),
      }),
    },
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

describe('OpportunityNotifications', () => {
  let service: OpportunityNotifications

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should log and return if no accounts found', async () => {
    jest.spyOn(accountResource, 'findMany').mockResolvedValue([])

    service = new OpportunityNotifications()

    const result = await service.handle()

    expect(result).toEqual(false)
  })

  it('should handle pipelines and send notifications', async () => {
    const fakeAccounts = [{ id: 'account-1' }]
    const fakePipelines = [
      {
        id: 'pipeline-1',
        accountId: 'account-1',
        name: 'Sales Pipeline',
        automations: [
          {
            id: 'automation-1',
            config: { delay: 'daily', from: { high: '5' } },
            pipelineId: 'pipeline-1',
            notifications: [],
          },
        ],
      },
    ]
    const fakeAdmins = [{ id: 'user-1', accountId: 'account-1', language: 'pt-BR' }]

    jest.spyOn(accountResource, 'findMany').mockResolvedValue(fakeAccounts)
    jest.spyOn(pipelineResource, 'findMany').mockResolvedValue(fakePipelines)
    jest.spyOn(userResource, 'findMany').mockResolvedValue(fakeAdmins)
    jest.spyOn(cardMovementRepository, 'getAllStoppedCards').mockResolvedValue(2)
    jest.spyOn(notificationResource, 'create').mockResolvedValue(undefined)
    jest.spyOn(pipelineResource, 'createNotification').mockResolvedValue(undefined)

    await service.handle()

    expect(accountResource.findMany).toHaveBeenCalled()
    expect(pipelineResource.findMany).toHaveBeenCalled()
    expect(userResource.findMany).toHaveBeenCalled()
    expect(cardMovementRepository.getAllStoppedCards).toHaveBeenCalled()
    expect(notificationResource.create).toHaveBeenCalled()
    expect(pipelineResource.createNotification).toHaveBeenCalled()
  })

  it('should not send notifications if no opportunities stopped', async () => {
    const fakeAccounts = [{ id: 'account-1' }]
    const fakePipelines = [
      {
        id: 'pipeline-1',
        accountId: 'account-1',
        name: 'Sales Pipeline',
        automations: [
          {
            id: 'automation-1',
            config: { delay: 'daily', from: { high: '5' } },
            pipelineId: 'pipeline-1',
            notifications: [],
          },
        ],
      },
    ]
    const fakeAdmins = [{ id: 'user-1', accountId: 'account-1', language: 'pt-BR' }]

    jest.spyOn(accountResource, 'findMany').mockResolvedValue(fakeAccounts)
    jest.spyOn(pipelineResource, 'findMany').mockResolvedValue(fakePipelines)
    jest.spyOn(userResource, 'findMany').mockResolvedValue(fakeAdmins)
    jest.spyOn(cardMovementRepository, 'getAllStoppedCards').mockResolvedValue(0)

    await service.handle()

    expect(notificationResource.create).not.toHaveBeenCalled()
    expect(pipelineResource.createNotification).not.toHaveBeenCalled()
  })

  it('should return true in validateSendNotification', async () => {
    service = new OpportunityNotifications()

    let automation = {
      config: { delay: 'daily' },
    }
    let result = service.validateSendNotification(automation)
    expect(result).toEqual(true)

    automation = {
      config: { delay: 'weekly', day: moment().format('dddd').toLowerCase() },
    }
    result = service.validateSendNotification(automation)
    expect(result).toEqual(true)

    automation = {
      config: { delay: 'biweekly', day: moment().format('dddd').toLowerCase() },
      notifications: [],
    }
    result = service.validateSendNotification(automation)
    expect(result).toEqual(true)

    automation = { config: { delay: 'monthly', day: moment().format('DD').toLowerCase() } }
    result = service.validateSendNotification(automation)
    expect(result).toEqual(true)

    automation = { config: { delay: 'monthly', day: '40' } }
    result = service.validateSendNotification(automation)
    expect(result).toEqual(false)
  })

  it('should return correct text in getText', async () => {
    service = new OpportunityNotifications()

    let result = service.getText('pt-BR', 10, '2', 'funnelName')
    expect(result).toEqual(
      'Você tem 10 oportunidades paradas há mais de 2 dia(s) no funil funnelName. Oportunidades paradas há muito tempo tem menos chances de conversão.###Retome o contato com os seus clientes agora mesmo.',
    )

    result = service.getText('en-US', 10, '2', 'funnelName')
    expect(result).toEqual(
      'You have 10 opportunities that have been stopped in the funnelName funnel for more than 2 day(s). Opportunities that have been stuck for a long time have a lower chance of converting.###Reconnect with your customers now.',
    )

    result = service.getText('es', 10, '2', 'funnelName')
    expect(result).toEqual(
      'Tienes 10 oportunidades inactivas durante más de 2 dia(s) en el embudo funnelName. Las oportunidades que han estado inactivas durante mucho tiempo tienen menos posibilidades de conversión.###Vuelva a ponerse en contacto con sus clientes ahora mismo.',
    )
  })
})
