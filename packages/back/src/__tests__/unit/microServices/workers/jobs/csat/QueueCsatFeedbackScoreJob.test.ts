import { MessageInstance } from '../../../../../../core/dbSequelize/models/Message'
import { QueueCsatFeedbackScoreJob } from '../../../../../../microServices/workers/jobs/csat/QueueCsatFeedbackScoreJob'

jest.mock('../../../../../../core/services/logs/Logger', () => jest.fn())

jest.mock('typedi', () => {
  return {
    __esModule: true,
    default: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),
        captureError: jest.fn(),
        dispatch: jest.fn(),
      }),
    },
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

jest.mock('../../../../../../core/resources/messageResource', () => ({
  decryptMessageText: jest.fn().mockResolvedValue('some string'),
}))
jest.mock('../../../../../../core/resources/creditMovementResource', () => jest.fn())
jest.mock('../../../../../../core/utils/translateHeaderTicketHistory', () => ({
  language: jest.fn().mockResolvedValue({
    startMessage: '\n\n Resumo inteligente (gerado por IA) \n',
    attendant: 'Atendente: ',
    customer: 'Cliente: ',
  }),
}))
jest.mock('../../../../../../core/config', () => jest.fn())
jest.mock('../../../../../../core/resources/ticketResource', () => jest.fn())
jest.mock('../../../../../../core/services/jobs/queue/QueueJobsDispatcher', () => ({
  dispatch: jest.fn(),
}))
jest.mock('../../../../../../core/resources/answersResource', () => jest.fn())
jest.mock('../../../../../../core/resources/accountResource', () => jest.fn())

jest.mock('openai', () => {
  return {
    OpenAI: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{ message: { content: 'FINALSCORE_5' } }],
          }),
        },
      },
    })),
  }
})

jest.mock('axios', () => ({
  post: jest.fn(),
}))

describe('Generate AI Score And Feedback', () => {
  let service: QueueCsatFeedbackScoreJob = new QueueCsatFeedbackScoreJob()
  let fakeMessage = {
    id: 'fake-id',
    text: 'encrypted-text',
    account: {
      id: 'fake-account-id',
    },
  } as unknown as MessageInstance

  let fakeMessageTwo = {
    id: 'fake-id-2',
    text: 'encrypted-text-2',
    account: {
      id: 'fake-account-id',
    },
  } as unknown as MessageInstance

  test('Generate IA CSAT with valid arguments', async () => {
    const request = await service.generateAIScoreAndFeedback([fakeMessage, fakeMessageTwo], 'userId', 'prompt')
    expect(request).toEqual('FINALSCORE_5')
  })

  test('Do not generate IA CSAT with an empty messages array', async () => {
    const result = await service.generateAIScoreAndFeedback([], 'userId', 'arg3')
    expect(result).toBeNull()
  })

  test('Do not generate IA CSAT with missing or invalid prompt', async () => {
    const result = await service.generateAIScoreAndFeedback([fakeMessage, fakeMessageTwo], 'userId', null)
    expect(result).toBeNull()
  })
})
