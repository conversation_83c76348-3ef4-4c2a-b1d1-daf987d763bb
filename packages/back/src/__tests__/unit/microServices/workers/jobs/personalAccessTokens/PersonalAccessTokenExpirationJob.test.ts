import oAuthAccessTokenResource from '../../../../../../core/resources/oAuthAccessTokenResource'
import notificationResource from '../../../../../../core/resources/notificationResource'
import queuedAsyncMap from '../../../../../../core/utils/array/queuedAsyncMap'
import { PersonalAccessTokenExpirationJob } from '../../../../../../microServices/workers/jobs/personalAccessTokens/crons/PersonalAccessTokenExpirationJob'

jest.mock('../../../../../../core/services/logs/Logger', () => jest.fn())

jest.mock('typedi', () => {
  const mockTypediContainer = {
    get: jest.fn().mockReturnValue({
      log: jest.fn(),
      captureError: jest.fn(),
      dispatch: jest.fn(),
    }),
  }

  return {
    __esModule: true,
    default: mockTypediContainer,
    Container: mockTypediContainer,
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

jest.mock('../../../../../../core/resources/oAuthAccessTokenResource', () => jest.fn())
jest.mock('../../../../../../core/resources/notificationResource', () => ({
  findOne: jest.fn().mockResolvedValue(null),
}))
jest.mock('../../../../../../core/utils/array/queuedAsyncMap', () => jest.fn())
jest.mock('../../../../../../core/config', () => jest.fn())

describe('ExpirationNotificationJob', () => {
  let job: PersonalAccessTokenExpirationJob = new PersonalAccessTokenExpirationJob()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should create a new notification if its does not exists', async () => {
    const fakeTokens = [
      {
        tokenId: 'token-123',
        tokenName: 'Token de Teste',
        'user.userId': 'user-1',
        'user.accountId': 'account-1',
      },
    ]
    oAuthAccessTokenResource.findMany = jest.fn().mockResolvedValue(fakeTokens)
    notificationResource.findOne = jest.fn().mockResolvedValue(null)
    notificationResource.create = jest.fn().mockResolvedValue({})

    await job.handle()

    expect(queuedAsyncMap).toHaveBeenCalledTimes(1)

    const callback = (queuedAsyncMap as jest.Mock).mock.calls[0][1]

    await callback(fakeTokens[0])

    expect(notificationResource.create).toHaveBeenCalledWith({
      accountId: 'account-1',
      userId: 'user-1',
      read: false,
      text: 'Token de Teste',
      type: 'personal-access-token-expiration',
      config: {
        tokenId: 'token-123',
        references: 'personal-access-token',
      },
    })
  })

  it('should not send notifications when does not exists tokens with match condition', async () => {
    oAuthAccessTokenResource.findMany = jest.fn().mockResolvedValue([])

    await job.handle()

    expect(queuedAsyncMap).toHaveBeenCalledTimes(0)
  })
})
