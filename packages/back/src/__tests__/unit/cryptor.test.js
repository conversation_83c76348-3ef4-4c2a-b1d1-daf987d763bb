import {
  decryptBufferWithSeparatedIv,
  encryptBufferWithSeparatedIv,
  decryptText,
  encryptText,
  randomBytes,
} from '../../core/utils/crypt/cryptor'

test('randomBytes', () => {
  const actual = randomBytes(128).length

  expect(actual).toBe(128)
})

test('encrypt decrypt text', () => {
  const password = randomBytes(32)

  const originalString = randomBytes(256).toString('base64')
  const encryptedString = encryptText(originalString, password)
  const decryptedString = decryptText(encryptedString, password)

  expect(originalString).toBe(decryptedString)
})

test('encrypt decrypt buffer', () => {
  const password = randomBytes(32)

  const originalBuffer = randomBytes(128)
  const [encryptedBuffer, iv] = encryptBufferWithSeparatedIv(originalBuffer, password)
  const decryptedBuffer = decryptBufferWithSeparatedIv(encryptedBuffer, password, iv)

  expect(originalBuffer).toEqual(decryptedBuffer)
})
