import { checksum, checksumStream } from '../../core/utils/crypt/checksum'
import { getStorage } from '../../core/services/storage'

const base64 =
  'R0lGODlhPQBEAPeoAJosM//AwO/AwHVYZ/z595kzAP/s7P+goOXMv8+fh' +
  '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'

const buffer = Buffer.from(base64, 'base64')
const expectedChecksum = 'e68849c69ed3413606a062bfb4aba9f371066f7d'

const memoryStorage = {}

jest.mock('../../core/services/storage', () => {
  const { Readable } = require('stream')
  return {
    getStorage: () => ({
      write: (path, content) => {
        memoryStorage[path] = content
      },
      createReadStream: (path) => {
        const data = memoryStorage[path]
        return Readable.from(data ?? Buffer.alloc(0))
      },
    }),
  }
})

test('checksum of a buffer', () => {
  expect(checksum(buffer)).toBe(expectedChecksum)
})

test('checksum of a stream', async () => {
  const storage = getStorage()
  await storage.write('_test.gif', buffer)
  const stream = storage.createReadStream('_test.gif')
  const result = await checksumStream(stream)
  expect(result).toBe(expectedChecksum)
})
