jest.mock('../../core/config', () => ({
  __esModule: true,
  default: jest.fn(() => 'a'.repeat(64)),
  setValues: jest.fn(),
  getValues: jest.fn(),
  setValue: jest.fn(),
}))

import {
  createEncryptedEncryptionKey,
  createEncryptionKey,
  getAccountEncryptionKey,
} from '../../core/services/crypt/accountCryptor'

describe('accountCryptor', () => {
  afterAll(() => {
    jest.clearAllMocks()
  })

  test('createEncryptionKey length', () => {
    expect(createEncryptionKey().length).toBe(32)
  })

  test('createEncryptedEncryptionKey length', () => {
    expect(createEncryptedEncryptionKey().length).toBe(48)
  })

  test('getAccountEncryptionKey length', () => {
    const account = {
      encryptionKey: createEncryptedEncryptionKey().toString('hex'),
    }

    const encryptionKey = getAccountEncryptionKey(account)

    expect(encryptionKey.length).toBe(32)
  })
})
