import wait from '../../core/utils/wait'
import queuedAsyncMap from '../../core/utils/array/queuedAsyncMap'

test('queuedAsyncMap with promise', async () => {
  const arr = [1, 2, 3]
  const asyncDoubler = (number) => new Promise((resolve) => resolve(number * 2))

  const result = await queuedAsyncMap(arr, asyncDoubler)

  expect(result).toEqual([2, 4, 6])
})

test('queuedAsyncMap sync', async () => {
  const arr = [1, 2, 3]
  const doubler = (number) => number * 2

  const result = await queuedAsyncMap(arr, doubler)

  expect(result).toEqual([2, 4, 6])
})

test('queuedAsyncMap throws error', async () => {
  expect.assertions(1)

  const arr = [1, 2, 3]

  const doubler = async (number) => {
    if (number === 2) throw new Error('2 not allowed!')
    return number
  }

  await queuedAsyncMap(arr, doubler).catch((e) => {
    expect(e.message).toBe('2 not allowed!')
  })
})

// Deve tentar processar até apenas os itens do laço que de reject, os proximos laços não por conta do pauseOnError true.
test('queuedAsyncMap reject', async () => {
  const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
  const parallel = 2

  const callback = async (item) =>
    new Promise((res, rej) => (item === 6 ? rej(new Error('Any error')) : res(item + 10)))

  const result = await queuedAsyncMap(arr, callback, parallel, false).catch((e) => {
    expect(e.message).toBe('Any error')
    return e
  })

  expect(result.message).toBe('Any error')

  const { data, error } = await queuedAsyncMap(arr, callback, parallel, true, true).catch((e) => e)

  expect(JSON.stringify(data)).toBe(JSON.stringify([11, 12, 13, 14, 15, 17]))
  expect(error.message).toBe('Any error')
})

test('queuedAsyncMap pauseOnError', async () => {
  const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
  const parallel = 2

  let count = 0

  // pauseOnError true
  const callback = async (item) => {
    count += 1
    return new Promise((res, rej) => (item === 6 ? rej(new Error('Any error')) : res(item + 10)))
  }

  const { data, error } = await queuedAsyncMap(arr, callback, parallel, true, true).catch((e) => e)

  await wait(1000)

  expect(JSON.stringify(data)).toBe(JSON.stringify([11, 12, 13, 14, 15, 17]))
  expect(error.message).toBe('Any error')
  expect(count).toBe(7)

  count = 0

  // pauseOnError false
  const { data: data1, error: error1 } = await queuedAsyncMap(arr, callback, parallel, true, false).catch((e) => e)

  expect(JSON.stringify(data1)).toBe(JSON.stringify([11, 12, 13, 14, 15, 17]))
  expect(error1.message).toBe('Any error')

  await wait(1000)

  expect(JSON.stringify(data1)).toBe(JSON.stringify([11, 12, 13, 14, 15, 17, 18, 19, 20]))
  expect(error1.message).toBe('Any error')
  expect(count).toBe(10)
})
