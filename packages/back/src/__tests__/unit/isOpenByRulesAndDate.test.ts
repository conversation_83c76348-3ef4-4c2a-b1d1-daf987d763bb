import isOpenByRulesAndDate from '../../core/utils/isOpenByRulesAndDate'

describe('isOpenByRulesAndDate', () => {
  const rules = [
    {
      start: '21:00',
      end: '22:00',
      weekDays: ['thu'],
    },
  ]

  test('should always be UTC', () => {
    expect(new Date().getTimezoneOffset()).toBe(0)
  })

  test('should be open on a valid date', () => {
    const date = new Date('2019-11-21T18:30:00.000Z')
    const open = isOpenByRulesAndDate(rules, date, 180)

    expect(open).toBe(true)
  })

  test('should be closed on an invalid date', () => {
    const date = new Date('2019-11-29T22:30:00.000Z')
    const open = isOpenByRulesAndDate(rules, date, 180)

    expect(open).toBe(false)
  })
})
