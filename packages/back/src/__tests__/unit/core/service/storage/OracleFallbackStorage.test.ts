import { Readable } from 'stream'
import OracleFallbackStorage from '../../../../../core/services/storage/OracleFallbackStorage'
import { S3 } from 'aws-sdk'
import Logger from '../../../../../core/services/logs/Logger'
import retry from '../../../../../core/utils/retry'

jest.mock('aws-sdk', () => {
  const mockedS3 = {
    upload: jest.fn().mockReturnThis(),
    promise: jest.fn(),
    getObject: jest.fn().mockReturnThis(),
    createReadStream: jest.fn(),
    headObject: jest.fn().mockReturnThis(),
    copyObject: jest.fn().mockReturnThis(),
    deleteObject: jest.fn().mockReturnThis(),
    getSignedUrlPromise: jest.fn(),
  }
  return { S3: jest.fn(() => mockedS3) }
})

jest.mock('../../../../../core/services/logs/Logger', () => {
  return jest.fn().mockImplementation(() => {
    return {
      log: jest.fn(),
    }
  })
})

jest.mock('../../../../../core/utils/retry')

describe('OracleFallbackStorage', () => {
  let oracleFallbackStorage: OracleFallbackStorage
  let mockS3: jest.Mocked<S3>
  let mockLogger: jest.Mocked<Logger>
  const mockConfig = { get: jest.fn() }

  beforeEach(() => {
    mockConfig.get.mockImplementation((key: string) => {
      if (key === 'oracleEndpoint') return 'http://localhost:4566'
      if (key === 'oracleRegion') return 'us-east-1'
      if (key === 'oracleAccessKeyId') return 'test-access-key-id'
      if (key === 'oracleSecretAccessKey') return 'test-secret-access-key'
      if (key === 'oracleBucketNameFallback') return 'test-bucket'
      return null
    })
    oracleFallbackStorage = new OracleFallbackStorage(mockConfig as any)
    mockS3 = new S3() as any
    mockLogger = new Logger(mockConfig as any) as any
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  describe('write method', () => {
    it('should upload a file', async () => {
      const filename = 'test.txt'
      const data = 'Hello, world!'
      const mockManagedUploadData: S3.ManagedUpload.SendData = {
        ETag: '"mock-etag"',
        Location: 'http://localhost:4566/test-bucket/test.txt',
        key: 'test.txt',
        Bucket: 'test-bucket',
      }

      mockS3.upload().promise.mockResolvedValue(mockManagedUploadData)
      ;(retry as jest.Mock).mockImplementation(async (operation: Function) => operation())

      const result = await oracleFallbackStorage.write(filename, data)

      expect(result).toEqual(mockManagedUploadData)
      expect(mockS3.upload).toHaveBeenCalledWith({
        Body: data,
        Key: filename,
      })
      expect(retry).toHaveBeenCalled()
    })
    it('should successfully write data to S3 on first try', async () => {
      const filename = 'file.txt'
      const data = 'Hello World'
      const mockResponse = { Location: 'http://example.com/file.txt' }
      mockS3.promise.mockResolvedValue(mockResponse)

      const result = await oracleFallbackStorage.write(filename, data)

      expect(result).toEqual(mockResponse)
      expect(mockS3.upload).toHaveBeenCalledWith({ Body: data, Key: filename })
      expect(mockS3.promise).toHaveBeenCalled()
    })
  })

  describe('createReadStream method', () => {
    it('should create a readable stream for a file', () => {
      const filename = 'test.txt'
      const mockStream = new Readable()
      mockS3.getObject().createReadStream.mockReturnValue(mockStream)

      const result = oracleFallbackStorage.createReadStream(filename)

      expect(result).toBeInstanceOf(Readable)
      expect(mockS3.getObject).toHaveBeenCalledWith({
        Key: filename,
      })
      expect(mockS3.createReadStream).toHaveBeenCalled()
    })
  })

  describe('exists method', () => {
    it('should check if a file exists', async () => {
      const filename = 'test.txt'
      mockS3.headObject().promise.mockResolvedValue({}) // Presence of the object

      const result = await oracleFallbackStorage.exists(filename)

      expect(result).toBeTruthy()
      expect(mockS3.headObject).toHaveBeenCalledWith({
        Key: filename,
      })
    })
  })

  describe('read method', () => {
    it('should read a file', async () => {
      const filename = 'test.txt'
      const mockData = Buffer.from('Hello, world!')
      mockS3.getObject().promise.mockResolvedValue(mockData)

      const result = await oracleFallbackStorage.read(filename)

      expect(result).toEqual(mockData)
      expect(mockS3.getObject).toHaveBeenCalledWith({
        Key: filename,
      })
    })
  })

  describe('getMetadata method', () => {
    it('should get metadata of a file', async () => {
      const filename = 'test.txt'
      const mockMetadata = { ContentType: 'text/plain' }
      mockS3.headObject().promise.mockResolvedValue(mockMetadata)

      const result = await oracleFallbackStorage.getMetadata(filename)

      expect(result).toEqual(mockMetadata)
      expect(mockS3.headObject).toHaveBeenCalledWith({
        Key: filename,
      })
    })
  })
})
