import SmartSummaryService from '../../../../core/services/SmartSummary/SmartSummaryService'
import messageResource from '../../../../core/resources/messageResource'

jest.mock('../../../../core/resources/messageResource', () => ({
  __esModule: true,
  default: {
    getMessagesForSummary: jest.fn().mockResolvedValue([]),
    updateById: jest.fn(),
  },
}))

jest.mock('../../../../core/resources/summaryResource', () => ({
  __esModule: true, // Garante que o Jest está lidando corretamente com módulos ES
  default: {
    updateById: jest.fn(),
  },
}))
jest.mock('../../../../core/dbSequelize/repositories/contractedCreditRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/accountRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/messageRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/summaryRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/creditMovementRepository', () => jest.fn())
jest.mock('../../../../core/resources/creditMovementResource', () => jest.fn())
jest.mock('../../../../core/resources/contractedCreditResource', () => jest.fn())
jest.mock('../../../../core/resources/notificationResource', () => jest.fn())
jest.mock('../../../../core/resources/accountResource', () => jest.fn())
jest.mock('../../../../core/utils/translateHeaderTicketHistory', () => jest.fn())
jest.mock('../../../../core/services/transcript', () => jest.fn())

jest.mock('../../../../core/services/logs/Logger', () => {
  return {
    __esModule: true,
    default: {
      log: jest.fn(),
    },
  }
})

jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../../core/services/logs/Logger').default) {
          return { log: jest.fn() }
        }
        return {}
      }),
    },
    Service: () => () => {},
    Inject: () => () => {},
  }
})

jest.mock('../../../../core/services/jobs/queue/QueueJobsDispatcher', () => jest.fn())

const mockTicket = { id: 1, accountId: 'account-123', getAccount: jest.fn() }
const mockContact = { id: 1, serviceId: 'service-123', accountId: 'account-123' }
const mockSummary = { id: 1, messageId: 1 }
const mockMessages = [{ id: 1, isFromMe: false, type: 'text', text: 'Test message' }]

describe('SmartSummaryService', () => {
  let service: SmartSummaryService

  beforeEach(() => {
    jest.clearAllMocks()
    service = new SmartSummaryService()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  test('start should return if cannot summarize', async () => {
    jest.spyOn(service, 'canSummarize').mockResolvedValue(false)
    await service.start(mockTicket, 'eventType', mockContact)
    expect(service.canSummarize).toHaveBeenCalledWith(mockTicket)
  })

  test('summarize should throw error if no messages', async () => {
    messageResource.getMessagesForSummary.mockResolvedValue([]) // Simula a resposta vazia de mensagens
    try {
      await service.summarize(mockTicket, mockSummary)
    } catch (error) {
      expect(error).toEqual(new Error(`Summary: There are no messages for ticket id ${mockTicket.id} to summarize`))
    }
  })

  test('summarize should update summary message', async () => {
    messageResource.getMessagesForSummary.mockResolvedValue(mockMessages)
    jest.spyOn(service, 'integrationByOpenAi').mockResolvedValue('Summary result')
    const summaryText = await service.summarize(mockTicket, mockSummary)
    expect(messageResource.updateById).toHaveBeenCalledWith(mockSummary.messageId, {
      text: summaryText,
      isTranscribing: false,
      transcribeError: true,
    })
  })
})
