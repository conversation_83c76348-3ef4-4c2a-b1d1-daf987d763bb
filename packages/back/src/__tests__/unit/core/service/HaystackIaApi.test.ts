jest.mock('../../../../core/services/transcript', () => jest.fn())

describe('HaystackIaApi', () => {
  let HaystackIaApi,
    api,
    mockAxios,
    mockConfig,
    mockMessageResource,
    mockQueuedAsyncMap,
    mockCreditMovementResource,
    mockFormData,
    mockPick,
    mockQs

  beforeAll(() => {
    jest.resetModules()
    mockAxios = { post: jest.fn(), delete: jest.fn() }
    mockConfig = { haystackIa: { username: 'user', password: 'pass', url: 'http://test' } }
    mockMessageResource = {
      findById: jest.fn(),
      decryptMessageText: jest.fn(),
      findMany: jest.fn(),
    }
    mockQueuedAsyncMap = jest.fn()
    mockCreditMovementResource = { createDebit: jest.fn() }
    mockFormData = jest.fn(() => ({
      append: jest.fn(),
      getBoundary: jest.fn(() => 'boundary'),
    }))
    mockPick = jest.fn((obj, keys) => obj)
    mockQs = { stringify: jest.fn(() => 'qsdata') }

    jest.doMock('axios', () => mockAxios)
    jest.doMock('../../../../core/configValues', () => mockConfig)
    jest.doMock('../../../../core/resources/messageResource', () => mockMessageResource)
    jest.doMock('../../../../core/utils/array/queuedAsyncMap', () => mockQueuedAsyncMap)
    jest.doMock('../../../../core/resources/creditMovementResource', () => mockCreditMovementResource)
    jest.doMock('form-data', () => mockFormData)
    jest.doMock('lodash/pick', () => mockPick)
    jest.doMock('qs', () => mockQs)

    HaystackIaApi = require('../../../../core/services/haystackIa').default
    api = new HaystackIaApi()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('getBearerToken', () => {
    it('should return token on success', async () => {
      mockAxios.post.mockResolvedValue({ data: { access_token: 'token123' } })
      const token = await api.getBearerToken()
      expect(token).toBe('token123')
      expect(mockAxios.post).toHaveBeenCalled()
    })

    it('should throw error on failure', async () => {
      mockAxios.post.mockRejectedValue({ message: 'fail' })
      await expect(api.getBearerToken()).rejects.toThrow('Failed to get bearer token: fail')
    })
  })

  describe('getConfig', () => {
    it('should return headers for messages', async () => {
      jest.spyOn(api, 'getBearerToken').mockResolvedValue('tok')
      const config = await api.getConfig(undefined, 'messages')
      expect(config.headers.Authorization).toBe('Bearer tok')
      expect(config.headers['Content-Type']).toBe('application/json')
    })

    it('should return headers for files', async () => {
      jest.spyOn(api, 'getBearerToken').mockResolvedValue('tok')
      const payload = { getBoundary: () => 'boundary' }
      const config = await api.getConfig(payload, 'files')
      expect(config.headers['Content-Type']).toContain('multipart/form-data')
    })

    it('should throw error if getBearerToken fails', async () => {
      jest.spyOn(api, 'getBearerToken').mockRejectedValue(new Error('fail'))
      await expect(api.getConfig()).rejects.toThrow('Failed to get config: fail')
    })
  })

  describe('createKnowledge', () => {
    it('should post and return docIds/sourceId', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: { details: { a: { doc_ids: [1], source_id: 'sid' } } } })
      const res = await api.createKnowledge('src', { accountId: 'acc', type: 'messages' })
      expect(res).toEqual({ docIds: [1], sourceId: 'sid' })
    })

    it('should throw error on failure', async () => {
      jest.spyOn(api, 'getConfig').mockRejectedValue(new Error('Failed to create knowledge: fail'))
      await expect(api.createKnowledge('src', { accountId: 'acc', type: 'messages' })).rejects.toThrow(
        'Failed to create knowledge: fail',
      )
    })
  })

  describe('findKnowledge', () => {
    it('should post and return mapped docs', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: [{ id: 1, content: 'c', meta: {}, blob: {} }] })
      const res = await api.findKnowledge(['1'], 'acc')
      expect(res[0].id).toBe(1)
    })

    it('should throw error on failure', async () => {
      jest.spyOn(api, 'getConfig').mockRejectedValue(new Error('fail'))
      await expect(api.findKnowledge(['1'], 'acc')).rejects.toThrow('Failed to search for knowledge: fail')
    })
  })

  describe('deleteKnowledgeDocs', () => {
    it('should delete and not throw if message exists', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.delete.mockResolvedValue({ data: { message: 'ok' } })
      await expect(api.deleteKnowledgeDocs(['1'], 'acc')).resolves.toBeUndefined()
    })

    it('should throw error if message missing', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.delete.mockResolvedValue({ data: {} })
      await expect(api.deleteKnowledgeDocs(['1'], 'acc')).rejects.toThrow(
        'Failed to delete documents of knowledge. DocumentIds: 1',
      )
    })

    it('should throw error on failure', async () => {
      jest.spyOn(api, 'getConfig').mockRejectedValue(new Error('fail'))
      await expect(api.deleteKnowledgeDocs(['1'], 'acc')).rejects.toThrow(
        'Failed to delete documents of knowledge: fail',
      )
    })
  })

  describe('suggestResponseByMessage', () => {
    it('should return message and subjectNotFound', async () => {
      mockMessageResource.findById.mockResolvedValue({ dataValues: { id: 1, serviceId: 'sid' } })
      mockMessageResource.decryptMessageText.mockResolvedValue('txt')
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: { message: 'msg', subjectNotFound: true } })
      mockCreditMovementResource.createDebit.mockResolvedValue()
      const res = await api.suggestResponseByMessage('mid', 'acc', false)
      expect(res).toEqual({ message: 'msg', subjectNotFound: true })
    })

    it('should throw error if response missing', async () => {
      mockMessageResource.findById.mockResolvedValue({ dataValues: { id: 1, serviceId: 'sid' } })
      mockMessageResource.decryptMessageText.mockResolvedValue('txt')
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: {} })
      await expect(api.suggestResponseByMessage('mid', 'acc', false)).rejects.toThrow(
        'Failed to suggest a response. MessageId: mid',
      )
    })

    it('should throw error if axios fails', async () => {
      mockMessageResource.findById.mockResolvedValue({ dataValues: { id: 1, serviceId: 'sid' } })
      mockMessageResource.decryptMessageText.mockResolvedValue('txt')
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockRejectedValue({ response: { data: { detail: 'fail' } } })
      await expect(api.suggestResponseByMessage('mid', 'acc', false)).rejects.toThrow('fail')
    })
  })

  describe('suggestResponseByTicket', () => {
    const messages = [
      {
        ticketId: 'tid',
        accountId: 'aid',
        serviceId: 'sId',
      },
    ]

    it('should return message and subjectNotFound', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: { message: 'msg', subjectNotFound: false } })
      mockCreditMovementResource.createDebit.mockResolvedValue()
      const res = await api.suggestResponseByTicket(messages, true)
      expect(res).toEqual({ message: 'msg', subjectNotFound: false })
    })

    it('should throw error if response missing', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockResolvedValue({ data: {} })
      await expect(api.suggestResponseByTicket(messages, true)).rejects.toThrow(
        'Failed to suggest a response. ticketId: tid',
      )
    })

    it('should throw error if axios fails', async () => {
      jest.spyOn(api, 'getConfig').mockResolvedValue({ headers: {} })
      mockAxios.post.mockRejectedValue({ response: { data: { detail: 'fail' } } })
      await expect(api.suggestResponseByTicket(messages, true)).rejects.toThrow('fail')
    })
  })
})
