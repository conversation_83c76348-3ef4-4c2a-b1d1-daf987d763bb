import { getWaitingTime, getTicketTime } from '../../../../../core/services/ticket/TicketTransferService'
import messageResource from '../../../../../core/resources/messageResource'

jest.mock('../../../../../core/services/logs/Logger', () => jest.fn())

jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),

        captureError: jest.fn(),
      }),
    },
    Inject: () => () => {},
    Service: () => () => {},
    Token: jest.fn().mockImplementation(() => ({})),
  }
})

jest.mock('../../../../../core/resources/userResource', () => jest.fn())

jest.mock('../../../../../core/resources/ticketResource', () => jest.fn())

jest.mock('../../../../../core/resources/messageResource', () => ({
  findOne: jest.fn(),
}))

jest.mock('../../../../../core/resources/contactResource', () => jest.fn())

jest.mock('../../../../../core/resources/ticketTransfersResource', () => jest.fn())

jest.mock('../../../../../core/resources/answersResource', () => jest.fn())

jest.mock('../../../../../core/services/SmartSummary/SmartSummaryService', () => jest.fn())

jest.mock('../../../../../core/services/queue/redisTaskQueue', () => jest.fn())

describe('TicketTransferService - Time Calculations', () => {
  const mockNow = new Date('2023-01-01T12:00:00Z')

  describe('getWaitingTime', () => {
    const mockTicket = { id: 'ticket-1' }

    it('should return full duration when no firstMessage exists', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: new Date('2023-01-01T11:30:00Z'),

        firstMessage: null,
      }

      const result = await getWaitingTime({
        ticketTransfer: mockTicketTransfer,

        ticket: mockTicket,

        now: mockNow,
      })

      // 30 minutos

      expect(result).toBe(1800)
    })

    it('should return 0 when firstMessage isFromMe and timestamp is before startedAt', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: new Date('2023-01-01T11:30:00Z'),

        firstMessage: {
          isFromMe: true,

          timestamp: new Date('2023-01-01T10:30:00Z'), // before startedAt
        },
      }

      const result = await getWaitingTime({
        ticketTransfer: mockTicketTransfer,

        ticket: mockTicket,

        now: mockNow,
      })

      expect(result).toBe(0)
    })

    it('should use firstOperatorMessage createdAt when found', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: new Date('2023-01-01T11:30:00Z'),

        firstMessage: {
          isFromMe: false,

          timestamp: new Date('2023-01-01T11:05:00Z'),
        },
      }

      const mockOperatorMessage = {
        createdAt: new Date('2023-01-01T11:15:00Z'),

        isFromMe: true,

        isFromBot: false,

        origin: 'operator',
      }

      ;(messageResource.findOne as jest.Mock).mockResolvedValueOnce(mockOperatorMessage)

      const result = await getWaitingTime({
        ticketTransfer: mockTicketTransfer,

        ticket: mockTicket,

        now: mockNow,
      })

      expect(messageResource.findOne).toHaveBeenCalledWith({
        where: {
          ticketId: 'ticket-1',

          isFromMe: true,

          isFromBot: false,

          origin: { $ne: 'bot' },

          timestamp: { $gte: mockTicketTransfer.startedAt },
        },

        order: [['timestamp', 'asc']],
      })

      // 15 minutes

      expect(result).toBe(900)
    })

    it('should return the duration when firstOperatorMessage is not found', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: new Date('2023-01-01T11:30:00Z'),

        firstMessage: {
          isFromMe: false,

          timestamp: new Date('2023-01-01T11:05:00Z'),
        },
      }

      ;(messageResource.findOne as jest.Mock).mockResolvedValueOnce(null)

      const result = await getWaitingTime({
        ticketTransfer: mockTicketTransfer,

        ticket: mockTicket,

        now: mockNow,
      })

      // 30 minutes

      expect(result).toBe(1800)
    })

    it('should use now when firstOperatorMessage is not found and ticketTransfer endedAt is null', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: null,

        firstMessage: {
          isFromMe: false,

          timestamp: new Date('2023-01-01T11:05:00Z'),
        },
      }

      ;(messageResource.findOne as jest.Mock).mockResolvedValueOnce(null)

      const result = await getWaitingTime({
        ticketTransfer: mockTicketTransfer,

        ticket: mockTicket,

        now: mockNow,
      })

      // 1 hour

      expect(result).toBe(3600)
    })

    it('should handle invalid dates', async () => {
      const mockTicketTransfer = {
        startedAt: undefined,

        endedAt: undefined,

        firstMessage: null,
      }

      await expect(
        getWaitingTime({
          ticketTransfer: mockTicketTransfer,

          ticket: mockTicket,

          now: mockNow,
        }),
      ).rejects.toThrow('Invalid date')
    })

    it('should handle invalid date formats', async () => {
      const mockTicketTransfer = {
        startedAt: new Date('invalid'),

        endedAt: new Date('invalid'),

        firstMessage: null,
      }

      await expect(
        getWaitingTime({
          ticketTransfer: mockTicketTransfer,

          ticket: mockTicket,

          now: mockNow,
        }),
      ).rejects.toThrow('Invalid date')
    })
  })

  describe('getTicketTime', () => {
    it('should return full duration', () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: new Date('2023-01-01T11:30:00Z'),
      }

      const result = getTicketTime({
        ticketTransfer: mockTicketTransfer,

        now: mockNow,
      })

      // 30 minutos

      expect(result).toBe(1800)
    })

    it('should use now when ticketTransfer endedAt is not found', () => {
      const mockTicketTransfer = {
        startedAt: new Date('2023-01-01T11:00:00Z'),

        endedAt: null,
      }

      const result = getTicketTime({
        ticketTransfer: mockTicketTransfer,

        now: mockNow,
      })

      // 1 hora

      expect(result).toBe(3600)
    })

    it('should handle invalid dates', () => {
      const mockTicketTransfer = {
        startedAt: undefined,

        endedAt: undefined,
      }

      expect(() =>
        getTicketTime({
          ticketTransfer: mockTicketTransfer,

          now: mockNow,
        }),
      ).toThrow('Invalid date')
    })

    it('should handle invalid date formats', () => {
      const mockTicketTransfer = {
        startedAt: new Date('invalid'),

        endedAt: new Date('invalid'),
      }

      expect(() =>
        getTicketTime({
          ticketTransfer: mockTicketTransfer,

          now: mockNow,
        }),
      ).toThrow('Invalid date')
    })
  })
})
