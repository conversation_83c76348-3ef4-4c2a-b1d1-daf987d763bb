jest.mock('openai', () => {
  return {
    OpenAI: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    })),
  }
})

jest.mock('../../../../../core/config', () => ({
  __esModule: true,
  default: () => ({
    apiKeyOpenIA: 'test-api-key',
  }),
}))

import { OpenAI } from 'openai'
import SuggestAgentPromptService from '../../../../../core/services/agent/SuggestAgentPromptService'

const MockedOpenAI = jest.mocked(OpenAI)

describe('SuggestAgentPromptService', () => {
  let suggestAgentPromptService: SuggestAgentPromptService
  let mockOpenAIInstance: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockOpenAIInstance = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    }

    MockedOpenAI.mockImplementation(() => mockOpenAIInstance)

    suggestAgentPromptService = new SuggestAgentPromptService()
    suggestAgentPromptService.logger = { log: jest.fn() } as any
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('getPrompt', () => {
    it('should throw error if companySegment, companySubject, or companyServices is missing', async () => {
      const payload = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: '',
        companySubject: '',
        companyServices: '',
        prompt: 'You are a helpful assistant.',
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user' } }],
      }

      await expect(suggestAgentPromptService.getPrompt(payload)).rejects.toThrow(
        'Missing required fields in payload: companySegment, companySubject, companyServices',
      )
    })

    it('should throw error if voiceTone, languageType, or function is invalid', async () => {
      const payload = {
        voiceTone: 'invalidTone',
        languageType: 'invalidLanguage',
        function: 'invalidFunction',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        prompt: 'You are a helpful assistant.',
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user' } }],
      }

      await expect(suggestAgentPromptService.getPrompt(payload)).rejects.toThrow(
        'Invalid agent configuration: function, voiceTone, languageType',
      )
    })

    it('should return a valid prompt', async () => {
      const payload = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        prompt: 'You are a helpful assistant.',
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user' } }],
      }

      const mockOpenAIResponse = {
        choices: [
          {
            message: {
              content: 'Suggested prompt',
            },
          },
        ],
      }

      mockOpenAIInstance.chat.completions.create.mockResolvedValue(mockOpenAIResponse)

      const result = await suggestAgentPromptService.getPrompt(payload)

      expect(result).toBe('Suggested prompt')
      expect(mockOpenAIInstance.chat.completions.create).toHaveBeenCalledTimes(1)
    })
  })
})
