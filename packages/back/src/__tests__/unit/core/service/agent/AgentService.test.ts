jest.mock('axios')

jest.mock('../../../../../core/resources/creditMovementResource', () => {
  jest.fn()
})
jest.mock('../../../../../core/resources/accountResource', () => {
  jest.fn()
})

const haystackIaUrl = 'https://qa-haystack-ia.digisac.io'

jest.mock('../../../../../core/configValues', () => ({
  haystackIa: { url: haystackIaUrl },
}))

import axios from 'axios'
import AgentService from '../../../../../core/services/agent/AgentService'
import { AgentConfig } from '../../../../../core/services/agent/utils'

const mockedAxios = jest.mocked(axios)

describe('AgentService', () => {
  let agentService: AgentService

  const createValidPayload = (overrides: Partial<AgentConfig> = {}): AgentConfig => ({
    // knowledgeBase: 'testKnowledgeBase',
    voiceTone: 'neutral',
    languageType: 'neutral',
    function: 'support',
    companySegment: 'e-commerce',
    companySubject: 'electronics',
    companyServices: 'consoles, computers, and accessories',
    prompt: 'You are a helpful assistant.',
    maxAttempts: 3,
    actions: [
      {
        action: {
          id: '1',
          name: 'Transferir para atendimento humano',
          description: 'Cliente solicitou falar com atendente',
          type: 'customer-requested-agent',
        },
      },
      {
        action: {
          id: '2',
          name: 'Transferir para atendimento humano',
          description: 'Agente não soube responder',
          type: 'agent-cannot-answer',
        },
      },
      {
        action: {
          id: '3',
          name: 'Transferir para atendimento humano',
          description: 'Agente está sem créditos',
          type: 'agent-out-of-credits',
        },
      },
    ],
    ...overrides,
  })

  beforeEach(() => {
    jest.clearAllMocks()

    agentService = new AgentService()
    agentService.logger = { log: jest.fn() } as any
    agentService.haystackApi = {
      getBearerToken: jest.fn().mockResolvedValue('test-token'),
      getConfig: jest.fn().mockReturnValue({
        headers: {
          Authorization: `Bearer test-token`,
          'Content-Type': 'application/json',
        },
      }),
    } as any
  })

  describe('createAgent', () => {
    it('should throw error if accountId is missing', async () => {
      const payload = createValidPayload()

      await expect(agentService.createAgent(payload, '')).rejects.toThrow('Missing required configuration: accountId')
    })

    it('should throw error if required fields are missing in payload', async () => {
      const payload = createValidPayload({ companyServices: '' })

      await expect(agentService.createAgent(payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: companyServices',
      )
    })

    it('should throw error if actions are missing in payload', async () => {
      const payload = createValidPayload({ actions: [] })

      await expect(agentService.createAgent(payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: actions',
      )
    })

    it('should throw error if action description is missing', async () => {
      const payload = createValidPayload({
        actions: [{ action: { id: '1', name: 'greet', description: '', type: '' } }],
      })

      await expect(agentService.createAgent(payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: action.description',
      )
    })

    it('should throw error if voiceTone, languageType or function is invalid', async () => {
      const payload = createValidPayload({
        voiceTone: 'invalidTone',
        languageType: 'invalidLanguage',
        function: 'invalidFunction',
      })

      await expect(agentService.createAgent(payload, 'accountId')).rejects.toThrow(
        'Invalid agent configuration: function, voiceTone, languageType',
      )
    })

    it('should create agent with valid payload', async () => {
      const payload = createValidPayload()

      const mockResponse = {
        data: {
          id: 'agentId',
          system_prompt: 'You are a helpful assistant.',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      }

      mockedAxios.post.mockResolvedValue(mockResponse)

      const response = await agentService.createAgent(payload, 'accountId')
      expect(response).toHaveProperty('id')
      expect(response).toHaveProperty('system_prompt')
      expect(mockedAxios.post).toHaveBeenCalledTimes(1)
    })
  })

  describe('updateAgent', () => {
    it('should throw error if agentId or accountId is missing', async () => {
      const payload = createValidPayload()

      await expect(agentService.updateAgent('', payload, '')).rejects.toThrow(
        'Missing required configuration: agentId or accountId',
      )
    })

    it('should throw error if required fields are missing in payload', async () => {
      const payload = createValidPayload({ companyServices: '' })

      await expect(agentService.updateAgent('agentId', payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: companyServices',
      )
    })

    it('should throw error if actions are missing in payload', async () => {
      const payload = createValidPayload({ actions: [] })

      await expect(agentService.updateAgent('agentId', payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: actions',
      )
    })

    it('should throw error if action description is missing', async () => {
      const payload = createValidPayload({
        actions: [{ action: { id: '1', name: 'greet', description: '', type: '' } }],
      })

      await expect(agentService.updateAgent('agentId', payload, 'accountId')).rejects.toThrow(
        'Missing required fields in payload: action.description',
      )
    })

    it('should throw error if voiceTone, languageType or function is invalid', async () => {
      const payload = createValidPayload({
        voiceTone: 'invalidTone',
        languageType: 'invalidLanguage',
        function: 'invalidFunction',
      })

      await expect(agentService.updateAgent('agentId', payload, 'accountId')).rejects.toThrow(
        'Invalid agent configuration: function, voiceTone, languageType',
      )
    })

    it('should update agent with valid payload', async () => {
      const payload = createValidPayload({
        function: 'triage',
      })

      const mockResponse = {
        data: {
          id: 'agentId',
          system_prompt: 'You are a helpful assistant.',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      }

      mockedAxios.put.mockResolvedValue(mockResponse)

      const response = await agentService.updateAgent('agentId', payload, 'accountId')
      expect(response).toHaveProperty('id')
      expect(response).toHaveProperty('system_prompt')
      expect(mockedAxios.put).toHaveBeenCalledTimes(1)
    })
  })

  describe('deleteAgent', () => {
    it('should throw error if agentId is missing', async () => {
      await expect(agentService.deleteAgent('')).rejects.toThrow('Missing agentId')
    })

    it('should delete agent successfully', async () => {
      const mockResponse = {
        data: { message: 'Bot deleted' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      }

      mockedAxios.delete.mockResolvedValue(mockResponse)

      await agentService.deleteAgent('agentId')
      expect(mockedAxios.delete).toHaveBeenCalledWith(
        `${haystackIaUrl}/v1/bot-profiles/agentId`,
        agentService.haystackApi.getConfig(),
      )
    })
  })
})
