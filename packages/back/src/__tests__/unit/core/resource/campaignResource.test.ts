import campaignResource, { CampaignResource } from '../../../../core/resources/campaignResource'

jest.mock('../../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/messageRepository', () => {
  return {
    getAckCountByCampaignId: () => 10,
  }
})
jest.mock('../../../../core/dbSequelize/repositories/tagRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignMessageRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignMessageProgressRepository', () => {
  return {
    getTotalFiredCount: () => 10,
    getFiredFailedCount: () => 10,
    getFiredBlockedByMessageRuleCount: () => 10,
    getFiredSuccessCount: () => 10,
    getFiredAnsweredCount: () => 10,
  }
})
jest.mock('../../../../core/dbSequelize/models/Campaign', () => jest.fn())
jest.mock('../../../../core/resources/BaseResource', () => jest.fn())
jest.mock('../../../../core/resources/campaignMessageResource', () => jest.fn())
jest.mock('../../../../core/resources/fileResource', () => jest.fn())
jest.mock('../../../../core/services/contacts/ContactService', () => jest.fn())
jest.mock('../../../../core/utils/error/HttpError', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => jest.fn())

describe('CampaignResource', () => {
  let campaignRes: CampaignResource

  beforeEach(() => {
    campaignRes = campaignResource
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('getStats', () => {
    it('should return correct statistics for the campaign', async () => {
      // Dados fictícios do CampaignInstance
      const campaign = {
        id: 1,
        totalMessagesCount: 100,
        sentMessagesCount: 80,
        totalContacts: 50,
        totalContactsImported: 40,
        totalValidContacts: 35,
        totalFiredCount: 60,
        firedFailedCount: 5,
        firedBlockedByMessageRuleCount: 2,
        firedSuccessCount: 53,
      }

      const stats = await campaignRes.getStats(campaign)

      expect(stats).toEqual({
        total: 100,
        sent: 80,
        failed: 10,
        sending: 10,
        sentToServer: 10,
        received: 10,
        viewed: 10,
        played: 10,
        answered: 10,
        sentAndNotReceived: 10,
        campaignInfo: {
          totalMessagesCount: 100,
          sentMessagesCount: 80,
          totalContacts: 50,
          totalContactsImported: 40,
          totalValidContacts: 35,
          totalFiredCount: 10,
          firedFailedCount: 10,
          firedBlockedByMessageRuleCount: 10,
          firedSuccessCount: 10,
        },
        messageInfo: {
          error: 10,
          sending: 10,
          sentToServer: 10,
          received: 10,
          viewed: 10,
          played: 10,
          answered: 10,
        },
      })
    })
  })
})
