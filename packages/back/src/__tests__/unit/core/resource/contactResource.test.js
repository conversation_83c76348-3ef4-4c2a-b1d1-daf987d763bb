import contactResource from '../../../../core/resources/contactResource'
import ticketService from '../../../../core/services/ticket/ticketService'

jest.mock('../../../../core/services/logs/Logger', () => jest.fn())
jest.mock('../../../../core/services/config/Config', () => jest.fn())
jest.mock('../../../../core/config', () => jest.fn())

jest.mock('typedi', () => {
  const mockOracleStorage = {
    setBucket: (key) => jest.fn(),
  }

  const mockConfig = {
    get: jest.fn((key) => {
      if (key === 'buckets') return 'bucket1,bucket2'
    }),
  }

  return {
    __esModule: true,
    default: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),
        captureError: jest.fn(),
        dispatch: jest.fn(),
      }),
    },
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../../core/services/config/Config')) return mockConfig
        else if (service === require('../../../../core/services/storage/OracleStorage').default)
          return mockOracleStorage
        return {}
      }),
    },
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

jest.mock('../../../../core/services/db/sequelize', () => ({
  __esModule: true,
  default: {
    define: jest.fn(() => ({
      prototype: {
        hasPermission: jest.fn(),
      },
      associate: jest.fn(),
      belongsTo: jest.fn(),
    })),
  },
}))

jest.mock('../../../../core/resources/contactResource', () => ({
  findById: jest.fn((key) => {
    if (key === 'contact-123')
      return {
        id: 'contact-123',
        currentTicketId: 'ticket-456',
        currentTicket: {},
        service: {},
      }
    else if (key === 'invalid-id') return null
    else {
    }
  }),
  summaryTicketById: jest.fn((key) => {
    if (key === 'contact-123') {
      return { summary: 'This is a ticket summary' }
    } else {
    }
  }),
}))
jest.mock('../../../../core/services/ticket/ticketService', () => ({
  summaryTicket: () => {
    summary: 'This is a ticket summary'
  },
}))
jest.mock('../../../../core/resources/messageResource', () => ({}))
jest.mock('../../../../core/resources/ticketTransfersResource', () => ({}))
jest.mock('../../../../core/resources/ticketResource', () => ({}))
jest.mock('../../../../core/resources/serviceResource', () => ({}))

describe('ContactResource.summaryTicketById', () => {
  it('should call summaryTicket with correct contact and return result', async () => {
    const mockContact = {
      id: 'contact-123',
      currentTicketId: 'ticket-456',
      currentTicket: {},
      service: {},
    }

    const findByIdMock = contactResource.findById
    findByIdMock('contact-123')

    const summaryResult = { summary: 'This is a ticket summary' }

    const result = await contactResource.summaryTicketById('contact-123')
    ticketService.summaryTicket(mockContact, {})

    expect(findByIdMock).toHaveBeenCalledWith('contact-123')
    expect(result).toStrictEqual(summaryResult)
  })
})
