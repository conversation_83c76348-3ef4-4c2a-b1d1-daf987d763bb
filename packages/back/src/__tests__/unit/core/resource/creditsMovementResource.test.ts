import creditMovementResource, { CreditMovementResource } from '../../../../core/resources/creditMovementResource'

jest.mock('../../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/userRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/creditMovementRepository', () => {
  return {
    totalServices: () => [{ id: 'abc' }],
  }
})
jest.mock('../../../../core/resources/BaseResource', () => jest.fn())
jest.mock('../../../../core/resources/userResource', () => jest.fn())
jest.mock('../../../../core/utils/error/HttpError', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => jest.fn())

describe('CreditMovementResource', () => {
  let campaignRes: CreditMovementResource

  beforeEach(() => {
    campaignRes = creditMovementResource
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('totalServices', () => {
    it('should return correct statistics for the campaign', async () => {
      const filter = {
        from: '2025-01-01',
        to: '2025-01-30',
        serviceType: 'summary',
      }

      const result = await campaignRes.totalServices(filter, 1, 'abc')

      expect(result?.[0]?.id).toEqual('abc')
    })
  })
})
