import { Container } from 'typedi'
import QueueStartSummaryJob from '../../../../../../../microServices/workers/jobs/summary/QueueStartSummaryJob'
import SmartSummaryService from '../../../../../../../core/services/SmartSummary/SmartSummaryService'

jest.mock('../../../../../../../core/resources/messageResource', () => jest.fn())
jest.mock('../../../../../../../core/dbSequelize/repositories/summaryRepository', () => jest.fn())
jest.mock('../../../../../../../core/resources/summaryResource', () => jest.fn())
jest.mock('../../../../../../../core/resources/creditMovementResource', () => jest.fn())
jest.mock('../../../../../../../core/resources/contactResource', () => jest.fn())
jest.mock('../../../../../../../core/resources/accountResource', () => jest.fn())
jest.mock('../../../../../../../core/services/transcript', () => jest.fn())
jest.mock('../../../../../../../core/services/jobs/queue/QueueJobsDispatcher', () => jest.fn())
jest.mock('../../../../../../../core/services/SmartSummary/SmartSummaryService', () => {
  return { start: jest.fn(), limitReached: jest.fn() }
})
jest.mock('../../../../../../../core/utils/translateHeaderTicketHistory', () => jest.fn())

jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn(),
    },
    Service: () => () => {},
    Inject: () => () => {},
  }
})

jest.mock('../../../../../../../core/services/logs/Logger', () => {
  return {
    __esModule: true,
    default: {
      log: jest.fn(),
    },
  }
})

jest.mock('../../../../../../../core/services/jobs/interfaces/Job', () => jest.fn())

describe('QueueSummaryJob', () => {
  let queueStartSummaryJob: QueueStartSummaryJob
  let smartSummaryServiceMock: jest.Mocked<SmartSummaryService>

  beforeEach(() => {
    smartSummaryServiceMock = {
      start: jest.fn(),
      limitReached: jest.fn(),
    } as unknown as jest.Mocked<SmartSummaryService>
    // ;(Container.get as jest.Mock).mockReturnValue(smartSummaryServiceMock)

    queueStartSummaryJob = {
      service: smartSummaryServiceMock,
      handle: QueueStartSummaryJob.prototype.handle,
    } as unknown as jest.Mocked<QueueStartSummaryJob>

    // ;(Container.get as jest.Mock).mockReturnValue(smartSummaryServiceMock)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('handle should start summary', async () => {
    const currentTicket = { id: 1, status: 'open' }
    const type = 'finalize'
    const contact = { id: 2, name: 'John Doe' }

    await queueStartSummaryJob.handle({ currentTicket, type, contact })

    expect(smartSummaryServiceMock.start).toHaveBeenCalledTimes(1)
    expect(smartSummaryServiceMock.start).toHaveBeenCalledWith(currentTicket, type, contact)
  })
})
