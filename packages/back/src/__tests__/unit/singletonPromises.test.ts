import singletonPromises from '../../core/utils/singletonPromises'

test('basic', async () => {
  const res = await Promise.all([
    singletonPromises('1', async () => 1),
    singletonPromises('1', async () => 2),
    singletonPromises('1', async () => 3),
  ])

  expect(res).toEqual([1, 1, 1])
})

test('call only first fn for same keys', async () => {
  const fn = jest.fn(async () => 1)
  const fn2 = jest.fn(async () => 1)

  await Promise.all([singletonPromises('1', fn), singletonPromises('1', fn2)])

  expect(fn).toBeCalled()
  expect(fn2).not.toBeCalled()
})

test('call all fn for different keys', async () => {
  const fn = jest.fn(async () => 1)
  const fn2 = jest.fn(async () => 2)

  const [res1, res2] = await Promise.all([singletonPromises('1', fn), singletonPromises('2', fn2)])

  expect(fn).toBeCalled()
  expect(fn2).toBeCalled()

  expect(res1).toBe(1)
  expect(res2).toBe(2)
})
