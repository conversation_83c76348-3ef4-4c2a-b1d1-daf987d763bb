import taskQueue, { TimeoutError } from '../../core/services/queue/taskQueue'
import wait from '../../core/utils/wait'

test('basic', async () => {
  const queue = taskQueue('basic-1')

  const res = await queue.run(async () => true)

  expect(res).toBe(true)
})

test('nested', async () => {
  const queue = taskQueue('nested-1')

  const res = await queue.run(async () => queue.run(async () => true))

  expect(res).toBe(true)
})

test('timeout', async () => {
  const queue = taskQueue('timeout-1', { timeout: 1 })

  const prom = queue.run(async () => wait(5))

  await expect(prom).rejects.toEqual(new TimeoutError())
})

test('exception', async () => {
  const queue = taskQueue('exception-1')

  const error = new Error('Broken!')

  const prom = queue.run(async () => {
    throw error
  })

  await expect(prom).rejects.toEqual(error)
})

test('multi', async () => {
  const queue = taskQueue('multi-1', { timeout: 1 })

  let counter = 0

  const task1 = jest.fn(async () => ++counter)
  const task2 = jest.fn(async () => ++counter)
  const task3 = jest.fn(async () => ++counter)

  const res = await Promise.all([queue.run(task1), queue.run(task2), queue.run(task3)])

  expect(res).toEqual([1, 2, 3])
})
