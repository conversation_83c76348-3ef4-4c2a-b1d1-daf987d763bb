import { hasLengthLesserThanOrEqual } from '../../../core/utils/validator/validators'

describe('validators', () => {
  it('tests hasLengthLesserThanOrEqual with invalid value', async () => {
    const value =
      'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6we' +
      'HKNatbGd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW2' +
      '1xEnXOYdYpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8' +
      'mJvMgUyPJ5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6U' +
      'LsiOn2EFoniXeBla9E8U3suwarCYH2MfGGYHEsVa'

    const isValid = hasLengthLesserThanOrEqual(255)({ value })

    expect(isValid).toBe(false)
  })

  it('tests hasLengthLesserThanOrEqual with valid value', async () => {
    const value = 'tt8vCAza0twkUQE4SNwy'

    const isValid = hasLengthLesserThanOrEqual(255)({ value })

    expect(isValid).toBe(true)
  })
})
