import { getErrors } from '../../../core/utils/validator'
import { hasLengthLesserThanOrEqual } from '../../../core/utils/validator/rules'

describe('getErrors', () => {
  it('works', async () => {
    const input = {
      name:
        'tt8vCAza0twkUQE4SNwyQmQL8vf6xBVaPZtF0oD6076W7tNIg21dC6weHKNatbG' +
        'd2wEAMcBN2EwYy0j9cihlbVn1KWeWwZ6OEcFep7UM3r3fi6ZCAmrrX0yy4eW21xEnXOYd' +
        'YpkhHl1PD5GKb8x7jCUg8ZK5cJgEJSsYSUv8ClIQS7gw2X31cEHTgQeHCEl8mJvMgUyPJ' +
        '5SVzRx7mU78pZHNbjdGFR0FaN2L8FS91DJDQvVlFr0OxgRf0WCJtbFRoS6ULsiOn2EFon' +
        'iXeBla9E8U3suwarCYH2MfGGYHEsVa',
    }
    const rules = {
      name: [hasLengthLesserThanOrEqual(255)],
    }

    const expectedErrors = {
      name: expect.objectContaining({
        types: ['hasLengthLesserThanOrEqual'],
      }),
    }
    const actualErrors = await getErrors(input, rules)

    expect(actualErrors).toEqual(expectedErrors)
  })
})
