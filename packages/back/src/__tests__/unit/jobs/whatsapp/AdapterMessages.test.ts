import AdapterMessages from '../../../../microServices/workers/jobs/whatsapp/adapter/AdapterMessages'
import { MessageInstance } from '../../../../core/dbSequelize/models/Message'
import messageResource from '../../../../core/resources/messageResource'
import fileResource from '../../../../core/resources/fileResource'
import { WAMessage } from '../../../../microServices/workers/jobs/whatsapp/adapter/connector/WhatsappController'

jest.mock('../../../../core/services/crypt/accountCryptor', () => ({
  decryptTextForAccount: jest.fn(),
}))

jest.mock('../../../../core/services/storage', () => ({
  getStorage: jest.fn(),
}))

jest.mock('../../../../core/resources/messageResource', () => ({
  findById: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  findMany: jest.fn(),
  update: jest.fn(),
  updateById: jest.fn(),
  maybeEventTransaction: jest.fn(),
}))

jest.mock('../../../../core/resources/fileResource', () => ({
  attachFileToMessage: jest.fn(),
  fileNameSanitizer: jest.fn(),
  create: jest.fn(),
}))

jest.mock('../../../../core/resources/contactResource', () => ({
  updateById: jest.fn(),
  findById: jest.fn(),
  findMany: jest.fn(),
  internalUpdate: jest.fn(),
}))

jest.mock('../../../../core/resources/ticketResource', () => ({
  findById: jest.fn(),
}))

jest.mock('../../../../core/config', () => jest.fn())

jest.mock('../../../../core/services/logs/reportError', () => jest.fn())

jest.mock('../../../../core/services/logs/Logger', () => ({
  __esModule: true,
  default: {
    log: jest.fn(),
  },
}))

jest.mock('../../../../core/services/tracer/Tracer', () => ({
  __esModule: true,
  default: {
    captureError: jest.fn(),
  },
}))

jest.mock('typedi', () => {
  const actual = jest.requireActual('typedi')

  return {
    __esModule: true,
    ...actual,
    Container: {
      ...actual.Container,
      get: jest.fn((token) => {
        if (token === require('../../../../core/services/logs/Logger').default) {
          return { log: jest.fn() }
        }
        if (token === require('../../../../core/services/tracer/Tracer').default) {
          return { captureError: jest.fn() }
        }
        return {}
      }),
      set: jest.fn(),
    },
  }
})

class FakeAdapter {
  service
  adapterContacts

  constructor() {
    this.service = {
      id: 'service1',
      accountId: 'account1',
    }

    this.adapterContacts = {
      getOrCreateContactByIdFromService: () => ({ id: 'contact1' }),
    }
  }

  log() {}
}

describe('AdapterMessages', () => {
  describe('saveMessage', () => {
    let adapterMessages
    let mockMessage
    let mockWAMessage

    beforeEach(() => {
      adapterMessages = new AdapterMessages(new FakeAdapter())
      mockMessage = {
        id: '1',
        accountId: 'account1',
        serviceId: 'service1',
        data: { ack: 0 },
      } as MessageInstance
      mockWAMessage = {
        id: 'message1',
        contactId: 'contact1',
        type: 'chat',
        message: 'Test message',
        timestamp: **********,
        fromId: 'user1',
        isFromMe: false,
        ack: 1,
        isFirst: false,
        isNew: true,
      } as unknown as WAMessage
    })

    it('should throw error when service is not defined', async () => {
      adapterMessages.adapter.service = null

      await expect(adapterMessages.saveMessage(mockMessage, mockWAMessage, {})).rejects.toThrow(
        'Parameter "service" is required.',
      )
    })

    it('should throw error when wMessage is not defined', async () => {
      await expect(adapterMessages.saveMessage(mockMessage, null, {})).rejects.toThrow(
        'Parameter "wMessage" is required.',
      )
    })

    it('should return null if the message type is unsupported', async () => {
      mockWAMessage.type = 'unsupported_type'

      const result = await adapterMessages.saveMessage(mockMessage, mockWAMessage, {})
      expect(result).toBeNull()
    })

    it('should create a new message when no existing message is found', async () => {
      messageResource.create.mockResolvedValue(mockMessage)

      const result = await adapterMessages.saveMessage(null, mockWAMessage, {})

      expect(messageResource.create).toHaveBeenCalledWith(
        expect.objectContaining({
          idFromService: 'message1',
          contactId: 'contact1',
          type: 'chat',
          text: 'Test message',
        }),
        expect.any(Object),
      )

      expect(result.__wasJustCreated).toBe(true)
    })

    it('should handle mentionedJidList as an array of strings', async () => {
      messageResource.create.mockResolvedValue(mockMessage)

      const result = await adapterMessages.saveMessage(
        null,
        { ...mockWAMessage, mentionedJidList: ['user1', 'user2'] },
        {},
      )

      expect(messageResource.create).toHaveBeenCalledWith(
        expect.objectContaining({
          idFromService: 'message1',
          contactId: 'contact1',
          type: 'chat',
          text: 'Test message',
        }),
        expect.any(Object),
      )

      expect(result.__wasJustCreated).toBe(true)
    })

    it('should handle mentionedJidList as an array of objects', async () => {
      messageResource.create.mockResolvedValue(mockMessage)

      const result = await adapterMessages.saveMessage(
        null,
        {
          ...mockWAMessage,
          mentionedJidList: [
            {
              server: 'c.us',
              user: 'user1',
              _serialized: '<EMAIL>',
            },
          ],
        },
        {},
      )

      expect(messageResource.create).toHaveBeenCalledWith(
        expect.objectContaining({
          idFromService: 'message1',
          contactId: 'contact1',
          type: 'chat',
          text: 'Test message',
        }),
        expect.any(Object),
      )

      expect(result.__wasJustCreated).toBe(true)
    })

    it('should handle file attachment if media type is downloadable', async () => {
      mockWAMessage.type = 'image'
      mockWAMessage.fileId = 'file123'
      messageResource.create.mockResolvedValue(mockMessage)

      const result = await adapterMessages.saveMessage(null, mockWAMessage, {})

      expect(fileResource.attachFileToMessage).toHaveBeenCalledWith(
        expect.objectContaining({ fileId: 'file123' }),
        expect.any(Object),
      )
      expect(result.__wasJustCreated).toBe(true)
    })

    it('should handle error when saving the message', async () => {
      messageResource.create.mockRejectedValue(new Error('Database error'))

      await expect(adapterMessages.saveMessage(null, mockWAMessage, {})).rejects.toThrow('Database error')
    })
  })
})
