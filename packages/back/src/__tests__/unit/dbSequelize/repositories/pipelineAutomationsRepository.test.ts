import pipelineAutomationsRepository from '../../../../core/dbSequelize/repositories/pipelineAutomationsRepository'

jest.mock('../../../../core/dbSequelize/models/PipelineAutomations', () => jest.fn())

jest.mock('../../../../core/dbSequelize/repositories/pipelineAutomationsRepository', () => {
  return {
    create: jest.fn((body) => body),
    update: jest.fn((id, body) => ({ id, ...body })),
    destroyById: jest.fn((id) => ({ id, deleted: true })),
  }
})

describe('pipelineAutomationsRepository', () => {
  describe('createPipelineAutomation', () => {
    it('should return the created pipeline automation', async () => {
      const body = {
        pipelineId: 'd6aab144-7983-4a63-9065-4a2ca2044cbc',
        type: 'notification',
      }
      const result = await pipelineAutomationsRepository.create(body)
      expect(result).toBe(body)
    })
  })

  describe('updatePipelineAutomation', () => {
    it('should return the updated pipeline automation', async () => {
      const id = 'd6aab144-7983-4a63-9065-4a2ca2044cbc'
      const body = {
        type: 'updated-notification',
      }
      const result = await pipelineAutomationsRepository.update(id, body)
      expect(result).toEqual({ id, ...body })
    })
  })

  describe('deletePipelineAutomation', () => {
    it('should return the deleted pipeline automation', async () => {
      const id = 'd6aab144-7983-4a63-9065-4a2ca2044cbc'
      const result = await pipelineAutomationsRepository.destroyById(id, {})
      expect(result).toEqual({ id, deleted: true })
    })
  })
})
