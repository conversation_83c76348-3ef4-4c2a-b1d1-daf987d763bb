import campaignMessageProgressRepository from '../../../../core/dbSequelize/repositories/campaignMessageProgressRepository'

jest.mock('../../../../core/dbSequelize/repositories/campaignMessageRepository', () => jest.fn())
jest.mock('../../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignMessageRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/BaseRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/messageRepository', () => {
  return {
    getAckCountByCampaignId: () => 10,
  }
})
jest.mock('../../../../core/dbSequelize/repositories/tagRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignRepository', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => {
  return {
    query: async () => [{ count: 10 }],
  }
})
jest.mock('../../../../core/dbSequelize/models/CampaignMessageProgress', () => jest.fn())

describe('CampaignMessageProgressRepository', () => {
  describe('getFiredAnsweredCount', () => {
    it('should return the correct fired answered count', async () => {
      const campaignId = '123'
      const result = await campaignMessageProgressRepository.getFiredAnsweredCount(campaignId)
      expect(result).toBe(10)
    })
  })
})
