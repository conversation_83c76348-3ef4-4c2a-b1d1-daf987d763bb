import creditMovementRepository from '../../../../core/dbSequelize/repositories/creditMovementRepository'

jest.mock('../../../../core/dbSequelize/repositories/campaignMessageRepository', () => jest.fn())
jest.mock('../../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignMessageRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/BaseRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/messageRepository', () => {
  return {
    getAckCountByCampaignId: () => 10,
  }
})
jest.mock('../../../../core/dbSequelize/repositories/tagRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/campaignRepository', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => {
  return {
    query: async () => [{ id: 'abc' }],
    QueryTypes: ['SELECT'],
  }
})
jest.mock('../../../../core/dbSequelize/models/CreditMovement', () => jest.fn())

describe('CreditMovementRepository.test', () => {
  describe('totalServices', () => {
    it('should return paginated result', async () => {
      const from = '2025-01-01'
      const to = '2025-01-30'
      const orders = ['"total" ASC']
      const serviceType = 'summary'
      const page = 1
      const accountId = '123'
      const result = await creditMovementRepository.totalServices(from, to, orders, [], serviceType, page, accountId)
      expect(result?.data?.[0]?.id).toBe('abc')
    })

    test('should throw error', async () => {
      try {
        await creditMovementRepository.totalServices(null, null, null, null, null, null, null)
      } catch (error) {
        expect(error).toEqual(new Error('Invalid param'))
      }
    })

    test('should throw error for service id invalid', async () => {
      try {
        const from = '2025-01-01'
        const to = '2025-01-30'
        const orders = ['"total" ASC']
        const serviceType = 'summary'
        const page = 1
        const accountId = '123'
        await creditMovementRepository.totalServices(from, to, orders, ['#@|-'], serviceType, page, accountId)
      } catch (error) {
        expect(error).toEqual(new Error('The services ids are invalid'))
      }
    })
  })
})
