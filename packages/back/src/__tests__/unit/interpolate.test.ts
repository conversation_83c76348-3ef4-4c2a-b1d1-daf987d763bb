import interpolate from '../../core/utils/interpolate'

describe('interpolate', () => {
  test('simple', () => {
    const actual = interpolate('{{foo}}-bar', {
      foo: 'baz',
    })
    const expected = 'baz-bar'

    expect(actual).toBe(expected)
  })

  test('simple - boolean true', () => {
    const actual = interpolate('{{foo}}', {
      foo: true,
    })
    const expected = 'true'

    expect(actual).toBe(expected)
  })

  test('simple - boolean false', () => {
    const actual = interpolate('{{foo}}', {
      foo: false,
    })
    const expected = 'false'

    expect(actual).toBe(expected)
  })

  test('simple - empty string', () => {
    const actual = interpolate('{{foo}}', {
      foo: '',
    })
    const expected = ''

    expect(actual).toBe(expected)
  })

  describe('modifier', () => {
    test('pad', () => {
      const actual = interpolate('{{count|pad:5}}', {
        count: 1,
      })
      const expected = '00001'

      expect(actual).toBe(expected)
    })

    test('upper', () => {
      const actual = interpolate('{{foo|upper}}', {
        foo: 'bar',
      })
      const expected = 'BAR'

      expect(actual).toBe(expected)
    })

    test('lower', () => {
      const actual = interpolate('{{foo|lower}}', {
        foo: 'Bar',
      })
      const expected = 'bar'

      expect(actual).toBe(expected)
    })

    test('pad and upper', () => {
      const actual = interpolate('{{foo|pad:5|upper}}', {
        foo: 'bar',
      })
      const expected = '00BAR'

      expect(actual).toBe(expected)
    })

    test('pad with replacement char', () => {
      const actual = interpolate('{{count|pad:5,F}}', {
        count: 1,
      })
      const expected = 'FFFF1'

      expect(actual).toBe(expected)
    })

    test('pad with spaces', () => {
      const actual = interpolate('{{ count | pad : 5}}', {
        count: 1,
      })
      const expected = '00001'

      expect(actual).toBe(expected)
    })

    test('pad do not break with weird params', () => {
      ;['{{count|pad:}}', '{{count|pad:,0}}', '{{count|pad}}', '{{count|pad:1,1,1,1,1,1}}'].forEach((str) => {
        const actual = interpolate('{{count|pad:}}', {
          count: 1,
        })
        const expected = '1'

        expect(actual).toBe(expected)
      })
    })

    test('nom existing modifier', () => {
      const actual = interpolate('{{count|foo}}', {
        count: 1,
      })
      const expected = '1'

      expect(actual).toBe(expected)
    })
  })
})
