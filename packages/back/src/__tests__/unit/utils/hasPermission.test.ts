import hasPermission from '../../../core/utils/hasPermission'

describe('hasPermission', () => {
  describe('any', () => {
    test('expect to have ANY of the permissions but has some', () => {
      const expected = true

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['foo', 'nope']
      const actual = hasPermission(userPermissions, expectedPermissions, true)

      expect(actual).toBe(expected)
    })

    test('expect to have ANY of the permissions and has all', () => {
      const expected = true

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['foo', 'bar']
      const actual = hasPermission(userPermissions, expectedPermissions, true)

      expect(actual).toBe(expected)
    })

    test('expect to have ANY of the permissions but has none', () => {
      const expected = false

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['nope']
      const actual = hasPermission(userPermissions, expectedPermissions, true)

      expect(actual).toBe(expected)
    })
  })

  describe('all', () => {
    test('expect to have ALL of the permissions but has some', () => {
      const expected = false

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['foo', 'nope']
      const actual = hasPermission(userPermissions, expectedPermissions)

      expect(actual).toBe(expected)
    })

    test('expect to have ALL of the permissions and has all', () => {
      const expected = true

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['foo', 'bar']
      const actual = hasPermission(userPermissions, expectedPermissions)

      expect(actual).toBe(expected)
    })

    test('expect to have ALL of the permissions but has none', () => {
      const expected = false

      const userPermissions = ['foo', 'bar', 'baz']
      const expectedPermissions = ['nope']
      const actual = hasPermission(userPermissions, expectedPermissions)

      expect(actual).toBe(expected)
    })
  })
})
