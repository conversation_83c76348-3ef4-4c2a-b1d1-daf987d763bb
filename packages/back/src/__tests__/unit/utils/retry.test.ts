import { RetryException } from '../../../core/utils/exception'
import retry from '../../../core/utils/retry'
import wait from '../../../core/utils/wait'
import getRandomInt from '../../../core/utils/getRandomInt'

jest.mock('../../../core/utils/wait')
jest.mock('../../../core/utils/getRandomInt')

describe('retry', () => {
  let mockOperation
  let mockLogger

  beforeEach(() => {
    jest.resetAllMocks()
    mockOperation = jest.fn()
    mockLogger = { log: jest.fn() }
    jest.spyOn(mockLogger, 'log')
    getRandomInt.mockReturnValue(1) // Set this here to control the randomness in your tests
    wait.mockResolvedValue(undefined)
  })

  it('should succeed on first try', async () => {
    mockOperation.mockResolvedValue('success')
    const result = await retry(mockOperation, mockLogger)
    expect(result).toBe('success')
    expect(mockOperation).toHaveBeenCalledTimes(1)
    expect(mockLogger.log).not.toHaveBeenCalledWith(expect.stringContaining('Retrying'))
  })

  it('should retry and succeed on second attempt', async () => {
    mockOperation.mockRejectedValueOnce(new Error('Fail')).mockResolvedValue('success')

    const result = await retry(mockOperation, mockLogger, 3, 5)

    expect(result).toBe('success')
    expect(mockOperation).toHaveBeenCalledTimes(2)
    expect(mockLogger.log).toHaveBeenCalledWith(expect.stringContaining('Retrying in 1 seconds... Attempt 1'))
    expect(wait).toHaveBeenCalledWith(1000)
  })

  it('should throw RetryException after exceeding max attempts', async () => {
    mockOperation.mockRejectedValue(new Error('Fail'))

    await expect(retry(mockOperation, mockLogger, 3, 5)).rejects.toThrow(RetryException)

    expect(mockOperation).toHaveBeenCalledTimes(3)
    expect(mockLogger.log).toHaveBeenCalledWith(expect.stringContaining('Failed after 3 attempts'))
    expect(wait).toHaveBeenCalledTimes(2)
  })

  it('should log appropriate retry messages', async () => {
    mockOperation
      .mockRejectedValueOnce(new Error('Fail'))
      .mockRejectedValueOnce(new Error('Fail again'))
      .mockResolvedValue('finally success')

    const result = await retry(mockOperation, mockLogger, 3, 5)

    expect(result).toBe('finally success')
    expect(mockLogger.log).toHaveBeenCalledWith(expect.stringContaining('Retrying in 1 seconds... Attempt 1'))
    expect(mockLogger.log).toHaveBeenCalledWith(expect.stringContaining('Retrying in 1 seconds... Attempt 2'))
    expect(mockOperation).toHaveBeenCalledTimes(3)
  })
})
