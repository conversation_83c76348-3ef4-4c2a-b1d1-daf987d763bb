import { execSync } from 'child_process'
import 'reflect-metadata'
import fs from 'fs'
import path from 'path'
import { path as projectRoot } from 'app-root-path'
import { Container } from 'typedi'
import sequelize from '../../core/services/db/sequelize'
import seed from './seed'
import setupSequelize from '../../core/services/db/setupSequelize'
import config from '../../core/config'

const dumpsPath = path.resolve(projectRoot, './storage/test-db-dumps')

const dbPassword = config('dbWritePassword')
const dbUsername = config('dbWriteUsername')
const dbDatabase = config('dbDatabase')
const dbHost = config('dbWriteHost')

const dumpDb = (hash) => {
  console.log(`Dumping db to ${dumpsPath}/${hash}.dump`)
  execSync(
    [
      `mkdir -p ${dumpsPath};`,
      `env PGPASSWORD='${dbPassword}'`,
      `pg_dump -Fc -U ${dbUsername}`,
      `-h ${dbHost}`,
      `-f ${dumpsPath}/${hash}.dump ${dbDatabase}`,
    ].join(' '),
  )
}

const restoreDb = (hash) => {
  const cmd = [
    `env PGPASSWORD=${dbPassword}`,
    `psql -h ${dbHost} -U ${dbUsername} ${dbDatabase} -c`,
    '"drop schema public cascade; create schema public;" > /dev/null 2>&1 &&',
    `env PGPASSWORD=${dbPassword}`,
    `pg_restore -h ${dbHost} -U ${dbUsername}`,
    `-d ${dbDatabase} ${dumpsPath}/${hash}.dump`,
  ].join(' ')

  try {
    execSync(cmd, { stdio: 'inherit' })
  } catch (e) {
    console.error(e)
    throw e
  }
}
const dumpExists = (hash) => fs.existsSync(`${dumpsPath}/${hash}.dump`)

const refreshDb = () => {
  console.log('Dropping DB...')
  try {
    execSync('env NODE_ENV=test yarn sequelize db:drop > /dev/null 2>&1')
  } catch (e) {
    // shh
  }
  console.log('DB dropped.')

  console.log('Creating DB...')
  execSync('env NODE_ENV=test yarn sequelize db:create > /dev/null 2>&1')
  console.log('DB created.')

  console.log('Migrating DB...')
  execSync('env NODE_ENV=test yarn sequelize db:migrate --debug > /dev/null 2>&1')
  console.log('DB migrated.')
}

export const setup = async () => {
  Container.set('apm', null)

  const hash = 2

  if (dumpExists(hash)) {
    restoreDb(hash)
    const models = setupSequelize()
    return { models }
  } else {
    jest.setTimeout(1000 * 60 * 3)
  }

  // refreshDb()
  dumpDb(hash)

  console.log('setupSequelize')
  const models = setupSequelize()
  console.log('seed')
  await seed()
  console.log('dumpDb')
  dumpDb(hash)
  console.log('finish')

  return { models }
}

export const teardown = () => sequelize.close()
