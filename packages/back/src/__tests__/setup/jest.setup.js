import 'reflect-metadata'
import { Container } from 'typedi'
import configValue from '../../core/configValues'
import Config from '../../core/services/config/Config'
import { TracerToken } from '../../core/services/tracer/Tracer'
import { GenericTracer } from '../../core/services/tracer/GenericTracer'

// jest.setTimeout(1000 * 60 * 2)

Container.set(TracerToken, Container.get(GenericTracer))

jest.mock('../../core/transformers/messageTransformer', () => jest.fn())
jest.mock('../../core/config', () => ({
  __esModule: true,
  default: jest.fn(),
  setValues: jest.fn(),
  getValues: jest.fn(),
  setValue: jest.fn(),
}))
jest.mock('../../core/services/config/Config', () => jest.fn())
jest.mock('../../core/services/queue/redisTaskQueue', () => jest.fn())
jest.mock('../../core/services/storage', () => ({
  getDriver: jest.fn(),
}))
jest.mock('../../core/services/logs/Logger', () => {
  return {
    __esModule: true,
    default: {
      log: jest.fn(),
    },
  }
})
jest.mock('../../core/services/tracer/Tracer', () => {
  return {
    __esModule: true,
    default: {
      captureError: jest.fn(),
    },
  }
})

jest.mock('typedi', () => {
  const mockTypediContainer = {
    get: jest.fn().mockReturnValue({
      log: jest.fn(),
      captureError: jest.fn(),
      dispatch: jest.fn(),
    }),
    set: jest.fn(),
  }

  return {
    __esModule: true,
    default: mockTypediContainer,
    Container: mockTypediContainer,
    Service: () => () => {},
    Inject: () => () => {},
  }
})
jest.mock('../../core/utils/routing/resourceRouter', () => jest.fn())
jest.mock('../../core/services/logs/reportError', () => jest.fn())
jest.mock('../../core/resources/fileResource', () => jest.fn())
jest.mock('../../core/resources/messageResource', () => ({
  findById: jest.fn(),
}))
jest.mock('../../core/resources/userResource', () => ({
  findById: jest.fn(),
}))
jest.mock('../../core/resources/contactResource', () => ({
  findById: jest.fn(),
}))
jest.mock('../../core/services/driverService', () => ({
  editMessage: jest.fn(),
}))
