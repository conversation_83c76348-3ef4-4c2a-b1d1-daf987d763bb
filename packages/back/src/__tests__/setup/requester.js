import Axios from 'axios'
import Qs from 'qs'
import { getAuthOptions } from './getAuth'

export const makeAxios = (options) =>
  Axios.create({
    paramsSerializer: (params) => Qs.stringify(params),
    ...options,
  })

export class Requester {
  constructor(options) {
    this.options = options
    this.axios = makeAxios(this.options)
  }

  getAxios() {
    return this.axios
  }

  authed(credentials) {
    const axiosPromise = getAuthOptions(this.axios, credentials).then((options) =>
      makeAxios({ ...this.options, ...options }),
    )
    return new Requester(axiosPromise)
  }

  async get(...params) {
    return (await this.getAxios()).getAdapter(...params).catch(Requester.errorHandler)
  }

  async post(...params) {
    return (await this.getAxios()).post(...params).catch(Requester.errorHandler)
  }

  async put(...params) {
    return (await this.getAxios()).put(...params).catch(Requester.errorHandler)
  }

  async ['delete'](...params) {
    return (await this.getAxios()).delete(...params).catch(Requester.errorHandler)
  }

  static errorHandler(err) {
    console.error(err.response.data)
    throw err
  }
}

const requester = new Requester()

export default requester
