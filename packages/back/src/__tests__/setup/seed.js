import oAuthClientResource from '../../core/resources/oAuthClientResource'
import accountResource from '../../core/resources/accountResource'
import roleResource from '../../core/resources/roleResource'
import userResource from '../../core/resources/userResource'
import serviceResource from '../../core/resources/serviceResource'
import savePermissions from '../../scripts/commands/helpers/savePermissions'
import * as fakeResources from '../../core/utils/tests/fakeResources'
import oAuthAccessTokenResource from '../../core/resources/oAuthAccessTokenResource'
import contactResource from '../../core/resources/contactResource'

export default async () => {
  await savePermissions()

  const oAuthClient = await oAuthClientResource.findOne({
    where: {
      clientId: 'api',
    },
  })

  const name = 'Test'

  const account = await accountResource.create({
    name: `Conta ${name}`,
    isActive: true,
    settings: {
      ticketsEnabled: true,
    },
    plan: {
      users: 3,
      services: 1,
      expiredNotificationSent: false,
      expirationNotificationSent: false,
    },
  })

  const adminRole = await roleResource.findOne({
    where: {
      isAdmin: true,
      accountId: account.id,
    },
  })

  const user = await userResource.create({
    name: `Usuário ${name}`,
    email: `user${name}@app.com`,
    password: '********',
    accountId: account.id,
    active: true,
  })

  await user.setRoles([adminRole])

  const service = await serviceResource.create({
    name: `WhatsApp ${name}`,
    data: {
      status: {
        isConnected: true,
      },
    },
    accountId: account.id,
  })

  const contact1 = await contactResource.create(
    {
      name: 'John Doe',
      number: '************',
      idFromService: '<EMAIL>',
      data: {},
      visible: true,
      serviceId: service.id,
      accountId: account.id,
    },
    { skipValidation: true },
  )

  const contact2 = await contactResource.create(
    {
      name: 'Me',
      number: '**********',
      idFromService: '<EMAIL>',
      tagIds: [],
      visible: true,
      serviceId: service.id,
      accountId: account.id,
    },
    { skipValidation: true },
  )

  const oAuthAccessToken = await oAuthAccessTokenResource.create({
    userId: user.id,
    clientId: oAuthClient.id,
  })

  return {
    account,
    service,
    user,
    adminRole,
    contact1,
    contact2,
    oAuthAccessToken,
  }
}
