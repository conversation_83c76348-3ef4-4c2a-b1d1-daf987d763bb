import Qs from 'qs'

export const getAuthTokens = (
  axios,
  { username = '<EMAIL>', password = '12345678', client_id = 'api', client_secret = 'secret' } = {},
) =>
  axios
    .post(
      '/oauth/token',
      Qs.stringify({
        grant_type: 'password',
        username,
        password,
        client_id,
        client_secret,
      }),
      {
        headers: {
          'Content-type': 'application/x-www-form-urlencoded',
        },
      },
    )
    .then((res) => res.data)

export const getAuthAccessToken = (axios, data) => getAuthTokens(axios, data).then((tokens) => tokens.access_token)

export const makeAuthHeader = (accessToken) => ({
  Authorization: `Bearer ${accessToken}`,
  Accept: 'application/json',
})

export const getAuthHeader = (axios, data) => getAuthAccessToken(axios, data).then(makeAuthHeader)

export const getAuthOptions = (axios, data) =>
  getAuthHeader(axios, data).then((authHeader) => ({ headers: authHeader }))
