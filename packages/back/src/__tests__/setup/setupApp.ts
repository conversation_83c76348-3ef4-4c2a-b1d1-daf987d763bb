import Http from 'http'
import Axios, { AxiosInstance } from 'axios'
import { Express } from 'express'
import Qs from 'qs'
import { Sequelize } from 'sequelize'
import app from '../../microServices/api/express'
import models from '../../core/dbSequelize/models'
import sequelize from '../../core/services/db/sequelize'
import getPort from './getPort'
import { getAuthOptions, getAuthTokens, makeAuthHeader } from './getAuth'
import * as setupDb from './setupDb'
import { UserInstance } from '../../core/dbSequelize/models/User'
import RedisClientContainer from '../../core/services/redis/RedisClientContainer'
import { Container } from 'typedi'

const httpListen =
  (http: Http.Server) =>
  (port: number): Promise<Http.Server> =>
    new Promise((resolve) => {
      const server = http.listen(port, () => {
        resolve(server)
      })
    })

export const makeAxios = (options) =>
  Axios.create({
    paramsSerializer: (params) => Qs.stringify(params),
    ...options,
  })

export type App = {
  sequelize: Sequelize
  models: typeof models
  app: Express
  server: Http.Server
  url: string
  axios: AxiosInstance
  authedAxios: AxiosInstance
  authTokens: { access_token: string; refresh_token: string }
  user: UserInstance
}

export async function setup(defaultPort = 7000): Promise<App> {
  const { models } = await setupDb.setup()
  const httpServer = Http.createServer(app)

  const serve = async () => {
    const port = await getPort(defaultPort)
    const url = `http://127.0.0.1:${port}`
    const server = await httpListen(httpServer)(port)

    const options = { baseURL: `${url}/v1` }
    const axios = makeAxios(options)
    const authTokens = await getAuthTokens(axios)
    const authHeaders = makeAuthHeader(authTokens.access_token)
    const authedAxios = makeAxios({ ...options, headers: authHeaders })
    const user = (await authedAxios.get('/me?include[]=account')).data

    return {
      sequelize,
      models,
      app,
      server,
      url,
      axios,
      authedAxios,
      authTokens,
      user,
    }
  }

  return serve()
}

export const teardown = async (app: App) => {
  if (!app) return
  app.server.close()
  Container.get(RedisClientContainer).stop()
  await setupDb.teardown()
}
