import { v4 as uuid } from 'uuid'
import App, { setup } from '../../../../microServices/_+_cemetery/taskQueueManager/App'
import taskQueue, { TimeoutError } from '../../../../core/services/queue/redisTaskQueue'
import wait from '../../../../core/utils/wait'

describe('redisTaskQueue', () => {
  let app: App

  beforeAll(async () => {
    app = await setup()
  })

  afterAll(async () => {
    await app.stop()
  })

  test('basic', async () => {
    const queue = taskQueue('basic-1')

    const randomValue = uuid()
    const res = await queue.run(async () => randomValue)

    expect(res).toBe(randomValue)
  })

  test('with slash on key', async () => {
    const queue = taskQueue('basic/1')

    const randomValue = uuid()
    const res = await queue.run(async () => randomValue)

    expect(res).toBe(randomValue)
  })

  test('nested', async () => {
    const queue = taskQueue('nested-1')

    const randomValue = uuid()
    const res = await queue.run(async () => queue.run(async () => randomValue))

    expect(res).toBe(randomValue)
  })

  test('timeout', async () => {
    const queue = taskQueue('timeout-1', { timeout: 1 })

    const prom = queue.run(async () => wait(5))

    await expect(prom).rejects.toEqual(new TimeoutError())
  })

  test('multi', async () => {
    const queue = taskQueue('multi-1', { timeout: 1 })

    let counter = 0

    const task1 = jest.fn(async () => ++counter)
    const task2 = jest.fn(async () => ++counter)
    const task3 = jest.fn(async () => ++counter)
    const task4 = jest.fn(async () => ++counter)
    const task5 = jest.fn(async () => ++counter)

    const res = await Promise.all([
      queue.run(task1),
      queue.run(task2),
      queue.run(task3),
      queue.run(task4),
      queue.run(task5),
    ])

    expect(counter).toEqual(5)
  })
})
