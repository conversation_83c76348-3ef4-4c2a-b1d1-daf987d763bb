import { Container } from 'typedi'
import RedisCacheStorage from '../../../core/services/cache/RedisCacheStorage'
import Config from '../../../core/services/config/Config'
import configValues from '../../../core/configValues'

describe('RedisCacheStorage', () => {
  let redisCacheStorage: RedisCacheStorage

  beforeAll(() => {
    Container.get(Config).setValues(configValues)
    redisCacheStorage = Container.get(RedisCacheStorage)
  })

  afterAll(async () => {
    await redisCacheStorage.destroy('foo')
  })

  describe('set and get', () => {
    test('string', async () => {
      await redisCacheStorage.set('foo', 'bar')

      expect(await redisCacheStorage.get('foo')).toBe('bar')
    })

    test('number', async () => {
      await redisCacheStorage.set('foo', 1)

      expect(await redisCacheStorage.get('foo')).toBe(1)
    })

    test('boolean', async () => {
      await redisCacheStorage.set('foo', true)

      expect(await redisCacheStorage.get('foo')).toBe(true)
    })

    test('null', async () => {
      await redisCacheStorage.set('foo', null)

      expect(await redisCacheStorage.get('foo')).toBe(null)
    })

    test('object', async () => {
      await redisCacheStorage.set('foo', { bar: 'baz' })

      expect(await redisCacheStorage.get('foo')).toEqual({ bar: 'baz' })
    })
  })
})
