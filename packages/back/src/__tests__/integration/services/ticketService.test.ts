import faker from 'faker'
import { addSeconds } from 'date-fns'
import * as setupDb from '../../setup/setupDb'
import messageRepository from '../../../core/dbSequelize/repositories/messageRepository'
import contactResource from '../../../core/resources/contactResource'
import ticketService from '../../../core/services/ticket/ticketService'
import messageResource from '../../../core/resources/messageResource'
import { MessageInstance } from '../../../core/dbSequelize/models/Message'
import serviceResource from '../../../core/resources/serviceResource'
import ticketResource from '../../../core/resources/ticketResource'
import ticketTransfersResource from '../../../core/resources/ticketTransfersResource'

describe('handleMessageCreated', () => {
  beforeAll(async () => {
    await setupDb.setup()
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  test('open ticket of a message', async () => {
    const service = await serviceResource.findOne({
      include: ['account'],
    })

    const contact = await contactResource.create(
      {
        name: faker.name.firstName(),
        number: faker.phone.phoneNumber(),
        data: {},
        serviceId: service.id,
        accountId: service.account.id,
      },
      { skipValidation: true },
    )

    const timestamp1 = new Date()

    const message1: MessageInstance = await messageRepository.create({
      text: 'message1',
      type: 'chat',
      timestamp: timestamp1,
      contactId: contact.id,
      serviceId: contact.serviceId,
      accountId: contact.accountId,
      data: {
        isNew: true,
      },
    })

    const res = await messageResource.eventTransaction(async (eventTransaction) =>
      ticketService.handleMessageCreated(message1, { eventTransaction }),
    )

    const { opened, ticket } = res
    expect(opened).toBe(true)
    expect(ticket).toBeTruthy()
  })

  test('open ticket of a message and attach the other', async () => {
    const contact = await contactResource.findOne({
      where: { name: 'John Doe' },
    })

    const now = new Date()

    const message1 = await messageRepository.create({
      text: 'message1',
      type: 'chat',
      timestamp: now,
      contactId: contact.id,
      serviceId: contact.serviceId,
      accountId: contact.accountId,
      isFromMe: false,
      data: {
        isNew: true,
      },
      createdAt: now,
    })

    const message2 = await messageRepository.create({
      text: 'message2',
      type: 'chat',
      timestamp: addSeconds(now, 30),
      contactId: contact.id,
      serviceId: contact.serviceId,
      accountId: contact.accountId,
      isFromMe: true,
      data: {
        isNew: true,
      },
      createdAt: addSeconds(now, 30),
    })

    const [res1, res2] = await messageResource.eventTransaction(async (eventTransaction) =>
      Promise.all([
        ticketService.handleMessageCreated(message1, { eventTransaction }),
        ticketService.handleMessageCreated(message2, { eventTransaction }),
      ]),
    )

    expect(res1.opened).toBeTruthy()
    expect(res2.opened).toBeFalsy()

    expect(res1.attached).toBeFalsy()
    expect(res2.attached).toBeTruthy()

    expect(res1.ticket.id).toBe(res2.ticket.id)

    expect(res2.ticket.metrics).toEqual({
      waitingTime: 30,
      messagingTime: 30,
    })
    expect(res2.ticketTransfer.metrics).toEqual({
      waitingTime: 30,
      messagingTime: 30,
    })

    await contactResource.closeTicketById(contact.id, {})

    const updatedTicket = await ticketResource.reload(res1.ticket)
    const updatedTicketTransfer = await ticketTransfersResource.reload(res2.ticketTransfer)

    expect(updatedTicketTransfer.metrics).toEqual({
      ticketTime: 0,
      waitingTime: 30,
      messagingTime: 30,
    })
    expect(updatedTicket.metrics).toEqual({
      ticketTime: 0,
      waitingTime: 30,
      messagingTime: 30,
    })
  })
})
