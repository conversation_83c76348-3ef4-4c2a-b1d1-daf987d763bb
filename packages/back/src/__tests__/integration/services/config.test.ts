import { Container } from 'typedi'
import AppContextCls from '../../../core/services/cls/AppContextCls'
import * as coreConfig from '../../../core/config'
import config from '../../../core/config'

describe('config', () => {
  test('basic', () => {
    coreConfig.setValue('foo', 1)

    expect(config('foo')).toBe(1)
  })

  test('isolated contexts', (done) => {
    expect.assertions(4)

    const appContextCls = Container.get(AppContextCls)

    appContextCls.runAndReturn(() => {
      appContextCls.setContainer('app1')
      coreConfig.setValues({ foo: 1 })

      expect(config('foo')).toBe(1)
    })

    appContextCls.runAndReturn(() => {
      appContextCls.setContainer('app2')
      coreConfig.setValues({ foo: 2 })

      expect(config('foo')).toBe(2)

      setInterval(() => {
        expect(config('foo')).toBe(2)
        done()
      }, 10)
    })

    appContextCls.runAndReturn(() => {
      appContextCls.setContainer('app1')

      expect(config('foo')).toBe(1)
    })
  })
})
