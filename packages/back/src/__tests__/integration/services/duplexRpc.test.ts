import { createServer, Server } from 'http'
import DuplexSocketIoRpcServer from '../../../core/utils/rpcEngine/DuplexSocketIoRpcServer'
import DuplexSocketIoRpcClient from '../../../core/utils/rpcEngine/DuplexSocketIoRpcClient'
import RpcEngineClient from '../../../core/utils/rpcEngine/RpcEngineClient'

const serverMethods = {
  serverMethod1: jest.fn(() => 'hello from server'),
}
const client1Methods = {
  clientMethod1: jest.fn(() => 'hello from client 1'),
}
const client2Methods = {
  clientMethod1: jest.fn(() => 'hello from client 2'),
}

let server: Server
let rpcServer: DuplexSocketIoRpcServer
let rpcClient1: DuplexSocketIoRpcClient
let rpcClient2: DuplexSocketIoRpcClient

beforeAll(async () => {
  const PORT = 3456
  const URL = `ws://127.0.0.1:${PORT}`

  rpcServer = new DuplexSocketIoRpcServer({ debug: false }).setMethods(serverMethods)

  server = createServer()

  rpcServer.start(server)

  await new Promise((resolve) => server.listen(PORT, resolve))

  rpcClient1 = new DuplexSocketIoRpcClient({ debug: false }).setMethods(client1Methods)
  await rpcClient1.start(URL)

  rpcClient2 = new DuplexSocketIoRpcClient().setMethods(client2Methods)
  await rpcClient2.start(URL)
})

afterAll(async () => {
  await rpcClient1.destroy()
  await rpcServer.destroy()
  await new Promise((resolve) => server.close(resolve))
})

beforeEach(() => {
  serverMethods.serverMethod1.mockClear()
  client1Methods.clientMethod1.mockClear()
  client2Methods.clientMethod1.mockClear()
})

test('call server method', async () => {
  const res = await rpcClient1.call('serverMethod1', ['param1'])
  expect(res).toBe('hello from server')

  expect(serverMethods.serverMethod1).toBeCalledTimes(1)

  const { calls } = serverMethods.serverMethod1.mock

  expect(calls[0].length).toBe(2)
  expect(calls[0][0]).toBe('param1')
  expect(calls[0][1]).toMatchObject({
    rpc: expect.any(RpcEngineClient),
    socket: expect.any(Object),
  })
})

test('call non existing server method', async () => {
  const promise = rpcClient1.call('serverMethud')

  await expect(promise).rejects.toMatchSnapshot()
})

test('client1 getAvailableServerMethods()', async () => {
  const result = await rpcClient1.getAvailableServerMethods()

  await expect(result).toEqual(Object.keys(serverMethods))
})

test('server getAvailableClientMethods() 1', async () => {
  const result = await rpcServer.getAvailableClientMethods(rpcServer.getSockets()[0].id)

  await expect(result).toEqual(Object.keys(client1Methods))
})

test('server getAvailableClientMethods() 2', async () => {
  const result = await rpcServer.getAvailableClientMethods(rpcServer.getSockets()[1].id)

  await expect(result).toEqual(Object.keys(client2Methods))
})

test('call non existing client method', async () => {
  const promise = rpcServer.call(rpcServer.getSockets()[0].id, 'clientMethud')

  await expect(promise).rejects.toMatchSnapshot()
})

test('call client method', async () => {
  const res = await rpcServer.call(rpcServer.getSockets()[0].id, 'clientMethod1', ['param1'])
  expect(res).toBe('hello from client 1')

  expect(client1Methods.clientMethod1).toBeCalledTimes(1)

  const { calls } = client1Methods.clientMethod1.mock
  expect(calls[0].length).toBe(1)
  expect(calls[0][0]).toBe('param1')
})
