import { Container } from 'typedi'
import TagCache from '../../../../core/services/redis/TagCache'

describe('TagCache', () => {
  let tagCache: TagCache

  beforeAll(() => {
    tagCache = Container.get(TagCache)
  })

  test('invalidate', async () => {
    const key = 'test-key'
    const tag1 = 'test-tag'

    await tagCache.set(key, 'test', [tag1])
    await expect(tagCache.get(key)).resolves.toBe('test')

    await tagCache.invalidate(tag1)

    await expect(tagCache.get(key)).resolves.toBe(null)
  })
})
