import { Container } from 'typedi'
import { makeIncludeTags } from '../../../core/dbSequelize/repositories/CachedBaseRepository'
import { parseInclude, prepareInclude } from '../../../core/dbSequelize/repositories/BaseSequelizeRepository'
import userResource from '../../../core/resources/userResource'
import * as setupDb from '../../setup/setupDb'
import accountResource from '../../../core/resources/accountResource'
import Config from '../../../core/services/config/Config'
import configValues from '../../../core/configValues'

describe('CachedBaseRepository', () => {
  beforeAll(async () => {
    Container.get(Config).setValues(configValues)
    await setupDb.setup()
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  test('makeIncludeTags', async () => {
    const rawInclude = ['roles', 'account']
    const users = await userResource.findMany({ include: rawInclude })

    const include = parseInclude(userResource.getRepository().getModel(), rawInclude)
    const tags = makeIncludeTags(include, users)

    expect(tags.length).toBe(2)
    expect(tags[0].startsWith('Role')).toBe(true)
    expect(tags[1].startsWith('Account')).toBe(true)
  })
})
