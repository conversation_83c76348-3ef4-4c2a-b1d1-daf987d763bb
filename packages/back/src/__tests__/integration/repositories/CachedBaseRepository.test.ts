import { Container } from 'typedi'
import { makeIncludeTags } from '../../../core/dbSequelize/repositories/CachedBaseRepository'
import { Include, parseInclude } from '../../../core/dbSequelize/repositories/BaseSequelizeRepository'
import userResource from '../../../core/resources/userResource'
import * as setupDb from '../../setup/setupDb'
import Config from '../../../core/services/config/Config'
import configValues from '../../../core/configValues'
import accountResource from '../../../core/resources/accountResource'
import { AccountInstance } from '../../../core/dbSequelize/models/Account'
import { UserInstance } from '../../../core/dbSequelize/models/User'
import { createAccount, createUser } from '../../../core/utils/tests/fakeResources'
import userRepository from '../../../core/dbSequelize/repositories/userRepository'
import TagCache from '../../../core/services/redis/TagCache'
import SpiedFunction = jest.SpiedFunction

describe('CachedBaseRepository', () => {
  let config: Config
  let tagCache: TagCache
  let setSpy: SpiedFunction<TagCache['set']>
  let getSpy: SpiedFunction<TagCache['get']>
  let invalidateSpy: SpiedFunction<TagCache['invalidate']>

  beforeAll(async () => {
    Container.get(Config).setValues(configValues)
    await setupDb.setup()

    config = Container.get(Config)
    tagCache = Container.get(TagCache)
    setSpy = jest.spyOn(tagCache, 'set')
    getSpy = jest.spyOn(tagCache, 'get')
    invalidateSpy = jest.spyOn(tagCache, 'invalidate')
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  beforeEach(async () => {
    setSpy.mockClear()
    getSpy.mockClear()
    invalidateSpy.mockClear()
    await tagCache.invalidateAll()
  })

  describe('makeIncludeTags', () => {
    const rawInclude = ['roles', 'account']

    let account: AccountInstance
    let users: UserInstance[]
    let include: Include[]

    beforeAll(async () => {
      account = await accountResource.findOne()

      users = await userResource.findMany({
        include: rawInclude,
        where: { accountId: account.id },
      })

      include = parseInclude(
        // @ts-ignore
        userResource.getRepository().getModel(),
        rawInclude,
      )
    })

    test('no accountId', async () => {
      const tags = makeIncludeTags(include, users)

      expect(tags).toMatchObject([
        'Role:no-account:list',
        `Role:${users[0].roles[0].id}`,
        'Account:no-account:list',
        `Account:${account.id}`,
      ])
    })

    test('with accountId', async () => {
      const tags = makeIncludeTags(include, users, undefined, account.id)

      expect(tags).toMatchObject([
        `Role:${account.id}:list`,
        `Role:${users[0].roles[0].id}`,
        `Account:${account.id}:list`,
        `Account:${account.id}`,
      ])
    })

    test('with whitelist and accountId', async () => {
      const tags = makeIncludeTags(include, users, ['roles'], account.id)

      expect(tags).toMatchObject([`Role:${account.id}:list`, `Role:${users[0].roles[0].id}`])
    })
  })

  test('query with accountId and invalidate on update', async () => {
    const account1 = await createAccount()
    const account2 = await createAccount()
    const user1 = await createUser({
      accountId: account1.id,
    })
    const user11 = await createUser({
      accountId: account1.id,
    })
    const user2 = await createUser({
      accountId: account2.id,
    })
    const version = config.get<string>('version')

    const queryUsers = (accountId: string) =>
      userRepository.findMany({
        where: { accountId },
        cache: true,
      })

    // account 1 is cached
    getSpy.mockClear()
    await queryUsers(account1.id)
    expect(getSpy).toHaveBeenCalledTimes(1)
    expect(setSpy).toHaveBeenLastCalledWith(expect.anything(), expect.anything(), [
      `${version}:User:${user1.id}`,
      `${version}:User:${user11.id}`,
      `${version}:User:${account1.id}:list`,
    ])

    // account 2 is cached
    getSpy.mockClear()
    await queryUsers(account2.id)
    expect(getSpy).toHaveBeenCalledTimes(1)
    expect(setSpy).toHaveBeenLastCalledWith(expect.anything(), expect.anything(), [
      `${version}:User:${user2.id}`,
      `${version}:User:${account2.id}:list`,
    ])

    // update user 1 from account 1
    invalidateSpy.mockClear()
    await userRepository.updateById(user1.id, { name: 'test' })
    expect(invalidateSpy).toHaveBeenCalledTimes(1)

    // account1 have been invalidated
    setSpy.mockClear()
    await queryUsers(account1.id)
    expect(setSpy).toHaveBeenCalled()

    // account1 have NOT been invalidated
    setSpy.mockClear()
    await queryUsers(account2.id)
    expect(setSpy).not.toHaveBeenCalled()
  })
})
