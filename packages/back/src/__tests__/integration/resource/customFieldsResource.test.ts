import * as setupDb from '../../setup/setupDb'
import accountResource from '../../../core/resources/accountResource'
import serviceResource from '../../../core/resources/serviceResource'
import contactResource from '../../../core/resources/contactResource'
import sequelize from '../../../core/services/db/sequelize'
import customFieldsResource from '../../../core/resources/customFieldsResource'

describe('Custom Fields Resource', () => {
  beforeAll(async () => {
    await setupDb.setup()
  })

  afterAll(async () => {
    await sequelize.query(`DELETE FROM custom_fields`)
    await setupDb.teardown()
  })

  beforeEach(async () => {
    await sequelize.query(`DELETE FROM custom_fields`)
  })

  test('Try to create custom field', async () => {
    const account = await accountResource.findOne()

    await customFieldsResource.create({
      type: 'text',
      allowed: 'contacts',
      name: 'Campo Customizado',
      accountId: account.id,
    })

    const customField = await customFieldsResource.findOne()
    expect(customField.name).toBe('Campo Customizado')
  })
})
