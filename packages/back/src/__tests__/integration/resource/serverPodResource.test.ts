import * as setupDb from '../../setup/setupDb'
import serverPodTypeResource from '../../../core/resources/serverPodTypeResource'
import serverPodResource from '../../../core/resources/serverPodResource'
import { ServerPodTypeInstance } from '../../../core/dbSequelize/models/ServerPodType'
import serviceResource from '../../../core/resources/serviceResource'
import { ServiceInstance } from '../../../core/dbSequelize/models/Service'
import { ServerPodInstance } from '../../../core/dbSequelize/models/ServerPod'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'

jest.setTimeout(5 * 60 * 1000)

describe('serverPodResource', () => {
  let service: ServiceInstance
  let serverPodTypes: ServerPodTypeInstance[]
  let serverPods: ServerPodInstance[]

  beforeEach(async () => {
    await setupDb.setup()
    service = await serviceResource.findOne()

    const serverPodTypesDatas = [
      {
        name: 'type1',
        cloud: 'foo',
        serverSize: '1',
        maxPerPod: 2,
        startPriority: 2,
      },
      {
        name: 'type2',
        cloud: 'foo',
        serverSize: '1',
        maxPerPod: 2,
        startPriority: 1,
      },
    ]
    serverPodTypes = await queuedAsyncMap(serverPodTypesDatas, (data) => serverPodTypeResource.create(data))

    const serverPodDatas = [
      {
        name: 'pod1',
        address: 'localhost:1',
        serverPodTypeId: serverPodTypes[0].id,
      },
      {
        name: 'pod2',
        address: 'localhost:2',
        serverPodTypeId: serverPodTypes[0].id,
      },
      {
        name: 'pod3',
        address: 'localhost:3',
        serverPodTypeId: serverPodTypes[1].id,
      },
    ]
    serverPods = await queuedAsyncMap(serverPodDatas, (data) => serverPodResource.create(data))

    const toDelete = await serverPodResource.create({
      name: 'deleted1',
      address: 'localhost:4',
      serverPodTypeId: serverPodTypes[0].id,
    })
    await serverPodResource.destroy(toDelete)
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  test('getAvailable', async () => {
    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)

    expect(serverPod.id).toBe(serverPods[0].id)
  })

  test('getAvailable with serviceIdWhitelist', async () => {
    const type = await serverPodTypeResource.create({
      name: 'whitelisted-type',
      cloud: 'foo',
      serverSize: '1',
      maxPerPod: 2,
      startPriority: 0,
      serviceIdWhitelist: [],
    })
    const sp = await serverPodResource.create({
      name: 'whitelisted-server-pod',
      address: 'localhost:5',
      serverPodTypeId: type.id,
    })
    await serverPodTypeResource.update(type, {
      serviceIdWhitelist: [service.id],
    })

    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)

    expect(serverPod.id).toBe(sp.id)
  })

  test('getAvailable with accountIdWhitelist', async () => {
    const type = await serverPodTypeResource.create({
      name: 'whitelisted-type',
      cloud: 'foo',
      serverSize: '1',
      maxPerPod: 2,
      startPriority: 0,
      serviceIdWhitelist: [],
      accountIdWhitelist: [],
    })
    const sp = await serverPodResource.create({
      name: 'whitelisted-server-pod',
      address: 'localhost:5',
      serverPodTypeId: type.id,
    })
    await serverPodTypeResource.update(type, {
      accountIdWhitelist: [service.accountId],
    })

    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)

    expect(serverPod.id).toBe(sp.id)
  })

  test('setService', async () => {
    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)

    const hasSet = await serverPodResource.setService(serverPod.id, service.id)

    expect(hasSet).toBeTruthy()
  })

  test('removeService', async () => {
    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)

    await serverPodResource.setService(serverPod.id, service.id)

    const hasSet = await serverPodResource.removeService(serverPod.id, service.id)

    expect(hasSet).toBeTruthy()
  })

  test('getSlotsForType', async () => {
    const serverPod = await serverPodResource.getAvailable(service.id, service.accountId)
    await serverPodResource.setService(serverPod.id, service.id)

    await expect(serverPodResource.getSlotsForType('type1')).resolves.toMatchObject({
      total: 4,
      used: 1,
      remaining: 3,
    })

    await expect(serverPodResource.getSlotsForType('type2')).resolves.toMatchObject({
      total: 2,
      used: 0,
      remaining: 2,
    })
  })

  test('getServerPodToDestroy', async () => {
    await expect(serverPodResource.getServerPodToDestroy('type1')).resolves.toMatchObject({
      id: serverPods[1].id,
      name: serverPods[1].name,
    })
  })
})
