import { Container } from 'typedi'
import knowledgeBaseItemResource from '../../../core/resources/knowledgeBaseItemResource'
import knowledgeBaseItemRepository from '../../../core/dbSequelize/repositories/knowledgeBaseItemRepository'
import knowledgeBaseItemDocRepository from '../../../core/dbSequelize/repositories/knowledgeBaseItemDocRepository'
import fileResource from '../../../core/resources/fileResource'
import HaystackIaApi from '../../../core/services/haystackIa'

jest.mock('../../../core/dbSequelize/repositories/knowledgeBaseItemDocRepository', () => {
  return {
    bulkCreate: jest.fn(),
    findMany: () => [{ docId: 'doc1' }],
  }
})

jest.mock('../../../core/dbSequelize/repositories/knowledgeBaseItemRepository', () => ({
  findById: () => ({
    id: '1',
    accountId: 'account1',
    docs: [{ docId: 'doc1' }],
    dataValues: { id: '1', accountId: 'account1' },
  }),
  create: () => ({ id: '1', accountId: 'account1', sourceId: 'source1' }),
  destroy: jest.fn(),
}))

jest.mock('../../../core/resources/BaseResource', () => jest.fn())
jest.mock('../../../core/resources/messageResource', () => jest.fn())
jest.mock('../../../core/resources/fileResource', () => {
  return {
    findById: () => ({ name: 'file1' }),
    getBuffer: () => Buffer.from('file content'),
    updateById: jest.fn(),
  }
})

describe('KnowledgeBaseItemResource', () => {
  afterEach(() => {
    jest.clearAllMocks()
  })

  test('findById should return knowledge without docs', async () => {
    const haystackIaApi = Container.get(HaystackIaApi)
    jest.spyOn(haystackIaApi, 'findKnowledge').mockResolvedValue(['doc1', 'doc2'])
    const mockKnowledge = { id: '1', accountId: 'account1' }
    jest.spyOn(knowledgeBaseItemRepository, 'findById').mockResolvedValue(mockKnowledge)
    const result = await knowledgeBaseItemResource.findById('1')
    expect(result).toEqual(mockKnowledge)
  })

  test('findById should return knowledge with docs', async () => {
    const haystackIaApi = Container.get(HaystackIaApi)
    jest.spyOn(haystackIaApi, 'findKnowledge').mockResolvedValue(['doc1', 'doc2'])
    const mockKnowledge = { id: '1', accountId: 'account1', docs: ['doc1', 'doc2'] }
    jest.spyOn(knowledgeBaseItemRepository, 'findById').mockResolvedValue(mockKnowledge)
    const result = await knowledgeBaseItemResource.findById('1')
    expect(result).toEqual({ docs: ['doc1', 'doc2'] })
  })

  test('create should create knowledge base item with docs', async () => {
    const mockData = { fileId: 'file1', name: 'test', accountId: 'account1' }
    const mockKnowledge = { id: '1', accountId: 'account1', sourceId: 'source1' }
    const mockDocIds = ['doc1', 'doc2']
    const haystackIaApi = Container.get(HaystackIaApi)
    jest.spyOn(haystackIaApi, 'createKnowledge').mockResolvedValue({ docIds: ['doc1', 'doc2'], sourceId: 'source1' })

    const result = await knowledgeBaseItemResource.create(mockData)
    expect(result).toEqual(mockKnowledge)
    expect(knowledgeBaseItemDocRepository.bulkCreate).toHaveBeenCalledWith(
      mockDocIds.map((docId) => ({ knowledgeBaseItemId: mockKnowledge.id, docId, accountId: mockKnowledge.accountId })),
      {},
    )
    expect(fileResource.updateById).toHaveBeenCalledWith(mockData.fileId, {
      attachedId: mockKnowledge.id,
      attachedType: 'knowledgebase.item',
    })
  })

  test('create should throw error on empty data', async () => {
    const mockData = { name: 'test', accountId: 'account1' }
    await expect(knowledgeBaseItemResource.create(mockData)).rejects.toThrow('You must provide fileId, text, or url')
  })

  test('destroyById should delete knowledge base item and docs', async () => {
    const haystackIaApi = Container.get(HaystackIaApi)
    jest.spyOn(haystackIaApi, 'deleteKnowledgeDocs').mockResolvedValue({})

    await knowledgeBaseItemResource.destroyById('1')
    expect(haystackIaApi.deleteKnowledgeDocs).toHaveBeenCalled()
    expect(knowledgeBaseItemRepository.destroy).toHaveBeenCalledWith(
      { accountId: 'account1', docs: ['doc1', 'doc2'], id: '1' },
      {},
    )
  })

  test('getSource should return file source', async () => {
    const mockData = { fileId: 'file1' }
    const mockFile = { name: 'file1' }
    const mockBuffer = Buffer.from('file content')

    const result = await knowledgeBaseItemResource.getSource(mockData)
    expect(result).toEqual({ name: mockFile.name, source: mockBuffer })
  })

  test('getSource should return text source', async () => {
    const mockData = { text: 'test text' }

    const result = await knowledgeBaseItemResource.getSource(mockData)
    expect(result).toEqual({ source: mockData.text })
  })
})
