import * as setupDb from '../../setup/setupDb'
import fileResource from '../../../core/resources/fileResource'
import mockBase64Image from '../../../core/utils/tests/base64Image'
import accountResource from '../../../core/resources/accountResource'
import config, { setValue as setConfigValue } from '../../../core/config'
import formatBase64Url from '../../../core/utils/base64/formatBase64Url'
import base64ToBuffer from '../../../core/utils/base64/base64ToBuffer'
import bufferToReadStream from '../../../core/utils/stream/bufferToReadStream'

let account

beforeAll(async () => {
  await setupDb.setup()
  account = await accountResource.findOne()
})

afterAll(async () => {
  await setupDb.teardown()
})

const runTests = () => {
  test('create with base64', async () => {
    const file = await fileResource.create({
      name: 'bus.gif',
      base64: mockBase64Image.base64,
      mimetype: mockBase64Image.mimetype,
      accountId: account.id,
    })

    expect(file).toMatchObject({
      isEncrypted: false,
      mimetype: 'image/gif',
      extension: 'gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
      attachedId: null,
      attachedType: null,
      publicFilename: 'bus.gif',
      filepath: `${file.accountId}/${file.id}.${file.extension}`,
      url: `${config('publicUrl')}/files/${file.accountId}/${file.id}.${file.extension}`,
    })
  })

  test('create with base64Url', async () => {
    const file = await fileResource.create({
      name: 'bus.gif',
      base64Url: formatBase64Url(mockBase64Image.base64, mockBase64Image.mimetype),
      accountId: account.id,
    })

    expect(file).toMatchObject({
      isEncrypted: false,
      mimetype: 'image/gif',
      extension: 'gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
      attachedId: null,
      attachedType: null,
      publicFilename: 'bus.gif',
      filepath: `${file.accountId}/${file.id}.${file.extension}`,
      url: `${config('publicUrl')}/files/${file.accountId}/${file.id}.${file.extension}`,
    })
  })

  test('create with buffer', async () => {
    const file = await fileResource.create({
      name: 'bus.gif',
      data: base64ToBuffer(mockBase64Image.base64),
      accountId: account.id,
    })

    expect(file).toMatchObject({
      isEncrypted: false,
      mimetype: 'image/gif',
      extension: 'gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
      attachedId: null,
      attachedType: null,
      publicFilename: 'bus.gif',
      filepath: `${file.accountId}/${file.id}.${file.extension}`,
      url: `${config('publicUrl')}/files/${file.accountId}/${file.id}.${file.extension}`,
    })
  })

  test('create with base64 encrypted', async () => {
    const file = await fileResource.create({
      name: 'bus.gif',
      base64: mockBase64Image.base64,
      mimetype: mockBase64Image.mimetype,
      accountId: account.id,
      encrypt: true,
    })

    expect(file).toMatchObject({
      isEncrypted: true,
      mimetype: 'image/gif',
      extension: 'gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
      attachedId: null,
      attachedType: null,
      publicFilename: 'bus.gif',
      filepath: `${file.accountId}/${file.id}.${file.extension}`,
      url: `${config('publicUrl')}/files/${file.accountId}/${file.id}.${file.extension}`,
    })
  })

  test('create with stream', async () => {
    const file = await fileResource.create({
      name: 'bus.gif',
      data: bufferToReadStream(base64ToBuffer(mockBase64Image.base64)),
      mimetype: 'image/gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
    })

    expect(file).toMatchObject({
      isEncrypted: false,
      mimetype: 'image/gif',
      extension: 'gif',
      checksum: '07cd7d7e08947f4620044cbc00b184fa17c5f157',
      accountId: account.id,
      attachedId: null,
      attachedType: null,
      publicFilename: 'bus.gif',
      filepath: `${file.accountId}/${file.id}.${file.extension}`,
      url: `${config('publicUrl')}/files/${file.accountId}/${file.id}.${file.extension}`,
    })
  })
}

describe('fs driver', () => {
  beforeAll(() => {
    setConfigValue('storageDriver', 'fs')
  })

  runTests()
})

describe('s3 driver', () => {
  beforeAll(() => {
    setConfigValue('storageDriver', 's3')
  })

  runTests()
})

describe('wpLvS3 driver', () => {
  beforeAll(() => {
    setConfigValue('storageDriver', 'wpLvS3')
  })

  runTests()
})
