import { Container } from 'typedi'
import * as setupApp from '../../../../setup/setupApp'
import RequestContextCls from '../../../../../core/services/cls/RequestContextCls'
import oAuthAccessTokenResource from '../../../../../core/resources/oAuthAccessTokenResource'

const setContextMock = jest.fn()

class RequestContextClsMock extends RequestContextCls {
  setContext(context: { userId?: string; accountId?: string; traceId?: string }, merge: boolean = false): any {
    setContextMock(context)
    return super.setContext(context, merge)
  }
}
Container.set(RequestContextCls, Container.get(RequestContextClsMock))

describe('userSetter', () => {
  let app: setupApp.App

  beforeAll(async () => {
    app = await setupApp.setup()
  })

  afterAll(async () => {
    await setupApp.teardown(app)
  })

  test('sets RequestContextCls context correctly for POST /me', async () => {
    const accessToken = await oAuthAccessTokenResource.findOne({
      where: { accessToken: app.authTokens.access_token },
    })

    setContextMock.mockClear()

    await app.authedAxios.get('/me')

    expect(setContextMock.mock.calls[1][0]).toMatchObject({
      userId: app.user.id,
      accountId: app.user.accountId,
      accessTokenId: accessToken.id,
    })
  })
})
