import { Container } from 'typedi'
import SocketReceiverJob from '../../../../microServices/workers/jobs/socket/SocketReceiverJob'
import * as setupDb from '../../../setup/setupDb'
import userResource from '../../../../core/resources/userResource'
import oAuthAccessTokenResource from '../../../../core/resources/oAuthAccessTokenResource'
import RedisCacheStorage from '../../../../core/services/cache/RedisCacheStorage'

describe('SocketReceiverJob', () => {
  beforeAll(async () => {
    await setupDb.setup()
    Container.import([RedisCacheStorage])
  })

  afterAll(async () => {
    await setupDb.teardown()
    Container.reset()
  })

  test('$handshake', async () => {
    const user = await userResource.findOne()
    const accessToken = await oAuthAccessTokenResource.findOne({
      where: { userId: user.id },
    })

    const job = Container.get(SocketReceiverJob)

    const payload = {
      gatewayAddress: 'gatewayAddress1',
      socketId: 'socketId1',
      event: '$handshake',
      data: {
        headers: {},
        method: 'GET',
        query: {
          access_token: accessToken.accessToken,
        },
      },
    }
    await job.handle(payload)

    await job.handle({
      gatewayAddress: 'gatewayAddress1',
      socketId: 'socketId1',
      event: '$disconnect',
    })
  })
})
