import { Container, Service } from 'typedi'
import SocketSenderJob from '../../../../microServices/workers/jobs/socket/SocketSenderJob'
import { Event } from '../../../../microServices/workers/jobs/socket/socketSenderHandlers/BaseSocketSenderHandler'
import JobsDispatcher from '../../../../core/services/jobs/interfaces/JobsDispatcher'
import RedisCacheStorage from '../../../../core/services/cache/RedisCacheStorage'
import SendBulkSocketJob from '../../../../microServices/workers/jobs/socket/socketSenderHandlers/send/SendBulkSocketJob'
import SocketReceiverJob from '../../../../microServices/workers/jobs/socket/SocketReceiverJob'
import userResource from '../../../../core/resources/userResource'
import oAuthAccessTokenResource from '../../../../core/resources/oAuthAccessTokenResource'
import * as setupDb from '../../../setup/setupDb'
import { UserInstance } from '../../../../core/dbSequelize/models/User'
import { OAuthAccessTokenInstance } from '../../../../core/dbSequelize/models/OAuthAccessToken'

const dispatchMock = jest.fn()

@Service()
class JobsDispatcherMock implements JobsDispatcher {
  dispatch = dispatchMock
}

describe('SocketSenderJob', () => {
  let user: UserInstance
  let accessToken: OAuthAccessTokenInstance
  let socketReceiverJob: SocketReceiverJob

  beforeAll(async () => {
    await setupDb.setup()
    Container.import([RedisCacheStorage, JobsDispatcherMock])

    socketReceiverJob = Container.get(SocketReceiverJob)

    user = await userResource.findOne()
    accessToken = await oAuthAccessTokenResource.findOne({
      where: { userId: user.id },
    })
    await socketReceiverJob.handle({
      gatewayAddress: 'gatewayAddress1',
      socketId: 'socketId1',
      event: '$handshake',
      data: {
        headers: {},
        method: 'GET',
        query: {
          access_token: accessToken.accessToken,
        },
      },
    })
  })

  afterAll(async () => {
    await socketReceiverJob.handle({
      gatewayAddress: 'gatewayAddress1',
      socketId: 'socketId1',
      event: '$disconnect',
    })

    await setupDb.teardown()
    Container.reset()
  })

  test('handle message.created', async () => {
    const socketSenderJob = Container.get(SocketSenderJob)

    await socketSenderJob.handle({
      event: 'message.created',
      data: {
        id: 'id1',
        accountId: user.accountId,
      },
    })

    expect(dispatchMock).toBeCalledWith(SendBulkSocketJob, {
      data: {
        accountId: user.accountId,
        id: 'id1',
      },
      event: 'message.created',
      socketTuples: [
        {
          gatewayAddress: 'gatewayAddress1',
          socketId: 'socketId1',
        },
      ],
    })
  })
})
