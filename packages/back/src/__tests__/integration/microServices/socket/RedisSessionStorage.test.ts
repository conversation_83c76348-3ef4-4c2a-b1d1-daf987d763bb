import { Container } from 'typedi'
import RedisSessionStorage from '../../../../microServices/workers/jobs/socket/RedisSessionStorage'
import RedisCacheStorage from '../../../../core/services/cache/RedisCacheStorage'

describe('RedisSessionStorage', () => {
  beforeAll(() => {
    Container.import([RedisCacheStorage])
  })

  afterAll(() => {
    Container.reset()
  })

  test('addSocketToUser, getUserSockets, getAccountSockets, removeSocketFromUser', async () => {
    const redisSessionStorage = Container.get(RedisSessionStorage)

    const gatewayAddress = 'address1'
    const socketId = 'socket1'
    const userId = 'user1'
    const accountId = 'account1'

    await redisSessionStorage.addSocketToUser({
      gatewayAddress,
      socketId,
      userId,
      accountId,
    })

    const actualUserSocketsRes = await redisSessionStorage.getUserSockets(userId)
    const expectedUserSocketsRes = [{ gatewayAddress, socketId }]
    expect(actualUserSocketsRes).toMatchObject(expectedUserSocketsRes)

    const actualAccountSocketsRes = await redisSessionStorage.getAccountSockets(accountId)
    const expectedAccountSocketsRes = [{ gatewayAddress, socketId }]
    expect(actualAccountSocketsRes).toMatchObject(expectedAccountSocketsRes)

    await redisSessionStorage.removeSocketFromUser({ socketId, gatewayAddress })

    expect(await redisSessionStorage.getUserSockets(userId)).toMatchObject([])
  })
})
