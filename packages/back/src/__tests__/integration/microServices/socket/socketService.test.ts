import Http, { Server } from 'http'
import 'reflect-metadata'
import { after } from 'lodash'
import socketIoClient from 'socket.io-client'
import { timeout } from 'promise-timeout'
import SocketService from '../../../../microServices/_+_cemetery/socket/SocketService'
import oAuthServer from '../../../../core/services/auth/oAuthServer'
import * as setupDb from '../../../setup/setupDb'
import * as fakeResources from '../../../../core/utils/tests/fakeResources'
import { AccountInstance } from '../../../../core/dbSequelize/models/Account'
import { ContactInstance } from '../../../../core/dbSequelize/models/Contact'
import { ServiceInstance } from '../../../../core/dbSequelize/models/Service'
import oAuthAccessTokenResource from '../../../../core/resources/oAuthAccessTokenResource'
import contactResource from '../../../../core/resources/contactResource'
import messageTransformer from '../../../../core/transformers/messageTransformer'
import { UserInstance } from '../../../../core/dbSequelize/models/User'
import contactTransformer from '../../../../core/transformers/contactTransformer'
import wait from '../../../../core/utils/wait'
import startTaskQueueManager from '../../../../microServices/_+_cemetery/taskQueueManager/start'

const clean = (obj) => JSON.parse(JSON.stringify(obj))

const waitForSocketConnect = async (socket) => {
  await new Promise((resolve, reject) => {
    socket.on('connect', resolve)
    socket.on('connect_error', reject)
    socket.on('connect_timeout', reject)
    socket.on('error', reject)
  })

  socket.emit('init', {})

  await new Promise((resolve) => socket.on('init_success', resolve))
}

describe('socketio', () => {
  let http: Server
  let socketService: SocketService
  let createSocket: (namespace: string) => any
  let account: AccountInstance
  let contact: ContactInstance
  let service: ServiceInstance
  let user: UserInstance
  let taskQueueManagerApp

  beforeAll(async () => {
    taskQueueManagerApp = await startTaskQueueManager()
    await setupDb.setup()

    contact = await contactResource.findOne({ include: ['account', 'service'] })
    service = contact.service
    account = contact.account

    const oAuthAccessToken = await oAuthAccessTokenResource.findOne({
      include: [
        {
          model: 'user',
          include: ['roles.permissions', 'departments'],
        },
      ],
    })
    user = oAuthAccessToken.user

    http = Http.createServer()
    socketService = new SocketService()

    await socketService.start(http, oAuthServer)

    const PORT = 50837

    await new Promise((resolve) => http.listen(PORT, resolve))

    const url = `http://localhost:${PORT}`

    createSocket = (namespace) =>
      socketIoClient(`${url}${namespace}`, {
        query: { access_token: oAuthAccessToken.accessToken },
        transports: ['websocket'],
        upgrade: false,
      })
  })

  afterAll(async () => {
    await socketService.destroy()
    await http.close()
    await setupDb.teardown()
    await taskQueueManagerApp.stop()
  })

  test('messages.created', async () => {
    const socket = createSocket('/messages')

    await waitForSocketConnect(socket)

    const [event, message] = await Promise.all([
      timeout(new Promise((resolve) => socket.on('created', resolve)), 100),
      fakeResources.createMessage({
        contactId: contact.id,
        accountId: account.id,
        contact,
      }),
    ])

    expect(event).toMatchObject({
      transactionId: expect.any(String),
      transactionSize: 1,
      data: clean(await messageTransformer(message, { user, account })),
    })
  })

  test('contacts.created', async () => {
    const socket = createSocket('/contacts')

    await waitForSocketConnect(socket)

    const [event, contact] = await Promise.all([
      timeout(new Promise((resolve) => socket.on('created', resolve)), 50),
      fakeResources.createContact({
        accountId: account.id,
        serviceId: service.id,
      }),
    ])

    expect(event).toMatchObject({
      transactionId: expect.any(String),
      transactionSize: 1,
      data: clean(await contactTransformer(contact, { user, account })),
    })
  })

  test('contacts.created transaction', async () => {
    const socket = createSocket('/contacts')

    await waitForSocketConnect(socket)

    const contact1Data = fakeResources.makeContact({
      accountId: account.id,
      serviceId: service.id,
    })
    const contact2Data = fakeResources.makeContact({
      accountId: account.id,
      serviceId: service.id,
    })

    const contact1 = await contactResource.create(contact1Data, {
      dontEmit: true,
    })
    const contact2 = await contactResource.create(contact2Data, {
      dontEmit: true,
    })

    const [event1, event2] = await Promise.all([
      timeout(new Promise((resolve) => socket.on('created', resolve)), 50),
      timeout(new Promise((resolve) => socket.on('created', resolve)), 50),
      wait(1).then(() => contactResource.emit('created', contact1)),
      wait(2).then(() => contactResource.emit('created', contact2)),
    ])

    expect(event1).toMatchObject({
      transactionId: expect.any(String),
      transactionSize: 2,
    })
    expect(event2).toMatchObject({
      transactionId: expect.any(String),
      transactionSize: 2,
    })
  })
})
