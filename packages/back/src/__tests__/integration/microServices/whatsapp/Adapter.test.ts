import * as config from '../../../../core/config'
import Adapter from '../../../../microServices/whatsapp/thread/adapter/Adapter'
import * as setupDb from '../../../setup/setupDb'
import serviceResource from '../../../../core/resources/serviceResource'
import { ServiceInstance } from '../../../../core/dbSequelize/models/Service'
import { ContactInstance } from '../../../../core/dbSequelize/models/Contact'
import contactResource from '../../../../core/resources/contactResource'
import messageResource from '../../../../core/resources/messageResource'
import ticketResource from '../../../../core/resources/ticketResource'
import wait from '../../../../core/utils/wait'
import contactTransformer from '../../../../core/transformers/contactTransformer'
import accountResource from '../../../../core/resources/accountResource'
import { UserInstance } from '../../../../core/dbSequelize/models/User'
import userResource from '../../../../core/resources/userResource'
import { AccountInstance } from '../../../../core/dbSequelize/models/Account'

describe('Adapter', () => {
  let account: AccountInstance
  let service: ServiceInstance
  let contact: ContactInstance
  let user: UserInstance

  beforeAll(async () => {
    await setupDb.setup()
    account = await accountResource.findOne()
    service = await serviceResource.findOne({
      where: { accountId: account.id },
    })
    contact = await contactResource.findOne({
      where: { accountId: account.id },
    })
    user = await userResource.findOne({
      where: { accountId: account.id },
      include: ['departments', 'roles.permissions'],
    })
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  test('sendAndSave - with right read permission', async () => {
    config.setValue('isFrontTest', true)

    const onContactUpdatedPromise = new Promise((resolve) => contactResource.onUpdated(resolve))
    const onTicketCreatedPromise = new Promise((resolve) => ticketResource.onCreated(resolve))
    const onMessageCreatedPromise = new Promise((resolve) => messageResource.onCreated(resolve))

    const adapter = new Adapter(service.id)

    await adapter.setup()
    await adapter.start()

    await adapter.sendAndSave({
      message: {
        text: 'test1',
      },
      contact,
    })

    await onContactUpdatedPromise
    await onTicketCreatedPromise
    await onMessageCreatedPromise

    const c1 = await contactResource.findById(contact.id, {
      include: [
        {
          model: 'lastMessage',
          include: [
            {
              model: 'ticket',
              attributes: ['id'],
              include: [
                {
                  model: 'ticketTransfers',
                  attributes: ['id', 'toUserId', 'toDepartmentId'],
                },
              ],
            },
          ],
        },
      ],
    })

    expect(c1.lastMessage).toBeTruthy()
    expect(c1.lastMessage.ticket.ticketTransfers).toBeTruthy()

    const formattedContact = await contactTransformer(c1, { account, user })

    expect(formattedContact.lastMessage.text).toBe('test1')
  })
})
