import { Container } from 'typedi'
import * as setupDb from '../../../../setup/setupDb'
import Webhook<PERSON>enderJob from '../../../../../microServices/workers/jobs/webhook/WebhookSenderJob'
import webhookResource from '../../../../../core/resources/webhookResource'
import serviceResource from '../../../../../core/resources/serviceResource'
import { WebhookInstance } from '../../../../../core/dbSequelize/models/Webhook'
import { ServiceInstance } from '../../../../../core/dbSequelize/models/Service'
import userResource from '../../../../../core/resources/userResource'
import accountResource from '../../../../../core/resources/accountResource'
import TagCache from '../../../../../core/services/redis/TagCache'
import SpiedFunction = jest.SpiedFunction
import roleResource from '../../../../../core/resources/roleResource'

jest.setTimeout(5 * 60 * 1000)

describe('WebhookSenderJob', () => {
  beforeAll(async () => {
    await setupDb.setup()
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  describe('whatsapp_low_level', () => {
    let service: ServiceInstance
    let webhook1: WebhookInstance
    let webhook2: WebhookInstance
    let webhook3: WebhookInstance

    beforeAll(async () => {
      const account = await accountResource.findOne()
      service = await serviceResource.create({
        name: 'service-test-1',
        type: 'whatsapp',
        accountId: account.id,
      })
      const service2 = await serviceResource.create({
        name: 'service-test-2',
        type: 'whatsapp',
        accountId: account.id,
      })
      const user = await userResource.findOne({
        where: {
          accountId: service.accountId,
        },
      })
      webhook1 = await webhookResource.create({
        name: 'webhook-test-1',
        type: 'whatsapp_low_level',
        events: ['service.updated'],
        services: [service],
        accountId: service.accountId,
        userId: user.id,
      })
      webhook2 = await webhookResource.create({
        name: 'webhook-test-2',
        events: ['service.updated'],
        services: [service],
        accountId: service.accountId,
        userId: user.id,
      })
      webhook3 = await webhookResource.create({
        name: 'webhook-test-3',
        events: ['service.updated'],
        accountId: service.accountId,
        userId: user.id,
      })

      const webhookExcluded = await webhookResource.create({
        name: 'webhook-test-4',
        events: ['service.updated'],
        services: [service2],
        accountId: service2.accountId,
        userId: user.id,
      })

      const serviceDeleted1 = await serviceResource.create({
        name: 'service-deleted-1',
        type: 'whatsapp',
        accountId: account.id,
      })
      const webhookDeleted1 = await webhookResource.create({
        name: 'webhook-deleted-1',
        events: ['service.updated'],
        type: 'whatsapp_low_level',
        services: [serviceDeleted1],
        accountId: serviceDeleted1.accountId,
        userId: user.id,
      })
      await serviceResource.destroy(serviceDeleted1)

      const serviceDeleted2 = await serviceResource.create({
        name: 'service-deleted-2',
        type: 'whatsapp',
        accountId: account.id,
      })
      const webhookDeleted2 = await webhookResource.create({
        name: 'webhook-deleted-2',
        events: ['service.updated'],
        // type: 'whatsapp_low_level',
        services: [serviceDeleted2],
        accountId: serviceDeleted2.accountId,
        userId: user.id,
      })
      await serviceResource.destroy(serviceDeleted2)
    })

    test('getWebhooks for service.updated', async () => {
      const webhookSenderJob = Container.get(WebhookSenderJob)

      const webhooks = await webhookSenderJob.getWebhooks('service.updated', service.accountId, service.id)

      expect(webhooks.map((w) => w.id).sort()).toEqual([webhook1.id, webhook2.id, webhook3.id].sort())
    })

    describe('cache', () => {
      let tagCache: TagCache
      let setSpy: SpiedFunction<TagCache['set']>
      let getSpy: SpiedFunction<TagCache['get']>
      let invalidateSpy: SpiedFunction<TagCache['invalidate']>
      let webhookSenderJob: WebhookSenderJob

      const query = () => webhookSenderJob.getWebhooks('service.updated', service.accountId, service.id)

      beforeAll(() => {
        tagCache = Container.get(TagCache)
        webhookSenderJob = Container.get(WebhookSenderJob)

        setSpy = jest.spyOn(tagCache, 'set')
        getSpy = jest.spyOn(tagCache, 'get')
        invalidateSpy = jest.spyOn(tagCache, 'invalidate')
      })

      beforeEach(async () => {
        setSpy.mockClear()
        getSpy.mockClear()
        invalidateSpy.mockClear()
        await tagCache.invalidateAll()
      })

      test('second query is cached', async () => {
        // query from db and set cache
        await query()

        // query from cache
        await query()

        expect(getSpy).toHaveBeenCalledTimes(2)
        expect(setSpy).toHaveBeenCalledTimes(1)
      })

      test('cache is invalidated after webhook is updated', async () => {
        // query from db and set cache
        const webhooks = await query()

        // query from cache
        await query()

        // invalidade cache
        const updateWebhookId = webhooks[0].id
        const updateData = {
          url: 'http://foo.com',
        }
        await webhookResource.updateById(updateWebhookId, updateData)

        expect(invalidateSpy).toHaveBeenCalledTimes(1)

        // query from db and set cache
        const webhooks2 = await query()

        expect(webhooks2.find((w) => w.id === updateWebhookId)).toMatchObject(updateData)

        expect(getSpy).toHaveBeenCalledTimes(3)
        expect(setSpy).toHaveBeenCalledTimes(2)
      })

      test('cache is invalidated after account is updated', async () => {
        // query from db and set cache
        const webhooks = await query()

        const aWebhook = webhooks[0]

        // query from cache
        await query()

        // update a service from webhooks
        await accountResource.updateById(aWebhook.account.id, {
          name: 'updated account name',
        })

        expect(invalidateSpy).toHaveBeenCalled()

        // query from db and set cache
        await query()

        expect(getSpy).toHaveBeenCalledTimes(3)
        expect(setSpy).toHaveBeenCalledTimes(2)
      })

      test('cache is invalidated after user is updated', async () => {
        // query from db and set cache
        const webhooks = await query()

        const aWebhook = webhooks[0]

        // query from cache
        await query()

        // update a service from webhooks
        await userResource.updateById(aWebhook.user.id, {
          name: 'updated user name',
        })

        expect(invalidateSpy).toHaveBeenCalled()

        // query from db and set cache
        await query()

        expect(getSpy).toHaveBeenCalledTimes(3)

        // 2 set calls means it invalidated cache one time
        expect(setSpy).toHaveBeenCalledTimes(2)
      })

      test('cache is invalidated after role is updated', async () => {
        // query from db and set cache
        const webhooks = await query()

        const aWebhook = webhooks[0]

        // query from cache
        await query()

        // update a service from webhooks
        await roleResource.updateById(aWebhook.user.roles[0].id, {
          displayName: 'updated role name',
        })

        expect(invalidateSpy).toHaveBeenCalled()

        // query from db and set cache
        await query()

        expect(getSpy).toHaveBeenCalledTimes(3)

        // 2 set calls means it invalidated cache one time
        expect(setSpy).toHaveBeenCalledTimes(2)
      })

      test('cache is NOT invalidated after service is updated', async () => {
        // query from db and set cache
        const webhooks = await query()

        const aWebhook = webhooks[0]
        const aService = aWebhook.services[0]

        // query from cache
        await query()

        // update a service from webhooks
        await serviceResource.updateById(aService.id, {
          name: 'updated service name',
        })

        expect(invalidateSpy).toHaveBeenCalled()

        // query from db and set cache
        await query()

        expect(getSpy).toHaveBeenCalledTimes(3)

        // just one set call means it invalidated cache one time
        expect(setSpy).toHaveBeenCalledTimes(1)
      })
    })
  })
})
