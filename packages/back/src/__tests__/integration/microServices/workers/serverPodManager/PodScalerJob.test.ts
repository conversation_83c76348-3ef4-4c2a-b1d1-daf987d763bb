import { Container, Service } from 'typedi'
import faker from 'faker'
import { times } from 'lodash'
import PodScalerJob from '../../../../../microServices/workers/jobs/serverPodManager/PodScalerJob'
import RedisClientContainer from '../../../../../core/services/redis/RedisClientContainer'
import serverPodTypeResource from '../../../../../core/resources/serverPodTypeResource'
import { ServerPodInstance } from '../../../../../core/dbSequelize/models/ServerPod'
import serverPodResource from '../../../../../core/resources/serverPodResource'
import * as setupDb from '../../../../setup/setupDb'
import { ServerPodTypeInstance } from '../../../../../core/dbSequelize/models/ServerPodType'

const createServerPods = (serverPodType: ServerPodTypeInstance, schema: number[]): Promise<ServerPodInstance[]> => {
  return Promise.all(
    schema.map((used) =>
      serverPodResource.create({
        name: faker.random.uuid(),
        serviceIds: times(used, () => faker.random.uuid()),
        address: faker.internet.url(),
        version: '1',
        serverPodTypeId: serverPodType.id,
      }),
    ),
  )
}

const createServersMockFn = jest.fn()
const destroyServerMockFn = jest.fn()

@Service()
class PodScalerJobMock extends PodScalerJob {
  createServers = createServersMockFn

  destroyServer = destroyServerMockFn
}

describe('PodScalerJob', () => {
  beforeEach(async () => {
    await setupDb.setup()
  })

  afterEach(async () => {
    createServersMockFn.mockClear()
    destroyServerMockFn.mockClear()
  })

  afterAll(async () => {
    Container.get(RedisClientContainer).stop()
    await setupDb.teardown()
  })

  describe('creator', () => {
    test.each([
      { maxPerPod: 4, desiredAvailable: 4, min: 0, expected: 1 },
      { maxPerPod: 4, desiredAvailable: 8, min: 0, expected: 2 },
      { maxPerPod: 4, desiredAvailable: 9, min: 0, expected: 3 },
      { maxPerPod: 4, desiredAvailable: 11, min: 0, expected: 3 },
      { maxPerPod: 4, desiredAvailable: 12, min: 0, expected: 3 },
      { maxPerPod: 4, desiredAvailable: 40, min: 0, expected: 10 },
      { maxPerPod: 4, desiredAvailable: 41, min: 0, expected: 10 },
      { maxPerPod: 4, desiredAvailable: 50, min: 0, expected: 10 },
      { maxPerPod: 1, desiredAvailable: 8, min: 0, expected: 8 },
      { maxPerPod: 1, desiredAvailable: 12, min: 0, expected: 10 },
      { maxPerPod: 2, desiredAvailable: 1, min: 10, expected: 5 },
      { maxPerPod: 2, desiredAvailable: 10, min: 10, expected: 5 },
      { maxPerPod: 2, desiredAvailable: 15, min: 10, expected: 8 },
      { maxPerPod: 2, desiredAvailable: 0, min: 10, expected: 5 },
      { maxPerPod: 2, desiredAvailable: 10, min: 0, expected: 5 },
      { maxPerPod: 2, desiredAvailable: 0, min: 0, expected: 0 },
    ])('scale up from zero server pods %o', async ({ maxPerPod, desiredAvailable, min, expected }) => {
      const TYPE = 'test'

      const serverPodType = await serverPodTypeResource.create(
        {
          name: TYPE,
          autoscalable: true,
          cloud: 'testCloud',
          serverSize: 'testSize',
          maxPerPod,
          desiredAvailable,
          min,
        },
        { where: { name: TYPE } },
      )

      const podScalerJob = Container.get(PodScalerJobMock)

      await podScalerJob.handle({
        scalerType: 'creator',
        type: TYPE,
      })

      if (expected === 0) {
        expect(createServersMockFn).not.toBeCalled()
      } else {
        expect(createServersMockFn).toBeCalledWith(TYPE, expected, 'testCloud', 'testSize')
      }
    })

    test.each([
      { desiredAvailable: 1, min: 0, expected: 0 },
      { desiredAvailable: 4, min: 0, expected: 0 },
      { desiredAvailable: 8, min: 0, expected: 0 },
      { desiredAvailable: 12, min: 0, expected: 1 },
      { desiredAvailable: 0, min: 20, expected: 1 },
      { desiredAvailable: 0, min: 24, expected: 2 },
      { desiredAvailable: 10, min: 24, expected: 2 },
      { desiredAvailable: 0, min: 40, expected: 6 },
      { desiredAvailable: 0, min: 60, expected: 10 },
    ])('scale up from 4 server pods with 2 fully used %o', async ({ desiredAvailable, min, expected }) => {
      const TYPE = 'test'

      const serverPodType = await serverPodTypeResource.create(
        {
          name: TYPE,
          autoscalable: true,
          cloud: 'testCloud',
          serverSize: 'testSize',
          maxPerPod: 4,
          desiredAvailable,
          min,
        },
        { where: { name: TYPE } },
      )
      await createServerPods(serverPodType, [4, 4, 0, 0])

      const podScalerJob = Container.get(PodScalerJobMock)

      await podScalerJob.handle({
        scalerType: 'creator',
        type: 'test',
      })

      if (expected === 0) {
        expect(createServersMockFn).not.toBeCalled()
      } else {
        expect(createServersMockFn).toBeCalledWith('test', expected, 'testCloud', 'testSize')
      }
    })
  })

  describe('destroyer', () => {
    test.each([
      { desiredAvailable: 0, min: 0, expected: true },
      { desiredAvailable: 0, min: 15, expected: true },
      { desiredAvailable: 0, min: 16, expected: false },
      { desiredAvailable: 4, min: 0, expected: true },
      { desiredAvailable: 5, min: 0, expected: false },
    ])('scale down %o', async ({ desiredAvailable, min, expected }) => {
      const TYPE = 'test'

      const serverPodType = await serverPodTypeResource.create({
        name: TYPE,
        autoscalable: true,
        cloud: 'testCloud',
        serverSize: 'testSize',
        maxPerPod: 4,
        desiredAvailable,
        min,
      })

      await createServerPods(serverPodType, [4, 4, 0, 0])

      const podScalerJob = Container.get(PodScalerJobMock)

      await podScalerJob.handle({
        scalerType: 'destroyer',
        type: 'test',
      })

      if (expected) {
        expect(destroyServerMockFn).toBeCalled()
      } else {
        expect(destroyServerMockFn).not.toBeCalled()
      }
    })
  })

  test('neither scale up nor down', async () => {
    const TYPE = 'test'

    const serverPodType = await serverPodTypeResource.create({
      name: TYPE,
      autoscalable: true,
      cloud: 'testCloud',
      serverSize: 'testSize',
      maxPerPod: 2,
      desiredAvailable: 0,
      min: 0,
    })

    const podScalerJob = Container.get(PodScalerJobMock)

    await podScalerJob.handle({
      scalerType: 'destroyer',
      type: 'test',
    })

    await podScalerJob.handle({
      scalerType: 'creator',
      type: 'test',
    })

    expect(destroyServerMockFn).not.toBeCalled()
    expect(createServersMockFn).not.toBeCalled()
  })
})
