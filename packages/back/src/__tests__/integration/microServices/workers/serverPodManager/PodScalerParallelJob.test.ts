import { Container } from 'typedi'
import PodScalerParallelJob from '../../../../../microServices/workers/jobs/serverPodManager/PodScalerParallelJob'
import HttpJobsDispatcher from '../../../../../core/services/jobs/http/HttpJobsDispatcher'
import ConsulRepository from '../../../../../microServices/workers/jobs/serverPodManager/ConsulRepository'
import { PodScalerConfig } from '../../../../../microServices/workers/jobs/serverPodManager/types/consulTypes'
import Config from '../../../../../core/services/config/Config'
import { ScalerType } from '../../../../../microServices/workers/jobs/serverPodManager/PodScalerJob'

describe('PodScalerParallelJob', () => {
  Container.get(Config).set('env', 'production')

  const dispatchFnMock = jest.fn()
  Container.set(HttpJobsDispatcher, {
    dispatch: dispatchFnMock,
  })

  Container.set(ConsulRepository, {
    getKv: async (): Promise<PodScalerConfig> => ({
      type1: {
        autoscalable: true,
        cloud: 'digitalocean',
      },
      type2: {
        cloud: 'contabo',
      },
    }),
  })
  const podScalerParallelJob = Container.get(PodScalerParallelJob)

  beforeEach(() => {
    dispatchFnMock.mockClear()
  })

  test.each(<ScalerType[]>['creator', 'destroyer'])('scaler of type %s', async (scalerType) => {
    await podScalerParallelJob.handle({ scalerType })

    expect(dispatchFnMock).toBeCalledTimes(1)
    expect(dispatchFnMock).toBeCalledWith('PodScalerJob', {
      scalerType,
      type: 'type1',
    })
  })
})
