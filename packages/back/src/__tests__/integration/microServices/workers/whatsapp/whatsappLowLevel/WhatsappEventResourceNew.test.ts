import { Container } from 'typedi'
import WhatsappEventResourceNew from '../../../../../../microServices/workers/jobs/whatsapp/adapterLowLevel/WhatsappEventResourceNew'
import * as setupDb from '../../../../../setup/setupDb'
import serviceResource from '../../../../../../core/resources/serviceResource'
import * as amqp from '../../../../../../core/services/amqp/amqpConnection'

jest.setTimeout(60000)

describe('WhatsappEventResource', () => {
  let amqpConn
  beforeAll(async () => {
    amqpConn = await amqp.start()
    await setupDb.setup()
  })

  afterAll(async () => {
    await setupDb.teardown()
    await amqp.stop(amqpConn)
  })

  test('handleMessageReceived', async () => {
    const eventResource = Container.get(WhatsappEventResourceNew)

    const service = await serviceResource.findOne()

    await eventResource.handleMessageReceived(service, {
      id: '1',
      message: 'teste',
      type: 'chat',
      timestamp: new Date().getTime() / 1000,
      fullUrl: 'test',
    })
    await eventResource.handleMessageReceived(service, {
      id: '1',
      message: 'teste2',
      type: 'chat',
      timestamp: new Date().getTime() / 1000,
      fullUrl: 'test',
    })
  })
})
