import queuedAsyncMap from '../../../../../core/utils/array/queuedAsyncMap'

describe('FallbackSyncMessage', () => {
  test('makeSyncMessage', async () => {
    const payload = Array.from({ length: 10 }).map((_, index) => index + 1)

    const generateException = () => {
      return new Promise((res, rej) => rej('reject type value'))
    }

    const actual = await queuedAsyncMap(payload, async (item) => {
      try {
        if (item % 2 === 0) await generateException()

        return item
      } catch (error) {
        return error
      }
    })

    const expected = [
      1,
      'reject type value',
      3,
      'reject type value',
      5,
      'reject type value',
      7,
      'reject type value',
      9,
      'reject type value',
    ]

    expect(actual).toMatchObject(expected)
  })
})
