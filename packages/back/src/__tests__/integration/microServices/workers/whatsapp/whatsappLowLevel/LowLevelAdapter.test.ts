import Axios from 'axios'
import Mimetypes from 'mime-types'
import fs from 'fs-jetpack'
import * as path from 'path'
import LowLevelAdapter from '../../../../../../microServices/workers/jobs/whatsapp/adapterLowLevel/LowLevelAdapter'
import * as fakeResources from '../../../../../../core/utils/tests/fakeResources'
import * as setupDb from '../../../../../setup/setupDb'

describe('LowLevelAdapter', () => {
  let account
  let service

  beforeAll(async () => {
    await setupDb.setup()
    account = await fakeResources.createAccount()
    service = await fakeResources.createService({ accountId: account.id })
  })

  afterAll(async () => {
    await setupDb.teardown()
  })

  test('makeSignedUrl', async () => {
    const lowLevelAdapter = new LowLevelAdapter(service, null)

    const filename = 'rabbit.jpg'
    const filePath = path.resolve(__dirname, './fixtures', filename)
    const mimetype = Mimetypes.lookup(filePath) || 'application/octet-stream'

    const actual = await lowLevelAdapter.makeSignedUrl({
      accountId: account.id,
      serviceId: service.id,
      filename,
      mimetype,
    })
    console.log('actual', actual)
    const expected = {
      signedRequest: expect.any(String),
      signedUrl: expect.any(String),
    }

    expect(actual).toMatchObject(expected)
  })

  test('makeSignedUrl and upload', async () => {
    const lowLevelAdapter = new LowLevelAdapter(service, null)

    const filename = 'rabbit.jpg'
    const filePath = path.resolve(__dirname, './fixtures', filename)
    const fileStream = fs.createReadStream(filePath)
    const mimetype = Mimetypes.lookup(filePath) || 'application/octet-stream'

    const { signedRequest, signedUrl } = await lowLevelAdapter.makeSignedUrl({
      accountId: account.id,
      serviceId: service.id,
      filename,
      mimetype,
    })

    await Axios.put(signedRequest, fileStream, {
      headers: {
        'content-type': mimetype,
      },
    })

    const res = await Axios.get(signedUrl)

    expect(res.status).toBe(200)
  })
})
