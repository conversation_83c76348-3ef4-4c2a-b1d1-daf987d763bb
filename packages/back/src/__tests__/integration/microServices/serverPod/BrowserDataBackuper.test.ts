import { Container } from 'typedi'
import fs from 'fs'
import BrowserDataBackuper from '../../../../microServices/serverPod/services/BrowserDataBackuper'

jest.setTimeout(5 * 60 * 1000)

describe('BrowserDataBackuper', () => {
  const mockBaseDir = '/tmp'
  const serviceId = 'test-id'
  const browserDataPath = `${mockBaseDir}/${serviceId}/Default`
  const browserSubFolders = ['IndexedDB', 'Local Storage']
  const testFileName = `test-file.txt`
  const testFileContent = 'test-content'

  Container.set('apm', null)

  const browserDataBackuper = Container.get(BrowserDataBackuper)
  browserDataBackuper.getBaseDir = () => mockBaseDir

  const deleteMockDir = async () => {
    await fs.promises.rm(browserDataPath, { recursive: true, force: true }).catch(() => {})
  }

  const createMockDir = async () => {
    await deleteMockDir()
    await Promise.all(
      browserSubFolders.map(async (s) => {
        await fs.promises.mkdir(`${browserDataPath}/${s}`, {
          recursive: true,
        })
        await fs.promises.writeFile(`${browserDataPath}/${s}/${testFileName}`, testFileContent)
      }),
    )
  }

  describe('backupService', () => {
    test('return false if dir does not exists', async () => {
      await deleteMockDir()
      await expect(browserDataBackuper.backup(serviceId)).resolves.toBe(false)
    })

    test('backup dir to s3', async () => {
      await createMockDir()
      await expect(browserDataBackuper.backup(serviceId)).resolves.toBe(true)
    })
  })

  describe('restoreService', () => {
    test('backup and restore', async () => {
      await createMockDir()
      await expect(browserDataBackuper.backup(serviceId)).resolves.toBe(true)
      await deleteMockDir()

      await expect(browserDataBackuper.restore(serviceId)).resolves.toBe(true)

      await Promise.all(
        browserSubFolders.map(async (s) => {
          const testFilePath = `${browserDataPath}/${s}/${testFileName}`

          await expect(fs.promises.stat(testFilePath)).resolves.toBeTruthy()

          await expect(fs.promises.readFile(testFilePath, 'utf-8')).resolves.toBe(testFileContent)
        }),
      )
    })
  })
})
