import { v4 as uuid } from 'uuid'
import { Container } from 'typedi'
import Axios from 'axios'
import { setup } from '../../../../microServices/podGateway/App'
import config from '../../../../core/config'
import HttpJobsRunner from '../../../../core/services/jobs/http/HttpJobsRunner'
import Job from '../../../../core/services/jobs/interfaces/Job'
import wait from '../../../../core/utils/wait'
import DuplexSocketIoRpcClient from '../../../../core/utils/rpcEngine/DuplexSocketIoRpcClient'

const handleFnMock = jest.fn()

class FooRpcJobMock implements Job {
  static jobName = 'WhatsappRemoteRpcJob'

  async handle(payload: { method: string; params: any[] }): Promise<void> {
    return handleFnMock(payload)
  }
}

describe('podGateway', () => {
  describe('app', () => {
    const url = `http://127.0.0.1:${config('podGatewayPort')}`
    let httpJobsRunner: HttpJobsRunner

    beforeAll(async () => {
      await setup()
      httpJobsRunner = Container.get(HttpJobsRunner)
      httpJobsRunner.registerJob(FooRpcJobMock)
      await httpJobsRunner.start()
    })

    test('call from client', async () => {
      const client = new DuplexSocketIoRpcClient()
      await client.start(url)

      await client.call('test', [1, 2, 3])

      await wait(10)

      expect(handleFnMock).toBeCalledWith({
        gatewayAddress: expect.any(String),
        clientId: expect.any(String),
        method: 'test',
        params: [1, 2, 3],
      })
    })

    test('call to client', async () => {
      const client = new DuplexSocketIoRpcClient()
      const testFnResult = uuid()
      client.setMethods({ test: () => testFnResult })
      await client.start(url)

      const res = await Axios.post(`${url}/send`, {
        id: client.getIo().id,
        method: 'test',
        params: [],
      }).then((r) => r.data)

      await wait(10)

      expect(res).toBe(testFnResult)
    })
  })
})
