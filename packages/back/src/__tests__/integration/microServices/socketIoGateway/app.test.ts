import Axios from 'axios'
import http, { Server } from 'http'
import createIo, { Socket } from 'socket.io-client'
import express from 'express'
import bodyParser from 'body-parser'
import { Container } from 'typedi'
import type App from '../../../../microServices/socket/App'
import type WorkersApp from '../../../../microServices/workers/App'
import wait from '../../../../core/utils/wait'
import Config from '../../../../core/services/config/Config'
import startWorkers from '../../../../microServices/workers/setup'
import setupSocketIoGateway from '../../../../microServices/socket/setup'
import SocketReceiverJob from '../../../../microServices/workers/jobs/socket/SocketReceiverJob'

const receiveFnMock = jest.fn()
const receiveFailFnMock = jest.fn()

const url = `http://127.0.0.1:5000`

describe('socketIoGateway', () => {
  describe('app', () => {
    let app: App
    let server: Server
    let ioClient: typeof Socket

    beforeEach(async () => {
      const expressApp = express()
      expressApp.use(bodyParser.json())
      expressApp.post('/receive', (req, res) => {
        receiveFnMock(req.body)
        res.sendStatus(200)
      })
      expressApp.post('/receive-fail', (req, res) => {
        receiveFailFnMock(req.body)
        res.sendStatus(400)
      })

      const httpServer = http.createServer(expressApp)
      server = await new Promise((resolve) => {
        const server = httpServer.listen(7357, () => resolve(server))
      })

      app = await setupSocketIoGateway(false)
      app.getAppContainer().get(Config).set('socketGatewayPort', 5000).set('withWorkersIntegration', false)
      await app.start()

      await Axios.post(`${url}/handlers`, {
        event: 'test-event',
        url: 'http://127.0.0.1:7357/receive',
        method: 'post',
      })

      ioClient = createIo(url)

      await new Promise((resolve) => ioClient.on('connect', resolve))
    })

    afterEach(async () => {
      ioClient.close()
      await app.stop()
      await new Promise((resolve) => server.close(() => resolve()))
    })

    test('register a handler', async () => {
      const receiveUrl = 'http://127.0.0.1:7357/boggers'

      const res = await Axios.post(`${url}/handlers`, {
        event: 'foo',
        url: receiveUrl,
        method: 'post',
      })

      expect(res.data).toMatchObject({
        id: expect.any(String),
        url: receiveUrl,
        method: 'post',
      })

      await Axios.delete(`${url}/handlers/${res.data.id}`)
    })

    test('emit and receive event', async () => {
      ioClient.emit('test-event', { foo: 'bar' })

      const listenerFnMock = jest.fn()

      ioClient.on('test-event-client', listenerFnMock)

      await wait(200)

      expect(receiveFnMock).toBeCalledWith({
        data: { foo: 'bar' },
        event: 'test-event',
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
      })

      receiveFnMock.mockClear()
    })

    test('$handshake success', async () => {
      const res = await Axios.post(`${url}/handlers`, {
        event: '$handshake',
        url: 'http://127.0.0.1:7357/receive',
        method: 'post',
      })

      const ioClient = createIo(url)
      await new Promise((resolve) => ioClient.on('connect', resolve))

      expect(receiveFnMock).toBeCalledWith({
        data: {
          headers: expect.any(Object),
          method: 'GET',
          query: expect.any(Object),
        },
        event: '$handshake',
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
      })

      receiveFnMock.mockClear()

      await Axios.delete(`${url}/handlers/${res.data.id}`)
    })

    test('$handshake fail', async () => {
      const res = await Axios.post(`${url}/handlers`, {
        event: '$handshake',
        url: 'http://127.0.0.1:7357/receive-fail',
        method: 'post',
      })

      const ioClient = createIo(url)
      await wait(50)

      expect(receiveFnMock).not.toBeCalled()
      expect(receiveFailFnMock).toBeCalledWith({
        data: {
          headers: expect.any(Object),
          method: 'GET',
          query: expect.any(Object),
        },
        event: '$handshake',
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
      })

      receiveFnMock.mockClear()

      expect(ioClient.disconnected).toBe(true)

      await Axios.delete(`${url}/handlers/${res.data.id}`)
    })
  })

  describe('workers integration', () => {
    let app: App
    let workersApp: WorkersApp

    // Workers setup
    const handleMock = jest.fn()
    class SocketReceiverJobMock extends SocketReceiverJob {
      handle = handleMock
    }
    Container.set(SocketReceiverJob, new SocketReceiverJobMock())

    beforeAll(async () => {
      app = await setupSocketIoGateway(false)
      app.getAppContainer().get(Config).set('socketGatewayPort', 5000).set('withWorkersIntegration', true)
      await app.start()

      workersApp = await startWorkers()
    })

    afterAll(async () => {
      await app.stop()
      await workersApp.stop()
    })

    test('$handshake, $connect and $disconnect', async () => {
      const ioClient = createIo(url, {
        autoConnect: false,
        query: { myQueryString: 'foo' },
      })

      ioClient.connect()
      await new Promise((resolve) => ioClient.on('connect', resolve))

      expect(handleMock).nthCalledWith(1, {
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
        event: '$handshake',
        data: {
          headers: expect.any(Object),
          method: 'GET',
          query: expect.objectContaining({ myQueryString: 'foo' }),
        },
      })
      expect(handleMock).nthCalledWith(2, {
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
        event: '$connect',
        data: null,
      })

      ioClient.disconnect()
      await wait(50)

      expect(handleMock).nthCalledWith(3, {
        socketId: expect.any(String),
        gatewayAddress: expect.any(String),
        event: '$disconnect',
        data: null,
      })
    })
  })
})
