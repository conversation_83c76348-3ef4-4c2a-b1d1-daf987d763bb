import Qs from 'qs'
import { setup, teardown } from '../../setup/setupApp'

describe('api', () => {
  describe('auth', () => {
    let app

    beforeEach(async () => {
      app = await setup()
    })
    afterEach(async () => {
      await teardown(app)
    })

    test('POST /oauth/token', async () => {
      const res = await app.axios.post(
        '/oauth/token',
        Qs.stringify({
          username: '<EMAIL>',
          password: '12345678',
          grant_type: 'password',
          client_id: 'api',
          client_secret: 'secret',
        }),
        {
          headers: {
            'Content-type': 'application/x-www-form-urlencoded',
          },
        },
      )

      expect(res.status).toBe(200)
      expect(res.data).toEqual({
        expires_in: expect.any(Number),
        access_token: expect.any(String),
        refresh_token: expect.any(String),
        token_type: 'Bearer',
      })
    })
  })
})
