import webhookServer, { WebhookServer } from '../utils/webhookServer'
import { waitForWebhookEventById } from '../utils/apiHelpers'
import addOrRemove9ForBrazilianNumber from '../../../core/utils/addOrRemove9ForBrazilianNumber'
import { numberToId } from '../../../core/utils/whatsapp/numberParser'
import {
  CONTACT_ID,
  NUMBER,
  SERVICE_ID,
  TEST_TUNNEL,
  TEST_TUNNEL_HOST,
  MESSAGE_CREATED_EVENT,
  BLOCKED_CONTACT_ID,
  ensureServiceIsConnected,
  sendMessage,
  setWebhook,
  unsetWebhook,
  forwardMessage,
  messageCreatedPayload,
} from './base'

jest.setTimeout(60 * 60 * 1000)

describe('whatsappLowLevel', () => {
  let webhookSv: WebhookServer
  let webhook: { id: string }

  beforeAll(async () => {
    await ensureServiceIsConnected(SERVICE_ID)
    webhookSv = await webhookServer({
      // handler: async (req) => console.log(req.body),
      tunnel: TEST_TUNNEL,
      tunnelHost: TEST_TUNNEL_HOST,
    })
    webhook = await setWebhook(webhookSv.url, SERVICE_ID).then((r) => r.data)
  })

  afterAll(async () => {
    await webhookSv.stop()
    await unsetWebhook(webhook.id)
  })

  describe('forward message', () => {
    test('forward message with queued=false', async () => {
      const request1 = await sendMessage({
        contactId: CONTACT_ID,
        text: 'a message to be forwarded (queue=false)',
        queued: false,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request1.data.id)

      const request2 = await forwardMessage({
        contactId: CONTACT_ID,
        messageIds: [request1.data.id],
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request2.data.ids[0])
    })

    test('forward message with queued=true', async () => {
      const request1 = await sendMessage({
        contactId: CONTACT_ID,
        text: 'a message to be forwarded (queued=true)',
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request1.data.id)

      const request2 = await forwardMessage({
        contactId: CONTACT_ID,
        messageIds: [request1.data.id],
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request2.data.ids[0])
    })

    test('forward image message with queued=true', async () => {
      const data = {
        contactId: CONTACT_ID,
        text: 'a message with caption to be forwarded (queued=true)',
        file: {
          mimetype: 'image/jpeg',
          url: 'https://upload.wikimedia.org/wikipedia/en/a/a9/Example.jpg',
        },
        queued: true,
      }
      const request1 = await sendMessage(data)
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request1.data.id)

      const request2 = await forwardMessage({
        contactId: CONTACT_ID,
        messageIds: [request1.data.id],
        queued: true,
      })
      const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request2.data.ids[0])
      expect(creationEvent).toMatchObject(
        messageCreatedPayload({
          data: {
            id: request2.data.ids[0],
            contactId: CONTACT_ID,
            text: data.text,
            serviceId: SERVICE_ID,
            type: 'image',
            preview: {
              mimetype: 'image/jpeg',
              url: expect.any(String),
            },
            file: {
              mimetype: data.file.mimetype,
              url: expect.any(String),
            },
          },
        }),
      )
    })

    test('forward message with queued=true and wrong 9', async () => {
      const contactId = numberToId(addOrRemove9ForBrazilianNumber(NUMBER))

      const request1 = await sendMessage({
        contactId,
        text: 'a message to be forwarded (queued=true) (wrong 9)',
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request1.data.id)

      const request2 = await forwardMessage({
        contactId,
        messageIds: [request1.data.id],
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request2.data.ids[0])
    })

    test('forward message to invalid number with queued=true', async () => {
      const sentRequest = await sendMessage({
        contactId: CONTACT_ID,
        text: 'a message to be forwarded (queued=true) (invalid number)',
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, sentRequest.data.id)

      const forwardData = {
        contactId: '<EMAIL>',
        messageIds: [sentRequest.data.id],
        queued: true,
      }
      const forwardRequest = await forwardMessage(forwardData)

      const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, forwardRequest.data.ids[0])

      expect(creationEvent).toMatchObject({
        event: MESSAGE_CREATED_EVENT,
        data: {
          id: forwardRequest.data.ids[0],
          ack: 'error',
          contactId: forwardData.contactId,
          error: 'INVALID_CONTACT_ID',
        },
      })
    })

    test('forward message to blocked contact with queued=false', async () => {
      const forwardData = {
        contactId: BLOCKED_CONTACT_ID,
        messageIds: ['fake-id'],
        queued: false,
      }
      const error = await forwardMessage(forwardData).catch((e) => e.response)

      expect(error).toMatchObject({
        status: 400,
        data: {
          error: 'HttpError',
          message: 'The contact is blocked.',
          status: 400,
        },
      })
    })

    test('forward message to blocked contact with queued=true', async () => {
      const forwardData = {
        contactId: BLOCKED_CONTACT_ID,
        messageIds: ['fake-id'],
        queued: true,
      }
      const forwardRequest = await forwardMessage(forwardData)

      const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, forwardRequest.data.ids[0])

      expect(creationEvent).toMatchObject({
        event: MESSAGE_CREATED_EVENT,
        data: {
          id: forwardRequest.data.ids[0],
          ack: 'error',
          contactId: forwardData.contactId,
          error: 'BLOCKED_CONTACT',
        },
      })
    })

    test('forward 2 messages, one invalid, with queued=true', async () => {
      const sentRequest = await sendMessage({
        contactId: CONTACT_ID,
        text: 'a message to be forwarded (queued=true) (2 messages, one invalid)',
        queued: true,
      })
      await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, sentRequest.data.id)

      const forwardData = {
        contactId: CONTACT_ID,
        messageIds: ['fake-id', sentRequest.data.id],
        queued: true,
      }
      const forwardRequest = await forwardMessage(forwardData)

      const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, forwardRequest.data.ids[0])

      expect(creationEvent).toMatchObject({
        event: MESSAGE_CREATED_EVENT,
        data: {
          id: forwardRequest.data.ids[0],
          ack: 'error',
          contactId: forwardData.contactId,
          error: 'MESSAGE_NOT_FOUND',
        },
      })

      const createdEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, forwardRequest.data.ids[1])

      expect(createdEvent).toMatchObject(
        messageCreatedPayload({
          data: {
            id: forwardRequest.data.ids[1],
            contactId: CONTACT_ID,
            serviceId: SERVICE_ID,
          },
        }),
      )
    })
  })
})
