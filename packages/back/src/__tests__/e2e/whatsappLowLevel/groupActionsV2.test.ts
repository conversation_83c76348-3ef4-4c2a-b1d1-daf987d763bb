import webhookServer, { WebhookServer } from '../utils/webhookServer'
import {
  CONTACT_ID,
  BLOCKED_CONTACT_ID,
  CONTACT_CONTACT_BLOCKED_ME_ID,
  CONTACT_FORBIDDEN_ID,
  CONTACT_NOT_ON_ADDRESS_BOOK_ID,
  CONTACT_NEVER_CONTACTED_ID,
  NUMBER,
  SERVICE_ID,
  TEST_TUNNEL,
  TEST_TUNNEL_HOST,
  addGroupParticipantV2,
  createGroupV2,
  deleteChat,
  ensureServiceIsConnected,
  leaveGroup,
  removeAllGroupParticipants,
  setWebhook,
  unsetWebhook,
  getChat,
  removeManyGroupParticipant,
} from './base'

jest.setTimeout(60 * 60 * 1000)

describe('whatsappLowLevel', () => {
  let webhookSv: WebhookServer
  let webhook: { id: string }

  beforeAll(async () => {
    await ensureServiceIsConnected(SERVICE_ID)
    webhookSv = await webhookServer({
      // handler: async (req) => console.log(req.body),
      tunnel: TEST_TUNNEL,
      tunnelHost: TEST_TUNNEL_HOST,
    })
    webhook = await setWebhook(webhookSv.url, SERVICE_ID).then((r) => r.data)
  })

  afterAll(async () => {
    await webhookSv.stop()
    await unsetWebhook(webhook.id)
  })

  describe('group actions v2', () => {
    let groupId: string

    test('attempt to create group without numbers or contactIds', async () => {
      const res = await createGroupV2({
        name: 'Test Group V2 - No numbers',
      }).catch((e) => e.response)

      expect(res.data).toMatchObject({
        error: 'ValidationError',
        message: 'The given data was invalid.',
        status: 400,
        errors: {
          body: {
            contactIds: {
              messages: ['Required if "numbers" was not given.'],
              types: ['requiredIfOtherNotPresent'],
            },
            numbers: {
              messages: ['Required if "contactIds" was not given.'],
              types: ['requiredIfOtherNotPresent'],
            },
          },
        },
      })
    })

    test('attempt to create group with numbers [null]', async () => {
      const res = await createGroupV2({
        name: 'Test Group V2 - Null number',
        numbers: [null],
      }).catch((e) => e.response)

      expect(res.data).toMatchObject({
        error: 'ValidationError',
        message: 'The given data was invalid.',
        status: 400,
        errors: {
          body: {
            numbers: {
              messages: ['Should be an array of string.'],
              types: ['arrayOf'],
            },
          },
        },
      })
    })

    test('attempt to create group with contactIds [null]', async () => {
      const res = await createGroupV2({
        name: 'Test Group V2 - Null contactIds',
        contactIds: [null],
      }).catch((e) => e.response)

      expect(res.data).toMatchObject({
        error: 'ValidationError',
        message: 'The given data was invalid.',
        status: 400,
        errors: {
          body: {
            contactIds: {
              messages: ['Should be an array of string.'],
              types: ['arrayOf'],
            },
          },
        },
      })
    })

    test('attempt to create group with every number invalid', async () => {
      const res = await createGroupV2({
        name: 'Test Group V2 - All invalid',
        numbers: ['123', '456'],
      }).catch((e) => e.response)

      expect(res.data).toMatchObject({
        error: 'HttpError',
        status: 400,
        message: 'Cannot create the group, all contacts are invalid.',
      })
    })

    test('create group', async () => {
      const data = {
        name: 'Test Group V2',
        numbers: [
          NUMBER,
          '123',
          CONTACT_CONTACT_BLOCKED_ME_ID,
          BLOCKED_CONTACT_ID,
          CONTACT_FORBIDDEN_ID,
          CONTACT_NOT_ON_ADDRESS_BOOK_ID,
          CONTACT_NEVER_CONTACTED_ID,
        ],
      }

      const createGroupRes = await createGroupV2(data).catch((e) => e.response)

      expect(createGroupRes).toMatchObject({
        status: 200,
        data: {
          id: expect.any(String),
          participants: [
            {
              contactId: `<EMAIL>`,
              success: false,
              error: 'INVALID_CONTACT_ID',
            },
            {
              contactId: expect.any(String),
              success: true,
            },
            {
              contactId: CONTACT_NEVER_CONTACTED_ID,
              success: false,
              error: 'CONTACT_NOT_IN_ADDRESS_BOOK',
            },
            {
              contactId: CONTACT_ID,
              success: true,
            },
            {
              contactId: CONTACT_NOT_ON_ADDRESS_BOOK_ID,
              success: false,
              error: 'CONTACT_NOT_IN_ADDRESS_BOOK',
            },
            {
              contactId: CONTACT_FORBIDDEN_ID,
              success: false,
              error: 'FORBIDDEN',
            },
            {
              contactId: CONTACT_CONTACT_BLOCKED_ME_ID,
              success: false,
              error: 'CONTACT_BLOCKED_ME',
            },
            {
              contactId: BLOCKED_CONTACT_ID,
              success: false,
              error: 'BLOCKED_CONTACT',
            },
          ],
        },
      })

      groupId = createGroupRes.data.id
    })

    test('get group', async () => {
      await expect(getChat(groupId)).resolves.toMatchObject({
        status: 200,
        data: {
          // avatarUrl: null,
          iAmAdmin: true,
          id: groupId,
          isGroup: true,
          name: 'Test Group V2',
          participants: [
            {
              avatarUrl: expect.any(String),
              id: expect.any(String),
              isAdmin: false,
              name: expect.any(String),
              number: expect.any(String),
              profileName: expect.any(String),
            },
            {
              avatarUrl: expect.any(String),
              id: expect.any(String),
              isAdmin: true,
              isMe: true,
              name: expect.any(String),
              number: expect.any(String),
              profileName: expect.any(String),
            },
          ],
        },
      })
    })

    test('remove many group participants', async () => {
      await expect(removeManyGroupParticipant(groupId, { numbers: [NUMBER] })).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('add group participants', async () => {
      await expect(
        addGroupParticipantV2(groupId, {
          numbers: [NUMBER, '123'],
        }),
      ).resolves.toMatchObject({
        status: 200,
        data: {
          participants: [
            {
              contactId: `<EMAIL>`,
              success: false,
              error: 'INVALID_CONTACT_ID',
            },
            {
              contactId: CONTACT_ID,
              success: true,
            },
          ],
        },
      })
    })

    test('try to add blocked and forbidden group participant', async () => {
      await expect(
        addGroupParticipantV2(groupId, {
          numbers: [
            '123',
            CONTACT_CONTACT_BLOCKED_ME_ID,
            BLOCKED_CONTACT_ID,
            CONTACT_FORBIDDEN_ID,
            CONTACT_NOT_ON_ADDRESS_BOOK_ID,
          ],
        }),
      ).rejects.toMatchObject({
        response: {
          status: 400,
          data: {
            participants: [
              {
                contactId: `<EMAIL>`,
                success: false,
                error: 'INVALID_CONTACT_ID',
              },
              {
                contactId: CONTACT_NOT_ON_ADDRESS_BOOK_ID,
                success: false,
                error: 'CONTACT_NOT_IN_ADDRESS_BOOK',
              },
              {
                contactId: CONTACT_FORBIDDEN_ID,
                success: false,
                error: 'FORBIDDEN',
              },
              {
                contactId: CONTACT_CONTACT_BLOCKED_ME_ID,
                success: false,
                error: 'CONTACT_BLOCKED_ME',
              },
              {
                contactId: BLOCKED_CONTACT_ID,
                success: false,
                error: 'BLOCKED_CONTACT',
              },
            ],
          },
        },
      })
    })

    test('remove all group participants', async () => {
      await expect(removeAllGroupParticipants(groupId)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('leave group', async () => {
      await expect(leaveGroup(groupId)).resolves.toMatchObject({
        status: 200,
        data: 'OK',
      })
    })

    test('delete group chat', async () => {
      await expect(deleteChat(groupId)).resolves.toMatchObject({
        status: 200,
        data: 'OK',
      })
    })

    test('get group after chat has been deleted', async () => {
      await expect(getChat(groupId).catch((err) => err.response)).resolves.toMatchObject({
        status: 404,
        data: {
          status: 404,
          error: 'NotFoundHttpError',
          message: 'Chat not found.',
        },
      })
    })
  })

  test.skip.each([1, 2, 3])('creates 10 groups - v2', async (number) => {
    const createGroupRes = await createGroupV2({
      name: `Test Group V2 ${number}`,
      numbers: [NUMBER],
    }).catch((e) => e.response)

    expect(createGroupRes).toMatchObject({
      status: 200,
      data: {
        id: expect.any(String),
        participants: [{ contactId: `${NUMBER}@c.us`, success: true }],
      },
    })

    const groupId = createGroupRes.data.id

    await expect(leaveGroup(groupId)).resolves.toMatchObject({
      status: 200,
      data: 'OK',
    })

    await expect(deleteChat(groupId)).resolves.toMatchObject({
      status: 200,
      data: 'OK',
    })

    await expect(getChat(groupId).catch((err) => err.response)).resolves.toMatchObject({
      status: 404,
      data: {
        status: 404,
        error: 'NotFoundHttpError',
        message: 'Chat not found.',
      },
    })
  })
})
