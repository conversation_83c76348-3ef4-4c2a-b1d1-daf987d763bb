import { range } from 'lodash'
import * as apiHelpers from '../utils/apiHelpers'
import webhookServer, { WebhookServer } from '../utils/webhookServer'
import env from '../../../core/utils/env'
import { waitForWebhookEventById } from '../utils/apiHelpers'
import wait from '../../../core/utils/wait'

jest.setTimeout(60 * 1000)

const TEST_TUNNEL = env('TEST_TUNNEL', 'localtunnel')
const TEST_TUNNEL_HOST = env('TEST_TUNNEL_HOST', 'https://localtunnel.ikatec.cloud')

describe.skip.each(range(10))('create service', (i) => {
  let webhookSv: WebhookServer

  beforeAll(async () => {
    webhookSv = await webhookServer({
      // handler: async (req) => console.log(req.body),
      tunnel: TEST_TUNNEL,
      tunnelHost: TEST_TUNNEL_HOST,
    })
  })

  afterAll(async () => {
    await webhookSv.stop()
  })

  test('create service and send low level webhooks', async () => {
    const serviceData = {
      name: `Test service #${i}`,
      type: 'whatsapp',
      lowLevel: true,
      disableQrCodeTimeout: true,
      allowScanAnotherNumber: true,
      keepOnline: true,
      defaultDepartmentId: null,
    }
    const serviceRes = await apiHelpers.sendPost('/v1/services', serviceData)

    const webhookData = {
      active: true,
      name: `Test service #${i}`,
      url: webhookSv.url,
      events: [
        'whatsapp.message.created',
        'whatsapp.message.updated',
        'whatsapp.contact.updated',
        'service.created',
        'service.updated',
      ],
      type: 'whatsapp_low_level',
      services: [serviceRes.data.id],
    }
    const webhookRes = await apiHelpers.sendPost('/v1/me/webhooks', webhookData)

    webhookSv.setHandler(async (req) => {
      // console.log(JSON.stringify(req.body, null, 2))
      return true
    })

    try {
      // isStarting webhook
      await waitForWebhookEventById(
        webhookSv,
        'service.updated',
        serviceRes.data.id,
        (event) => event.data?.data?.status?.isStarting,
        { timeout: 30 * 1000 },
      )

      // QR Code webhook
      await waitForWebhookEventById(
        webhookSv,
        'service.updated',
        serviceRes.data.id,
        (event) => event.data?.data?.status?.qrCodeUrl,
        { timeout: 30 * 1000 },
      )
    } finally {
      await apiHelpers.sendDelete(`/v1/me/webhooks/${webhookRes.data.id}`)
      await apiHelpers.sendDelete(`/v1/services/${serviceRes.data.id}`)
    }
  })
})
