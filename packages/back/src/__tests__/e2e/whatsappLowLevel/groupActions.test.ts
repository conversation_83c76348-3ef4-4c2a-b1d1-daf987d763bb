import webhookServer, { WebhookServer } from '../utils/webhookServer'
import addOrRemove9ForBrazilianNumber from '../../../core/utils/addOrRemove9ForBrazilianNumber'
import { numberToId } from '../../../core/utils/whatsapp/numberParser'
import {
  CONTACT_ID,
  NUMBER,
  SERVICE_ID,
  TEST_TUNNEL,
  TEST_TUNNEL_HOST,
  addGroupParticipant,
  createGroup,
  deleteChat,
  demoteGroupParticipant,
  ensureServiceIsConnected,
  expectTextSendAndReceiveFlow,
  leaveGroup,
  promoteGroupParticipant,
  removeAllGroupParticipants,
  removeManyGroupParticipant,
  removeOneGroupParticipant,
  sendMessage,
  setWebhook,
  unsetWebhook,
  getChat,
} from './base'

jest.setTimeout(60 * 60 * 1000)

describe('whatsappLowLevel', () => {
  let webhookSv: WebhookServer
  let webhook: { id: string }

  beforeAll(async () => {
    await ensureServiceIsConnected(SERVICE_ID)
    webhookSv = await webhookServer({
      // handler: async (req) => console.log(req.body),
      tunnel: TEST_TUNNEL,
      tunnelHost: TEST_TUNNEL_HOST,
    })
    webhook = await setWebhook(webhookSv.url, SERVICE_ID).then((r) => r.data)
  })

  afterAll(async () => {
    await webhookSv.stop()
    await unsetWebhook(webhook.id)
  })

  describe('group actions', () => {
    let groupId: string

    test('attempt to create group with every number invalid', async () => {
      const res = await createGroup({
        name: 'Test Group 1',
        numbers: ['123', '456'],
      }).catch((e) => e.response)

      expect(res.data).toMatchObject({
        error: 'HttpError',
        status: 400,
        message: 'Cannot create the group, all contacts are invalid.',
      })
    })

    test('create group', async () => {
      const createGroupRes = await createGroup({
        name: 'Test Group 1',
        numbers: [NUMBER, '123'],
      }).catch((e) => e.response)

      expect(createGroupRes).toMatchObject({
        status: 200,
        data: {
          id: expect.any(String),
          participants: {
            '<EMAIL>': false,
            [`${NUMBER}@c.us`]: true,
          },
        },
      })

      groupId = createGroupRes.data.id
    })

    test('get group', async () => {
      await expect(getChat(groupId)).resolves.toMatchObject({
        status: 200,
        data: {
          // avatarUrl: null,
          iAmAdmin: true,
          id: groupId,
          isGroup: true,
          name: 'Test Group 1',
          participants: [
            {
              // avatarUrl: expect.any(String),
              id: expect.any(String),
              isAdmin: false,
              name: expect.any(String),
              number: expect.any(String),
              // profileName: expect.any(String),
            },
            {
              // avatarUrl: expect.any(String),
              id: expect.any(String),
              isAdmin: true,
              isMe: true,
              name: expect.any(String),
              number: expect.any(String),
              // profileName: expect.any(String),
            },
          ],
        },
      })
    })

    test('send text message with queued=false', async () => {
      const data = {
        contactId: groupId,
        text: `a message with queued=false`,
        queued: false,
      }
      const request = await sendMessage(data)

      await expectTextSendAndReceiveFlow(webhookSv, request, data.text, groupId)
    })

    test('send text message with queued=true', async () => {
      const data = {
        contactId: groupId,
        text: `a message with queued=true`,
        queued: true,
      }
      const request = await sendMessage(data)

      await expectTextSendAndReceiveFlow(webhookSv, request, data.text, groupId)
    })

    test('remove many group participants', async () => {
      await expect(removeManyGroupParticipant(groupId, { contactIds: [CONTACT_ID] })).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('add invalid group participant', async () => {
      await expect(
        addGroupParticipant(groupId, {
          contactIds: ['<EMAIL>'],
        }),
      ).resolves.toMatchObject({
        status: 200,
        data: { '<EMAIL>': false },
      })
    })

    test('add valid group participant', async () => {
      await expect(
        addGroupParticipant(groupId, {
          contactIds: [CONTACT_ID],
        }),
      ).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('promote group participant', async () => {
      await expect(promoteGroupParticipant(groupId, CONTACT_ID)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('demote group participant', async () => {
      await expect(demoteGroupParticipant(groupId, CONTACT_ID)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('remove one group participant', async () => {
      await expect(removeOneGroupParticipant(groupId, CONTACT_ID)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    const contactIdWrong9 = numberToId(addOrRemove9ForBrazilianNumber(NUMBER))

    test('add group participant with wrong 9', async () => {
      await expect(
        addGroupParticipant(groupId, {
          contactIds: [contactIdWrong9],
        }),
      ).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('remove one group participant with wrong 9', async () => {
      await expect(removeOneGroupParticipant(groupId, contactIdWrong9)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('remove all group participants while having none', async () => {
      await expect(removeAllGroupParticipants(groupId)).resolves.toMatchObject({
        status: 200,
        data: {},
      })
    })

    test('add group participant again using wrong 9', async () => {
      await expect(
        addGroupParticipant(groupId, {
          contactIds: [addOrRemove9ForBrazilianNumber(CONTACT_ID)],
        }),
      ).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('remove all group participants', async () => {
      await expect(removeAllGroupParticipants(groupId)).resolves.toMatchObject({
        status: 200,
        data: { [CONTACT_ID]: true },
      })
    })

    test('leave group', async () => {
      await expect(leaveGroup(groupId)).resolves.toMatchObject({
        status: 200,
        data: 'OK',
      })
    })

    test('delete group chat', async () => {
      await expect(deleteChat(groupId)).resolves.toMatchObject({
        status: 200,
        data: 'OK',
      })
    })

    test('get group after chat has been deleted', async () => {
      await expect(getChat(groupId).catch((err) => err.response)).resolves.toMatchObject({
        status: 404,
        data: {
          status: 404,
          error: 'NotFoundHttpError',
          message: 'Chat not found.',
        },
      })
    })
  })
})
