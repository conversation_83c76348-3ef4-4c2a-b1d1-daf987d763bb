import { defaultsDeep } from 'lodash/fp'
import { AxiosResponse } from 'axios'
import env from '../../../core/utils/env'
import { sendDelete, sendGet, sendPost, sendPut, waitForWebhookEventById } from '../utils/apiHelpers'
import pooling from '../../../core/utils/pooling/pooling'
import { WebhookServer } from '../utils/webhookServer'

export const SERVICE_ID = env('TEST_SERVICE_ID')
export const CONTACT_ID = env('TEST_CONTACT_ID')
export const BLOCKED_CONTACT_ID = env('TEST_BLOCKED_CONTACT_ID')
export const CONTACT_CONTACT_BLOCKED_ME_ID = env('TEST_CONTACT_CONTACT_BLOCKED_ME_ID')
export const CONTACT_FORBIDDEN_ID = env('TEST_CONTACT_FORBIDDEN_ID')
export const CONTACT_NOT_ON_ADDRESS_BOOK_ID = env('TEST_CONTACT_NOT_ON_ADDRESS_BOOK_ID')
export const CONTACT_NEVER_CONTACTED_ID = env('TEST_CONTACT_NEVER_CONTACTED_ID')
export const OLD_MESSAGE_ID = env('TEST_OLD_MESSAGE_ID')
export const NUMBER = CONTACT_ID.replace('@c.us', '')
export const TEST_TUNNEL = env('TEST_TUNNEL', 'localtunnel')
export const TEST_TUNNEL_HOST = env('TEST_TUNNEL_HOST', 'https://localtunnel.me/')

export const MESSAGE_CREATED_EVENT = 'whatsapp.message.created'
export const MESSAGE_UPDATED_EVENT = 'whatsapp.message.updated'

export const setWebhook = async (webhookUrl: string, serviceId: string) => {
  const webhookEndpoint = '/v1/me/webhooks'
  const webhookName = 'Testing'

  const webhooks = await sendGet(`${webhookEndpoint}?where[name]=${webhookName}`).then((r) => r.data)

  const data = {
    name: webhookName,
    active: true,
    type: 'whatsapp_low_level',
    url: webhookUrl,
    services: [serviceId],
    events: [MESSAGE_CREATED_EVENT, MESSAGE_UPDATED_EVENT, 'whatsapp.contact.updated'],
  }

  if (webhooks.data[0]) {
    return sendPut(`${webhookEndpoint}/${webhooks.data[0].id}`, data)
  }

  return sendPost(webhookEndpoint, data)
}

export const unsetWebhook = async (webhookId: string) => {
  const webhookEndpoint = '/v1/me/webhooks'
  return sendDelete(`${webhookEndpoint}/${webhookId}`)
}

const serviceEndpoint = '/v1/services'
export const getService = (serviceId) => sendGet(`${serviceEndpoint}/${serviceId}`).then((r) => r.data)

export const ensureServiceIsConnected = async (serviceId: string) => {
  const service = await getService(serviceId)

  if (service?.data?.status?.isConnected) return true

  await sendPost(`${serviceEndpoint}/${serviceId}/start`)

  return pooling(async () => (await getService(serviceId))?.data?.status?.isConnected, {
    timeout: 1 * 30 * 1000,
    interval: 500,
  })
}

export const sendMessage = (data) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/messages`
  return sendPost(url, data)
}

export const sendMessageOld = (data) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/send`
  return sendPost(url, data)
}

export const forwardMessage = (data) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/messages/forward`
  return sendPost(url, data)
}

export const revokeMessage = (messageId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/messages/${messageId}/revoke`
  return sendPost(url)
}

export const createGroup = (data: { name: string; numbers: string[] }) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups`
  return sendPost(url, data)
}

export const createGroupV2 = (
  data: {
    name: string
  } & ({ numbers?: string[] } | { contactIds?: string[] }),
) => {
  const url = `/v2/whatsapp/${SERVICE_ID}/chats/groups`
  return sendPost(url, data)
}

export const getChat = (chatId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/${chatId}`
  return sendGet(url)
}

export const removeAllGroupParticipants = (chatId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants/remove-all`
  return sendPost(url)
}

export const removeOneGroupParticipant = (chatId: string, participantId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants/${participantId}`
  return sendDelete(url)
}

export const promoteGroupParticipant = (chatId: string, participantId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants/${participantId}/promote`
  return sendPost(url)
}

export const demoteGroupParticipant = (chatId: string, participantId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants/${participantId}/demote`
  return sendPost(url)
}

export const removeManyGroupParticipant = (chatId: string, data: { contactIds: string[] } | { numbers: string[] }) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants/remove`
  return sendPost(url, data)
}

export const addGroupParticipant = (chatId: string, data: { contactIds: string[] }) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants`
  return sendPost(url, data)
}

export const addGroupParticipantV2 = (chatId: string, data: { contactIds: string[] } | { numbers: string[] }) => {
  const url = `/v2/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/participants`
  return sendPost(url, data)
}

export const leaveGroup = (chatId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/groups/${chatId}/leave`
  return sendPost(url)
}

export const deleteChat = (chatId: string) => {
  const url = `/v1/whatsapp/${SERVICE_ID}/chats/${chatId}`
  return sendDelete(url)
}

export const baseMessagePayload = (data) =>
  defaultsDeep(
    {
      data: {
        contactId: CONTACT_ID,
        fromId: expect.any(String),
        type: 'chat',
        isFromMe: true,
        timestamp: expect.any(String),
        accountId: expect.any(String),
      },
      webhookId: expect.any(String),
      timestamp: expect.any(String),
    },
    data,
  )

export const messageCreatedPayload = (data) =>
  baseMessagePayload(
    defaultsDeep(
      {
        event: MESSAGE_CREATED_EVENT,
        data: {
          ack: 'pending',
          data: { ack: 0 },
          contact: {
            id: expect.any(String),
            name: expect.any(String),
            // aparece para contatos não adicionados na agenda
            // profileName: expect.any(String),
            number: expect.any(String),
            avatarUrl: expect.any(String),
          },
        },
      },
      data,
    ),
  )

export const messageUpdatedPayload = (data) =>
  baseMessagePayload(
    defaultsDeep(
      {
        event: MESSAGE_UPDATED_EVENT,
        data: {
          ack: expect.any(String),
          data: { ack: expect.any(Number) },
        },
      },
      data,
    ),
  )

export const expectTextSendAndReceiveFlow = async (
  webhookSv: WebhookServer,
  request: AxiosResponse,
  text: string,
  contactId: string = CONTACT_ID,
) => {
  // verifica status da resposta
  expect(request.status).toBe(200)

  // verifica payload da resposta
  expect(request.data).toMatchObject({
    id: expect.any(String),
  })

  // espera chegada do evento de criação no wobhook
  const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

  // verifica payload do evento do webhook
  expect(creationEvent).toMatchObject({
    event: MESSAGE_CREATED_EVENT,
    data: {
      id: request.data.id,
      contactId,
      fromId: expect.any(String),
      type: 'chat',
      isFromMe: true,
      timestamp: expect.any(String),
      text,
      ack: 'pending',
      data: { ack: 0 },
      serviceId: SERVICE_ID,
      accountId: expect.any(String),
    },
    webhookId: expect.any(String),
    timestamp: expect.any(String),
  })

  // espera chegada do evento de criação no wobhook
  const updatedEvent = await waitForWebhookEventById(webhookSv, MESSAGE_UPDATED_EVENT, request.data.id)

  // verifica payload do evento do webhook
  expect(updatedEvent).toMatchObject({
    event: MESSAGE_UPDATED_EVENT,
    data: {
      id: request.data.id,
      contactId,
      fromId: expect.any(String),
      type: 'chat',
      isFromMe: true,
      timestamp: expect.any(String),
      text,
      ack: expect.any(String),
      data: { ack: expect.any(Number) },
      serviceId: SERVICE_ID,
      accountId: expect.any(String),
    },
    webhookId: expect.any(String),
    timestamp: expect.any(String),
  })
}
