import webhookServer, { WebhookServer } from '../utils/webhookServer'
import { waitForWebhookEventById } from '../utils/apiHelpers'
import addOrRemove9ForBrazilianNumber from '../../../core/utils/addOrRemove9ForBrazilianNumber'
import queuedAsyncTimes from '../../../core/utils/array/queuedAsyncTimes'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import { numberToId } from '../../../core/utils/whatsapp/numberParser'
import {
  CONTACT_ID,
  NUMBER,
  SERVICE_ID,
  TEST_TUNNEL,
  TEST_TUNNEL_HOST,
  MESSAGE_CREATED_EVENT,
  MESSAGE_UPDATED_EVENT,
  BLOCKED_CONTACT_ID,
  OLD_MESSAGE_ID,
  ensureServiceIsConnected,
  expectTextSendAndReceiveFlow,
  sendMessage,
  setWebhook,
  unsetWebhook,
  revokeMessage,
  messageUpdatedPayload,
  messageCreatedPayload,
  sendMessageOld,
} from './base'

jest.setTimeout(60 * 60 * 1000)

describe('whatsappLowLevel', () => {
  let webhookSv: WebhookServer
  let webhook: { id: string }

  beforeAll(async () => {
    await ensureServiceIsConnected(SERVICE_ID)
    webhookSv = await webhookServer({
      // handler: async (req) => console.log(req.body),
      tunnel: TEST_TUNNEL,
      tunnelHost: TEST_TUNNEL_HOST,
    })
    webhook = await setWebhook(webhookSv.url, SERVICE_ID).then((r) => r.data)
  })

  afterAll(async () => {
    await webhookSv.stop()
    await unsetWebhook(webhook.id)
  })

  test.each([true, false])('send and receive flow with queued=%s', async (queued) => {
    const data = {
      contactId: CONTACT_ID,
      text: `a message with queued=${queued}`,
      queued,
    }
    const request = await sendMessage(data)

    await expectTextSendAndReceiveFlow(webhookSv, request, data.text)
  })

  test('send and receive flow using old send endpoint', async () => {
    const data = {
      contactId: CONTACT_ID,
      text: `a message using old endpoint`,
    }
    const request = await sendMessageOld(data)

    await expectTextSendAndReceiveFlow(webhookSv, request, data.text)
  })

  test('send and receive flow using old send endpoint with wrong 9', async () => {
    const data = {
      contactId: numberToId(addOrRemove9ForBrazilianNumber(NUMBER)),
      text: `a message using old endpoint with wrong 9`,
    }
    const request = await sendMessageOld(data)

    await expectTextSendAndReceiveFlow(webhookSv, request, data.text)
  })

  test('send text message using number', async () => {
    const data = {
      number: NUMBER,
      text: `a text message using number`,
    }
    const request = await sendMessage(data)

    await expectTextSendAndReceiveFlow(webhookSv, request, data.text)
  })

  test('send text message using wrong 9', async () => {
    const numberWithWrong9 = addOrRemove9ForBrazilianNumber(NUMBER)

    const data = {
      number: numberWithWrong9,
      text: `a text message using wrong 9`,
      queued: true,
    }
    const request = await sendMessage(data)

    await expectTextSendAndReceiveFlow(webhookSv, request, data.text)
  })

  test('send text reply', async () => {
    const preData = {
      contactId: CONTACT_ID,
      text: `a message to be replied`,
    }
    const preRequest = await sendMessage(preData)
    await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, preRequest.data.id)

    const data = {
      contactId: CONTACT_ID,
      text: `a reply message`,
      quotedMessageId: preRequest.data.id,
    }
    const request = await sendMessage(data)

    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    expect(creationEvent).toMatchObject(
      messageCreatedPayload({
        data: {
          id: request.data.id,
          contactId: CONTACT_ID,
          text: data.text,
          serviceId: SERVICE_ID,
          quotedMessage: {
            id: preRequest.data.id,
            contactId: CONTACT_ID,
            fromId: expect.any(String),
            type: 'chat',
            isFromMe: true,
            timestamp: expect.any(String),
            text: preData.text,
            ack: expect.any(String),
            data: { ack: expect.any(Number) },
          },
        },
      }),
    )

    const updatedEvent = await waitForWebhookEventById(webhookSv, MESSAGE_UPDATED_EVENT, request.data.id)

    expect(updatedEvent).toMatchObject(
      messageUpdatedPayload({
        data: {
          id: request.data.id,
          contactId: CONTACT_ID,
          text: data.text,
          serviceId: SERVICE_ID,
          quotedMessage: {
            id: preRequest.data.id,
            contactId: CONTACT_ID,
            fromId: expect.any(String),
            type: 'chat',
            isFromMe: true,
            timestamp: expect.any(String),
            text: preData.text,
            ack: expect.any(String),
            data: { ack: expect.any(Number) },
          },
        },
      }),
    )
  })

  test('revoke message', async () => {
    const preData = {
      contactId: CONTACT_ID,
      text: `a message to be revoked`,
    }
    const preRequest = await sendMessage(preData)
    await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, preRequest.data.id)

    await revokeMessage(preRequest.data.id)

    const updatedEvent = await waitForWebhookEventById(
      webhookSv,
      MESSAGE_UPDATED_EVENT,
      preRequest.data.id,
      (e) => !!e.data.revokedMessageNewId,
    )

    expect(updatedEvent).toMatchObject(
      messageUpdatedPayload({
        data: {
          id: preRequest.data.id,
          revokedMessageNewId: expect.any(String),
          contactId: CONTACT_ID,
          serviceId: SERVICE_ID,
          text: null,
          type: 'revoked',
        },
      }),
    )
  })

  test.each([true, false])('try to send message without destination (queued=%s)', async (queued) => {
    const data = {
      text: 'test',
      queued,
    }
    const error = await sendMessage(data).catch((e) => e.response)

    expect(error).toMatchObject({
      status: 400,
      data: {
        error: 'ValidationError',
        message: 'The given data was invalid.',
        status: 400,
        errors: {
          body: {
            contactId: {
              messages: ['Required if "number" was not given.'],
              types: ['requiredIfOtherNotPresent'],
            },
            number: {
              messages: ['Required if "contactId" was not given.'],
              types: ['requiredIfOtherNotPresent'],
            },
          },
        },
      },
    })
  })

  test('try to revoke non existent message', async () => {
    const error = await revokeMessage('true_554796654143@c.us_3EB0EA3A8C443E8B3451').catch((e) => e.response)

    expect(error).toMatchObject({
      status: 400,
      data: {
        error: 'HttpError',
        message: 'No message found with the sent id.',
        status: 400,
      },
    })
  })

  test('try to revoke old message', async () => {
    if (!OLD_MESSAGE_ID) {
      console.warn('Env "TEST_OLD_MESSAGE_ID not set, skipping test.')
      return
    }

    const error = await revokeMessage(OLD_MESSAGE_ID).catch((e) => e.response)

    expect(error).toMatchObject({
      status: 400,
      data: {
        error: 'HttpError',
        message: 'Cannot revoke message.',
        status: 400,
      },
    })
  })

  // TODO https://ikatec.atlassian.net/browse/INFRA-668
  test.skip('revoke many messages in parallel', async () => {
    const preRequests = await queuedAsyncTimes(
      async (i) => {
        const preData = {
          contactId: CONTACT_ID,
          text: `a message to be revoked in parallel ${i}`,
        }
        const preRequest = await sendMessage(preData)
        await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, preRequest.data.id)

        return preRequest
      },
      5,
      1,
    )

    await queuedAsyncMap(
      preRequests,
      async (preRequest) => {
        const res = await revokeMessage(preRequest.data.id)
        expect(res.status).toBe(200)
      },
      5,
    )

    await queuedAsyncMap(
      preRequests,
      async (preRequest) => {
        const updatedEvent = await waitForWebhookEventById(
          webhookSv,
          MESSAGE_UPDATED_EVENT,
          preRequest.data.id,
          (e) => !!e.data.revokedMessageNewId,
        )

        expect(updatedEvent).toMatchObject(
          messageUpdatedPayload({
            data: {
              id: preRequest.data.id,
              revokedMessageNewId: expect.any(String),
              contactId: CONTACT_ID,
              serviceId: SERVICE_ID,
              text: null,
              type: 'revoked',
            },
          }),
        )
      },
      5,
    )
  })

  test.each([
    {
      type: 'image',
      mimetype: 'image/jpeg',
      url: 'https://upload.wikimedia.org/wikipedia/en/a/a9/Example.jpg',
      preview: true,
    },
    {
      type: 'sticker',
      mimetype: 'image/jpeg',
      receiveMimetype: 'image/webp',
      url: 'https://upload.wikimedia.org/wikipedia/en/a/a9/Example.jpg',
      asSticker: true,
    },
    {
      type: 'document',
      mimetype: 'image/jpeg',
      url: 'https://upload.wikimedia.org/wikipedia/en/a/a9/Example.jpg',
      preview: false,
      asDocument: true,
      name: 'Example.jpg',
    },
    {
      type: 'document',
      mimetype: 'application/msword',
      url: 'https://filesamples.com/samples/document/doc/sample2.doc',
      name: 'sample2.doc',
    },
    {
      type: 'document',
      mimetype: 'application/pdf',
      url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      name: 'sample3.pdf',
      preview: true,
    },
    {
      type: 'video',
      mimetype: 'video/mp4',
      url: 'https://objectstorage.sa-vinhedo-1.oraclecloud.com/n/axvaplbwrlcl/b/tests-fixtures/o/sample_960x540.mp4',
      preview: true,
    },
    // {
    //   type: 'video',
    //   mimetype: 'video/webm',
    //   url: 'https://dl6.webmfiles.org/big-buck-bunny_trailer.webm',
    //   preview: false,
    //   asDocument: false,
    // },
    {
      type: 'audio',
      mimetype: 'audio/mp3',
      receiveMimetype: 'audio/mpeg',
      url: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/0/00/C_P_E_Bach_Solfeggio.mid/C_P_E_Bach_Solfeggio.mid.mp3',
    },
    {
      type: 'ptt',
      mimetype: 'audio/mp3',
      receiveMimetype: 'audio/mpeg',
      url: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/0/00/C_P_E_Bach_Solfeggio.mid/C_P_E_Bach_Solfeggio.mid.mp3',
      isPtt: true,
    },
    {
      type: 'ptt',
      mimetype: 'audio/ogg',
      receiveMimetype: 'audio/ogg; codecs=opus',
      url: 'https://axvaplbwrlcl.objectstorage.sa-vinhedo-1.oci.customer-oci.com/n/axvaplbwrlcl/b/tests-fixtures/o/WhatsApp%20Ptt%202024-04-04%20at%2011.27.31%20PM.ogg',
      isPtt: true,
    },
  ])('send message of type $type and mimetype $mimetype', async (row) => {
    const data = {
      contactId: CONTACT_ID,
      text: row.mimetype.startsWith('audio/') ? null : `a ${row.type} caption`,
      file: {
        url: row.url,
        mimetype: row.mimetype,
        ...(row.name && {
          name: row.name,
        }),
        ...(row.asSticker && {
          asSticker: true,
        }),
        ...(row.asDocument && {
          asDocument: true,
        }),
      },
      ...(row.isPtt && {
        isPtt: true,
      }),
      queued: true,
    }
    const request = await sendMessage(data)

    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    expect(creationEvent).toMatchObject(
      messageCreatedPayload({
        data: {
          id: request.data.id,
          contactId: CONTACT_ID,
          text: data.text,
          serviceId: SERVICE_ID,
          type: row.type,
          ...(row.preview && {
            preview: {
              mimetype: 'image/jpeg',
              url: expect.any(String),
            },
          }),
          file: {
            mimetype: row.receiveMimetype || row.mimetype,
            url: expect.any(String),
          },
        },
      }),
    )

    const updatedEvent = await waitForWebhookEventById(webhookSv, MESSAGE_UPDATED_EVENT, request.data.id)

    expect(updatedEvent).toMatchObject(
      messageUpdatedPayload({
        data: {
          id: request.data.id,
          contactId: CONTACT_ID,
          text: data.text,
          serviceId: SERVICE_ID,
          type: row.type,
        },
      }),
    )
  })

  test('try to send large (>16mb) image file (queued=false)', async () => {
    const data = {
      contactId: CONTACT_ID,
      file: {
        url: 'https://getsamplefiles.com/download/bmp/sample-3.bmp',
        mimetype: 'image/bmp',
      },
      queued: false,
    }
    const error = await sendMessage(data).catch((e) => e.response)

    expect(error).toMatchObject({
      status: 400,
      data: {
        error: 'BadRequestHttpError',
        message: 'The file is too large.',
        status: 400,
      },
    })
  })

  test('try to send large (>16mb) image file (queued=true)', async () => {
    const data = {
      contactId: CONTACT_ID,
      file: {
        url: 'https://getsamplefiles.com/download/bmp/sample-3.bmp',
        mimetype: 'image/bmp',
      },
      queued: true,
    }
    const request = await sendMessage(data)

    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    expect(creationEvent).toMatchObject({
      event: MESSAGE_CREATED_EVENT,
      data: {
        id: request.data.id,
        ack: 'error',
        contactId: data.contactId,
        error: 'FILE_TOO_LARGE',
      },
    })
  })

  test('receive webhook after a failure on first try', async () => {
    webhookSv.setHandler(() => {
      throw new Error('Will fail')
    })

    // envia mensagem
    const request = await sendMessage({
      contactId: CONTACT_ID,
      text: 'a message',
    })

    webhookSv.resetHandler()

    // espera chegada do evento de criação no webhook
    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    // verifica payload do evento do webhook
    expect(creationEvent).toMatchObject({
      event: MESSAGE_CREATED_EVENT,
      data: {
        id: request.data.id,
        ack: 'pending',
      },
    })

    const updatedEvent = await waitForWebhookEventById(webhookSv, MESSAGE_UPDATED_EVENT, request.data.id)

    expect(updatedEvent).toMatchObject({
      event: MESSAGE_UPDATED_EVENT,
      data: {
        id: request.data.id,
        ack: expect.any(String),
      },
    })

    expect(['sent', 'delivered'].includes(updatedEvent.data.ack)).toBeTruthy()
  })

  test('send message to wrong number with queued=true', async () => {
    // envia mensagem
    const data = {
      contactId: '<EMAIL>',
      text: 'a message to wrong number',
      queued: true,
    }
    const request = await sendMessage(data)

    // espera chegada do evento de criação no wobhook
    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    // verifica payload do evento do webhook
    expect(creationEvent).toMatchObject({
      event: MESSAGE_CREATED_EVENT,
      data: {
        id: request.data.id,
        ack: 'error',
        contactId: data.contactId,
        error: 'INVALID_CONTACT_ID',
      },
    })
  })

  test('send message to blocked contact with queued=false', async () => {
    // envia mensagem
    const data = {
      contactId: BLOCKED_CONTACT_ID,
      text: 'test message',
      queued: false,
    }

    const error = await sendMessage(data).catch((e) => e.response)

    expect(error).toMatchObject({
      status: 400,
      data: {
        error: 'HttpError',
        message: 'The contact is blocked.',
        status: 400,
      },
    })
  })

  test('send message to blocked contact with queued=true', async () => {
    // envia mensagem
    const data = {
      contactId: BLOCKED_CONTACT_ID,
      text: 'test message',
      queued: true,
    }
    const request = await sendMessage(data)

    // espera chegada do evento de criação no webhook
    const creationEvent = await waitForWebhookEventById(webhookSv, MESSAGE_CREATED_EVENT, request.data.id)

    // verifica payload do evento do webhook
    expect(creationEvent).toMatchObject({
      event: MESSAGE_CREATED_EVENT,
      data: {
        id: request.data.id,
        ack: 'error',
        contactId: data.contactId,
        error: 'BLOCKED_CONTACT',
      },
    })
  })
})
