import { Method } from 'axios'
import { makeAxios } from '../../setup/setupApp'
import env from '../../../core/utils/env'
import { WebhookServer } from './webhookServer'
import pooling from '../../../core/utils/pooling/pooling'

const TEST_DEBUG = env.boolean('TEST_DEBUG')
const BASE_URL = env('TEST_API_BASE_URL')
const ACCESS_TOKEN = env('TEST_ACCESS_TOKEN')

const log = TEST_DEBUG ? console.debug : () => {}
const logError = TEST_DEBUG ? console.error : () => {}
const logTrace = TEST_DEBUG ? console.trace : () => {}

export const getAuthedAxios = (options = {}) => {
  const o = {
    baseURL: BASE_URL,
    headers: {
      Authorization: `Bearer ${ACCESS_TOKEN}`,
      Accept: 'application/json',
    },
    ...options,
  }
  return makeAxios(o)
}

export const sendRequest = async (method: Method, url: string, data?: any) => {
  log('sending %o to %o', method, url)
  const { stack } = new Error()
  return getAuthedAxios()
    .request({ method, url, data })
    .catch((error) => {
      logError(JSON.stringify(error.response.data, null, 2))
      error.stack = stack
      throw error
    })
}

export const sendPost = async (url: string, data?: any) => sendRequest('post', url, data)

export const sendPut = async (url: string, data?: any) => sendRequest('put', url, data)

export const sendGet = async (url: string) => sendRequest('get', url)

export const sendDelete = async (url: string) => sendRequest('delete', url)

export const waitForWebhookEventById = async (
  webhookSv: WebhookServer,
  type,
  id,
  filter?: (event) => boolean,
  pollingOptions: { timeout?: number; interval?: number } = {
    timeout: 30 * 1000,
    interval: 100,
  },
) => {
  log('waiting for event %o of id %o', type, id)

  return pooling(async () => {
    const events = webhookSv.handler.mock.calls
    const res = events
      .map(([req]) => req.body)
      .find((e) => e.event === type && e.data.id === id && (filter ? filter(e) : true))

    if (res) log('found webhook %o', JSON.stringify(res, null, 2))

    return res
  }, pollingOptions)
}
