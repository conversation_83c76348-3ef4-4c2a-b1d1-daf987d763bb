import express, { Request, Response } from 'express'
import { createHttpTerminator } from 'http-terminator'
import http from 'http'
import localtunnel from 'localtunnel'
import ngrok from 'ngrok'
import bodyParser from 'body-parser'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'

type Options = {
  handler?: (req: Request, res: Response) => Promise<any>
  port?: number
  method?: string
  tunnel?: 'ngrok' | 'localtunnel'
  tunnelHost?: string
}

type Awaited<T> = T extends PromiseLike<infer U> ? U : T

export type WebhookServer = Awaited<ReturnType<typeof webhookServer>>

const tunnelMakers = {
  ngrok: async ({ port }: { port: number }) => {
    return ngrok.connect({
      addr: `127.0.0.1:${port}`,
    })
  },
  localtunnel: async ({ port, host }: { port: number; host?: string }) => {
    const tunnel = await localtunnel({ port, host })
    return tunnel.url
  },
}

const webhookServer = async (options: Options = {}) => {
  const app = express()

  app.use(bodyParser.json({ limit: '100mb' }))

  const defaultHandler = () => true
  let innerHandler = options.handler || defaultHandler

  const fn = promiseHandler(async (req, res) => innerHandler(req, res))
  const port = options.port || 0
  const method = options.method || 'post'
  const endpoint = '/webhook'

  const handler = jest.fn(fn)
  app[method](endpoint, handler)

  const server = await new Promise<http.Server>((resolve) => {
    const s = app.listen(port, () => resolve(s))
  })

  const httpTerminator = createHttpTerminator({
    server,
  })

  // @ts-ignore
  const actualPort = server.address().port

  const baseUrl = await tunnelMakers[options.tunnel || 'ngrok']({
    port: actualPort,
    host: options.tunnelHost,
  })

  const url = `${baseUrl}${endpoint}`

  const setHandler = (h: Options['handler']) => {
    innerHandler = h
  }
  const resetHandler = () => {
    innerHandler = defaultHandler
  }

  return {
    app,
    server,
    stop: httpTerminator.terminate,
    handler,
    url,
    setHandler,
    resetHandler,
  }
}

export default webhookServer
