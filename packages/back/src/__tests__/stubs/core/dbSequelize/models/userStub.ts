import { UserInstance } from '../../../../../core/dbSequelize/models/User'

export const userStub: UserInstance = {
  id: '123e4567-e89b-12d3-a456-************',
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+*************',
  branch: 'SP',
  password: 'hashed_password',
  activationToken: 'activation_token',
  resetPasswordToken: 'reset_token',
  isSuperAdmin: false,
  isClientUser: false,
  active: true,
  isFirstLogin: false,
  accountId: 'acc-123',
  account: {
    id: '513eb427-b134-48dc-bbab-ad7aa7b7ec50',
    name: 'Conta 1',
    settings: {
      flags: {
        'bots-v2': false,
        'bots-v3': true,
        distribution: false,
        'internal-chat': false,
        'enable-chatgpt': false,
        'enable-copilot': true,
        'search-messages': true,
        'disable-hsm-limit': false,
        'enable-magic-text': true,
        'absence-management': false,
        'enable-sales-funnel': true,
        'enable-smart-summary': true,
        'enable-audio-waveform': true,
        'enable-smart-csat-score': true,
        'enable-audio-transcription': false,
        'invalid-webhooks-inactivator': false,
        'by-user-and-by-department-tabs': false,
        'use-block-message-rules-by-service': false,
      },
      drivers: {
        email: true,
        webchat: false,
        'sms-wavy': true,
        telegram: true,
        whatsapp: true,
        instagram: true,
        'reclame-aqui': true,
        'whatsapp-business': false,
        'facebook-messenger': true,
        'whatsapp-remote-pod': false,
        'google-business-message': true,
      },
      campaign: {
        'sms-wavy': true,
        whatsapp: false,
        'auto-pause-mode': 'disabled',
        'whatsapp-business': false,
      },
      timezone: 'ESAST',
      workPlan: [],
      allowedIps: [],
      ipRestriction: false,
      topicRequired: false,
      protocolFormat: '{{date}}{{count}}',
      showTagsInChat: false,
      ticketsEnabled: true,
      showSupportInfo: true,
      ticketInactiveTime: 30,
      userNameInMessages: false,
      allowDuplicateNames: false,
      isUserEmailRequired: true,
      twoFactorAuthActive: true,
      userAwayMinutesTime: 5,
      expirationPasswordTime: 5,
      newMessageNotification: true,
      ticketOpenNotification: true,
      twoFactorAuthMandatory: false,
      smartCsatScoreEnabledAt: '2025-07-04T18:21:43.803Z',
      isQueueNotificationActive: false,
      isPasswordExpirationActive: false,
      ticketTransferNotification: true,
      userPasswordCreationMethod: 'manual',
      autoGenerateSummaryOnClosure: true,
      disableDefaultTicketTransfer: false,
      autoGenerateSummaryOnTransfer: true,
      twoFactorAuthMandatorySchedule: null,
      changeUserPasswordOnFirstAccess: false,
      userCanChangeVisibilityTagsOnChat: false,
    },
  } as any,
  roles: [],
  departments: [],
  departmentsDefault: [],
  timetableId: 'tt-123',
  timetable: {} as any,
  data: {
    sentResetPasswordAt: new Date(),
    oneSignalToken: 'onesignal_token',
    expoPushToken: 'expo_token',
    domain: 'example.com',
  },
  internalChatToken: 'chat_token',
  archivedAt: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  status: 'online',
  clientsStatus: {
    web: 'online',
    app: 'offline',
  },
  offlineAt: new Date(),
  hasPermission: (permission: string) => permission === 'admin',
  getAccount: async () => ({} as any),
  getRoles: async () => [],
  getDepartments: async () => [],
  permissions: [],
  language: 'pt-BR',
  countTickets: 10,
  isActiveInternalChat: true,
  passwordExpiresAt: new Date(Date.now() + 1000000),
  hasPasswordExpired: false,
  authHistory: [],
  otpSecretKey: 'otp_secret',
  otpAuthActive: true,
  preferences: {
    audioSpeed: 1.5,
    isShowTagsChat: true,
  },
}
