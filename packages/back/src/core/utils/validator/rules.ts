import * as validators from './validators'
import { Validator } from './validators'
import zxcvbnHelper from '../zxcvbnHelper'

export type Rule = {
  validator: Validator
  message: string | ((...args: any[]) => string)
  name: string
}

export const required: Rule = {
  validator: validators.required,
  message: 'Required.',
  name: 'required',
}

export const requiredIfOtherPresent = (otherKey: string): Rule => ({
  validator: validators.requiredIfOtherPresent(otherKey),
  message: ({ key }) => `Required if "${otherKey}" was given.`,
  name: 'requiredIfOtherPresent',
})

export const requiredIfOthersNotPresent = (othersKey: string[]): Rule => ({
  validator: () => othersKey.every((otherKey) => validators.requiredIfOtherNotPresent(otherKey)),
  message: ({ key }) => `Required if "${othersKey.join(', ')}" was not given.`,
  name: 'requiredIfOtherNotPresent',
})

export const requiredIfOtherNotPresent = (otherKey: string): Rule => ({
  validator: validators.requiredIfOtherNotPresent(otherKey),
  message: ({ key }) => `Required if "${otherKey}" was not given.`,
  name: 'requiredIfOtherNotPresent',
})

export const requiredIfOtherFieldIsTrue = (otherFiled: string): Rule => ({
  validator: validators.requiredIfOtherFieldIsTrue(otherFiled),
  message: () => `Required if "${otherFiled}" was given true.`,
  name: 'requiredIfOtherFieldIsTrue',
})

export const string: Rule = {
  validator: validators.acceptBlank(validators.string),
  message: 'Should be of type string.',
  name: 'string',
}

export const requiredStringTrim: Rule = {
  validator: ({ inputData, key }) => {
    if (typeof inputData?.[key] === 'string' && typeof inputData?.[key]?.trim === 'function') {
      inputData[key] = inputData[key].trim()

      if (inputData[key]) {
        return true
      }
    }

    return false
  },
  message: 'Required string with trim',
  name: 'requiredStringTrim',
}

export const date: Rule = {
  validator: validators.acceptBlank(validators.date),
  message: 'Should be of type Date.',
  name: 'date',
}

export const oneOf = (values: string[]): Rule => ({
  validator: validators.acceptBlank(validators.oneOf(values)),
  message: `Should be one of the values: ${values.join(', ')}.`,
  name: 'oneOf',
})

export const number: Rule = {
  validator: validators.acceptBlank(validators.number),
  message: 'Should be of type number.',
  name: 'number',
}

export const boolean: Rule = {
  validator: validators.acceptBlank(validators.boolean),
  message: 'Should be of type boolean.',
  name: 'boolean',
}

export const array: Rule = {
  validator: validators.acceptBlank(validators.array),
  message: 'Should be an array.',
  name: 'array',
}

export const arrayOf = (rule: Rule): Rule => ({
  validator: validators.acceptBlank(validators.arrayOf(rule.validator)),
  message: `Should be an array of ${rule.name}.`,
  name: 'arrayOf',
})

export const regex = (reg: RegExp | string): Rule => ({
  validator: validators.acceptBlank(validators.regex(reg)),
  message: `Should match regex "${reg}".`,
  name: 'regex',
})

export const uuid4: Rule = {
  validator: validators.acceptBlank(validators.uuid4),
  message: 'Should be an UUID v4 string.',
  name: 'uuid4',
}

export const isGreaterThan = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.isGreaterThan(num)),
  message: `Should be greater than ${num}.`,
  name: 'isGreaterThan',
})

export const isGreaterThanOrEqual = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.isGreaterThanOrEqual(num)),
  message: `Should be greater or equal ${num}.`,
  name: 'isGreaterThanOrEqual',
})

export const hasLengthGreaterThan = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.hasLengthGreaterThan(num)),
  message: `Should contain more than ${num} characters.`,
  name: 'hasLengthGreaterThan',
})

export const hasLengthGreaterThanOrEqual = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.hasLengthGreaterThanOrEqual(num)),
  message: `Should contain at least ${num} characters.`,
  name: 'hasLengthGreaterThanOrEqual',
})

export const hasLengthLesserThan = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.hasLengthLesserThan(num)),
  message: `Should contain lesser than ${num} characters.`,
  name: 'hasLengthLesserThan',
})

export const hasLengthLesserThanOrEqual = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.hasLengthLesserThanOrEqual(num)),
  message: `Should contain lesser than or equals ${num} characters.`,
  name: 'hasLengthLesserThanOrEqual',
})

export const arrayHasLengthLesserThanOrEqual = (num: number): Rule => ({
  validator: validators.acceptBlank(validators.arrayHasLengthLesserThanOrEqual(num)),
  message: `Should contain lesser than or equals ${num} indexes.`,
  name: 'arrayHasLengthLesserThanOrEqual',
})

const dayIsValid = (weekDays: string[]) => {
  const weekDaysValid = new Set(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])

  const resultWeek = weekDays.filter((day) => weekDaysValid.has(day))

  return resultWeek.length === weekDays.length
}

const workPlanIsValid = (workPlan) => {
  const valuesEmpty = workPlan.every(
    (v) => v?.weekDays && v?.start && v?.end && v?.end.replace(':', '') > v?.start.replace(':', ''),
  )

  if (!valuesEmpty) {
    return false
  }

  return workPlan
    .filter((item) => item.weekDays.length)
    .map((item) => dayIsValid(item.weekDays))
    .every((v) => v)
}

export const validTimeTable = (): Rule => ({
  validator: ({ value }) => workPlanIsValid(value),
  message: () => 'Should be contained the fields end, start and weekDays.',
  name: 'validTimeTable',
})

export const hasLowerCaseCharacter = (): Rule => ({
  validator: validators.acceptBlank(validators.hasLowerCaseCharacter),
  message: `Should contain at least one lowercase letter.`,
  name: 'hasLowerCaseCharacter',
})

export const hasUpperCaseCharacter = (): Rule => ({
  validator: validators.acceptBlank(validators.hasUpperCaseCharacter),
  message: `Should contain at least one uppercase letter.`,
  name: 'hasUpperCaseCharacter',
})

export const hasDigitCharacter = (): Rule => ({
  validator: validators.acceptBlank(validators.hasDigitCharacter),
  message: `Should contain at least one numeric digit.`,
  name: 'hasDigitCharacter',
})

export const hasSpecialCharacter = (): Rule => ({
  validator: validators.acceptBlank(validators.hasSpecialCharacter),
  message: `Should contain at least one special character.`,
  name: 'hasSpecialCharacter',
})

export const notContainKnownPatternsInZxcvbnResult = (): Rule => ({
  validator: validators.acceptBlank(
    validators.notContainKnownPatternsInZxcvbnResult(zxcvbnHelper.getPasswordPatterns()),
  ),
  message: 'Should not contain any known sequence. Like: 000,123,aaa,abc,... Try input another characters sequence.',
  name: 'notContainKnownPatternsInZxcvbnResult',
})

export const isContainedInEnum = (enumObj): Rule => ({
  validator: ({ value }) => {
    const enumValues = Object.values(enumObj)
    return enumValues.includes(value)
  },
  message: `The value is not valid. Allowed values are: ${Object.values(enumObj).join(', ')}`,
  name: 'isContainedInEnum',
})
