import http from 'http'
import ExtendableError from './ExtendableError'

class HttpError extends ExtendableError {
  status: number

  extra?: {}

  constructor(status = 500, message = http.STATUS_CODES[status], extra?: {}, cause?: unknown) {
    super()
    this.name = 'HttpError'
    this.status = status
    this.message = message
    this.extra = extra
    this.cause = cause
  }
}

export default HttpError
