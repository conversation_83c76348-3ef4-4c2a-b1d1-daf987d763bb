import BaseResource from './BaseResource'
import { CopilotTranscriptionInstance } from '../dbSequelize/models/CopilotTranscription'
import copilotTranscriptionsRepository from '../dbSequelize/repositories/copilotTranscriptionsRepository'

export class CopilotTranscriptionResource extends BaseResource<CopilotTranscriptionInstance> {
  constructor() {
    super(copilotTranscriptionsRepository)
  }
}

export default new CopilotTranscriptionResource()
