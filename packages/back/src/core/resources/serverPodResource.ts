import { QueryTypes } from 'sequelize'
import BaseResource from './BaseResource'
import serverPodRepository from '../dbSequelize/repositories/serverPodRepository'
import ServerPod, { ServerPodInstance } from '../dbSequelize/models/ServerPod'
import sequelize from '../services/db/sequelize'

export class ServerPodResource extends BaseResource<ServerPodInstance> {
  constructor() {
    super(serverPodRepository)
  }

  async getAvailable(serviceId: string, accountId: string): Promise<ServerPodInstance> {
    const res = await sequelize.query(
      `select s.* from server_pods s inner join server_pod_types spt on s."serverPodTypeId" = spt.id
where s."deletedAt" is null and jsonb_array_length(s."serviceIds") < spt."maxPerPod"
    and (jsonb_array_length(spt."accountIdWhitelist") = 0 or spt."accountIdWhitelist" ? :accountId)
    and (jsonb_array_length(spt."serviceIdWhitelist") = 0 or spt."serviceIdWhitelist" ? :serviceId)
order by spt."serviceIdWhitelist" ? :serviceId desc,
         spt."accountIdWhitelist" ? :accountId desc,
         spt."startPriority" desc,
         jsonb_array_length(s."serviceIds") desc,
         s."createdAt" asc,
         s.name asc
limit 1;`,
      {
        replacements: { serviceId, accountId },
        model: ServerPod,
        mapToModel: true,
      },
    )

    if (!res.length) return null

    return res[0]
  }

  async getServerPodToDestroy(type: string): Promise<ServerPodInstance> {
    const res = await sequelize.query(
      `select sp.* from server_pods sp inner join server_pod_types spt on sp."serverPodTypeId" = spt.id
       where sp."deletedAt" is null and spt.name = :type and jsonb_array_length(sp."serviceIds") = 0 limit 1;`,
      {
        replacements: { type },
        model: ServerPod,
        mapToModel: true,
      },
    )

    if (!res.length) return null

    return res[0]
  }

  async setService(serverPodId: string, serviceId: string) {
    const res = await sequelize.query(
      `update server_pods set "serviceIds"  = "serviceIds" || array_to_json(array[:serviceId])::jsonb
          where id = :serverPodId and not ("serviceIds" ? :serviceId);`,
      { replacements: { serviceId, serverPodId }, type: QueryTypes.UPDATE },
    )

    const hasSet = res[1] === 1

    if (!hasSet) {
      throw new Error(`Failed to set "${serviceId}" to server pod "${serverPodId}".`)
    }

    await this.getRepository().getCached().forgetCacheById(serverPodId)

    return hasSet
  }

  async removeService(serverPodId: string, serviceId: string) {
    try {
      await sequelize.query(
        `UPDATE server_pods SET "serviceIds" = regexp_replace("serviceIds"::text,'"${serviceId}:[a-zA-Z0-9=+\\/]{28}"(, )?|(, )?"${serviceId}:[a-zA-Z0-9=+\\/]{28}"','','g')::json;`,
      )

      await this.getRepository().getCached().forgetCacheById(serverPodId)

      return true
    } catch (e) {
      throw new Error(`Failed to remove "${serviceId}" from server pod "${serverPodId}".`)
    }
  }

  async getCurrentServerPod(serviceId: string): Promise<ServerPodInstance> {
    const res = await sequelize.query(
      `select sp.* from server_pods sp where sp."deletedAt" is null and sp."serviceIds" ? :serviceId limit 1;`,
      {
        replacements: { serviceId },
        type: QueryTypes.SELECT,
        model: ServerPod,
        mapToModel: true,
      },
    )

    if (!res.length) return null

    return res[0]
  }

  async getSlotsForType(type: string) {
    const res = await sequelize.query(
      `select (spt."maxPerPod" * spCount.count)                as total,
              spUsed.sum                                       as used,
              ((spt."maxPerPod" * spCount.count) - spUsed.sum) as remaining
       from server_pod_types spt,
           lateral (select sum(jsonb_array_length(sp."serviceIds")) sum
       from server_pods sp
       where sp."deletedAt" is null and sp."serverPodTypeId" = spt.id) spUsed,
           lateral (select count(sp.id) count from server_pods sp where sp."deletedAt" is null and sp."serverPodTypeId" = spt.id) spCount
       where spt.name = :type;`,
      {
        replacements: {
          type,
        },
        type: QueryTypes.SELECT,
      },
    )

    return {
      total: Number(res[0].total),
      used: Number(res[0].used),
      remaining: Number(res[0].remaining),
    }
  }
}

export default new ServerPodResource()
