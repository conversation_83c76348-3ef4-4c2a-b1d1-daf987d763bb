import BaseResource from './BaseResource'
import holidayRepository from '../dbSequelize/repositories/holidayRepository'
import { HolidayInstance } from '../dbSequelize/models/Holiday'
import HttpError from '../utils/error/HttpError'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

const getQuery = (data: { accountId: string; from: Date; to: Date; id?: string }) => {
  const { accountId, from, to, id = null } = data

  return {
    where: {
      accountId,
      id: { $ne: id },
      $or: {
        from: {
          $between: [from, to],
        },
        to: {
          $between: [from, to],
        },
      },
    },
  }
}

export class HolidayResource extends BaseResource<HolidayInstance> {
  constructor() {
    super(holidayRepository)
  }

  async create(data: any, options: any) {
    const countHolidayPeriod = await this.count(getQuery(data))

    if (countHolidayPeriod) {
      throw new HttpError(402, 'Holiday time already exists.')
    }

    return holidayRepository.create(data, options)
  }

  async updateById(id: string, data: any, options: any) {
    const countHolidayPeriod = await this.count(getQuery({ ...data, id }))

    if (countHolidayPeriod) {
      throw new HttpError(402, 'Holiday time already exists.')
    }

    return holidayRepository.updateById(id, data, options)
  }

  async createMany(data: HolidayInstance[], options: any) {
    await queuedAsyncMap<HolidayInstance, HolidayInstance[]>(data, async (holidayData) =>
      this.bulkDestroy(getQuery(holidayData)),
    )

    return holidayRepository.bulkCreate(data, options)
  }
}

export default new HolidayResource()
