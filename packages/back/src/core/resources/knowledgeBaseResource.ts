import { Container } from 'typedi'
import BaseResource from './BaseResource'
import { KnowledgeBaseInstance } from '../dbSequelize/models/KnowledgeBase'
import knowledgeBaseRepository from '../dbSequelize/repositories/knowledgeBaseRepository'
import knowledgeBaseItemRepository from '../dbSequelize/repositories/knowledgeBaseItemRepository'
import knowledgeBaseItemDocRepository from '../dbSequelize/repositories/knowledgeBaseItemDocRepository'
import HaystackIaApi from '../services/haystackIa'
import { CopilotTranscriptionInstance } from '../../core/dbSequelize/models/CopilotTranscription'
import { MessageInstance } from '../../core/dbSequelize/models/Message'
import messageResource from './messageResource'
import copilotTranscriptionResource from './copilotTranscriptionResource'
import queuedAsyncMap from '../../core/utils/array/queuedAsyncMap'
import QueueAudioTranscribeJob from '../../microServices/workers/jobs/transcribe/QueuedAudioTranscribeJob'
import QueueJobsDispatcher from '../../core/services/jobs/queue/QueueJobsDispatcher'
import creditMovementResource from './creditMovementResource'
import Logger from '../services/logs/Logger'
import config from '../config'

const logger = Container.get(Logger)

export class KnowledgeBaseResource extends BaseResource<KnowledgeBaseInstance> {
  queueJobsDispatcher: QueueJobsDispatcher

  constructor(readonly haystackIaApi: HaystackIaApi, know: typeof knowledgeBaseRepository) {
    super(know)
    this.queueJobsDispatcher = Container.get(QueueJobsDispatcher)
  }

  async suggestResponseByMessage(
    messageId: string,
    accountId: string,
    outsideTheKnowledgeBase: boolean,
  ): Promise<{ message: string }> {
    if (config('blockAiConsumption')) {
      const limitResponse = await this.checkCreditLimit(accountId)
      if (limitResponse) return limitResponse
    }

    return this.haystackIaApi.suggestResponseByMessage(messageId, accountId, outsideTheKnowledgeBase)
  }

  async getMessagesByTicket(ticketId: string, accountId: string): Promise<Array<MessageInstance>> {
    const messages = await messageResource.findMany({
      attributes: ['id', 'isFromMe', 'type', 'isFromBot', 'text', 'ticketId', 'accountId', 'serviceId'],
      where: { ticketId, accountId, type: { $in: ['chat', 'audio', 'ptt'] } },
      order: [['createdAt', 'DESC']],
      limit: 20,
    })

    return queuedAsyncMap(messages, async (message) => ({
      ...message?.dataValues,
      text: await messageResource.decryptMessageText(message),
    }))
  }

  async suggestResponseByTicket(
    ticketId: string,
    accountId: string,
    userId: string,
    outsideTheKnowledgeBase: boolean,
  ): Promise<{ message: string } | CopilotTranscriptionInstance> {
    try {
      if (config('blockAiConsumption')) {
        const limitResponse = await this.checkCreditLimit(accountId)
        if (limitResponse) return limitResponse
      }

      const messages = await this.getMessagesByTicket(ticketId, accountId)

      if (messages.length === 0) {
        throw new Error('THERE_ARE_NO_MESSAGES')
      }

      const messageAudios = messages.filter((message) => ['audio', 'ptt'].includes(message?.type) && !message?.text)

      if (messageAudios?.length > 0) {
        return this.dispatchTranscriptions(messageAudios, userId)
      }

      return this.haystackIaApi.suggestResponseByTicket(messages, outsideTheKnowledgeBase)
    } catch (error) {
      if (error.response?.data?.detail) {
        throw new Error(error.response?.data?.detail)
      }
      if (error.message === 'THERE_ARE_NO_MESSAGES') {
        throw new Error(error.message)
      }
      throw new Error(`Failed to suggest a response: ${error.message}`)
    }
  }

  async dispatchTranscriptions(messageAudios: MessageInstance[], userId: string) {
    await queuedAsyncMap(messageAudios, async (message) => {
      await this.queueJobsDispatcher.dispatch<QueueAudioTranscribeJob>(
        'queued-audio-transcribe',
        { idMessage: message?.id, notCharge: true },
        {
          hashKey: message?.id,
        },
      )
    })

    copilotTranscriptionResource.create({
      ticketId: messageAudios?.[0]?.ticketId,
      userId,
      accountId: messageAudios?.[0]?.accountId,
    })

    return { message: 'TRANSCRIBING_AUDIO_MESSAGES' }
  }

  private async checkCreditLimit(accountId: string): Promise<{ message: string }> {
    if (await this.limitReached(accountId)) {
      logger.log('The copilot limit has been reached', 'error')
      return { message: 'COPILOT_LIMIT_REACHED' }
    }
    return null
  }

  async limitReached(accountId: string): Promise<Boolean> {
    try {
      const credits = await creditMovementResource.balance(accountId, 'copilot')
      return credits <= 0
    } catch (error) {
      logger.log(error)
      return true
    }
  }

  async destroy(knowledgeBase: KnowledgeBaseInstance): Promise<void> {
    const items = await knowledgeBaseItemRepository.findMany({
      where: { knowledgeBaseId: knowledgeBase.id },
      include: ['docs'],
    })

    await queuedAsyncMap(items, async (knowledgeItem) => {
      const knowledgeDocs = knowledgeItem?.docs

      if (knowledgeDocs?.length > 0) {
        const knowledgeDocIds = knowledgeDocs?.map((doc: any) => doc?.docId)

        await this.haystackIaApi.deleteKnowledgeDocs(knowledgeDocIds, knowledgeItem?.accountId)
      }

      await knowledgeBaseItemRepository.destroy(knowledgeItem, {})
    })

    await knowledgeBaseRepository.destroy(knowledgeBase, {})
  }
}
