import moment from 'moment'
import { UserInstance } from '../dbSequelize/models/User'
import { ActivityLogInstance } from '../mongoDB/models/ActivityLog'
import BaseMongoDBRepository from '../mongoDB/repositories/BaseMongoDBRepository'
import activityLogRepository from '../mongoDB/repositories/activityLogRepository'
import userResource from './userResource'
import formatDate from '../utils/date/formatDate'
import iteratePaginated from '../utils/iteratePaginated'

export class ActivityLogResource {
  protected readonly repository: BaseMongoDBRepository<ActivityLogInstance>

  constructor(
    // eslint-disable-next-line @typescript-eslint/no-shadow
    activityLogRepository: BaseMongoDBRepository<ActivityLogInstance>,
  ) {
    this.repository = activityLogRepository
  }

  async formatData(data: any): Promise<any> {
    if (data.resourceType === 'bot' && data.data?.contexts) {
      // Formatação realizada para os registros de contextos do bot.
      // O caracter $ é reservado no mongo, não pode ser utilizado como chave do JSON.
      data.data.contexts = JSON.stringify(data.data.contexts)
      data.data.contexts = data.data.contexts.split('$').join('')
      data.data.contexts = JSON.parse(data.data.contexts)
    }
    return data
  }

  async create(data: ActivityLogInstance): Promise<ActivityLogInstance> {
    return this.repository.create(await this.formatData(data))
  }

  formatFilter = (options) => ({
    ...options.filters,
    accountId: options.where.accountId,
    createdAt: {
      $gte: moment(options.filters.from),
      $lt: moment(options.filters.to),
    },
  })

  async findManyPaginated(options: any): Promise<{
    data: ActivityLogInstance[]
    total: number
    limit: number
    skip: number
    currentPage: number
    lastPage: number
    from: number
    to: number
  }> {
    const query = this.formatFilter(options)
    const response = await this.repository.findManyPaginated({
      ...options,
      filters: query,
    })
    const users = await this.getUsers(response.data)
    response.data = response.data.map((dt) => ({
      ...dt._doc,
      id: dt._id,
      user: users.find((user) => user.id === dt.userId),
    }))
    return response
  }

  async getUsers(logs: ActivityLogInstance[]) {
    const userIds = logs.map((dt) => dt.userId)
    return userResource.findManyByIds(userIds, {
      attributes: ['id', 'name'],
    })
  }

  async findById(id: string): Promise<ActivityLogInstance> {
    return this.repository.findById(id)
  }

  getLanguages(language) {
    if (language === 'en-US') {
      return {
        header: {
          user: 'User',
          module: 'Module',
          event: 'Event',
          date: 'Date',
        },
        modules: {
          schedules: 'Schedules',
          question: 'Rating',
          campaign: 'Campaigns',
          service: 'Connections',
          contact: 'Contacts',
          account: 'Accounts',
          user: 'Users',
          tag: 'Tags',
          webhook: 'Webhook',
          hsm: 'HSM',
          bot: 'Robots',
          role: 'Cargos/Puestos',
          people: 'Personas',
          department: 'Departamentos',
          organization: 'Organizaciones',
          quickReplies: 'Respuestas rápidas',
          customFields: 'Campos personalizados',
          timetable: 'Tabla de horários',
          integration: 'Integraciones',
          ticket: 'Tickets',
          ticketTopics: 'Subject',
          distribution: 'Call distribution',
        },
        events: {
          created: 'Creation',
          updated: 'Update',
          destroyed: 'Exclusion',
        },
      }
    }
    if (language === 'es') {
      return {
        header: {
          user: 'Usuario',
          module: 'Módulo',
          event: 'Evento',
          date: 'Fecha',
        },
        modules: {
          schedules: 'Programación',
          question: 'Calificaciones',
          campaign: 'Campañas',
          service: 'Conexiones',
          contact: 'Contactos',
          account: 'Cuentas',
          user: 'Usuarios',
          tag: 'Tag/Etiquetas',
          webhook: 'Webhook',
          hsm: 'HSM',
          bot: 'Robots',
          role: 'Positions',
          people: 'People',
          department: 'Departments',
          organization: 'Organizations',
          quickReplies: 'Quick answers',
          customFields: 'Custom fields',
          timetable: 'Timetable',
          integration: 'Integrations',
          ticket: 'Llamados',
          ticketTopics: 'Tema',
          distribution: 'Distribución de llamadas',
        },
        events: {
          created: 'Creación',
          updated: 'Cambio',
          destroyed: 'Exclusión',
        },
      }
    }
    return {
      header: {
        user: 'Usuário',
        module: 'Módulo',
        event: 'Evento',
        date: 'Data',
      },
      modules: {
        schedules: 'Agendamentos',
        question: 'Avaliações',
        campaign: 'Campanhas',
        service: 'Conexões',
        contact: 'Contatos',
        account: 'Contas',
        user: 'Usuários',
        tag: 'Tags',
        webhook: 'Webhook',
        hsm: 'HSM',
        bot: 'Robôs',
        role: 'Cargos',
        people: 'Pessoas',
        department: 'Departamentos',
        organization: 'Organizações',
        quickReplies: 'Respostas rápidas',
        customFields: 'Campos personalizados',
        timetable: 'Tabela de horários',
        integration: 'Integrações',
        ticket: 'Chamados',
        ticketTopics: 'Assuntos',
        distribution: 'Distribuição de chamados',
      },
      events: {
        created: 'Criação',
        updated: 'Alteração',
        destroyed: 'Exclusão',
      },
    }
  }

  async exportCsv(options: any, user: UserInstance) {
    const query = { ...options, filters: this.formatFilter(options) }
    const { header, modules, events } = this.getLanguages(user.language)
    const account = user.account
    const users = {}
    const body = await iteratePaginated(
      ({ page }) =>
        this.repository.findManyPaginated({
          page,
          perPage: 500,
          ...query,
        }),
      async (log: ActivityLogInstance) => {
        if (!users[log.userId]) {
          users[log.userId] = await userResource.findById(log.userId, {
            attributes: ['id', 'name'],
          })
        }
        return [
          users[log.userId].name || '',
          modules[log.resourceType],
          events[log.event],
          formatDate(log.createdAt, 'dd/MM/yyyy HH:mm:ss', account.settings.timezone),
        ]
      },
      1,
      true,
    )

    return { body, header }
  }
}

export default new ActivityLogResource(activityLogRepository)
