import { addSeconds } from 'date-fns'
import BaseResource from './BaseResource'
import serviceEventsRepository from '../dbSequelize/repositories/serviceEventsRepository'
import { ServiceInstance } from '../dbSequelize/models/Service'
import { ServiceEventInstance } from '../dbSequelize/models/ServiceEvent'
import accountResource from './accountResource'

export class ServiceEventsResource extends BaseResource<ServiceEventInstance> {
  constructor() {
    super(serviceEventsRepository)
  }

  async addEventsBlockMessageRules(service: ServiceInstance): Promise<void> {
    const account = await accountResource.findById(service.accountId)

    if (!account?.settings?.flags?.['use-block-message-rules-by-service']) {
      return
    }

    const now = new Date()
    const blockMessageRulesActive = service?.settings?.blockMessageRulesActive
    const unblockByReceiveMessage = service?.settings?.unblockByReceiveMessage

    await Promise.all([
      this.addEventBlockMessageRulesActive(blockMessageRulesActive, service.id, service.accountId, now),
      this.addEventUnblockByReceiveMessage(unblockByReceiveMessage, service.id, service.accountId, addSeconds(now, 1)),
    ])
  }

  async addEventBlockMessageRulesActive(
    blockMessageRulesActive: boolean,
    serviceId: string,
    accountId: string,
    now: Date,
  ): Promise<void> {
    if (typeof blockMessageRulesActive !== 'boolean' || !serviceId || !accountId || !now) {
      return
    }

    const reasonBlockMessageRulesActive = blockMessageRulesActive
      ? 'enabled_block_message_rules_active'
      : 'disabled_block_message_rules_active'

    const lastEventBlockMessageRulesActive = await this.findOne({
      where: {
        reason: ['enabled_block_message_rules_active', 'disabled_block_message_rules_active'],
        endedAt: null,
        serviceId,
        accountId,
        reasonCategory: 'service_block_message',
      },
      order: [['startedAt', 'DESC']],
    })

    if (lastEventBlockMessageRulesActive) {
      if (lastEventBlockMessageRulesActive.reason === reasonBlockMessageRulesActive) {
        return
      }

      await this.update(lastEventBlockMessageRulesActive, {
        endedAt: now,
      })
    }

    await this.create({
      online: null,
      reason: reasonBlockMessageRulesActive,
      startedAt: now,
      serviceId,
      accountId,
      reasonCategory: 'service_block_message',
    })
  }

  async addEventUnblockByReceiveMessage(
    unblockByReceiveMessage: boolean,
    serviceId: string,
    accountId: string,
    now: Date,
  ): Promise<void> {
    if (typeof unblockByReceiveMessage !== 'boolean' || !serviceId || !accountId || !now) {
      return
    }

    const reasonUnblockByReceiveMessage = unblockByReceiveMessage
      ? 'enabled_unblock_by_receive_message'
      : 'disabled_unblock_by_receive_message'

    const lastEventUnblockByReceiveMessage = await this.findOne({
      where: {
        reason: ['enabled_unblock_by_receive_message', 'disabled_unblock_by_receive_message'],
        endedAt: null,
        serviceId,
        accountId,
        reasonCategory: 'service_block_message',
      },
      order: [['startedAt', 'DESC']],
    })

    if (lastEventUnblockByReceiveMessage) {
      if (lastEventUnblockByReceiveMessage.reason === reasonUnblockByReceiveMessage) {
        return
      }

      await this.update(lastEventUnblockByReceiveMessage, {
        endedAt: now,
      })
    }

    await this.create({
      online: null,
      reason: reasonUnblockByReceiveMessage,
      startedAt: now,
      serviceId,
      accountId,
      reasonCategory: 'service_block_message',
    })
  }
}

export default new ServiceEventsResource()
