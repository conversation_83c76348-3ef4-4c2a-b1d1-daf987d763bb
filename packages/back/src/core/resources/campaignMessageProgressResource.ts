import campaignMessageProgressRepository from '../dbSequelize/repositories/campaignMessageProgressRepository'
import BaseResource from './BaseResource'
import { CampaignMessageProgressInstance } from '../dbSequelize/models/CampaignMessageProgress'

export class CampaignMessageProgressResource extends BaseResource<CampaignMessageProgressInstance> {
  constructor() {
    super(campaignMessageProgressRepository)
  }
}

export default new CampaignMessageProgressResource()
