import { ContactInstance } from '../../dbSequelize/models/Contact'

/**
 * Sms Bulk Message Interface
 *
 * @see https://docs.wavy.global/documentacao-tecnica/todas-as-integracoes/sms-api#envio-de-mensagens-mt
 */
export interface SmsBulkMessageInterface {
  messages: Array<{
    destination: string
    messageText?: string
  }>
  defaultValues: {
    messageText: string
    flashSMS?: boolean
  }
  timeZone?: string
  scheduledDate?: string
  timeWindow?: Array<Number>
}
