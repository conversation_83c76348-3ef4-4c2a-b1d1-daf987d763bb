/* eslint-disable class-methods-use-this */
import MimeTypes from 'mime-types'
import { Container } from 'typedi'
import fileType from 'file-type'
import { readAsync, removeAsync } from 'fs-jetpack'
import { Readable } from 'stream'
import thumbsupply from 'thumbsupply'
import sharp from 'sharp'
import base64ToBuffer from '../utils/base64/base64ToBuffer'
import parseBase64Url from '../utils/base64/parseBase64Url'
import readStreamToBuffer from '../utils/stream/readStreamToBuffer'
import BaseResource, { Options } from './BaseResource'
import fileRepository from '../dbSequelize/repositories/fileRepository'
import { getStorage, getDriver } from '../services/storage'
import { checksum as getChecksum } from '../utils/crypt/checksum'
import { createDecipherForAccount, encryptBufferForAccountWithSeparatedIv } from '../services/crypt/accountCryptor'
import accountResource from './accountResource'
import { ivLength } from '../utils/crypt/cryptor'
import { FileInstance } from '../dbSequelize/models/File'
import reportError from '../services/logs/reportError'
import FsStorage from '../services/storage/FsStorage'
import QueueJobsDispatcher from '../services/jobs/queue/QueueJobsDispatcher'
import Logger from '../services/logs/Logger'
import AudioMetadataJob from '../../microServices/workers/jobs/file/AudioMetadataJob'
import fileTransformer from '../transformers/fileTransformer'
import configValues from '../configValues'
import convertAudio, {
  getFileTypeFromBuffer,
  getFormatAndCodecByServiceId,
  mustConvertAudio,
} from '../utils/convertAudio'
import bufferToReadStream from '../utils/stream/bufferToReadStream'
import getRandomBucket from '../utils/getRandomBucket'
import stickerResource from './stickerResource'
import HttpError from '../utils/error/HttpError'
import { HttpClient } from '../services/httpClient/HttpClient'

export type CreateData = {
  serviceType?: string
  serviceId?: string
  isPtt?: boolean
  accountId: string
  name?: string
  filename?: string
  encrypt?: boolean
  base64Url?: string
  mimetype?: string
  extension?: string
  base64?: string
  url?: string
  data?: Buffer | Readable
  checksum?: string
  attachedId?: string
  attachedType?: string
  deferredUpload?: boolean
  metadata?: object
}

const logger = Container.get(Logger)

const getBufferMimetype = async (buffer: Buffer): Promise<string | null> => {
  const type = await fileType.fromBuffer(buffer)
  return type ? type.mime : null
}

// TODO destroy file on storage
export class FileResource extends BaseResource<FileInstance> {
  constructor() {
    // @ts-ignore
    super(fileRepository)
  }

  async parseCreateInput(
    input: {
      base64Url?: string
      mimetype?: string
      base64?: string
      data?: Buffer | Readable
      encrypt?: boolean
      deferredUpload?: boolean
    } = {},
  ): Promise<{ buffer?: Buffer; stream?: Readable; mimetype: string }> {
    // Base64 URL
    if (input.base64Url) {
      const { base64, mimetype } = parseBase64Url(input.base64Url)
      const buffer = base64ToBuffer(base64)

      return { buffer, mimetype }
    }

    // Base64
    if (input.base64) {
      const buffer = base64ToBuffer(input.base64)
      const mimetype = input.mimetype

      return { buffer, mimetype }
    }

    // Buffer
    if (input.data && Buffer.isBuffer(input.data)) {
      const buffer = input.data
      const mimetype = input.mimetype || (await getBufferMimetype(buffer))

      return { buffer, mimetype }
    }

    // Stream
    if (input.data && input.data instanceof Readable) {
      const mimetype = input.mimetype
      const stream = input.data

      return { stream, mimetype }
    }

    if (input.deferredUpload) {
      const mimetype = input.mimetype
      return { mimetype }
    }

    throw new Error('Invalid input.')
  }

  async createMessageThumbnail(
    fileInstance: Partial<FileInstance>,
    messageId: string,
    accountId: string,
    options: { eventTransaction?: Options<Partial<FileInstance>>['eventTransaction'] } = {},
  ) {
    const driverStorage = await getRandomBucket()
    const type = fileInstance.mimetype.split('/')[0]

    if (!['image', 'video'].includes(type)) return

    const stream = await this.getReadStream(fileInstance)

    const minSize = configValues.minImageSizeToGenerateThumbnail

    const size = stream.readableLength

    if (type === 'image' && size < minSize) return

    try {
      if (type === 'image') {
        const imageThumbnailInstance = await super.create(
          {
            name: `${String(new Date().getTime())}.png`,
            extension: 'png',
            mimetype: 'image/png',
            attachedId: messageId,
            attachedType: 'message.thumbnail',
            accountId,
            storage: driverStorage,
          },
          { ...(options.eventTransaction && { eventTransaction: options.eventTransaction }), dontEmit: true },
        )

        const thumbnailStream = stream.pipe(
          sharp().resize({
            width: 400,
            height: 400,
            fit: sharp.fit.cover,
          }),
        )

        await getStorage(driverStorage).write(imageThumbnailInstance.filepath, thumbnailStream, {
          ContentType: imageThumbnailInstance.mimetype,
          ...(size && {
            ContentLength: String(size),
            Metadata: {
              'Content-Length': String(size),
            },
          }),
        })
      }

      if (type === 'video') {
        const fsStorage = Container.get(FsStorage)

        fsStorage.ensureDirectoryExistence(fileInstance.filepath)
        const filepath = fsStorage.resolvePath(fileInstance.filepath)

        const transformedFile = await fileTransformer(fileInstance)

        await Container.get(HttpClient)
          .get(transformedFile.url, {
            responseType: 'stream',
          })
          .then((r) => fsStorage.write(filepath, r.data, {}))

        await thumbsupply
          .generateThumbnail(filepath, {
            mimetype: fileInstance.mimetype,
            size: {
              height: 400,
              width: 400,
              name: fileInstance.name,
            },
          })
          .then(async (thumbnail) => {
            const buffer = await readAsync(thumbnail, 'buffer')

            // Não usar transaction aqui!
            await this.create(
              {
                accountId,
                attachedId: messageId,
                attachedType: 'message.preview',
                name: fileInstance.name,
                data: buffer,
                mimetype: 'image/png',
              },
              {
                ...(options.eventTransaction && { eventTransaction: options.eventTransaction }),
                dontEmit: true,
              },
            )
          })
          .catch((e) => {
            if (e.message === 'Cannot find ffprobe') {
              console.error(
                'Needs ffmpeg installed on the server to generate the video thumbnail. Thumbnail not created.',
              )
            }
            return null
          })
          .finally(async () => removeAsync(filepath))
      }
    } catch (error) {
      reportError(error)
    }
  }

  async attachFileToMessage(
    {
      fileId,
      accountId,
      attachedId,
    }: {
      fileId: string
      accountId: string
      attachedId: string
    },
    options: Options<Partial<FileInstance>> = {},
  ): Promise<Partial<FileInstance>> {
    const driverStorage = await getRandomBucket()

    return this.maybeEventTransaction((eventTransaction) => {
      return this.nestedTransaction(async (transaction) => {
        const fileInstance = await super.updateById(
          fileId,
          { attachedId },
          {
            ...options,
            dontEmit: true,
            transaction,
            eventTransaction,
          },
        )

        const type = fileInstance.mimetype.split('/')[0]

        const imageThumbnailInstance =
          type === 'image' &&
          (await super.create(
            {
              mimetype: fileInstance.mimetype,
              extension: fileInstance.extension,
              attachedId,
              attachedType: 'message.thumbnail',
              name: fileInstance.name,
              accountId: fileInstance.accountId,
              storage: driverStorage,
            },
            {
              ...options,
              dontEmit: true,
              transaction,
              eventTransaction,
            },
          ))

        return { fileInstance, imageThumbnailInstance }
      }, options.transaction).then(async ({ fileInstance, imageThumbnailInstance }) => {
        const type = fileInstance.mimetype.split('/')[0]

        if (type === 'image') {
          const transformer = sharp().resize({
            width: 400,
            height: 400,
            fit: sharp.fit.cover,
          })

          const stream = (await this.getReadStream(fileInstance)).pipe(transformer)

          const size = stream?.readableLength

          await getStorage(driverStorage).write(imageThumbnailInstance.filepath, stream, {
            ContentType: fileInstance.mimetype,
            ...(size && {
              ContentLength: String(size),
              Metadata: {
                'Content-Length': String(size),
              },
            }),
          })
        }

        if (type === 'video') {
          const fsStorage = Container.get(FsStorage)

          fsStorage.ensureDirectoryExistence(fileInstance.filepath)
          const filepath = fsStorage.resolvePath(fileInstance.filepath)

          const transformedFile = await fileTransformer(fileInstance)

          await Container.get(HttpClient)
            .get(transformedFile.url, {
              responseType: 'stream',
            })
            .then((r) => fsStorage.write(filepath, r.data, {}))

          await thumbsupply
            .generateThumbnail(filepath, {
              mimetype: fileInstance.mimetype,
              size: {
                height: 400,
                width: 400,
                name: fileInstance.name,
              },
            })
            .then(async (thumbnail) => {
              const buffer = await readAsync(thumbnail, 'buffer')

              // Não usar transaction aqui!
              await this.create(
                {
                  accountId,
                  attachedId,
                  attachedType: 'message.preview',
                  name: fileInstance.name,
                  data: buffer,
                  mimetype: 'image/png',
                },
                {
                  dontEmit: true,
                  eventTransaction,
                },
              )
            })
            .catch((e) => {
              if (e.message === 'Cannot find ffprobe') {
                console.error(
                  'Needs ffmpeg installed on the server to generate the video thumbnail. Thumbnail not created.',
                )
              }
              return null
            })
            .finally(async () => removeAsync(filepath))
        }

        if (!options.dontEmit) this.emitCreated(fileInstance)

        return fileInstance
      })
    }, options.eventTransaction)
  }

  async create(data: CreateData, options: Options<Partial<FileInstance>> = {}) {
    let { buffer, stream, mimetype } = await this.parseCreateInput(data)
    const driverStorage = await getRandomBucket()

    let extension = MimeTypes.extension(mimetype === 'image/jpg' ? 'image/jpeg' : mimetype)

    if (!extension) {
      extension = mimetype === 'audio/mp3' ? 'mp3' : 'unknown'
    }

    if (!data.deferredUpload && mimetype.split('/')[0] === 'audio') {
      const audioStream = stream || bufferToReadStream(buffer)

      const { isPtt = true, serviceId } = data

      if (!data.serviceId || (await mustConvertAudio(mimetype, isPtt, serviceId))) {
        const { format, codec, channels, bitrate } = await getFormatAndCodecByServiceId(serviceId, isPtt)

        const convertedAudioStream = await convertAudio(audioStream, format, codec, channels, bitrate)

        const convertedAudioBuffer = await readStreamToBuffer(convertedAudioStream)

        const { ext = format === 'mp3' ? 'mp3' : extension, mime = format === 'mp3' ? 'audio/mp3' : mimetype } =
          await getFileTypeFromBuffer(convertedAudioBuffer)

        if (buffer) buffer = convertedAudioBuffer
        if (stream) stream = convertedAudioStream

        if (data.name?.trim()) {
          const name = data.name.trim()
          const lastDot = name.lastIndexOf('.')

          data.name = `${name.slice(0, lastDot)}.${ext}`
        }

        mimetype = mime
        extension = ext
      }
    }

    const isEncrypted = data.encrypt

    const account = await accountResource.findById(data.accountId)

    const { encrypedBuffer, iv }: any = (() => {
      if (!(isEncrypted && buffer)) return {}

      const result = encryptBufferForAccountWithSeparatedIv(account, buffer)
      // eslint-disable-next-line @typescript-eslint/no-shadow
      const encrypedBuffer = result[0]
      // eslint-disable-next-line @typescript-eslint/no-shadow
      const iv = result[1].toString('base64')

      return { encrypedBuffer, iv }
    })()

    const checksum = buffer ? getChecksum(buffer) : data.checksum
    const name = this.fileNameSanitize(data.name) || `${String(new Date().getTime())}.${extension}`
    const filename = this.fileNameSanitize(data.filename)

    const size = buffer ? buffer?.length : stream?.readableLength
    const fileData = {
      ...data,
      name,
      filename,
      mimetype: mimetype === 'image/jpg' ? 'image/jpeg' : mimetype,
      checksum,
      extension,
      isEncrypted,
      iv,
      storage: driverStorage,
      data: data.metadata || {},
    }

    const instance = await super.create(fileData, {
      dontEmit: true,
      ...options,
    })

    if (!data.deferredUpload) {
      const bufferOrStream = stream || (isEncrypted ? encrypedBuffer : buffer)

      await getStorage(driverStorage).write(instance.filepath, bufferOrStream, {
        ContentType: mimetype,
        ...(size && {
          ContentLength: String(size),
          Metadata: {
            'Content-Length': String(size),
          },
        }),
      })
    }

    if (this.isMessageAudioWithouMetadata(instance)) {
      // Evitar uso de await já que não precisa esperar o envio para a fila
      this.processAudioMetadata(instance, 2)
    }

    if (!options.dontEmit) this.emitCreated(instance)

    return instance
  }

  async duplicateFile(fileId: string, attachedType?: string) {
    const file = await this.findById(fileId)
    const fileBuffer = await this.getBuffer(file)

    let stickerBuffer = null
    let stickerChecksum = null
    const isSticker = attachedType === 'sticker.file'

    if (isSticker) {
      const stickerResult = await stickerResource.generateWebpStickerBuffer(fileBuffer)

      if (!stickerResult?.data) {
        throw new HttpError(
          stickerResult.errorCode || 400,
          stickerResult.errorMessage || 'Error to generate sticker buffer',
        )
      }

      stickerBuffer = stickerResult.data
      stickerChecksum = getChecksum(stickerBuffer)
    }

    const createdFile = await this.create({
      data: isSticker ? stickerBuffer : fileBuffer,
      name: file.name,
      checksum: isSticker ? stickerChecksum : file.checksum,
      mimetype: file.mimetype,
      attachedType: attachedType || file.attachedType,
      accountId: file.accountId,
    })

    return createdFile
  }

  fileExistOnDisk(file: Partial<FileInstance>): Promise<boolean> {
    return getDriver(file.storage).exists(file.filepath)
  }

  fileNameSanitize(file) {
    return file ? String(file).replace(/,/g, '-').replace(/ /g, '-') : null
  }

  async getReadStreamForEncryptedFile(file: Partial<FileInstance>, options?): Promise<Readable> {
    const { filepath, account } = file
    const { iv } = file

    if (iv) {
      const decipher = createDecipherForAccount(Buffer.from(iv, 'base64'), account)

      return getDriver(file.storage).createReadStream(filepath, options).pipe(decipher)
    }

    const stream = getDriver(file.storage).createReadStream(file.filepath)

    const theIv = await new Promise((resolve) => {
      const ivReadableHandler = () => {
        stream.removeListener('readable', ivReadableHandler)
        resolve(stream.read(ivLength))
      }
      stream.on('readable', ivReadableHandler)
    })

    const decipher = createDecipherForAccount(theIv, file.account)

    return stream.pipe(decipher)
  }

  async getReadStream(file: Partial<Partial<FileInstance>>, options?): Promise<Readable> {
    if (
      !(await getDriver(file.storage)
        .exists(file.filepath)
        .catch(() => false))
    )
      return

    if (file.isEncrypted) return this.getReadStreamForEncryptedFile(file, options)

    return getDriver(file.storage).createReadStream(file.filepath, options)
  }

  async getBuffer(file: Partial<FileInstance>, options?): Promise<Buffer> {
    const readStream = await this.getReadStream(file, options)

    return readStreamToBuffer(readStream)
  }

  async getBase64(file: Partial<FileInstance>, options?): Promise<string> {
    const buffer = await this.getBuffer(file, options)
    return buffer.toString('base64')
  }

  getMetadata(file: Partial<FileInstance>, options?): Promise<any> {
    return getDriver(file.storage).getMetadata(file.filepath, options)
  }

  isMessageAudioWithouMetadata(file: FileInstance): boolean {
    return file?.attachedType === 'message.file' && file?.mimetype?.startsWith('audio/') && !file?.data?.audioMetadata
  }

  async processAudioMetadata(file: FileInstance): Promise<void> {
    if (
      await accountResource.exists({
        where: {
          id: file.accountId,
          settings: { flags: { 'enable-audio-waveform': false } },
        },
      })
    ) {
      logger.log(`Feature flag enable-audio-waveform is disabled - fileId: ${file.id}`, 'warn')
      return
    }

    if (file?.id && this.isMessageAudioWithouMetadata(file)) {
      const queueJobsDispatcher = Container.get(QueueJobsDispatcher)

      queueJobsDispatcher
        .dispatch<AudioMetadataJob>(
          'audio-metadata',
          { fileId: file.id },
          {
            hashKey: file.id,
          },
        )
        .then(() => logger.log(`Success to dispatch audio-metadata-job - fileId: ${file.id}`, 'info'))
        .catch(() => logger.log(`Error to dispatch audio-metadata-job - fileId: ${file.id}`, 'error'))
    }
  }
}

export default new FileResource()
