import EventEmitter from 'events'
import { groupBy } from 'lodash'
import uniqByReverse from '../../utils/array/uniqByReverse'
import { EventTransactionTuple, Instance } from '../BaseResource'

export default (events: EventTransactionTuple[]) => {
  const emitterRefs: number[] = []
  const getEmitterId = (emitter) => {
    if (emitterRefs.indexOf(emitter) === -1) {
      emitterRefs.push(emitter)
    }

    return emitterRefs.indexOf(emitter)
  }

  const getFullEventName = (emitter, event) => `${getEmitterId(emitter)}/${event}`

  const singularEvents: [EventEmitter, string, Instance][] = []

  events.forEach(([emitter, event, data]) => {
    if (Array.isArray(data)) {
      data.forEach((model) => {
        singularEvents.push([emitter, event, model])
      })
      return
    }

    singularEvents.push([emitter, event, data])
  })

  const dedupedSingularEvents = uniqByReverse(
    singularEvents,
    ([emitter, event, model]) => `${getFullEventName(emitter, event)}/${model.id}`,
  )

  const grouped = groupBy(dedupedSingularEvents, ([emitter, event, model]) => getFullEventName(emitter, event))

  return Object.entries(grouped).reduce((arr, [id, tuples]) => {
    const models = tuples.map(([emitter, event, models]) => models)
    const data = models.length === 1 ? models[0] : models

    arr.push([tuples[0][0], tuples[0][1], data])

    return arr
  }, [])
}
