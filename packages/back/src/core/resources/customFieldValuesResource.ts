import BaseResource from './BaseResource'
import customFieldValuesRepository from '../dbSequelize/repositories/customFieldValuesRepository'
import { CustomFieldValueInstance } from '../dbSequelize/models/CustomFieldValue'

export class CustomFieldValuesResource extends BaseResource<CustomFieldValueInstance> {
  constructor() {
    super(customFieldValuesRepository)
  }

  public async save(data, options = {}) {
    if (data.customFieldId?.id) {
      data.customFieldId = data.customFieldId?.id
    }
    const existsValue = await super.findOne({
      where: {
        relatedId: data.relatedId,
        relatedType: data.relatedType,
        customFieldId: data.customFieldId,
      },
    })
    if (existsValue) {
      return super.updateById(existsValue.id, { value: data.value })
    }
    return super.create(data, options)
  }
}

export default new CustomFieldValuesResource()
