import * as xlsLib from 'excel4node'
import { Sequelize } from 'sequelize'
import { isEmpty, omit } from 'lodash'
import moment from 'moment'
import BaseResource from './BaseResource'
import userResource from './userResource'
import creditMovementRepository from '../dbSequelize/repositories/creditMovementRepository'
import { CreditMovementInstance, CreditOrigin, ServiceType } from '../dbSequelize/models/CreditMovement'
import taskQueue from '../services/queue/redisTaskQueue'
import PaymentRequiredError from '../utils/error/PaymentRequiredError'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

export const SERVICE_TYPES = ['sms-wavy', 'transcription', 'summary', 'magic-text', 'copilot', 'csat']

interface Translations {
  wsSummaryHeader: string
  wsSummaryPeriod: string
  wsSummaryTranscriptionTotal: string
  wsSummarySummaryTotal: string
  wsSummaryMagicTextTotal: string
  wsSummaryCopilotTotal: string
  wsSummaryCSATTotal: string
  wsDailyConsumption: string
  wsDailyConsumptionHeader: string
  wsDailyConsumptionDate: string
  wsDailyConsumptionTranscription: string
  wsDailyConsumptionSummary: string
  wsDailyConsumptionMagicText: string
  wsDailyConsumptionCopilot: string
  wsDailyConsumptionCSAT: string
  wsPackagesAquisition: string
  wsPackagesAquisitionDate: string
  wsPackagesAquisitionActivity: string
  wsPackagesAquisitionService: string
  transcription: string
  summary: string
  magicText: string
  copilot: string
  cSAT: string
}

interface Styles {
  header1Style: xlsLib.Style
  header2Style: xlsLib.Style
  header3Style: xlsLib.Style
  textStyle: xlsLib.Style
}

interface Filters {
  from: string
  to: string
  serviceType: string
  order?: Array<string>
  servicesIds?: Array<string>
}

export class CreditMovementResource extends BaseResource<CreditMovementInstance> {
  protected queue = taskQueue('service-credits')

  constructor() {
    super(creditMovementRepository)
  }

  async balance(accountId: string, serviceType: string, afterDate = null) {
    //vai funcionar para varios tipos
    const ins =
      (await this.repository.getModel().sum('amount', {
        where: { accountId, serviceType, type: 'in', ...(afterDate && { createdAt: { $gte: afterDate } }) },
      })) || 0

    if (ins <= 0) return ins

    const outs =
      (await this.repository.getModel().sum('amount', {
        where: { accountId, serviceType, type: 'out', ...(afterDate && { createdAt: { $gte: afterDate } }) },
      })) || 0

    return ins - outs
  }

  async balanceV2(accountId, serviceType, from, to, serviceId) {
    const whereCondition = {
      accountId,
      serviceType,
      ...((from || to) && {
        createdAt: {
          ...(from && { $gte: from }),
          ...(to && { $lt: to }),
        },
      }),
      ...(serviceId && { serviceId }),
    }

    const balance =
      (await this.getRepository().findMany({
        attributes: [
          Sequelize.literal('DATE("CreditMovement"."createdAt" - interval \'3 hour\') as date'),
          'serviceId',
          [
            this.getRepository()
              .getModel()
              .sequelize.fn(
                'SUM',
                this.getRepository().getModel().sequelize.literal(`
            CAST(
            CASE WHEN "CreditMovement"."type" = 'in' THEN "amount" ELSE 0 END 
            AS INTEGER)
          `),
              ),
            'ins',
          ],
          [
            this.getRepository()
              .getModel()
              .sequelize.fn(
                'SUM',
                this.getRepository().getModel().sequelize.literal(`
            CAST(
            CASE WHEN "CreditMovement"."type" = 'out' THEN "amount" ELSE 0 END 
            AS INTEGER)
          `),
              ),
            'outs',
          ],
          [
            this.getRepository().getModel().sequelize.literal(`
          CAST(
            SUM(CASE WHEN "CreditMovement"."type" = 'in' THEN "amount" ELSE 0 END) - 
            SUM(CASE WHEN "CreditMovement"."type" = 'out' THEN "amount" ELSE 0 END) 
          AS INTEGER)
          `),
            'balance',
          ],
          [this.getRepository().getModel().sequelize.col('service.name'), 'name'],
        ],
        where: whereCondition,
        include: [
          {
            model: 'service',
            attributes: ['id', 'name'],
          },
        ],
        group: ['date', 'serviceId', 'service.name', 'service.id'],
        order: [[this.getRepository().getModel().sequelize.col('date'), 'ASC']],
        raw: true,
      })) || 0
    return balance
  }

  async balances(accountId: string) {
    //retorna para todos os tipos
    const collection = await queuedAsyncMap(SERVICE_TYPES, async (serviceType) => ({
      serviceType,
      amount: await this.balance(accountId, serviceType),
    }))

    return collection.reduce((obj, item) => {
      obj[item.serviceType] = item.amount
      return obj
    }, {})
  }

  async balancesV2(accountId: string, from, to, serviceId, withHistory = true) {
    //retorna para todos os tipos
    const balances: { [key: string]: any } = {}

    await Promise.all(
      SERVICE_TYPES.map(async (serviceType) => {
        const serviceBalance: {
          [key: string]: { id: string; ins: number; outs: number; balance: number; used: number; history: object }
        } = {}
        const totalBalance = {
          ins: 0,
          outs: 0,
          balance: 0,
        }

        const balance = await this.balanceV2(accountId, serviceType, from, to, serviceId)
        let totalUsed = 0

        balance.forEach((item) => {
          totalBalance.ins += Number(item.ins)
          totalBalance.outs += Number(item.outs)
          if (item.name) {
            totalUsed += Number(item.outs)
          }

          serviceBalance[item.name] = {
            id: item.serviceId,
            ins: (serviceBalance?.[item.name]?.ins || 0) + (Number(item.ins) || 0),
            outs: (serviceBalance?.[item.name]?.outs || 0) + (Number(item.outs) || 0),
            balance: item.balance || 0,
          }
        })

        if (withHistory) {
          serviceBalance.history = balance
        }

        serviceBalance.all = {
          ins: totalBalance.ins,
          outs: totalBalance.outs,
          balance: totalBalance.ins - totalBalance.outs,
          used: totalUsed,
        }

        balances[serviceType] = serviceBalance
      }),
    )

    return balances
  }

  async totalServices(filters: Filters, page: number, accountId: string) {
    const { from, to, serviceType, servicesIds, order } = filters
    return creditMovementRepository.totalServices(
      from,
      to,
      order?.filter(Boolean),
      servicesIds,
      serviceType,
      page,
      accountId,
    )
  }

  async tryWithCredits(
    //retorna uma função se tiver creditos
    data: {
      accountId: string
      serviceType: string
      amount: number
      origin?: string
    },
    fn,
  ) {
    const { creditMovement } = await this.requestCredit(data, true)
    try {
      return await fn()
    } catch (e) {
      await this.destroy(creditMovement)
      throw e
    }
  }

  async requestCredit(data: { accountId: string; serviceType: string; amount: number }, debit = false) {
    return this.queue.run(async () => {
      const available = await this.balance(data.accountId, data.serviceType)

      if (available < data.amount) {
        throw new PaymentRequiredError('Insufficient credits')
      }

      let creditMovement

      if (debit) {
        creditMovement = await this.createDebit({
          accountId: data.accountId,
          serviceType: data.serviceType as ServiceType,
          amount: data.amount,
          origin: 'single',
        })
      }

      return {
        creditMovement,
        available: available - data.amount,
      }
    })
  }

  async createDebit(data: {
    //computa saida de cretidos
    accountId: string
    serviceType: ServiceType
    amount: number
    origin?: CreditOrigin
    campaignId?: string
    serviceId?: string
  }) {
    return this.create({
      ...data,
      type: 'out',
    })
  }

  async exportHistory(res, query) {
    const wb = new xlsLib.Workbook()
    const styles: Styles = this.createExportStyles(wb)

    const translationsParams = {
      to: new Date(query.to).toLocaleDateString('pt-BR'),
      from: moment(query.from).subtract(1, 'days').format('DD/MM/YYYY'),
    }

    const translations: Translations = await this.translations(res.locals.user.id, translationsParams)

    query.where.$and.push(
      [
        {
          createdAt: { $gte: query.from },
        },
      ],
      [
        {
          createdAt: { $lte: query.to },
        },
      ],
    )

    await this.addSummaryWorksheet(wb, styles, query, translations)
    await this.addDailyConsumeWorksheet(wb, styles, query, translations)
    await this.addPackagesAquisitionWorksheet(wb, styles, query, translations)

    const result = await wb.writeToBuffer().then((buffer) => Buffer.from(buffer, 'binary'))

    return res.send(result)
  }

  private createExportStyles(wb: xlsLib.Workbook) {
    return {
      header1Style: wb.createStyle({
        font: {
          color: '#000000',
          size: 11,
          bold: true,
        },
        fill: {
          type: 'pattern',
          patternType: 'solid',
          fgColor: '#dae9f8',
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
        },
      }),
      header2Style: wb.createStyle({
        font: {
          color: '#FFFFFF',
          size: 11,
          bold: true,
        },
        fill: {
          type: 'pattern',
          patternType: 'solid',
          fgColor: '#4d93d9',
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
        },
      }),
      header3Style: wb.createStyle({
        font: {
          color: '#000000',
          size: 11,
          bold: true,
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
        },
      }),
      textStyle: wb.createStyle({
        font: {
          color: '#000000',
          size: 11,
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
        },
      }),
    }
  }

  private async addSummaryWorksheet(wb: xlsLib.Workbook, styles: Styles, query: object, translations: Translations) {
    const ws = wb.addWorksheet(translations.summary)

    const dailyConsume = await super.findOne({
      attributes: [
        [
          Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'transcription' AND type = 'out' THEN amount ELSE 0 END)`),
          'transcription_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'summary' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'summary_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'magic-text' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'magic_text_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'copilot' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'copilot_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'csat' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'csat_total',
        ],
      ],
      ...query,
    })

    const {
      transcription_total: transcriptionConsumedCredits,
      summary_total: summaryConsumedCredits,
      magic_text_total: magicTextConsumedCredits,
      copilot_total: copilotConsumedCredits,
      csat_total: cSATConsumedCredits,
    } = dailyConsume.dataValues

    let transcriptionCreditsToText

    if (isEmpty(transcriptionConsumedCredits)) {
      transcriptionCreditsToText = '0m'
    } else {
      const transcriptionCreditsToDate = new Date(0)
      transcriptionCreditsToDate.setSeconds(transcriptionConsumedCredits || 0)

      transcriptionCreditsToText =
        (transcriptionCreditsToDate.getHours() ? `${transcriptionCreditsToDate.getHours()}h ` : ``) +
        (transcriptionCreditsToDate.getMinutes() ? `${transcriptionCreditsToDate.getMinutes()}m ` : ``) +
        (transcriptionCreditsToDate.getSeconds() ? `${transcriptionCreditsToDate.getSeconds()}s ` : ``)
    }

    const rows = 4
    const columns = 5

    for (let i = 1; i <= rows; i++) {
      ws.row(i).setHeight(30)
    }

    for (let i = 1; i <= columns; i++) {
      ws.column(i).setWidth(30)
    }

    ws.cell(1, 1, 1, columns, true).string(translations.wsSummaryHeader).style(styles.header1Style)
    ws.cell(2, 1, 2, columns, true).string(translations.wsSummaryPeriod).style(styles.header2Style)
    ws.cell(3, 1).string(translations.wsSummaryTranscriptionTotal).style(styles.header3Style)
    ws.cell(3, 2).string(translations.wsSummarySummaryTotal).style(styles.header3Style)
    ws.cell(3, 3).string(translations.wsSummaryMagicTextTotal).style(styles.header3Style)
    ws.cell(3, 4).string(translations.wsSummaryCopilotTotal).style(styles.header3Style)
    ws.cell(3, 5).string(translations.wsSummaryCSATTotal).style(styles.header3Style)
    ws.cell(4, 1).string(transcriptionCreditsToText).style(styles.textStyle)
    ws.cell(4, 2)
      .number(summaryConsumedCredits || 0)
      .style(styles.textStyle)
    ws.cell(4, 3)
      .number(magicTextConsumedCredits || 0)
      .style(styles.textStyle)
    ws.cell(4, 4)
      .number(copilotConsumedCredits || 0)
      .style(styles.textStyle)
    ws.cell(4, 5)
      .number(cSATConsumedCredits || 0)
      .style(styles.textStyle)
  }

  private async addDailyConsumeWorksheet(
    wb: xlsLib.Workbook,
    styles: Styles,
    query: object,
    translations: Translations,
  ) {
    const ws = wb.addWorksheet(translations.wsDailyConsumption)

    const rows = 2
    const columns = 6

    for (let i = 1; i <= rows; i++) {
      ws.row(i).setHeight(30)
    }

    for (let i = 1; i <= columns; i++) {
      ws.column(i).setWidth(20)
    }

    ws.cell(1, 1, 1, columns, true).string(translations.wsDailyConsumptionHeader).style(styles.header1Style)
    ws.cell(2, 1).string(translations.wsDailyConsumptionDate).style(styles.header2Style)
    ws.cell(2, 2).string(translations.wsDailyConsumptionTranscription).style(styles.header2Style)
    ws.cell(2, 3).string(translations.wsDailyConsumptionSummary).style(styles.header2Style)
    ws.cell(2, 4).string(translations.wsDailyConsumptionMagicText).style(styles.header2Style)
    ws.cell(2, 5).string(translations.wsDailyConsumptionCopilot).style(styles.header2Style)
    ws.cell(2, 6).string(translations.wsDailyConsumptionCSAT).style(styles.header2Style)

    let row = 3
    const dailyConsume = await super.findMany({
      attributes: [
        [Sequelize.fn('TO_CHAR', Sequelize.fn('DATE_TRUNC', 'day', Sequelize.col('createdAt')), 'DD/MM/YYYY'), 'day'],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'transcription' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'transcription_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'summary' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'summary_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'magic-text' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'magic_text_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'copilot' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'copilot_total',
        ],
        [
          Sequelize.cast(
            Sequelize.literal(`SUM(CASE WHEN "serviceType" = 'csat' AND type = 'out' THEN amount ELSE 0 END)`),
            'INTEGER',
          ),
          'csat_total',
        ],
      ],
      ...query,
      group: [Sequelize.fn('DATE_TRUNC', 'day', Sequelize.col('createdAt'))],
      order: [[Sequelize.fn('DATE_TRUNC', 'day', Sequelize.col('createdAt')), 'ASC']],
    })

    dailyConsume.forEach((item) => {
      const {
        day,
        transcription_total: transcriptionConsumedCredits,
        summary_total: summaryConsumedCredits,
        magic_text_total: magicTextConsumedCredits,
        copilot_total: copilotConsumedCredits,
        csat_total: cSATConsumedCredits,
      } = item.dataValues

      ws.cell(row, 1).string(day).style(styles.textStyle)
      ws.cell(row, 2).number(transcriptionConsumedCredits).style(styles.textStyle)
      ws.cell(row, 3).number(summaryConsumedCredits).style(styles.textStyle)
      ws.cell(row, 4).number(magicTextConsumedCredits).style(styles.textStyle)
      ws.cell(row, 5).number(copilotConsumedCredits).style(styles.textStyle)
      ws.cell(row, 6).number(cSATConsumedCredits).style(styles.textStyle)

      row++
    })
  }

  private async addPackagesAquisitionWorksheet(
    wb: xlsLib.Workbook,
    styles: Styles,
    query: object,
    translations: Translations,
  ) {
    const ws = wb.addWorksheet(translations.wsPackagesAquisition)

    const packagesAquisition = await super.findMany({
      attributes: [
        [Sequelize.fn('TO_CHAR', Sequelize.col('createdAt'), 'DD/MM/YYYY'), 'day'],
        [Sequelize.col('amount'), 'amount'],
        [
          Sequelize.literal(`
            CASE WHEN "serviceType" = 'transcription' THEN '${translations.transcription}'
              WHEN "serviceType" = 'summary' THEN '${translations.summary}'
              WHEN "serviceType" = 'magic-text' THEN '${translations.magicText}'
              WHEN "serviceType" = 'copilot' THEN '${translations.copilot}'
              WHEN "serviceType" = 'csat' THEN '${translations.cSAT}'
            END
          `),
          'service',
        ],
        'serviceType',
      ],
      ...query,
      where: {
        ...omit(query?.where, 'serviceId'),
        type: 'in',
      },
      order: [[Sequelize.fn('DATE', Sequelize.col('createdAt')), 'ASC']],
    })

    ws.row(1).setHeight(30)
    ws.row(2).setHeight(30)
    ws.column(1).setWidth(20)
    ws.column(2).setWidth(20)
    ws.column(3).setWidth(20)

    ws.cell(1, 1, 1, 3, true).string(translations.wsPackagesAquisition.toUpperCase()).style(styles.header1Style)
    ws.cell(2, 1).string(translations.wsPackagesAquisitionDate).style(styles.header2Style)
    ws.cell(2, 2).string(translations.wsPackagesAquisitionActivity).style(styles.header2Style)
    ws.cell(2, 3).string(translations.wsPackagesAquisitionService).style(styles.header2Style)

    let row = 3

    packagesAquisition.forEach((item) => {
      const { day, amount, service, serviceType } = item.dataValues

      const getAmount = () => {
        if (serviceType === 'transcription') {
          const time = moment.utc(moment.duration(amount, 'seconds').asMilliseconds())
          return `${time.format('HH')}h ${time.format('mm')}m ${time.format('ss')}s`
        }
        return amount
      }

      ws.cell(row, 1).string(day).style(styles.textStyle)
      if (serviceType === 'transcription') {
        ws.cell(row, 2).string(getAmount()).style(styles.textStyle)
      } else {
        ws.cell(row, 2).number(getAmount()).style(styles.textStyle)
      }
      ws.cell(row, 3).string(service).style(styles.textStyle)

      row++
    })
  }

  private async translations(userId: string, params: { to?: string; from?: string } = {}) {
    const user = await userResource.findById(userId)

    const language = user?.language || 'pt-BR'
    let translations = null

    switch (language) {
      default:
      case 'pt-BR':
        translations = {
          wsSummaryHeader: 'DASHBOARD - CONSUMO DE IA',
          wsSummaryPeriod: `Período: De ${params.from}, Até ${params.to}`,
          wsSummaryTranscriptionTotal: 'TRANSCRIÇÃO DE ÁUDIO - TOTAL',
          wsSummarySummaryTotal: 'RESUMO INTELIGENTE- TOTAL',
          wsSummaryMagicTextTotal: 'TEXTO MÁGICO - TOTAL',
          wsSummaryCopilotTotal: 'COPILOTO - TOTAL',
          wsSummaryCSATTotal: 'IA CSAT - TOTAL',
          wsDailyConsumption: 'Consumo diário',
          wsDailyConsumptionHeader: 'CONSUMO DE IA',
          wsDailyConsumptionDate: 'DATA',
          wsDailyConsumptionTranscription: 'TRANSCRIÇÃO DE ÁUDIO',
          wsDailyConsumptionSummary: 'RESUMO INTELIGENTE',
          wsDailyConsumptionMagicText: 'TEXTO MÁGICO',
          wsDailyConsumptionCopilot: 'COPILOTO',
          wsDailyConsumptionCSAT: 'IA CSAT',
          wsPackagesAquisition: 'Aquisição de pacotes',
          wsPackagesAquisitionDate: 'DATA',
          wsPackagesAquisitionActivity: 'ATIVIDADE',
          wsPackagesAquisitionService: 'SERVIÇO DE IA',
          transcription: 'Transcrição',
          summary: 'Resumo',
          magicText: 'Texto Mágico',
          copilot: 'Copiloto',
          cSAT: 'IA CSAT',
        }
        break
      case 'en-US':
        translations = {
          wsSummaryHeader: 'DASHBOARD - IA CONSUMPTION',
          wsSummaryPeriod: `Period: From ${params.to}, To ${params.from}`,
          wsSummaryTranscriptionTotal: 'AUDIO TRANSCRIPTION - TOTAL',
          wsSummarySummaryTotal: 'SMART SUMMARY - TOTAL',
          wsSummaryMagicTextTotal: 'MAGIC TEXT - TOTAL',
          wsSummaryCopilotTotal: 'COPILOT - TOTAL',
          wsSummaryCSATTotal: 'AI CSAT - TOTAL',
          wsDailyConsumption: 'Daily consumption',
          wsDailyConsumptionHeader: 'IA CONSUMPTION',
          wsDailyConsumptionDate: 'DATE',
          wsDailyConsumptionTranscription: 'AUDIO TRANSCRIPTION',
          wsDailyConsumptionSummary: 'SMART SUMMARY',
          wsDailyConsumptionMagicText: 'MAGIC TEXT',
          wsDailyConsumptionCopilot: 'COPILOT',
          wsDailyConsumptionCSAT: 'AI CSAT',
          wsPackagesAquisition: 'Packages acquisition',
          wsPackagesAquisitionDate: 'DATE',
          wsPackagesAquisitionActivity: 'ACTIVITY',
          wsPackagesAquisitionService: 'IA SERVICE',
          transcription: 'Transcription',
          summary: 'Summary',
          magicText: 'Magic Text',
          copilot: 'Copilot',
          cSAT: 'AI CSAT',
        }
        break
      case 'es':
        translations = {
          wsSummaryHeader: 'DASHBOARD - CONSUMO DE IA',
          wsSummaryPeriod: `Período: Del ${params.to} al ${params.from}`,
          wsSummaryTranscriptionTotal: 'TRANSCRIPCIÓN DE AUDIO - TOTAL',
          wsSummarySummaryTotal: 'RESUMEN INTELIGENTE - TOTAL',
          wsSummaryMagicTextTotal: 'TEXTO MÁGICO - TOTAL',
          wsSummaryCopilotTotal: 'COPILOTO - TOTAL',
          wsSummaryCSATTotal: 'IA CSAT - TOTAL',
          wsDailyConsumption: 'Consumo Diario',
          wsDailyConsumptionHeader: 'CONSUMO DE IA',
          wsDailyConsumptionDate: 'FECHA',
          wsDailyConsumptionTranscription: 'TRANSCRIPCIÓN DE AUDIO',
          wsDailyConsumptionSummary: 'RESUMEN INTELIGENTE',
          wsDailyConsumptionMagicText: 'TEXTO MÁGICO',
          wsDailyConsumptionCopilot: 'COPILOT',
          wsDailyConsumptionCSAT: 'IA CSAT',
          wsPackagesAquisition: 'Adquisición de Paquetes',
          wsPackagesAquisitionDate: 'FECHA',
          wsPackagesAquisitionActivity: 'ACTIVIDAD',
          wsPackagesAquisitionService: 'SERVICIO DE IA',
          transcription: 'Transcripción',
          summary: 'Resumen',
          magicText: 'Texto Mágico',
          copilot: 'Copilot',
          cSAT: 'IA CSAT',
        }
        break
    }
    return translations
  }
}

export default new CreditMovementResource()
