import { pick } from 'lodash'
import { v4 as uuid } from 'uuid'
import { UserInstance } from '../dbSequelize/models/User'
import BaseResource from './BaseResource'
import ticketRepository from '../dbSequelize/repositories/ticketRepository'
import { TicketInstance } from '../dbSequelize/models/Ticket'
import Contact, { ContactInstance } from '../dbSequelize/models/Contact'
import { TopicData } from '../../microServices/api/controllers/dashboard/service/conversation/topics'
import { DepartmentTotals } from '../../microServices/api/controllers/dashboard/service/conversation/departments'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import messageResource from './messageResource'
import ticketTransfersResource from './ticketTransfersResource'
import contactResource from './contactResource'
import { UserTotals } from '../../microServices/api/controllers/dashboard/service/conversation/usersV2'

export const OPENED = 'opened'
export const CLOSED = 'closed'
export const INACTIVE = 'inactive'
export const TRANSFER = 'transfer'
export const BEFORE_CLOSE = 'before_close'

export class TicketResource extends BaseResource<TicketInstance> {
  constructor() {
    super(ticketRepository)

    this.events = [...super.getEvents(), OPENED, CLOSED, INACTIVE, TRANSFER, BEFORE_CLOSE]
  }

  onClosed(listener) {
    return this.on(CLOSED, listener)
  }

  emitClosed(data, evenTransaction) {
    return this.emit(CLOSED, data, evenTransaction)
  }

  emitBeforeClose(data, evenTransaction) {
    return this.emit(BEFORE_CLOSE, data, evenTransaction)
  }

  onBeforeClose(listener) {
    return this.on(BEFORE_CLOSE, listener)
  }

  onOpened(listener) {
    return this.on(OPENED, listener)
  }

  emitOpened(data, evenTransaction?) {
    return this.emit(OPENED, data, evenTransaction)
  }

  onTransfer(listener) {
    return this.on(TRANSFER, listener)
  }

  onInactive(listener) {
    return this.on(INACTIVE, listener)
  }

  emitInactive(data, evenTransaction?) {
    return this.emit(INACTIVE, data, evenTransaction)
  }

  getFirstWaitingTime = async (ticketId: string) => {
    const ticket = await this.findById(ticketId)
    return this.formatSeconds(ticket.metrics?.waitingTime || 0)
  }

  formatSeconds = (waitSeconds: number) => {
    if (waitSeconds === 0) {
      return '00:00:00'
    }
    const date = new Date(waitSeconds * 1000)
    const hours = date.getUTCHours().toString()
    const minutes = date.getUTCMinutes().toString()
    const seconds = date.getSeconds().toString()
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:${seconds.padStart(2, '0')}`
  }

  async getAverageOpenTicketTime(query) {
    return ticketRepository.getAverageOpenTicketTime(query)
  }

  async getAverageOpenTicketsFirstWaitTime(query) {
    return ticketRepository.getAverageOpenTicketsFirstWaitTime(query)
  }

  getStatusQuery(data) {
    const { status } = data
    if (status == 'open') {
      return 'and "isOpen"=true'
    }
    if (status == 'closed') {
      return 'and "isOpen"=false'
    }
    return ''
  }

  getBaseFiltersWheres(query: {}) {
    return `contacts."deletedAt" is null
      and contacts."visible" = true
      and contacts."isGroup" = false
      ${query.tagId ? 'and contact_tags."tagId" = $tagId ' : ''}

      ${
        query.departmentId && query.departmentId === 'null'
          ? ' and tickets."departmentId" IS NULL '
          : query.departmentId
          ? ' and tickets."departmentId" = $departmentId '
          : ''
      }
      ${query.ticketTopicId ? 'and ticket_ticket_topics."ticketTopicId" = $ticketTopicId ' : ''}
      and (tickets."startedAt" between $startPeriod::timestamp and $endPeriod::timestamp)
      ${this.getStatusQuery(query)}
    `
  }

  async statisticsByDeparment(query: {}, departmentId: string = undefined): Promise<DepartmentTotals[]> {
    const filters = this.getBaseFiltersWheres(query)
    return ticketRepository.statisticsByDeparment(query, filters, departmentId)
  }

  async statisticsByUser(query: {}, userId: string = undefined): Promise<UserTotals[]> {
    const filters = this.getBaseFiltersWheres(query)
    return ticketRepository.statisticsByUser(query, filters, userId)
  }

  async statisticsByTopic(query: {}, topicId: string = undefined): Promise<TopicData[]> {
    const filters = this.getBaseFiltersWheres(query)
    return ticketRepository.statisticsByTopic(query, filters, topicId)
  }

  async statisticsByService(query: {}, serviceId: string = undefined): Promise<TopicData[]> {
    const filters = this.getBaseFiltersWheres(query)
    return ticketRepository.statisticsByService(query, filters, serviceId)
  }

  async countOpenTicketsByService(serviceId: string): Promise<number> {
    return ticketRepository.count({
      include: [
        {
          model: Contact,
          as: 'contact',
          where: {
            serviceId,
          },
        },
      ],
      where: {
        isOpen: true,
      },
    })
  }

  async bulkTransfer(params: {}, user: UserInstance, protocolsEnabled: boolean) {
    const data = {
      ...pick(params, ['departmentId', 'userId', 'comments', 'ticketsSelectedId', 'allContactsSelected', 'query']),
      byUserId: user.id,
      protocolsEnabled,
    }

    const transfers = []
    const transferMessages = []
    let ticketsSelectedId = []
    let tickets
    if (data?.allContactsSelected) {
      const queryParam = JSON.parse(data.query?.query)
      const query = {
        ...queryParam,
        where: {
          ...queryParam.where,
          isOpen: true,
        },
      }
      tickets = await this.findMany(query)
      ticketsSelectedId = tickets.map((ticket) => ticket.id)
    } else {
      ticketsSelectedId = data.ticketsSelectedId
      tickets = await this.findManyByIds(ticketsSelectedId, {
        include: [
          {
            model: 'contact',
            required: true,
          },
        ],
      })
    }

    const now = new Date()
    await queuedAsyncMap(tickets, async (ticket) => {
      const contact = ticket?.contact || {}
      const messageId = uuid()
      transferMessages.push({
        id: messageId,
        data: { ticketTransfer: true },
        type: 'ticket',
        origin: 'ticket',
        contact,
        timestamp: now,
        ticketId: ticket.id,
        contactId: contact.id,
        serviceId: contact.serviceId,
        accountId: contact.accountId,
        account: user.account,
      })

      transfers.push({
        action: 'transferred',
        accountId: contact.accountId,
        toDepartmentId: data.departmentId,
        fromDepartmentId: ticket.departmentId,
        toUserId: data.userId,
        fromUserId: ticket.userId,
        transferredMessageId: messageId,
        ticketId: ticket.id,
        byUserId: user.id,
        comments: data.comments,
        startedAt: now,
      })
    })

    await this.getRepository().transaction(async (transaction) => {
      await this.bulkUpdate(data, {
        where: { id: { $in: ticketsSelectedId }, accountId: user.accountId },
        ...transaction,
        dontEmit: true,
      }).then((models) => this.emit(TRANSFER, models))
      await messageResource.bulkCreate(transferMessages, transaction)
      await ticketTransfersResource.bulkCreate(transfers, transaction)
    })
  }

  async closeManyByTicketIds(
    ticketIds: string[],
    data: {
      accountId: string
      byUserId?: string
      comments?: string
      ticketTopicIds?: string[] | string
      transferredByBot?: Boolean
      transaction?: any
    },
  ) {
    if (!ticketIds?.length) return

    let contacts
    do {
      const result = await contactResource.findManyPaginated({
        page: 1,
        perPage: 100,
        include: [
          {
            model: 'currentTicket',
            required: true,
            include: [
              {
                model: 'currentTicketTransfer',
              },
            ],
            where: {
              id: { $in: ticketIds },
              isOpen: true,
            },
          },
          {
            model: 'service',
            attributes: ['id', 'botId'],
            required: true,
          },
        ],
        where: {
          ...(data.accountId && { accountId: data.accountId }),
        },
        ...(!data.accountId && { noAccountId: true }),
      })

      contacts = result.data

      if (!contacts?.length) break

      const clearMarkForNoBan = await contactResource.setMarkForNoBan(
        data.accountId,
        contacts.map((c) => c.id),
      )

      await this.eventTransaction(async (eventTransaction) =>
        queuedAsyncMap(contacts, async (contact: ContactInstance) => {
          return contactResource.closeTicket(
            {
              contact,
              ...data,
            },
            { eventTransaction, transaction: data.transaction },
          )
        }),
      )

      await Promise.allSettled([...clearMarkForNoBan.map((fn) => fn())])
    } while (contacts?.length)
  }
}

export default new TicketResource()
