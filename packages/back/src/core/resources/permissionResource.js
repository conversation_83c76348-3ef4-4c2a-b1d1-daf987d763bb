import BaseResource from './BaseResource'
import permissionRepository from '../dbSequelize/repositories/permissionRepository'

export class PermissionResource extends BaseResource {
  constructor() {
    super(permissionRepository)
  }

  findOneByName(...props) {
    return this.getRepository().findOneByName(...props)
  }

  findManyByNames(...props) {
    return this.getRepository().findManyByNames(...props)
  }

  async save(newPermission) {
    const permissionModel = await this.findOneByName(newPermission.name)

    if (permissionModel) {
      return this.updateById(permissionModel.id, newPermission)
    }
    return this.create(newPermission)
  }
}

export default new PermissionResource()
