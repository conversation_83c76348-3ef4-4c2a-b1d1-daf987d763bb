import sequelize from '../services/db/sequelize'
import BaseResource from './BaseResource'
import contractedCreditRepository from '../dbSequelize/repositories/contractedCreditRepository'
import { ContractedCreditInstance } from '../dbSequelize/models/ContractedCredit'

export class ContractedCreditResource extends BaseResource<ContractedCreditInstance> {
  constructor() {
    super(contractedCreditRepository)
  }

  getActiveCreditSystemByAccount(accountId: string) {
    const currentDate = new Date()

    return this.findMany({
      attributes: ['id', 'type', 'expiredAt', 'contractedQuantity', 'consumedQuantity', 'consumedServiceTypes'],
      where: {
        type: { $or: ['credit-system-plan', 'credit-system-additional'] },
        startedAt: { $lt: currentDate },
        expiredAt: {
          $or: [{ $eq: null }, { $gt: currentDate }],
        },
        isRevoked: false,
        accountId,
      },
      order: [
        [
          // Ordenação da coluna type que é um enum, a ordenação convencional com ASC/DESC em enums não funciona como esperado
          sequelize.literal(
            `CASE "type" WHEN 'credit-system-plan' THEN 0 WHEN 'credit-system-additional' THEN 1 ELSE 2 END`,
          ),
        ],
        ['expiredAt', 'ASC NULLS LAST'],
      ],
    })
  }

  async getActiveCreditSystemResume(accountId: string) {
    const activeCreditSystem = await this.getActiveCreditSystemByAccount(accountId)

    return activeCreditSystem.reduce(
      (acc, credit) => {
        acc.totalIn += credit.contractedQuantity
        acc.totalOut += credit.consumedQuantity

        // Contabilizar o tipo de contratação
        switch (credit.type) {
          case 'credit-system-plan':
            acc.totalPlanIn += credit.contractedQuantity
            acc.totalPlanOut += credit.consumedQuantity
            acc.quantityPlanActive += 1
            break
          case 'credit-system-additional':
            acc.totalAdditionalIn += credit.contractedQuantity
            acc.totalAdditionalOut += credit.consumedQuantity
            acc.quantityAdditionalActive += 1

            if (acc.nextAdditionalExpireAt === null) {
              acc.nextAdditionalInExpire = credit.contractedQuantity
              acc.nextAdditionalExpireAt = credit.expiredAt
            }
            break
        }

        // Contabilizar o consumo do tipo de serviço
        for (const [serviceType, consumed] of Object.entries(credit.consumedServiceTypes)) {
          if (acc.consumedServiceTypes[serviceType] !== undefined) {
            acc.consumedServiceTypes[serviceType] += consumed
          }
        }

        return acc
      },
      {
        totalIn: 0,
        totalOut: 0,
        totalPlanIn: 0,
        totalPlanOut: 0,
        quantityPlanActive: 0,
        totalAdditionalIn: 0,
        totalAdditionalOut: 0,
        quantityAdditionalActive: 0,
        nextAdditionalInExpire: 0,
        nextAdditionalExpireAt: null,
        consumedServiceTypes: {
          email: 0,
          'facebook-messenger': 0,
          'google-business-message': 0,
          instagram: 0,
          'reclame-aqui': 0,
          'sms-wavy': 0,
          telegram: 0,
          webchat: 0,
          whatsapp: 0,
          'whatsapp-business': 0,
        },
      },
    )
  }

  existsCreditSystemAdditionalByEventId(eventId: string, accountId: string): Promise<boolean> {
    return this.exists({
      where: {
        type: 'credit-system-additional',
        contractedEventId: eventId,
        accountId,
      },
    })
  }

  existsCreditSystemPlanByStartedAt(startedAt: Date, accountId: string): Promise<boolean> {
    return this.exists({
      where: {
        type: 'credit-system-plan',
        startedAt,
        accountId,
      },
    })
  }

  updateExpiredCreditSystemPlan(startedAt: Date, accountId: string): Promise<ContractedCreditInstance[]> {
    return this.bulkUpdate(
      {
        expiredAt: new Date(),
      },
      {
        where: {
          type: 'credit-system-plan',
          startedAt: { $ne: startedAt },
          expiredAt: null,
          accountId,
        },
      },
    )
  }
}

export default new ContractedCreditResource()
