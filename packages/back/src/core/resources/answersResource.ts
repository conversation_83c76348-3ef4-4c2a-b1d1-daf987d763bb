import omit from 'lodash/omit'
import isBefore from 'date-fns/isBefore'
import BaseResource, { EventTransaction } from './BaseResource'
import answersRepository from '../dbSequelize/repositories/answersRepository'
import contactResource from './contactResource'
import messageResource from './messageResource'
import questionResource from './questionResource'
import { AnswerInstance } from '../dbSequelize/models/Answer'
import { ContactInstance } from '../dbSequelize/models/Contact'
import reportError from '../services/logs/reportError'

export class AnswersResource extends BaseResource<AnswerInstance> {
  constructor() {
    super(answersRepository)
  }

  async createAnswerForContact(
    contact: ContactInstance,
    text: string | null,
    options?: { transaction: any; eventTransaction: any },
  ): Promise<AnswerInstance> {
    const answer = await this.findOne({
      where: { ticketId: contact.data.survey.ticketId },
      ...options,
    })

    if (!answer) {
      return this.create(
        {
          questionId: contact.data.survey.questionId,
          ticketId: contact.data.survey.ticketId,
          text,
        },
        options,
      )
    }
    return answer
  }

  async updateAnswerForContact(
    contact: ContactInstance,
    text: string | null,
    options?: { transaction: any },
    answer?: AnswerInstance,
  ): Promise<AnswerInstance> {
    const getData = () => {
      if (answer?.text && answer?.text !== 'nv' && !answer?.reason) return { reason: text }
      return { text }
    }

    if (answer) {
      const data = getData()
      return this.update(answer, data)
    }
    return this.createAnswerForContact(contact, text, options)
  }

  async unflagSurveyFromContact(
    contact: ContactInstance,
    options?: {
      transaction?: any
      eventTransaction?: EventTransaction
    },
  ): Promise<ContactInstance> {
    return contactResource.updateById(
      contact.id,
      {
        data: {
          ...omit(contact.data, ['survey']),
        },
      },
      { ...options, dontEmit: true },
    )
  }

  async sendMessage(contact: ContactInstance, text: string) {
    messageResource
      .send({
        contact,
        text,
        origin: 'bot',
        dontOpenTicket: true,
      })
      .catch(reportError)
  }

  async decrementTries(contact: ContactInstance, options: { transaction?: any }): Promise<ContactInstance> {
    return contactResource.updateById(
      contact.id,
      {
        data: {
          ...contact.data,
          survey: {
            ...contact.data.survey,
            tries: contact.data.survey.tries - 1,
          },
        },
      },
      { ...options, dontEmit: true },
    )
  }

  async handleAnswer(data: {
    contact: ContactInstance
    text: string
    options?: { transaction?: any; eventTransaction?: any }
  }): Promise<boolean> {
    const { contact, text, options = {} } = data

    return this.maybeEventTransaction(async (eventTransaction) => {
      return this.nestedTransaction(async (transaction) => {
        const question = await questionResource.findById(contact.data.survey.questionId, {
          transaction,
          eventTransaction,
        })

        const surveyExpired = !isBefore(new Date(), new Date(contact.data.survey.expiresAt))

        // Duration expired
        if (surveyExpired) {
          await this.createAnswerForContact(contact, '-1', { transaction, eventTransaction })
          await this.unflagSurveyFromContact(contact, { transaction, eventTransaction })
          return true
        }

        const answer = await this.findOne({
          where: {
            ticketId: contact.data.survey.ticketId,
          },
          transaction,
        })

        const isValid = () => {
          if (answer?.text !== 'nv' && question?.reasonMessage && !answer?.reason) {
            return true
          }
          if (question.type === 'nps') {
            return text && Number(text) >= 0 && Number(text) <= 10
          }
          if (question.type === 'csat') {
            return text && Number(text) > 0 && Number(text) <= 5
          }
          return false
        }

        const isValidAnswer = isValid()

        // Invalid message
        if (!isValidAnswer) {
          // No remaining tries
          if (contact.data.survey.tries <= 1) {
            await this.createAnswerForContact(contact, '-1', { transaction })
            await this.unflagSurveyFromContact(contact, { transaction })
            return false
          }

          // Have remaining tries
          if (question.invalidMessage) {
            this.sendMessage(contact, question.invalidMessage).then(async (r) => this.decrementTries(contact, {}))
            return
          }

          await this.decrementTries(contact, { transaction })
          return false
        }

        // Valid message
        await this.updateAnswerForContact(contact, text, { transaction }, answer)

        // não executar assincrono
        ;(async () => {
          if (answer?.text !== 'nv' && question?.reasonMessage && !answer?.reason) {
            this.sendMessage(contact, question.reasonMessage).catch(reportError)
          }

          if (answer?.text !== 'nv' && ((question?.reasonMessage && answer?.reason) || !question?.reasonMessage)) {
            if (question.successMessage) await this.sendMessage(contact, question.successMessage)

            this.unflagSurveyFromContact(contact).catch(reportError)
            return false
          }
        })()
      }, options.transaction)
    }, options.eventTransaction)
  }
}

export default new AnswersResource()
