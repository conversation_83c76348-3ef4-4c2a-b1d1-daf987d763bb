import SmtpClient from '../../microServices/workers/jobs/email/driver/connector/SmtpClient'
import taskQueue from '../services/queue/taskQueue'
import config from '../config'

export class FeedbackResource {
  sendFeedback = async (payload: any) => {
    const feedbackQueue = taskQueue('feedback')

    feedbackQueue.run(async () => {
      const smtpClient = new SmtpClient({
        auth: {
          user: config('feedbackEmailUser'),
          password: config('feedbackEmailPassword'),
        },
        host: config('feedbackEmailHost'),
        port: config('feedbackEmailPort'),
        providerType: 'other',
        requireTLS: true,
      })

      await smtpClient.connect()

      const bodyHtml = `
        <div>
          Feedback: ${payload?.formData?.message}
        </div><br>
        <div>
          ${JSON.stringify(payload, null, '&nbsp;').split('\n').join('<br>')}
        </div>
      `

      await smtpClient.sendMessage({
        from: `<${config('feedbackEmail')}>`,
        to: config('feedbackEmail'),
        subject: 'Feedback',
        html: bodyHtml,
        replyTo: payload?.formData?.email,
      })

      await smtpClient.disconnect()
    })
  }
}

export default new FeedbackResource()
