import omit from 'lodash/omit'
import isEmpty from 'lodash/isEmpty'
import Container from 'typedi'
import BaseResource from './BaseResource'
import interactiveMessageRepository from '../dbSequelize/repositories/interactiveMessageRepository'
import { InteractiveMessageInstance } from '../dbSequelize/models/InteractiveMessage'
import departmentResource from './departmentResource'
import fileResource from './fileResource'
import { getStorage } from '../services/storage'
import { mustConvertAudio } from '../utils/convertAudio'
import reportError from '../services/logs/reportError'
import Logger from '../services/logs/Logger'

const logger = Container.get(Logger)
export class InteractiveMessageResource extends BaseResource<InteractiveMessageInstance> {
  constructor() {
    super(interactiveMessageRepository)
  }

  async create(data, options: any = {}) {
    const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)

    const departments = await departmentResource.findManyByIds(departmentIds, {})

    let instance = await super.create(omit(data, ['id', 'departments']), {
      ...options,
      dontEmit: true,
    })

    if (departments) {
      await instance.setDepartments(departments)
    }

    if (options.include) {
      instance = await super.reload(instance, options)
    }

    if (!isEmpty(data.file)) {
      if (data.file.id) {
        const file = await fileResource.findById(data.file.id)

        if (await mustConvertAudio(data.file.mimetype, true)) {
          const buffer = await fileResource.getBuffer({
            ...file,
            storage: file.storage,
            filepath: `${file.accountId}/${file.id}.${file.extension}`,
          })
          await fileResource.create({
            ...omit(file, ['id', 'attachedId', 'createdAt', 'updatedAt']),
            data: buffer,
            attachedType: 'message.file',
            isPtt: true,
            accountId: data.accountId,
          })
        } else {
          const createdFile = await fileResource.getRepository().create({
            ...omit(file, ['id', 'createdAt', 'updatedAt', 'attachedId', 'attachedType']),
            attachedType: 'interactive.file',
            attachedId: instance.id,
            accountId: data.accountId,
            storage: file.storage,
          })

          await getStorage(file.storage)
            .copy(`${file.accountId}/${file.id}.${file.extension}`, `${data.accountId}/${createdFile.filename}`)
            .catch((error) => {
              logger.log(`Couldn't copy media file from fileId: ${file?.id}`, 'error')
              reportError(error)
            })
        }
      } else {
        await fileResource.create(
          {
            name: data.file.fileName,
            base64: data.file.base64Url.split('base64,')[1],
            mimetype: data.file.mimetype,
            attachedId: instance.id,
            attachedType: 'interactive.file',
            accountId: data.accountId,
          },
          options,
        )
      }
    }

    if (!options.dontEmit) this.emitCreated(instance)

    return instance
  }

  async update(model, data, options: any = {}) {
    const prevModelHeaderType = model.interactive.header?.type

    const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)

    const departments = await departmentResource.findManyByIds(departmentIds, {})

    let instance = await super.update(model, omit(data, ['departments']), {
      ...options,
      dontEmit: true,
    })

    if (departments) {
      await instance.setDepartments(departments)
    }

    instance = await super.reload(instance, { ...options, include: ['file'] })

    if (!isEmpty(data.file)) {
      if (data.file.fileName !== instance.file?.name) {
        await fileResource.eventTransaction(async (eventTransaction) =>
          fileResource.transaction(async (transaction) => {
            if (instance.file) {
              await fileResource.destroy(instance.file, {
                transaction,
                eventTransaction,
                ...options?.transaction,
                ...options?.eventTransaction,
              })
            }
            instance.file = await fileResource.create(
              {
                name: data.file?.fileName,
                base64: data.file?.base64Url.split('base64,')[1],
                mimetype: data.file?.mimetype,
                attachedId: instance.id,
                attachedType: 'interactive.file',
                accountId: data.accountId,
              },
              {
                transaction,
                eventTransaction,
                ...options?.transaction,
                ...options?.eventTransaction,
              },
            )
          }),
        )
      }
    } else if (instance.file && data.interactive.header?.type !== prevModelHeaderType) {
      await fileResource.destroy(instance.file, {
        ...options?.transaction,
        ...options?.eventTransaction,
      })
      instance.file = null
    }

    if (!options.dontEmit) this.emitUpdated(instance)

    return instance
  }
}

export default new InteractiveMessageResource()
