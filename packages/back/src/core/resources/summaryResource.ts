import BaseResource from './BaseResource'
import summaryRepository from '../dbSequelize/repositories/summaryRepository'
import { SummaryInstance } from '../dbSequelize/models/Summary'
import userResource from './userResource'
import { UserInstance } from '../dbSequelize/models/User'
import { Op } from 'sequelize'

export class SummaryResource extends BaseResource<SummaryInstance> {
  constructor() {
    super(summaryRepository)
  }

  async getUsersAdminNotNotified(accountId: string, attributes: array<string>): Promise<UserInstance[]> {
    const date = new Date()
    return userResource.findMany({
      attributes,
      where: {
        accountId,
        '$notifications.id$': { $is: null },
      },
      include: [
        {
          model: 'roles',
          attributes: ['id'],
          where: { isAdmin: true },
          required: true,
        },
        {
          model: 'notifications',
          attributes: ['id'],
          where: {
            createdAt: {
              $gte: new Date(date.setHours(0, 1)),
              $lte: new Date(date.setHours(23, 1)),
            },
          },
          required: false,
        },
      ],
    })
  }
}

export default new SummaryResource()
