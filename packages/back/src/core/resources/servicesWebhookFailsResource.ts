import BaseResource from './BaseResource'
import { ServicesWebhookFailInstance } from '../dbSequelize/models/ServicesWebhookFail'
import servicesWebhookFailsRepository from '../dbSequelize/repositories/ServicesWebhookFailsRepository'

export class ServicesWebhookFailsResource extends BaseResource<ServicesWebhookFailInstance> {
  constructor() {
    super(servicesWebhookFailsRepository)
  }
}

export default new ServicesWebhookFailsResource()
