import pick from 'lodash/pick'
import campaignMessageRepository from '../dbSequelize/repositories/campaignMessageRepository'
import BaseResource from './BaseResource'
import fileResource from './fileResource'

export class CampaignMessageResource extends BaseResource {
  constructor() {
    super(campaignMessageRepository)
  }

  async create(data, options = {}) {
    const messageData = pick(data, ['text', 'campaignId', 'accountId', 'extraOptions', 'hsmId', 'hsmFileId'])
    const message = await campaignMessageRepository.create(messageData, options)

    let file = null

    if (data.hsmFileId) {
      file = await fileResource.findById(data.hsmFileId)
    } else if (data.base64Url) {
      file = await fileResource.create(
        {
          ...pick(data, ['base64Url', 'accountId']),
          encrypt: false,
        },
        pick(options, ['transaction']),
      )
    }

    if (file) {
      await message.setFile(file, pick(options, ['transaction']))
    }

    if (!options.dontEmit) this.emitCreated(message)

    return message
  }

  async update(model, data, options = {}) {
    const messageData = pick(data, ['text', 'campaignId', 'accountId', 'extraOptions', 'hsmId', 'hsmFileId'])
    const message = await campaignMessageRepository.update(model, messageData, options)

    const file = model.file || (await model.getFile())

    if (file && (data.base64Url || !data.file)) {
      await fileResource.destroy(file)
    }

    if (data.base64Url) {
      const newFileData = {
        ...pick(data, ['base64Url', 'accountId']),
        encrypt: false,
      }
      const newFile = await fileResource.create(newFileData, pick(options, ['transaction']))
      await message.setFile(newFile)
    }

    if (!options.dontEmit) this.emitCreated(message)

    return message
  }
}

export default new CampaignMessageResource()
