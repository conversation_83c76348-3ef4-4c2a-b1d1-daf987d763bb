import { chunk } from 'lodash'
import BaseResource from './BaseResource'
import { NotificationReadInstance } from '../dbSequelize/models/NotificationRead'
import notificationReadRepository from '../dbSequelize/repositories/notificationReadRepository'
import sequelize from '../../core/services/db/sequelize'
import { NotificationInstance } from '../dbSequelize/models/Notification'
import queuedAsyncMap from '../../core/utils/array/queuedAsyncMap'

export class NotificationReadResource extends BaseResource<NotificationReadInstance> {
  constructor() {
    super(notificationReadRepository)
  }

  // Marca uma notificação como lida
  async markSingleNotificationAsRead(notificationId: string, userId: string): Promise<NotificationReadInstance> {
    const notification = await this.repository.findOne({
      where: {
        notificationId,
        userId,
      },
    })

    if (!notification) {
      return this.repository.create({
        notificationId,
        userId,
        readAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }

    return this.repository.update(notification, {
      readAt: new Date(),
      updatedAt: new Date(),
    })
  }

  // Marca uma notificação como não lida
  async markSingleNotificationAsUnread(
    notificationId: string,
    userId: string,
  ): Promise<NotificationReadInstance | null> {
    const notification = await this.repository.findOne({
      where: {
        notificationId,
        userId,
      },
    })

    if (notification) {
      return this.repository.update(notification, {
        readAt: null,
        updatedAt: new Date(),
      })
    }

    return null
  }

  // Marca todas as notificações como lida
  async markMultipleNotificationsAsRead(
    userId: string,
    notifications: NotificationInstance[],
  ): Promise<{ total: number; read: number; unread: number }> {
    try {
      await sequelize.query(`delete from notification_reads where "userId" = :userId`, { replacements: { userId } })

      const now = new Date()

      const lots = chunk(notifications, 500)

      await queuedAsyncMap(
        lots,
        async (lot) => {
          const notificationsRead = lot.map((notification) => ({
            userId,
            notificationId: notification.id,
            readAt: now,
            createdAt: now,
            updatedAt: now,
          }))

          await this.repository.bulkCreate(notificationsRead, {})
        },
        1,
      )

      return {
        total: notifications.length,
        read: notifications.length,
        unread: 0,
      }
    } catch (error) {
      throw new Error(`Failed to process notifications: ${error.message}`)
    }
  }
}

export default new NotificationReadResource()
