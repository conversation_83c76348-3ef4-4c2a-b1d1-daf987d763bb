import { isEmpty, uniq } from 'lodash'
import { subDays } from 'date-fns'
import sequelize from '../services/db/sequelize'
import WhatsappBusinessTemplateRepository from '../dbSequelize/repositories/WhatsappBusinessTemplateRepository'
import WhatsappBusinessTemplateHistoryRepository from '../dbSequelize/repositories/WhatsappBusinessTemplateHistoryRepository'
import BaseResource from './BaseResource'
import fileResource from './fileResource'
import { AccountInstance } from '../dbSequelize/models/Account'
import accountResource from './accountResource'
import serviceResource from './serviceResource'
import hub360 from '../services/hub360'
import Balance from '../services/hub360/interfaces/Balance'
import config from '../configValues'
import { findParamsInComponent } from '../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions'
import { WhatsappBusinessTemplateInstance } from '../dbSequelize/models/WhatsappBusinessTemplate'

export class WhatsappBusinessTemplateResource extends BaseResource<any> {
  constructor() {
    super(<any>WhatsappBusinessTemplateRepository)
  }

  validateDuplicateParams(data): boolean {
    if (!data?.components) {
      return false
    }
    const bodyParams = findParamsInComponent(data.components, 'BODY', undefined)
    const headerParams = findParamsInComponent(data.components, 'HEADER', 'TEXT')

    const isDuplicateBodyParams = bodyParams.length > uniq(bodyParams).length
    const isDuplicateHeaderParams = headerParams.length > uniq(headerParams).length

    return isDuplicateBodyParams || isDuplicateHeaderParams
  }

  async create(data, options = {}) {
    data.status = data.status.toUpperCase()

    if (this.validateDuplicateParams(data)) {
      throw new Error('Parameters cannot be duplicated')
    }

    const exists = await super.exists({
      where: {
        name: data.name,
        serviceId: data.serviceId,
        accountId: data.accountId,
        language: data.language,
        archivedAt: {
          $or: [null, { $gte: subDays(new Date(), 30) }],
        },
      },
    })

    if (exists) {
      throw new Error(`Template name already exists. ${data.name}`)
    }

    const template = await super.create(data, options)

    if (!isEmpty(data.fileExample)) {
      await fileResource.create(
        {
          name: data.fileExample.fileName,
          base64: data.fileExample.base64Url.split('base64,')[1],
          mimetype: data.fileExample.mimetype,
          attachedId: template.id,
          attachedType: 'hsm.file',
          accountId: data.accountId,
        },
        options,
      )
    }

    return template
  }

  async update(model, data, options = {}) {
    if ('status' in data) {
      data.status = data.status.toUpperCase()
    }

    if ('name' in data) {
      delete data.name
    }

    if (this.validateDuplicateParams(data)) {
      throw new Error('Parameters cannot be duplicated')
    }

    if ('quality' in data) {
      data.quality = await this.treatQuality(data.quality)
    }

    await this.createHistory(model, data)

    const template = await super.update(model, data, {
      ...options,
      include: [
        {
          model: 'fileExample',
          order: [['createdAt', 'DESC']],
          limit: 1,
        },
      ],
    })

    if (!isEmpty(data.fileExample) && data.fileExample.fileName !== template.fileExample?.[0]?.fileName) {
      await fileResource.create(
        {
          name: data.fileExample?.fileName,
          base64: data.fileExample?.base64Url.split('base64,')[1],
          mimetype: data.fileExample?.mimetype,
          attachedId: template.id,
          attachedType: 'hsm.file',
          accountId: data.accountId,
        },
        options,
      )
    }

    return template
  }

  async treatQuality(quality) {
    const mappings = {
      GREEN: 'HIGH',
      YELLOW: 'MEDIUM',
      RED: 'LOW',
      'ACTIVE - HIGH QUALITY': 'HIGH',
      'ACTIVE - MEDIUM QUALITY': 'MEDIUM',
      'ACTIVE - LOW QUALITY': 'LOW',
      'ACTIVE - QUALITY PENDING': 'UNKNOWN',
    }

    return mappings[quality?.toUpperCase()] || quality
  }

  async createHistory(oldData, newData) {
    if (
      (newData?.status && oldData?.status !== newData.status) ||
      (newData?.quality && oldData?.quality !== newData.quality)
    ) {
      await WhatsappBusinessTemplateHistoryRepository.create({
        accountId: oldData?.accountId,
        wabaTemplateId: oldData?.id,
        ...(newData.status && { statusFrom: oldData.status }),
        ...(newData.status && { statusTo: newData.status }),
        ...(newData.quality && { qualityFrom: oldData.quality }),
        ...(newData.quality && { qualityTo: newData.quality }),
      })
    }
  }

  async getClientId(account: AccountInstance): Promise<string> {
    if (!account?.settings?.idClientHub360) {
      const idHub360 = account?.settings?.idHub360
      const clientId = await hub360.getClientId(idHub360)
      if (!clientId) {
        return null
      }

      await accountResource.updateById(
        account.id,
        {
          settings: { idClientHub360: clientId },
        },
        { mergeJson: ['settings'] },
      )
      return clientId
    }

    return account?.settings?.idClientHub360
  }

  async balance(query: any): Promise<Balance[]> {
    const response = [
      {
        business_initiated_paid_quantity: 0,
        business_initiated_quantity: 0,
        free_entry_point: 0,
        free_quantity: 0,
        paid_quantity: 0,
        quantity: 0,
        user_initiated_paid_quantity: 0,
        user_initiated_quantity: 0,
      },
    ]

    const services = await serviceResource.findMany({
      attributes: ['data', 'internalData'],
      where: {
        type: 'whatsapp-business',
        archivedAt: { $eq: null },
        deletedAt: { $eq: null },
        $and: [sequelize.where(sequelize.literal(`data->>'providerType'`), '360Dialog')],
      },
    })

    if (!services.length) {
      return response
    }

    const hub360PartnerId =
      services.find((service) => service?.data?.hub360PartnerId)?.data?.hub360PartnerId ?? config?.hub360Api?.partnerId
    const clientId = services.find((service) => service?.internalData?.clientId)?.internalData?.clientId
    return (await hub360.balance(hub360PartnerId, clientId, query))?.usage ?? response
  }

  async findManyPaginated(options): Promise<{
    data: WhatsappBusinessTemplateInstance[]
    total: number
    limit: number
    skip: number
    currentPage: number
    lastPage: number
    from: number
    to: number
  }> {
    const modifiedOptions = {
      ...options,
      include: [
        ...((Array.isArray(options.include) && options.include) || [options.include]).filter(Boolean),
        {
          model: 'fileExample',
          required: false,
        },
      ],
    }

    return await this.repository.findManyPaginated(modifiedOptions)
  }
}

export default new WhatsappBusinessTemplateResource()
