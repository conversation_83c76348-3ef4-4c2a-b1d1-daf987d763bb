import sharp from 'sharp'
import BaseResource from './BaseResource'
import { StickerInstance } from '../dbSequelize/models/Sticker'
import stickerRepository from '../dbSequelize/repositories/stickerRepository'
import fileResource from './fileResource'
import messageResource from './messageResource'
import stickerUserResource from './stickerUserResource'
import base64ToBuffer from '../utils/base64/base64ToBuffer'
import { checksum as getChecksum } from '../utils/crypt/checksum'
import HttpError from '../utils/error/HttpError'

/**
 * https://developers.facebook.com/docs/whatsapp/cloud-api/messages/sticker-messages
 * Static sticker Max Size: 100 KB
 * Animated sticker Max Size: 500 KB
 */
const STICKER_STATIC_MAX_SIZE_BYTES = 100 * 1024
const STICKER_ANIMATED_MAX_SIZE_BYTES = 500 * 1024
const STICKER_FORMAT = 'webp'
const STICKER_WIDTH = 512
const STICKER_HEIGHT = 512

export class StickerResource extends BaseResource<StickerInstance> {
  constructor() {
    super(stickerRepository)
  }

  async create(data: {
    data?: Buffer
    base64?: string
    name: string
    mimetype: string
    userId: string
    accountId: string
  }): Promise<StickerInstance> {
    const stickerResult = await this.generateWebpStickerBuffer(data.data || base64ToBuffer(data.base64))

    if (!stickerResult?.data) {
      throw new HttpError(
        stickerResult.errorCode || 400,
        stickerResult.errorMessage || 'Error to generate sticker buffer',
      )
    }

    const stickerBuffer = stickerResult.data
    const checksum = getChecksum(stickerBuffer)

    return this.eventTransaction(async (eventTransaction) =>
      this.transaction(async (transaction) => {
        const createdSticker = await super.create(
          {
            type: 'created',
            originFileChecksum: checksum,
            originFilehash: null,
            originMessageId: null,
            userId: data.userId,
            accountId: data.accountId,
          },
          { eventTransaction, transaction },
        )

        const createdFile = await fileResource.create(
          {
            data: stickerBuffer,
            name: `${data.name}.webp`,
            mimetype: 'image/webp',
            checksum,
            attachedId: createdSticker.id,
            attachedType: 'sticker.file',
            accountId: data.accountId,
          },
          { eventTransaction, transaction },
        )

        createdSticker.file = createdFile

        return createdSticker
      }),
    )
  }

  async findByFileOrMessage(data: {
    fileChecksum?: string
    filehash?: string
    messageId: string
    accountId: string
  }): Promise<{ stickerId: string }> {
    if (!data.messageId) throw new Error('Invalid message ID')

    const stickerQuery: any[] = [{ originMessageId: data.messageId }]

    if (data.fileChecksum) {
      stickerQuery.push({ originFileChecksum: data.fileChecksum })
    }

    if (data.filehash) {
      stickerQuery.push({ originFilehash: data.filehash })
    }

    const sticker = await super.findOne({
      where: {
        $or: stickerQuery,
        accountId: data.accountId,
      },
    })

    return {
      stickerId: sticker?.id,
    }
  }

  async add(data: { messageId: string; userId: string; accountId: string }): Promise<StickerInstance> {
    const message = await messageResource.findOne({
      where: {
        id: data.messageId,
        accountId: data.accountId,
      },
      include: ['file'],
    })

    if (!message?.id || !message?.file) {
      throw new Error('Message or file not found')
    }

    const stickerQuery: any[] = [{ originMessageId: message.id }]

    if (message.file.checksum) {
      stickerQuery.push({ originFileChecksum: message.file.checksum })
    }

    if (message.data.mediaFilehash) {
      stickerQuery.push({ originFilehash: message.data.mediaFilehash })
    }

    const sticker = await super.findOne({
      where: {
        $or: stickerQuery,
        accountId: data.accountId,
      },
      include: ['file'],
      paranoid: false,
    })

    if (sticker && !sticker.deletedAt) {
      // Figurinha já existe e está ativa
      return sticker
    }

    if (sticker && sticker.deletedAt) {
      // Figurinha já existe, mas está removida
      // @ts-ignore
      const restoredSticker = await sticker.restore()

      const updatedSticker = await super.update(restoredSticker, { userId: data.userId })

      return updatedSticker
    }

    // Figurinha não existe, precisa criar
    const createdFile = await fileResource.duplicateFile(message.file.id, 'sticker.file')

    const createdSticker = await super.create({
      type: 'added',
      originFileChecksum: createdFile.checksum,
      originFilehash: message.data.mediaFilehash,
      originMessageId: message.id,
      userId: data.userId,
      accountId: data.accountId,
    })

    const updatedFile = await fileResource.update(createdFile, {
      attachedId: createdSticker.id,
    })

    createdSticker.file = updatedFile

    return createdSticker
  }

  async remove(data: { stickerId: string; userId: string; accountId: string }): Promise<boolean> {
    const sticker = await super.findOne({
      where: {
        id: data.stickerId,
        accountId: data.accountId,
      },
    })

    if (!sticker) return false

    if (sticker.userId !== data.userId) {
      await super.update(sticker, { userId: data.userId })
    }

    await Promise.all([super.destroy(sticker), stickerUserResource.bulkDestroy({ where: { stickerId: sticker.id } })])

    return true
  }

  async generateWebpStickerBuffer(
    buffer: Buffer,
  ): Promise<{ data?: Buffer; errorCode?: number; errorMessage?: string }> {
    const sharpBuffer = sharp(buffer, { animated: true })
    const { pages, format, size, width, height, pageHeight } = await sharpBuffer.metadata()

    const isAnimated = pages && pages > 1
    const stickerHeight = isAnimated ? pageHeight : height
    const stickerMaxBytesSize = isAnimated ? STICKER_ANIMATED_MAX_SIZE_BYTES : STICKER_STATIC_MAX_SIZE_BYTES

    if (
      format === STICKER_FORMAT &&
      width === STICKER_WIDTH &&
      stickerHeight === STICKER_HEIGHT &&
      size < stickerMaxBytesSize
    ) {
      // O buffer enviado não precisa ser alterado
      return { data: buffer }
    }

    const sharpNewBuffer = await sharpBuffer
      .resize({
        width: STICKER_WIDTH,
        height: STICKER_HEIGHT,
        fit: sharp.fit.cover,
      })
      .webp()
      .toBuffer({ resolveWithObject: true })

    if (sharpNewBuffer?.info?.size >= stickerMaxBytesSize) {
      return { data: null, errorCode: 413, errorMessage: `Sticker size (${sharpNewBuffer?.info?.size}) is too large.` }
    }

    return { data: sharpNewBuffer.data }
  }
}

export default new StickerResource()
