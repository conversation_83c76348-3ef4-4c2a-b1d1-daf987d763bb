import Container from 'typedi'
import workerpool from 'workerpool'
import addSeconds from 'date-fns/addSeconds'
import { chunk, omit } from 'lodash'
import BaseResource, { Options } from './BaseResource'
import contactBlockListRepository from '../dbSequelize/repositories/contactBlockListRepository'
import { ContactBlockListInstance } from '../dbSequelize/models/ContactBlockList'
import QueuedSaveContactBlockListJob from '../../microServices/workers/jobs/contactBlockLists/QueuedSaveContactBlockListJob'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import QueueJobsDispatcher from '../services/jobs/queue/QueueJobsDispatcher'
import { offloadValidateContacts } from '../utils/validator/validateNumberFormat'
import contactBlockListsControlResource from './contactBlockListsControlResource'

export class ContactBlockListResource extends BaseResource<ContactBlockListInstance> {
  constructor() {
    super(contactBlockListRepository)
  }

  async create(data: any, options?: Options<ContactBlockListInstance>): Promise<ContactBlockListInstance> {
    if (!data.contacts?.length) return
    const timeLabel1 = `${+new Date()} Time to parse and validate ${data.contacts.length} contacts: `

    console.time(timeLabel1)

    const pool = workerpool.pool()

    const validContacts = await offloadValidateContacts(pool, data.contacts, data.defaultDDI).finally(() => {
      pool.terminate()
    })

    console.timeEnd(timeLabel1)

    const createdContactBlockList = await super.create(
      omit(
        {
          ...data,
          status: !validContacts.length ? 'done' : 'ready',
          totalCount: data.contacts.length,
          validCount: validContacts.length,
          saveCount: 0,
        },
        ['contacts'],
      ),
      options,
    )

    if (!validContacts.length) return createdContactBlockList

    const chunkedContacts = chunk(validContacts, 2000)

    const jobsDispatcher = Container.get(QueueJobsDispatcher)

    // Não usar await aqui pois a request pode levar muito tempo dependendo da lista
    queuedAsyncMap(chunkedContacts, async (contacts) =>
      jobsDispatcher.dispatch<QueuedSaveContactBlockListJob>(
        'queued-save-contact-block-list',
        {
          accountId: data.accountId,
          userId: data.userId,
          contactBlockListId: createdContactBlockList.id,
          contacts,
          defaultDDI: data.defaultDDI,
        },
        {
          hashKey: createdContactBlockList.id,
        },
      ),
    )

    return createdContactBlockList
  }

  async update(
    model: ContactBlockListInstance,
    data: any,
    options?: Options<ContactBlockListInstance>,
  ): Promise<ContactBlockListInstance> {
    const timeLabel1 = `${+new Date()} Time to parse and validate ${data.contacts?.length} contacts: `

    console.time(timeLabel1)

    const pool = workerpool.pool()

    const validContacts = await offloadValidateContacts(pool, data.contacts || [], data.defaultDDI).finally(() => {
      pool.terminate()
    })

    console.timeEnd(timeLabel1)

    const updatedContactBlockList = await super.update(
      model,
      omit(
        {
          ...omit(data, ['userId']),
          ...(data.contacts?.length && {
            userId: data.userId, // Só altera o usuário se ele alterar a lista
            status: !validContacts.length ? 'done' : 'ready',
            totalCount: data.contacts.length,
            validCount: validContacts.length,
            saveCount: 0,
          }),
        },
        ['contacts'],
      ),
      options,
    )

    if (!validContacts?.length) return updatedContactBlockList

    await contactBlockListRepository.removeItems(updatedContactBlockList.id)

    const chunkedContacts = chunk(validContacts, 2000)

    const jobsDispatcher = Container.get(QueueJobsDispatcher)

    // Não usar await aqui pois a request pode levar muito tempo dependendo da lista
    queuedAsyncMap(chunkedContacts, async (contacts) =>
      jobsDispatcher.dispatch<QueuedSaveContactBlockListJob>(
        'queued-save-contact-block-list',
        {
          accountId: data.accountId,
          userId: data.userId,
          contactBlockListId: updatedContactBlockList.id,
          contacts,
          defaultDDI: data.defaultDDI,
        },
        {
          hashKey: updatedContactBlockList.id,
        },
      ),
    )

    return updatedContactBlockList
  }

  async destroy(model: ContactBlockListInstance) {
    if (model.status === 'processing') throw new Error('Contact block list cannot be deleted while processing')

    const isProcessing = await contactBlockListsControlResource.exists({
      where: {
        contactBlockListId: model.id,
        status: 'processing',
      },
    })

    if (isProcessing)
      throw new Error('Contact block list cannot be deleted because there are blocks/unblocks in progress')

    await contactBlockListRepository.removeItems(model.id)

    return super.destroy(model)
  }
}

export default new ContactBlockListResource()
