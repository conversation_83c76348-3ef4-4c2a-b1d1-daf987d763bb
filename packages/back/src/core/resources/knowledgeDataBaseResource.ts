import BaseResource from './BaseResource'
import { KnowledgeDataBaseInstance } from '../dbSequelize/models/KnowledgeDataBase'
import knowledgeDataBaseRepository from '../dbSequelize/repositories/knowledgeDataBaseRepository'

export class KnowledgeDataBaseResource extends BaseResource<KnowledgeDataBaseInstance> {
  constructor() {
    super(knowledgeDataBaseRepository)
  }
}

export default new KnowledgeDataBaseResource()
