import { pick } from 'lodash'
import { Op } from 'sequelize'
import tagRepository from '../dbSequelize/repositories/tagRepository'
import BaseResource from './BaseResource'
import contactResource from './contactResource'
import departmentResource from './departmentResource'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import Department from '../dbSequelize/models/Department'
import sequelize from '../services/db/sequelize'
import iteratePaginated from '../utils/iteratePaginated'

export class TagResource extends BaseResource {
  constructor() {
    super(tagRepository)
  }

  async findManyPaginated(query = {}, userDepartmentIds = [], isAdmin = false) {
    const result = await this.getRepository().findManyPaginated({
      ...query,
      include: [
        ...(query.include || []),
        {
          model: Department,
          as: 'departments',
          required: false,
        },
      ],
      where: {
        ...query.where,
        [Op.and]: [
          { accountId: query.where.accountId },
          ...(!isAdmin
            ? [
                {
                  [Op.or]: [
                    sequelize.literal(`NOT EXISTS (
                SELECT 1 FROM tag_departments td
                WHERE td."tagId" = "Tag"."id"
              )`),
                    sequelize.literal(`EXISTS (
                SELECT 1 FROM tag_departments td
                WHERE td."tagId" = "Tag"."id"
                AND td."departmentId" IN (${userDepartmentIds.map((id) => `'${id}'`).join(',')})
              )`),
                  ],
                },
              ]
            : []),
        ],
      },
    })

    result.data = await queuedAsyncMap(
      result.data,
      async (tag) => {
        return {
          ...tag.dataValues,
          linkedContacts: await this.getRepository().countContactsByTagId(tag.id),
        }
      },
      1,
    )

    return result
  }

  async findAllPaginated(query = {}) {
    const result = await this.getRepository().findManyPaginated({
      ...query,
      include: [...(query.include || []), 'departments'],
    })

    result.data = await queuedAsyncMap(
      result.data,
      async (tag) => {
        return {
          ...tag.dataValues,
          linkedContacts: await this.getRepository().countContactsByTagId(tag.id),
        }
      },
      1,
    )

    return result
  }

  async findById(id, query) {
    const result = await super.findById(id, query)

    if (result && query?.customInclude?.includes('linkedContacts')) {
      result.linkedContacts = await this.getRepository().countContactsByTagId(id)
    }

    return result
  }

  async create(data, options = {}) {
    const trimLabel = data.label?.trim()

    if (!trimLabel) {
      throw new Error('Tag label is required.')
    }

    const tagData = {
      ...pick(data, ['accountId', 'backgroundColor']),
      label: trimLabel,
    }

    let tag = await super.create(tagData, {
      ...options,
      dontEmit: true,
    })

    const departments = await departmentResource.findManyByIds(data.departments)
    await tag.setDepartments(departments)

    if (!options.dontEmit) this.emitCreated(tag)

    if (options.include) {
      tag = await super.reload(tag, options)
    }

    return tag
  }

  async update(model, data, options = {}) {
    const trimLabel = data.label?.trim()

    if (!trimLabel) {
      throw new Error('Tag label is required.')
    }

    const tagData = {
      ...pick(data, ['accountId', 'backgroundColor']),
      label: trimLabel,
    }

    return this.getRepository().transaction(async (transaction) => {
      model.changed('updatedAt', true)

      let tag = await super.update(model, tagData, {
        ...options,
        transaction,
        dontEmit: true,
      })

      const departments = await departmentResource.findManyByIds(data.departments)
      await tag.setDepartments(departments)

      if (!options.dontEmit) this.emitUpdated(tag)

      if (options.include) {
        tag = await super.reload(tag, { ...options, transaction })
      }

      return tag
    })
  }

  async attachContacts(tag, contacts) {
    return tag.addContacts(contacts)
  }

  async detachContacts(tag, contacts) {
    return tag.removeContacts(contacts)
  }

  async attachManyToDepartments(tagIds, departmentIds, accountId) {
    const attachAllDepartments = departmentIds.length === 0

    const departments = attachAllDepartments
      ? []
      : await departmentResource.findMany({
          where: {
            id: departmentIds,
            accountId,
          },
        })

    if (departmentIds.length !== departments.length) {
      throw new Error('The amount department ids is different of found departments')
    }

    await iteratePaginated(
      ({ page }) =>
        this.getRepository().findManyPaginated({
          where: {
            id: tagIds,
            accountId,
          },
          include: ['departments'],
          page,
          perPage: 50,
        }),
      async (model) => {
        if (model.departments.length === 0) {
          // Caso 1: Tag já está em todos os departamentos (um array vazio), não precisa realizar nenhuma ação
          return
        }

        if (attachAllDepartments) {
          // Caso 2: Atribuir todos os departamentos na tag (utilizar um array vazio)
          await model.setDepartments([])
          return
        }

        /**
         * Tag não está em todos os departamentos (Caso 1)
         * Também não é para atribuir todos os departamentos na tag (Caso 2)
         * Caso 3: Portanto, é para adicionar os departamentos informados
         * Caso 3.1: Tag tem o departamento Financeiro -> o resultado será Financeiro, Suporte e Comercial
         * Caso 3.2: Tag tem o departamento Suporte OU Comercial -> o resultado será Suporte e Comercial
         * Caso 3.3: Tag tem o departamento Suporte E Comercial -> o resultado será Suporte e Comercial
         */
        await model.addDepartments(departments)
      },
    )

    return true
  }

  destroy(model) {
    return tagRepository.transaction(async (transaction) => {
      // const contacts = await model.getContacts()
      const contacts = await model.getContacts({
        attributes: ['id'],
        raw: true,
      })

      // await model.removeContacts(contacts)
      await model.setContacts([])
      await model.destroy({ transaction })

      await model.setDepartments([])

      contactResource.emitUpdated(contacts)
      this.emitDestroyed(model)

      return true
    })
  }

  async destroyManyPaginated(tagIds, accountId) {
    await iteratePaginated(
      ({ page }) =>
        this.getRepository().findManyPaginated({
          where: {
            id: tagIds,
            accountId,
          },
          page,
          perPage: 50,
        }),
      async (model) => {
        await this.destroy(model)
      },
    )

    return true
  }
}

export default new TagResource()
