import { pick } from 'lodash'
import quickReplyRepository from '../dbSequelize/repositories/quickReplyRepository'
import BaseResource from './BaseResource'
import departmentResource from './departmentResource'
import categoryResource from './categoryResource'
import fileResource from './fileResource'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

export class QuickReplyResource extends BaseResource {
  constructor() {
    super(quickReplyRepository)
  }

  async create(data, options = {}) {
    const replyData = {
      ...pick(data, ['title', 'text', 'accountId']),
    }

    const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)
    const departments = await departmentResource.findManyByIds(departmentIds)

    const categoryIds = data.categoryIds || (data.categories || []).map((d) => d.id)
    const categories = await categoryResource.findManyByIds(categoryIds)

    let reply = await super.create(replyData, {
      ...options,
      dontEmit: true,
    })

    if (departments) {
      await reply.setDepartments(departments)
    }

    if (categories) {
      await reply.setCategories(categories)
    }

    await queuedAsyncMap(data?.files ?? [], async (file) => {
      const createdFile = await fileResource.updateById(file?.id, { attachedId: reply?.id })
    })

    if (!options.dontEmit) this.emitCreated(reply)

    if (options.include) {
      reply = await super.reload(reply, options)
    }

    return reply
  }

  async update(model, data, options = {}) {
    const replyData = {
      ...pick(data, ['title', 'text', 'accountId']),
    }

    return this.getRepository().transaction(async (transaction) => {
      model.changed('updatedAt', true)

      let quickReply = await super.update(model, replyData, {
        ...options,
        transaction,
        dontEmit: true,
      })

      const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)
      const departments = await departmentResource.findManyByIds(departmentIds)

      const categoryIds = data.categoryIds || (data.categories || []).map((d) => d.id)
      const categories = await categoryResource.findManyByIds(categoryIds)

      await quickReply.setDepartments(departments)

      await quickReply.setCategories(categories)

      await queuedAsyncMap(data?.files ?? [], async (file) => {
        const createdFile = await fileResource.updateById(file?.id, { attachedId: model?.id })
      })

      await fileResource.destroyMany({
        where: { attachedId: model?.id, id: { $notIn: data?.files?.map((file) => file?.id) } },
      })

      if (!options.dontEmit) this.emitUpdated(quickReply)

      if (options.include) {
        quickReply = await super.reload(quickReply, { ...options, transaction })
      }

      return quickReply
    })
  }

  destroy(model, options) {
    return this.getRepository().transaction(async (transaction) => super.destroy(model, { transaction, ...options }))
  }

  async loadOnDemand(query, user) {
    const departmentIds = user.departments.map((dept) => dept.id)
    return this.repository.findManyWithTotal(query, departmentIds)
  }
}

export default new QuickReplyResource()
