import { AuthInfoAppLogInstance } from '../mongoDB/models/AuthInfoAppLog'
import BaseMongoDBRepository from '../mongoDB/repositories/BaseMongoDBRepository'
import authInfoAppLogRepository from '../mongoDB/repositories/authInfoAppLogRepository'

export class AuthAppLogResource {
  protected readonly repository: BaseMongoDBRepository<AuthInfoAppLogInstance>

  constructor(
    // eslint-disable-next-line @typescript-eslint/no-shadow
    authInfoAppLogRepository: BaseMongoDBRepository<AuthInfoAppLogInstance>,
  ) {
    this.repository = authInfoAppLogRepository
  }

  async create(data: AuthInfoAppLogInstance): Promise<AuthInfoAppLogInstance> {
    return this.repository.create(data)
  }
}

export default new AuthAppLogResource(authInfoAppLogRepository)
