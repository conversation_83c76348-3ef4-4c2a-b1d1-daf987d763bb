import { pick } from 'lodash'
import { v4 as uuid } from 'uuid'
import BaseResource from './BaseResource'
import acceptanceTermsRepository from '../dbSequelize/repositories/acceptanceTermsRepository'
import { AcceptanceTermInstance } from '../dbSequelize/models/AcceptanceTerm'
import contactResource from './contactResource'
import fileResource from './fileResource'

const createFile = async (id, accountId, file, uploadedMedia) => {
  const response = await fileResource.create({
    name: file.name,
    data: uploadedMedia,
    mimetype: file.mimetype,
    attachedId: id,
    attachedType: 'acceptanceTerm.files',
    accountId,
  })

  return response.id
}

export class AcceptanceTermsResource extends BaseResource<AcceptanceTermInstance> {
  constructor() {
    super(acceptanceTermsRepository)
  }

  async updateById(id, data) {
    const { accountId, file } = data

    const acceptanceTerm = await super.findById(id, {
      include: [{ model: 'file' }],
    })

    const base64 = acceptanceTerm.file ? await fileResource.getBase64(acceptanceTerm.file) : null

    if (acceptanceTerm.textField !== data.textField || base64 !== file.base64) {
      // Reseta todos os contatos que ja aceitaram esse termo

      await contactResource.bulkUpdate(
        {
          acceptedTermAt: null,
          acceptanceTermId: null,
        },
        {
          where: {
            acceptanceTermId: id,
            acceptedTermAt: { $not: null },
            deletedAt: { $is: null },
          },
        },
      )
    }

    await super.updateById(id, data)

    if (file && file.base64) {
      const uploadMedia = async (data) => {
        const { base64 } = data
        return Buffer.from(base64, 'base64')
      }

      const uploadedMedia = await uploadMedia(file)

      if (base64) {
        await fileResource.destroyById(acceptanceTerm.file.id)

        const fileId = await createFile(id, accountId, file, uploadedMedia)

        await super.updateById(id, { fileId })

        return acceptanceTerm
      }

      const fileId = await createFile(id, accountId, file, uploadedMedia)

      await super.updateById(id, { fileId })
    }

    if (!file.mimetype) {
      await super.updateById(id, { fileId: null })
    }

    return acceptanceTerm
  }

  async create(data) {
    const { accountId } = data

    let fileId = null

    if (data.file && data.file.base64) {
      const { file } = data

      const uploadMedia = async (data) => {
        const { base64 } = data
        return Buffer.from(base64, 'base64')
      }

      const uploadedMedia = await uploadMedia(file)

      const response = await fileResource.create({
        name: file.name,
        data: uploadedMedia,
        mimetype: file.mimetype,
        attachedId: uuid(),
        attachedType: 'acceptanceTerm.files',
        accountId,
      })

      fileId = response.id
    }

    const acceptanceTerm = await super.create({
      ...data,
      ...(fileId && { fileId }),
    })

    return acceptanceTerm
  }

  async sendAcceptanceTerms({ contact, bot, action }) {
    const acceptanceTermId = action.data.acceptanceTerms

    const acceptanceTerms = await this.findById(acceptanceTermId)

    let file

    if (acceptanceTerms.fileId) {
      const fileInstance = await fileResource.findById(acceptanceTerms.fileId)

      if (!fileInstance) {
        throw new Error(`File with id "${acceptanceTerms.fileId}" not found.`)
      }

      file = {
        ...pick(fileInstance, ['mimetype', 'name']),
        base64: await fileResource.getBase64(fileInstance),
      }
    }

    return {
      contact,
      acceptanceTerms,
      file,
      bot,
    }
  }
}

export default new AcceptanceTermsResource()
