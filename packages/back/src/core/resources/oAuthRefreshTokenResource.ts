import oAuthRefreshTokenRepository from '../dbSequelize/repositories/oAuthRefreshTokenRepository'
import BaseResource from './BaseResource'
import { OAuthRefreshTokenInstance } from '../dbSequelize/models/OAuthRefreshToken'

export class OAuthRefreshTokenResource extends BaseResource<OAuthRefreshTokenInstance> {
  constructor() {
    super(oAuthRefreshTokenRepository)
  }
}

export default new OAuthRefreshTokenResource()
