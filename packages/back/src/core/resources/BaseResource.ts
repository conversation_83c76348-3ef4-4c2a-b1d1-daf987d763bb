import { Transaction } from 'sequelize'
import { EventEmitter } from 'events'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
// eslint-disable-next-line import/no-cycle
import BaseRepository from '../dbSequelize/repositories/BaseRepository'
// eslint-disable-next-line import/no-cycle
import optimizeEvents from './utils/optimizeEvents'
// eslint-disable-next-line import/no-cycle
import { Instance } from '../dbSequelize/models'
import config from '../config'
import iteratePaginated from '../utils/iteratePaginated'

export const CREATED = 'created'
export const UPDATED = 'updated'
export const DESTROYED = 'destroyed'

export type EventTransactionTuple = [EventEmitter, string, Instance | Instance[]]

export type EventTransaction = {
  events: EventTransactionTuple[]
  pushEmit(emitter: EventEmitter, event: string, data: Instance | Instance[]): void
}

export type ResourceOptions = {
  dontEmit?: boolean
}

export type Options<TModel> = ResourceOptions & {
  where?: { [Properties in keyof TModel]: any } | { [x: string]: any }
  attributes?: any[]
  group?: any
  having?: string[] | any
  include?: any[]
  fields?: any[]
  order?: any
  offset?: number
  limit?: number
  transaction?: Transaction
  raw?: boolean
  nest?: boolean
  eventTransaction?: EventTransaction
  updateOnDuplicate?: (keyof TModel)[] | string[]
  force?: boolean
  skipValidation?: boolean
  distinct?: boolean
  customFilter?: { [key: string]: any }
  noTag?: boolean
  deferredUpload?: boolean
  mergeJson?: string[]
  cache?: boolean
  cacheIncludeWhitelist?: string[]
  subQuery?: boolean
  logging?: Console['log'] | boolean
  paranoid?: boolean
  returning?: boolean
  required?: boolean
  ignoreDuplicates?: boolean
}

export type BuildOptions<T> = Options<T> & {
  isNewRecord?: boolean
}

export type PaginatedOptions<T> = Options<T> & {
  page?: number
  perPage?: number
  noAccountId?: boolean
}

export default class BaseResource<TModel extends Instance> {
  protected readonly repository: BaseRepository<TModel>

  protected readonly emitter: EventEmitter

  protected events: string[]

  constructor(repository: BaseRepository<TModel>) {
    this.repository = repository
    this.emitter = new EventEmitter()
    this.emitter.setMaxListeners(config('emitterMaxListeners'))
    this.events = [CREATED, UPDATED, DESTROYED]

    this.getRepository = this.getRepository.bind(this)
    this.getEmitter = this.getEmitter.bind(this)
    this.emit = this.emit.bind(this)
    this.emitCreated = this.emitCreated.bind(this)
    this.emitUpdated = this.emitUpdated.bind(this)
    this.emitDestroyed = this.emitDestroyed.bind(this)
    this.reload = this.reload.bind(this)
    this.findMany = this.findMany.bind(this)
    this.count = this.count.bind(this)
    this.max = this.max.bind(this)
    this.findManyPaginated = this.findManyPaginated.bind(this)
    this.findOne = this.findOne.bind(this)
    this.findById = this.findById.bind(this)
    this.findManyByIds = this.findManyByIds.bind(this)
    this.create = this.create.bind(this)
    this.update = this.update.bind(this)
    this.bulkUpdate = this.bulkUpdate.bind(this)
    this.bulkDestroy = this.bulkDestroy.bind(this)
    this.updateById = this.updateById.bind(this)
    this.destroy = this.destroy.bind(this)
    this.destroyById = this.destroyById.bind(this)
    this.existsById = this.existsById.bind(this)
    this.exists = this.exists.bind(this)
    this.build = this.build.bind(this)
  }

  getRepository(): BaseRepository<TModel> {
    return this.repository
  }

  getEvents() {
    return this.events
  }

  getEmitter(): EventEmitter {
    return this.emitter
  }

  emit(event: string, data: Instance | Instance[] | any, eventTransaction?: EventTransaction) {
    if ((Array.isArray(data) && !data.length) || !data) return null

    if (eventTransaction && this.emitter) {
      return eventTransaction.pushEmit(this.emitter, event, data)
    }

    if (this.emitter && this.emitter.emit) return this.emitter.emit(event, data)
  }

  on(event, listener) {
    if (this.emitter && this.emitter.emit) {
      return this.emitter.on(event, listener)
    }
  }

  maybeEventTransaction<TRes>(
    fn: (eventTransaction: EventTransaction) => Promise<TRes>,
    eventTransaction?: EventTransaction,
  ) {
    if (eventTransaction) {
      return fn(eventTransaction)
    }

    return this.eventTransaction(fn)
  }

  transaction<TR>(fn: (transaction: any) => TR): TR {
    return this.getRepository().transaction(fn)
  }

  nestedTransaction<TR>(fn: (transaction: any) => TR, transaction): TR {
    return this.getRepository().nestedTransaction(fn, transaction)
  }

  async eventTransaction<TRes>(fn: (eventTransaction: EventTransaction) => Promise<TRes>): Promise<TRes> {
    const transaction: EventTransaction = {
      events: [],
      pushEmit(emitter, event, data) {
        this.events.push([emitter, event, data])
      },
    }

    const emit = (events: EventTransactionTuple[]) => {
      const optimizedEvents = optimizeEvents(events)

      optimizedEvents.forEach(([emitter, event, data]) => {
        emitter.emit(event, data)
      })
    }

    try {
      const res = await fn(transaction)

      emit(transaction.events)

      return res
    } catch (e) {
      throw e
    }
  }

  build(modelData, options?) {
    return this.getRepository().build(modelData, options)
  }

  emitCreated(data, eventTransaction?: EventTransaction) {
    return this.emit(CREATED, data, eventTransaction)
  }

  emitUpdated(data, eventTransaction?: EventTransaction) {
    return this.emit(UPDATED, data, eventTransaction)
  }

  emitDestroyed(data, eventTransaction?: EventTransaction) {
    return this.emit(DESTROYED, data, eventTransaction)
  }

  onCreated(listener) {
    return this.on(CREATED, listener)
  }

  onUpdated(listener) {
    return this.on(UPDATED, listener)
  }

  onDestroyed(listener) {
    return this.on(DESTROYED, listener)
  }

  reload(model, options = {}) {
    return this.getRepository().reload(model, options)
  }

  findManyPaginated(query: PaginatedOptions<TModel>): Promise<{
    data: TModel[]
    total: number
    limit: number
    skip: number
    currentPage: number
    lastPage: number
    from: number
    to: number
  }> {
    return this.getRepository().findManyPaginated(query)
  }

  findMany(query: Options<TModel> = {}) {
    return this.getRepository().findMany(query)
  }

  count(query: Options<TModel>) {
    return this.getRepository().count(query)
  }

  max(key: string, options: Options<TModel> = {}) {
    return this.getRepository().max(key, options)
  }

  findManyWithTotal(query: Options<TModel>) {
    return this.getRepository().findManyWithTotal(query)
  }

  findById(id, query: Options<TModel> = {}): Promise<TModel> {
    return this.getRepository().findById(id, query)
  }

  findManyByIds(ids, query: Options<TModel> = {}) {
    return this.getRepository().findManyByIds(ids, query)
  }

  existsById(id, query: Options<TModel> = {}) {
    return this.getRepository().existsById(id, query)
  }

  exists(query: Options<TModel>) {
    return this.getRepository().exists(query)
  }

  findOne(query: Options<TModel> = {}): Promise<TModel> {
    return this.getRepository().findOne(query)
  }

  create(data: Partial<TModel>, options: Options<TModel> = {}): Promise<TModel> {
    return this.getRepository()
      .create(data, options)
      .then((model) => {
        if (!options.dontEmit) this.emitCreated(model, options.eventTransaction)
        return model
      })
  }

  upsert(data: Partial<TModel>, options: Options<TModel> = {}): Promise<[TModel, boolean]> {
    return this.getRepository().upsert(data, options)
  }

  createMany(data: Partial<TModel>[], options: Options<TModel> = {}) {
    return queuedAsyncMap(data, (model) => this.create(model, { ...options, dontEmit: true })).then((models) => {
      if (!options.dontEmit) this.emitCreated(models, options.eventTransaction)
      return models
    })
  }

  updateMany(models: TModel[], datas: Partial<TModel>[], options: Options<TModel> = {}) {
    const items = models.map((m, i) => ({
      model: m,
      data: datas[i],
    }))

    return queuedAsyncMap(items, (item) => this.update(item.model, item.data, { ...options, dontEmit: true })).then(
      (models) => {
        if (!options.dontEmit) this.emitUpdated(models, options.eventTransaction)
        return models
      },
    )
  }

  update(
    model: TModel,
    data: Partial<TModel>,
    options: Options<TModel> & { mergeJson?: string[] } = {},
  ): Promise<TModel> {
    return this.getRepository()
      .update(model, data, options)
      .then((updatedModel) => {
        if (!options.dontEmit) this.emitUpdated(updatedModel, options.eventTransaction)
        return updatedModel
      })
  }

  async bulkCreate(data: Partial<TModel>[], options: Options<TModel> = {}) {
    return this.getRepository()
      .bulkCreate(data, options)
      .then((models) => {
        if (!options.dontEmit) this.emitCreated(models, options.eventTransaction)
        return models
      })
  }

  async bulkUpdate(data: Partial<TModel>, options: Options<TModel> = {}) {
    return this.getRepository()
      .bulkUpdate(data, { ...options, returning: true })
      .then((models) => {
        if (!options.dontEmit) this.emitUpdated(models, options.eventTransaction)
        return models
      })
  }

  async bulkDestroy(options: Options<TModel> = {}) {
    options.raw = true
    const models = await iteratePaginated(
      ({ page }) =>
        this.findManyPaginated({
          page,
          perPage: 500,
          ...options,
        }),
      async (model) => model,
      100,
      true,
    )

    return this.getRepository()
      .bulkDestroy(options)
      .then(() => {
        if (!options.dontEmit) this.emitDestroyed(models, options.eventTransaction)
        return models
      })
  }

  updateById(
    id: string,
    data: Partial<TModel> = {},
    options: Options<TModel> & { mergeJson?: string[] } = {},
  ): Promise<Partial<TModel>> {
    return this.getRepository()
      .findById(id, options)
      .then((model) => this.update(model, data, options))
  }

  destroy(model: TModel, options: Options<TModel> = {}) {
    return this.getRepository()
      .destroy(model, options)
      .then(() => {
        if (!options.dontEmit) this.emitDestroyed(model, options.eventTransaction)
        return model
      })
  }

  destroyMany(query: Options<TModel>, options: Options<TModel> = {}) {
    return this.nestedTransaction(
      (transaction) =>
        this.findMany({ ...query, transaction: options.transaction })
          .then((models) =>
            queuedAsyncMap(models, (model) => this.destroy(model, { transaction, ...options, dontEmit: true })),
          )
          .then((models) => {
            if (!options.dontEmit) this.emitDestroyed(models, options.eventTransaction)
            return models
          }),
      options.transaction,
    )
  }

  destroyById(id, options: Options<TModel> = {}) {
    return this.getRepository()
      .findById(id, options)
      .then((model) => this.destroy(model, options))
  }
}
