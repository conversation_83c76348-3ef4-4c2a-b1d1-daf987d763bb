import moment from 'moment'
import { stringify } from 'csv-stringify'
import BaseResource from './BaseResource'
import userResource from './userResource'
import absenceRepository from '../dbSequelize/repositories/absenceRepository'
import { prepareInclude } from '../dbSequelize/repositories/BaseSequelizeRepository'
import { AbsenceInstance } from '../dbSequelize/models/Absence'
import formatDate from '../utils/date/formatDate'
import iteratePaginated from '../utils/iteratePaginated'
import hasher from '../utils/crypt/hasher'

export class AbsenceResource extends BaseResource<AbsenceInstance> {
  constructor() {
    super(absenceRepository)
  }

  showFiltered = async (data) => {
    const { userId, reason, createdAt, endedAt, page, perPage, where } = data

    const response = await this.findManyPaginated({
      where: {
        accountId: where.accountId,
        ...(userId && { userId }),
        ...(reason && { reason }),
        ...(createdAt &&
          endedAt && {
            createdAt: {
              $gte: moment(createdAt),
              $lt: moment(endedAt),
            },
          }),
      },
      include: ['user'],
      page,
      perPage,
    })

    return response
  }

  unlock = async (params) => {
    const absence = params.absence

    const user = params.user
    const password = absence?.password
    const reason = absence?.reason

    if (!absence.id) {
      const requestAbsence = await this.findOne({
        where: {
          accountId: absence.accountId,
          userId: user.id,
          endedAt: { $eq: null },
        },
        attributes: ['id'],
      })
      absence.id = requestAbsence.id
    }

    if (user.account.settings?.absence?.reason_required && !reason) {
      return {
        success: false,
        message: 'INVALID_REASON',
      }
    }

    if (user.account.settings?.absence?.password_required) {
      if (!password) {
        return {
          success: false,
          message: 'INVALID_PASSWORD',
        }
      }

      const same = await hasher.compare(password, user.password)
      if (!same) {
        return {
          success: false,
          message: 'INVALID_PASSWORD',
        }
      }
    }

    await this.updateById(absence.id, { reason, endedAt: new Date() })
    await userResource.updateStatus(user.id, 'online', 'web')

    return { success: true }
  }

  async exportCSV(data) {
    const { accountId, user, req, res } = data

    const query = {
      ...req.body,
      where: {
        ...req.body.where,
        accountId,
      },
      include: prepareInclude([...((req.body && req.body.include) || []), 'user']),
    }

    const delimiter = ','
    const language = user?.language || 'pt-BR'
    const columnLabels = []

    switch (language) {
      default:
      case 'pt-BR':
        columnLabels.push(...['Usuário', 'De', 'Até', 'Motivo'])
        break
      case 'en-US':
        columnLabels.push(...['User', 'From', 'To', 'Reason'])
        break
      case 'es':
        columnLabels.push(...['Usuario', 'Del', 'Hasta', 'Razón'])
        break
    }

    const stringifier = stringify({ delimiter })
    stringifier.pipe(res)
    stringifier.write(columnLabels)

    await iteratePaginated(
      ({ page }) =>
        this.findManyPaginated({
          ...query,
          page,
          perPage: 500,
        }),
      async (absence) => {
        const row = [
          absence.user?.name,
          formatDate(absence.createdAt, 'dd/MM/yyyy HH:mm:ss', user.account.settings.timezone),
          formatDate(absence.endedAt, 'dd/MM/yyyy HH:mm:ss', user.account.settings.timezone),
          absence.reason,
        ]
        stringifier.write(row)
      },
      1,
    )
    stringifier.end()
  }
}

export default new AbsenceResource()
