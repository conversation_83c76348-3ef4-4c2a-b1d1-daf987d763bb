import { Container } from 'typedi'
import { omit } from 'lodash'
import BaseResource, { CREATED, DESTROYED, UPDATED } from './BaseResource'
import { CardInstance } from '../dbSequelize/models/Card'
import { CardProductInstance } from '../dbSequelize/models/CardProduct'
import { CardCommentInstance } from '../dbSequelize/models/CardComment'
import cardRepository from '../dbSequelize/repositories/cardRepository'
import cardProductRepository from '../dbSequelize/repositories/cardProductRepository'
import cardCommentRepository from '../dbSequelize/repositories/cardCommentRepository'
import cardMovementResource from './cardMovementResource'
import contactResource from './contactResource'
import pipelineResource from './pipelineResource'
import PipelineService from '../services/pipeline/PipelineService'
import queuedAsyncMap from '../../core/utils/array/queuedAsyncMap'
import sequelize from '../services/db/sequelize'
import cardTransformer from '../../core/transformers/cardTransformer'

const pipelineService = Container.get(PipelineService)

const addTotalProductsQuery = (attributes) => {
  const queryTotalProducts = `(
    SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalValue
    FROM pipeline.card_products AS pcp 
    WHERE pcp."cardId" = "Card".id
  )`
  attributes.push([sequelize.literal(queryTotalProducts), 'totalValue'])
}

export class CardResource extends BaseResource<CardInstance> {
  constructor() {
    super(cardRepository)

    this.events = [CREATED, UPDATED, DESTROYED]
  }

  async findById(id: string): Promise<CardInstance> {
    const include = [
      'stage_status',
      'stage_reason',
      'products',
      'comments',
      {
        model: 'pipeline',
        include: [
          {
            model: 'stages',
            include: ['statuses', 'reasons'],
          },
        ],
        required: true,
      },
      {
        model: 'pipeline_stage',
        include: ['statuses'],
      },
      {
        model: 'contact',
        include: [
          'avatar',
          'tags',
          'service',
          {
            model: 'schedules',
            include: ['department', 'user'],
          },
        ],
        required: true,
      },
      {
        model: 'movements',
        include: [
          {
            model: 'user',
            attributes: ['id', 'name'],
          },
          {
            model: 'from_pipeline',
            attributes: ['id', 'name'],
          },
          {
            model: 'to_pipeline',
            attributes: ['id', 'name'],
          },
          {
            model: 'from_pipeline_stage',
            attributes: ['id', 'name'],
          },
          {
            model: 'to_pipeline_stage',
            attributes: ['id', 'name'],
          },
          {
            model: 'from_stage_status',
            attributes: ['id', 'name'],
          },
          {
            model: 'to_stage_status',
            attributes: ['id', 'name'],
          },
        ],
      },
    ]
    return super.findOne({ where: { id }, include })
  }

  async getFirsStage(pipelineId: string): Promise<string> {
    const pipeline = await pipelineResource.findById(pipelineId)
    return pipeline.stages?.[0]?.id
  }

  async create(data: any, options: any): Promise<CardInstance> {
    const card = await super.create(data, options)
    const movement = {
      ...(card?.dataValues || card),
      userId: data.userId,
      toStageId: data?.pipelineStageId,
    }
    await cardMovementResource.move(card, movement)
    await this.addProducts(card?.id, data?.products)
    const pipeline = await pipelineResource.findById(card?.pipelineId)
    pipelineResource.emit(UPDATED, pipeline)
    return card
  }

  async updateById(id: string, data: any, options: any): Promise<CardInstance> {
    const card = await this.findById(id)
    await cardMovementResource.move(card, data)
    if (data?.products) {
      const idProducts = data?.products?.filter((product) => !product?.new).map((product) => product?.id)
      await this.destroyProducts(card?.id, idProducts)
      await this.addProducts(card?.id, data?.products)
    }

    const updatedCard = super.update(card, { ...data, totalValue: card?.totalValue }, options)

    const pipeline = await pipelineResource.findById(card?.pipelineId)

    pipelineResource.emit(UPDATED, pipeline)

    return updatedCard
  }

  async updateOrder(id: string, data: any): Promise<CardInstance> {
    const card = await cardRepository.findById(id)
    await cardRepository.updateOrder(card?.pipelineId, card?.pipelineStageId, card?.accountId, data?.order, card?.order)

    const pipeline = await pipelineResource.findById(card?.pipelineId)

    pipelineResource.emit(UPDATED, pipeline)

    return super.update(card, { order: data?.order })
  }

  async addProduct(product: CardProductInstance): Promise<CardProductInstance> {
    if (product?.id) {
      return cardProductRepository.updateById(product?.id, { ...omit(product, ['id']) })
    }
    return cardProductRepository.create(product)
  }

  async addProducts(cardId: string, products: CardProductInstance[]): Promise<CardProductInstance> {
    const response = []

    await queuedAsyncMap(
      products || [],
      async (product) => {
        response.push(await this.addProduct({ ...product, cardId }))
      },
      1,
    )

    return response
  }

  async destroyProducts(cardId: string, idProducts: string[]) {
    const products = await cardProductRepository.findMany({
      where: {
        cardId,
        ...(idProducts?.length && {
          id: {
            $notIn: idProducts,
          },
        }),
      },
    })
    await queuedAsyncMap(
      products || [],
      async (product) => {
        await this.destroyProduct(product)
      },
      1,
    )
  }

  async destroyProduct(product: CardProductInstance) {
    return cardProductRepository.destroy(product, {})
  }

  async addComment(comment: CardCommentInstance, userId: string): Promise<CardCommentInstance> {
    if (comment?.id) {
      return cardCommentRepository.updateById(comment?.id, { ...omit(comment, ['id', 'cardId']) })
    }
    return cardCommentRepository.create({ ...comment, userId })
  }

  async destroyComment(commentId: string) {
    const comment = await cardCommentRepository.findById(commentId)
    return cardCommentRepository.destroy(comment, {})
  }

  async findMany(query) {
    const attributes = query?.attributes || []
    addTotalProductsQuery(attributes)

    const response = await super.findMany({
      ...query,
      attributes,
    })

    return queuedAsyncMap(response || [], async (card) => await cardTransformer(card, {}))
  }

  async getCards(query) {
    const attributes = query?.attributes || []
    addTotalProductsQuery(attributes)

    const getOrder = () => {
      const field = query?.order?.[0]?.[0]
      const order = query?.order?.[0]?.[1]
      if (field && order) {
        return [sequelize.literal(`"${field}" ${order}`)]
      }
      return query?.order
    }

    const response = await super.findMany({
      ...query,
      attributes,
      order: getOrder(),
    })

    return response
  }

  async createBulk(payload: any, userId: string): Promise<boolean> {
    const contacts = await contactResource.findMany({
      ...payload.filters,
      ...(payload?.tagId
        ? {
            include: [
              {
                model: 'tags',
                required: true,
                where: { id: payload.tagId },
              },
            ],
          }
        : {}),
      where: { ...payload.filters.where, accountId: payload.accountId },
    })

    queuedAsyncMap(
      contacts,
      async (contact) => {
        const data = {
          ...omit(payload, ['filters']),
          contactId: contact.id,
          userId,
        }
        await pipelineService.attachContactPipeline(data)
      },
      5,
    )

    return true
  }
}

export default new CardResource()
