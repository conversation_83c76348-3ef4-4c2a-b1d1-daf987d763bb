import BaseResource from './BaseResource'
import distributionRepository from '../dbSequelize/repositories/distributionRepository'
import { DistributionInstance } from '../dbSequelize/models/Distribution'
import departmentResource from './departmentResource'
import roleResource from './roleResource'
import reduceArrayModelId from '../utils/array/reduceObjectArrayToPropArray'

export class DistributionResource extends BaseResource<DistributionInstance> {
  constructor() {
    super(distributionRepository)
  }

  // @ts-ignore
  async create(data) {
    const distribution = await super.create(data)
    const { departments = [], roles = [] } = data

    if (roles?.length) {
      await distribution.setRoles(await roleResource.findManyByIds(await reduceArrayModelId(roles, 'id'), {}))
    }

    if (!departments.length) return distribution

    await departmentResource.bulkUpdate(
      {
        distributionId: distribution.id,
      },
      {
        where: {
          id: {
            $in: await reduceArrayModelId(departments, 'id'),
          },
        },
      },
    )
  }

  async update(data, accountId) {
    const { id: distributionId, departments = [], roles = [] } = data
    const distribution = await super.findById(distributionId)
    await distribution.setRoles(await roleResource.findManyByIds(await reduceArrayModelId(roles, 'id'), {}))
    await super.update(distribution, data)

    await departmentResource.bulkUpdate(
      {
        distributionId: null,
      },
      {
        where: {
          accountId,
          distributionId,
        },
      },
    )

    if (!departments?.length) return distribution

    return departmentResource.bulkUpdate(
      {
        distributionId: distribution.id,
      },
      {
        where: {
          accountId,
          id: {
            $in: await reduceArrayModelId(departments, 'id'),
          },
        },
      },
    )
  }

  async destroy(distributionId: string, accountId?: string) {
    await departmentResource.bulkUpdate(
      {
        distributionId: null,
      },
      {
        where: {
          accountId,
          distributionId,
        },
      },
    )

    await distributionRepository.bulkDestroy({
      where: {
        id: distributionId,
        accountId,
      },
    })
  }
}

export default new DistributionResource()
