import webhookRepository from '../dbSequelize/repositories/webhookRepository'
import BaseResource from './BaseResource'
import { WebhookInstance } from '../dbSequelize/models/Webhook'

export class WebhookResource extends BaseResource<WebhookInstance> {
  constructor() {
    super(webhookRepository)
  }

  async create(data, options = {}) {
    let instance = await super.create(data, {
      ...options,
      dontEmit: true,
    })

    if (data.services) {
      await instance.setServices(data.services)
    }

    if (!options.dontEmit) this.emitCreated(instance, options.eventTransaction)

    if (options.include) {
      instance = await super.reload(instance, options)
    }

    return instance
  }

  async update(model, data, options = {}) {
    //Verifica se está ativando webhook inativo para limpar flags de erros residuais
    if (data?.active && !model.active) {
      data.errorAt = null
      data.errorNotifiedAt = null
    }

    let instance = await super.update(model, data, {
      ...options,
      dontEmit: false,
    })

    if (data.services) {
      await instance.setServices(data.services)
    }

    if (!options.dontEmit) this.emitUpdated(instance, options.eventTransaction)

    if (options.include) {
      instance = await super.reload(instance, options)
    }

    return instance
  }
}

export default new WebhookResource()
