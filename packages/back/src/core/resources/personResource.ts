import BaseResource, { Options } from './BaseResource'
import peopleRepository from '../dbSequelize/repositories/personRepository'
import { PersonInstance } from '../dbSequelize/models/Person'
import { ContactInstance } from '../dbSequelize/models/Contact'

export class PersonRepository extends BaseResource<PersonInstance> {
  constructor() {
    super(peopleRepository)
  }

  async create(data, options: Options<PersonInstance> = {}): Promise<PersonInstance> {
    let instance = await super.create(data, {
      ...options,
      dontEmit: true,
    })

    if (data.organizationIds) {
      await instance.setOrganizations(data.organizationIds)
    }

    if (!options.dontEmit) this.emitCreated(instance, options.eventTransaction)

    if (options.include) {
      instance = await super.reload(instance, options)
    }

    return instance
  }

  async update(model: PersonInstance, data, options: Options<PersonInstance> = {}): Promise<PersonInstance> {
    let instance = await super.update(model, data, {
      ...options,
      dontEmit: false,
    })

    if (data.organizationIds) {
      await instance.setOrganizations(data.organizationIds)
    }

    if (!options.dontEmit) this.emitUpdated(instance, options.eventTransaction)

    if (options.include) {
      instance = await super.reload(instance, options)
    }

    return instance
  }

  async getLastContact(id: string): Promise<ContactInstance> {
    return peopleRepository.getLastContact(id)
  }
}

export default new PersonRepository()
