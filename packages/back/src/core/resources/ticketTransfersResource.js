import { uniq, uniqBy } from 'lodash'
import sequelize from '../services/db/sequelize'
import BaseResource from './BaseResource'
// eslint-disable-next-line import/no-cycle
import ticketTransfersRepository from '../dbSequelize/repositories/ticketTransfersRepository'

export class TicketTransfersResource extends BaseResource {
  constructor() {
    super(ticketTransfersRepository)
  }

  async getTicketTransfer(ticketIds) {
    const condition = uniq(ticketIds.filter((id) => id)).join("','")
    const tickets = await sequelize.query(
      `SELECT 
        t.id AS "ticketId",
        tt."toUserId",
        tt."toDepartmentId"
      FROM
        tickets t
      left join ticket_transfers tt
      on tt."ticketId" = t.id
      WHERE
        t.id IN ('${condition}')
      `,
      { nest: true },
    )

    const uniqTickets = uniqBy(tickets, 'ticketId')

    return uniqTickets.map((ticket) => {
      return {
        ticketId: ticket.ticketId,
        transferUserIds: uniq(tickets.filter((t) => t.ticketId === ticket.ticketId).map((t) => t.toUserId)),
        transferDepartmentIds: uniq(tickets.filter((t) => t.ticketId === ticket.ticketId).map((t) => t.toDepartmentId)),
      }
    })
  }

  async getFirstTicketTransfer(ticketId) {
    const ticket = await this.findOne({
      where: {
        ticketId,
        action: 'transferred',
      },
      order: [['createdAt', 'ASC']],
    })

    return ticket
  }
}

export default new TicketTransfersResource()
