import { pick, uniqBy } from 'lodash'
import { stringify } from 'csv-stringify'
import BaseResource, { CREATED, DESTROYED, UPDATED } from './BaseResource'
import { UserInstance } from '../dbSequelize/models/User'
import { PipelineInstance } from '../dbSequelize/models/Pipeline'
import { CardInstance } from '../dbSequelize/models/Card'
import { PipelineStageInstance } from '../dbSequelize/models/PipelineStage'
import { PipelineStageStatusInstance } from '../dbSequelize/models/PipelineStageStatus'
import { PipelineStageReasonInstance } from '../dbSequelize/models/PipelineStageReason'
import { PipelineNotificationsInstance } from '../dbSequelize/models/PipelineNotifications'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import pipelineRepository from '../dbSequelize/repositories/pipelineRepository'
import pipelineStageRepository from '../dbSequelize/repositories/pipelineStageRepository'
import pipelineStageStatusRepository from '../dbSequelize/repositories/pipelineStageStatusRepository'
import pipelineAutomationsRepository from '../dbSequelize/repositories/pipelineAutomationsRepository'
import pipelineNotificationsRepository from '../dbSequelize/repositories/pipelineNotificationsRepository'
import departmentResource from './departmentResource'
import cardResource from './cardResource'
import { Options } from './BaseResource'
import formatDate from '../utils/date/formatDate'
import sequelize, { Op } from 'sequelize'
import { PipelineAutomationsInstance } from '../dbSequelize/models/PipelineAutomations'

export class PipelineResource extends BaseResource<PipelineInstance> {
  constructor() {
    super(pipelineRepository)

    this.events = [CREATED, UPDATED, DESTROYED]
  }

  async validations(data: Partial<PipelineInstance>, id?: string) {
    const otherPipelineWithSameName = await super.findOne({
      where: {
        name: data.name,
        ...(id && { id: { $ne: id } }),
      },
    })

    if (otherPipelineWithSameName) {
      throw new Error(`There is a pipeline with name ${data.name}`)
    }

    if (data?.stages?.length > uniqBy(data?.stages, (stage: PipelineStageInstance) => stage?.name).length) {
      throw new Error(`There are duplicate stages`)
    }

    await queuedAsyncMap(data?.stages ?? [], async (stage: PipelineStageInstance) => {
      if (stage?.statuses?.length > 10) {
        throw new Error(`The stage ${stage?.name} has more than 10 statuses`)
      }

      if (
        stage?.statuses &&
        stage?.statuses?.length > uniqBy(stage?.statuses, (status: PipelineStageStatusInstance) => status.name).length
      ) {
        throw new Error(`The stage ${stage?.name} has duplicate status`)
      }

      if (stage?.reasons?.length > 10) {
        throw new Error(`The stage ${stage?.name} has more than 10 reasons`)
      }

      if (
        stage?.reasons &&
        stage?.reasons?.length > uniqBy(stage?.reasons, (status: PipelineStageReasonInstance) => status.name).length
      ) {
        throw new Error(`The stage ${stage?.name} has duplicate reasons`)
      }
    })
  }

  async create(
    data: Partial<PipelineInstance> & {
      user?: UserInstance
      departmentIds?: string[]
      departments?: Array<{ id: string }>
    },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    return this.nestedTransaction(async (transaction): Promise<PipelineInstance> => {
      await this.validations(data)
      const { user } = data

      const instance = await this.getRepository().create(
        {
          ...data,
          accountId: user?.accountId,
        },
        {
          ...options,
          transaction,
        },
      )

      const departmentIds = data?.departmentIds || (data?.departments || []).map((d) => d.id)
      const departments = await departmentResource.findManyByIds(departmentIds, {})
      if (departments) {
        await instance.setDepartments(departments, { transaction })
      }

      await queuedAsyncMap(data?.stages ?? [], async (stage: PipelineStageInstance) => {
        await pipelineStageRepository.create(
          {
            ...pick(stage, ['name', 'position', 'statuses', 'reasons']),
            pipelineId: instance.id,
            accountId: data.accountId,
          },
          { transaction },
        )
      })

      if (!data?.stages) {
        const getStep = () => {
          switch (user?.language) {
            default:
            case 'pt-BR':
              return 'Etapa'
            case 'en-US':
              return 'Step'
            case 'es':
              return 'Paso'
          }
        }

        const getFinalStep = () => {
          switch (user?.language) {
            default:
            case 'pt-BR':
              return 'Finalizados'
            case 'en-US':
              return 'Finalized'
            case 'es':
              return 'Finalizado'
          }
        }

        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getStep()} 1`,
            position: 1,
            accountId: user?.accountId,
          },
          { transaction },
        )
        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getStep()} 2`,
            position: 2,
            accountId: user?.accountId,
          },
          { transaction },
        )
        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getFinalStep()}`,
            position: 10,
            accountId: user?.accountId,
            statuses: [
              {
                name: 'STATUS_WON',
                position: 1,
              },
              {
                name: 'STATUS_LOOSE',
                position: 2,
              },
            ],
          },
          { transaction },
        )
      }

      return instance
    }, options.transaction).then(async (instance) => {
      const pipeline = await this.findById(instance?.id)
      if (!options.dontEmit) {
        this.emitCreated(pipeline)
      }

      return pipeline
    })
  }

  async update(
    id: string,
    data: Partial<PipelineInstance> & {
      userId?: string
      departmentIds?: string[]
      departments?: Array<{ id: string }>
      accountId?: string
    },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    await this.validations(data, id)

    return this.nestedTransaction(async (transaction): Promise<PipelineInstance> => {
      const instance = (await this.getRepository().updateById(id, data, {
        ...options,
        transaction,
      })) as PipelineInstance

      const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)
      const departments = await departmentResource.findManyByIds(departmentIds, {})
      if (departments) {
        await instance.setDepartments(departments, { transaction })
      }

      await queuedAsyncMap(data?.stages || [], async (stage: PipelineStageInstance) => {
        if (stage?.new) {
          return pipelineStageRepository.create(
            { ...pick(stage, ['name', 'position']), pipelineId: instance.id, accountId: data.accountId },
            { transaction },
          )
        }

        const oldStage = await pipelineStageRepository.findById(stage.id, { transaction })
        return pipelineStageRepository.update(oldStage, stage, { transaction })
      })

      let stagesToRemove: PipelineStageInstance[]
      const idsStages = (data?.stages?.filter((stage) => !stage?.new) || [])?.map((stage) => stage.id)

      if (idsStages.length) {
        stagesToRemove = await pipelineStageRepository.findMany({
          where: { pipelineId: id, id: { $notIn: idsStages } },
        })
        const cardsToMove = await cardResource.findMany({
          where: { pipelineId: id, pipelineStageId: { $in: stagesToRemove.map((stage) => stage.id) } },
        })

        await queuedAsyncMap(cardsToMove || [], async (card: CardInstance) => {
          await cardResource.updateById(
            card.id,
            { userId: data?.userId, pipelineStageId: idsStages?.[0] },
            { transaction },
          )
        })
      }

      await queuedAsyncMap(stagesToRemove || [], async (stage: PipelineStageInstance) => {
        await pipelineStageRepository.destroy(stage, { transaction })
      })

      return instance
    }, options.transaction).then(async (instance) => {
      const pipeline = await this.findById(instance?.id)
      if (!options.dontEmit) {
        this.emitUpdated(pipeline)
      }

      return pipeline
    })
  }

  async findById(id: string, options?: {}): Promise<PipelineInstance> {
    return super.findOne({
      ...options,
      where: { id },
      include: [
        {
          model: 'stages',
          include: ['statuses', 'reasons'],
          attributes: {
            include: [
              [
                sequelize.literal(`(
                  SELECT COUNT(*) as totalCards
                  FROM pipeline.cards AS card
                  WHERE card."pipelineStageId" = "PipelineStage".id
                  AND card."isArchived" = false
                  AND EXISTS (
                    SELECT 1
                    FROM contacts AS contact
                    WHERE contact.id = card."contactId"
                      AND contact."deletedAt" IS NULL
                  )
                )`),
                'totalCards',
              ],
              [
                sequelize.literal(`(
                  SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalValue
                  FROM pipeline.card_products AS pcp
                  JOIN pipeline.cards AS card
                  ON pcp."cardId" = card.id
                  WHERE card."pipelineStageId" = "PipelineStage".id
                  AND card."isArchived" = false
                )`),
                'totalCardsValue',
              ],
              [
                sequelize.literal(`(
                  SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalWonCardsValue
                  FROM pipeline.card_products AS pcp
                  JOIN pipeline.cards AS card
                  ON pcp."cardId" = card.id
                  WHERE card."pipelineStageId" = "PipelineStage".id
                  AND card."success" = true
                  AND card."isArchived" = false
                )`),
                'totalWonCardsValue',
              ],
              [
                sequelize.literal(`(
                  SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalLostCardsValue
                  FROM pipeline.card_products AS pcp
                  JOIN pipeline.cards AS card
                  ON pcp."cardId" = card.id
                  WHERE card."pipelineStageId" = "PipelineStage".id
                  AND card."success" = false
                  AND card."isArchived" = false
                )`),
                'totalLostCardsValue',
              ],
            ],
          },
          order: [['position', 'ASC']],
        },
      ],
    })
  }

  async export(data) {
    const { id, req, res, user } = data
    const { createdAtInit, createdAtFinal, wonAtInit, wonAtFinal } = req?.body
    const createdAtInitFormated = formatDate(createdAtInit, 'dd/MM/yyyy', user.account.settings.timezone)
    const createdAtFinalFormated = formatDate(createdAtFinal, 'dd/MM/yyyy', user.account.settings.timezone)
    const delimiter = ';'
    const language = user?.language || 'pt-BR'
    const summaryColumnLabels = []
    const columnLabels = []
    const createPeriod = []
    const onlyWon = wonAtInit && wonAtFinal

    switch (language) {
      default:
      case 'pt-BR':
        summaryColumnLabels.push(...['POTENCIAL DE RECEITA', 'RECEITA GANHA', 'CONVERSÃO GERAL'])
        columnLabels.push(
          ...[
            'OPORTUNIDADE',
            'CONEXÃO',
            'CONTATO',
            'ETAPA',
            'STATUS',
            'MOTIVO',
            'DESCRIÇÃO',
            'VALOR TOTAL DA OPORTUNIDADE',
            'NOME DA EMRPESA',
            'SEGMENTO DA EMPRESA',
            'CANAL DE ORIGEM',
            'CAMPANHA DE ORIGEM',
            'DATA DE CRIAÇÃO DA OPORTUNIDADE',
          ],
        )
        createPeriod.push(...[`Período: De ${createdAtInitFormated}, até ${createdAtFinalFormated}`])
        break
      case 'en-US':
        summaryColumnLabels.push(...['POTENTIAL REVENUE', 'REVENUE EARNED', 'OVERALL CONVERSION'])
        columnLabels.push(
          ...[
            'OPPORTUNITY',
            'CONNECTION',
            'CONTACT',
            'STAGE',
            'STATUS',
            'REASON',
            'DESCRIPTION',
            'TOTAL OPPORTUNITY VALUE',
            'COMPANY NAME',
            'COMPANY SEGMENT',
            'SOURCE CHANNEL',
            'SOURCE CAMPAIGN',
            'OPPORTUNITY CREATION DATE',
          ],
        )
        createPeriod.push(...[`Period: From ${createdAtInitFormated}, to ${createdAtFinalFormated}`])
        break
      case 'es':
        summaryColumnLabels.push(...['POTENCIAL DE INGRESOS', 'INGRESOS GANADOS', 'CONVERSIÓN GENERAL'])
        columnLabels.push(
          ...[
            'OPORTUNIDAD',
            'CONEXIÓN',
            'CONTACTO',
            'ETAPA',
            'ESTADO',
            'MOTIVO',
            'DESCRIPCIÓN',
            'VALOR TOTAL DE LA OPORTUNIDAD',
            'NOMBRE DE LA EMPRESA',
            'SEGMENTO DE LA EMPRESA',
            'CANAL DE ORIGEN',
            'CAMPAÑA DE ORIGEN',
            'FECHA DE CREACIÓN DE LA OPORTUNIDAD',
          ],
        )
        createPeriod.push(...[`Período: Desde ${createdAtInitFormated}, hasta ${createdAtFinalFormated}`])
        break
    }

    const pipeline = await super.findOne({
      where: { id },
      attributes: ['name'],
      include: [
        {
          model: 'stages',
          attributes: ['id', 'name'],
          include: [
            {
              model: 'statuses',
              attributes: ['id', 'name'],
            },
            {
              where: {
                createdAt: {
                  [Op.gte]: new Date(createdAtInit),
                  [Op.lte]: new Date(createdAtFinal),
                },
              },
              model: 'cards',
              attributes: [
                'id',
                'description',
                'originChannel',
                'originCampaign',
                'success',
                'organization',
                'organizationSegment',
                'createdAt',
              ],
              include: [
                {
                  model: 'stage_reason',
                  attributes: ['name'],
                },
                {
                  where: {
                    createdAt: onlyWon
                      ? {
                          [Op.gte]: new Date(wonAtInit),
                          [Op.lte]: new Date(wonAtFinal),
                        }
                      : {
                          [Op.gte]: new Date(createdAtInit),
                          [Op.lte]: new Date(createdAtFinal),
                        },
                  },
                  order: [['createdAt', 'DESC']],
                  limit: 1,
                  model: 'movements',
                  attributes: ['toPipelineStageId', 'toStageStatusId', 'createdAt'],
                  include: [
                    'to_pipeline_stage',
                    {
                      model: 'to_stage_status',
                      attributes: ['name'],
                      ...(onlyWon && {
                        where: { name: 'STATUS_WON' },
                      }),
                    },
                  ],
                },
                {
                  model: 'products',
                  attributes: ['value', 'ammount'],
                },
                {
                  model: 'stage_status',
                  attributes: ['name'],
                  where: {
                    ...(onlyWon && {
                      name: 'STATUS_WON',
                    }),
                  },
                },
                {
                  model: 'contact',
                  attributes: ['name', 'data'],
                  include: [
                    {
                      model: 'service',
                      attributes: ['name'],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    })
    const potentialRevenue = pipeline?.stages?.reduce((sum, stage) => {
      return (
        sum +
        stage?.cards.reduce((sum1, card) => {
          return (
            sum1 +
            (['STATUS_WON', 'STATUS_LOOSE'].includes(card?.stage_status?.name)
              ? 0
              : card?.products?.reduce((sum2, product) => {
                  return sum2 + (product.ammount ?? 0) * (product.value ?? 0)
                }, 0))
          )
        }, 0)
      )
    }, 0)
    const totalPotentialRevenue = pipeline?.stages?.reduce((sum, stage) => {
      return (
        sum +
        stage?.cards.reduce((sum1, card) => {
          return (
            sum1 +
            card?.products?.reduce((sum2, product) => {
              return sum2 + (product.ammount ?? 0) * (product.value ?? 0)
            }, 0)
          )
        }, 0)
      )
    }, 0)
    const revenue = pipeline?.stages?.reduce((sum, stage) => {
      return (
        sum +
        stage.cards
          ?.filter((card) => card.success)
          .reduce((sum1, sucessCard) => {
            return (
              sum1 +
              sucessCard.products.reduce((sum2, product) => {
                return sum2 + (product.ammount ?? 0) * (product.value ?? 0)
              }, 0)
            )
          }, 0)
      )
    }, 0)
    const conversion = `${
      totalPotentialRevenue === 0 ? '' : ((revenue / totalPotentialRevenue) * 100).toFixed(2).concat('%')
    }`

    const stringifier = stringify({ delimiter })
    stringifier.pipe(res)

    stringifier.write([pipeline?.name])
    stringifier.write(createPeriod)
    stringifier.write(summaryColumnLabels)
    stringifier.write([
      potentialRevenue.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }),
      revenue.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }),
      conversion,
    ])

    stringifier.write([pipeline?.name])
    stringifier.write(columnLabels)
    pipeline?.stages?.forEach((stage) => {
      stage?.cards?.forEach((card) => {
        const number = card?.contact?.data?.number
        const email = card?.contact?.data?.email
        const row = [
          card?.contact?.name,
          card?.contact?.service?.name,
          `${number ? number : ''}${number && email ? ', ' : ''}${email ? email : ''}`,
          card?.movements?.[card?.movements?.length - 1]?.to_pipeline_stage?.name,
          stage?.statuses?.find((status) => {
            return (
              status.id ===
              card.movements?.reduce((mostRecent, current) => {
                return current.createdAt > mostRecent.createdAt ? current : mostRecent
              })?.toStageStatusId
            )
          })?.name,
          card?.stage_reason?.name,
          card?.description?.split('\n').join('|||'),
          card?.products
            ?.reduce((sum, product) => sum + product.ammount * product.value, 0)
            .toLocaleString('pt-BR', {
              style: 'currency',
              currency: 'BRL',
            }),
          card?.organization,
          card?.organizationSegment,
          card?.originChannel,
          card?.originCampaign,
          formatDate(card?.createdAt, 'dd/MM/yyyy HH:mm:ss', user.account.settings.timezone),
        ]
        stringifier.write(row)
      })
    })

    stringifier.end()
  }

  async createAutomation(data: PipelineAutomationsInstance): Promise<PipelineAutomationsInstance> {
    if (!data || !data.pipelineId) {
      throw new Error('Automation data is invalid')
    }
    const automation = await pipelineAutomationsRepository.create({ ...data })
    const pipeline = await this.findById(automation?.pipelineId)
    this.emit(UPDATED, pipeline)
    return automation
  }

  async updateAutomation(id: string, data: PipelineAutomationsInstance): Promise<PipelineAutomationsInstance> {
    let automation = await pipelineAutomationsRepository.findById(id)
    if (!automation) {
      throw new Error('Automation not found')
    }
    automation = await pipelineAutomationsRepository.update(automation, data)
    const pipeline = await this.findById(automation?.pipelineId)
    this.emit(UPDATED, pipeline)
    return automation
  }

  async getAutomation(pipelineId: string): Promise<PipelineAutomationsInstance> {
    const automation = await pipelineAutomationsRepository.findOne({ where: { pipelineId: pipelineId } })
    return automation || []
  }

  async destroyAutomation(id: string) {
    const automation = await pipelineAutomationsRepository.findById(id, {})
    if (!automation) {
      throw new Error('Automations not found')
    }
    await pipelineAutomationsRepository.destroy(automation, {})
  }

  async createNotification(notification: {
    pipelineId: string
    pipelineAutomationId: string
  }): Promise<PipelineNotificationsInstance> {
    return pipelineNotificationsRepository.create(notification)
  }

  async createStage(
    data: Partial<PipelineStageInstance> & { pipelineId: string },
    user: UserInstance,
  ): Promise<PipelineStageInstance | string> {
    let pipeline = await this.findById(data?.pipelineId)
    if (pipeline.stages.length >= 10) {
      return 'STAGES_LIMIT'
    }
    const stageFound = pipeline?.stages.find((stage) => stage.name === data?.name)
    if (stageFound) {
      return 'STAGE_ALREADY_EXIST'
    }
    const stage = await pipelineStageRepository.create({ ...data, accountId: user?.accountId })

    // refresh pipeline
    pipeline = await this.findById(pipeline.id)
    this.emitUpdated(pipeline)

    return stage
  }

  async updateStage(id: string, data: Partial<PipelineStageInstance>): Promise<PipelineStageInstance | string> {
    let pipeline = await this.findById(data?.pipelineId)
    const stageFound = pipeline?.stages.find((stage) => stage.name === data?.name && stage.id !== data?.id)
    if (stageFound) {
      return 'STAGE_ALREADY_EXIST'
    }
    const stage = await pipelineStageRepository.findById(id)

    const updatedStage = await pipelineStageRepository.update(stage, data)

    // refresh pipeline
    pipeline = await this.findById(pipeline.id)
    this.emitUpdated(pipeline)

    return updatedStage
  }

  async destroyStage(id: string, data: { typeAction: string; idNewStage: string }): Promise<string> {
    const stage = await pipelineStageRepository.findById(id, {
      include: [
        {
          model: 'pipeline',
          include: ['stages'],
        },
        'cards',
      ],
    })

    if (stage?.pipeline?.stages?.length === 2) {
      return 'PIPELINES_MIN_STAGES'
    }

    if (data?.typeAction == 'move') {
      await cardResource.bulkUpdate(
        {
          pipelineStageId: data?.idNewStage,
          statusId: null,
          success: null,
          finishedAt: null,
          reasonId: null,
        },
        {
          where: {
            pipelineStageId: id,
          },
        },
      )
    } else {
      const firstStage = stage?.pipeline?.stages?.find((s: PipelineStageInstance) => s?.position === 1)
      await cardResource.bulkUpdate(
        {
          pipelineStageId: firstStage?.id,
          isArchived: true,
          archivedAt: new Date(),
        },
        {
          where: {
            pipelineStageId: id,
          },
        },
      )
    }

    await pipelineStageRepository.destroy(stage, {})

    this.emitUpdated(stage?.pipeline)

    return 'MESSAGE_STAGE_DELETED'
  }

  async getStatuses(pipelineId: string): Promise<PipelineStageStatusInstance[]> {
    return pipelineStageStatusRepository.findMany({
      attributes: ['name'],
      group: ['name'],
      where: { pipelineId },
    })
  }

  async archive(
    id: string,
    data: { archive: boolean },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    const instance = await this.findById(id)

    const archivedAt = data.archive ? new Date() : null

    await super.update(
      instance,
      { archivedAt },
      {
        ...options,
      },
    )

    return instance
  }

  async findManyWithTotals(query?: Options<PipelineInstance>) {
    let cardDateFilter = {}

    if (Array.isArray(query.where?.$and)) {
      query.where.$and = query.where.$and.filter((cond) => {
        if (cond.createdAt) {
          cardDateFilter = { ...cardDateFilter, ...cond.createdAt }
          return false
        }
        return true
      })
      if (query.where.$and.length === 0) delete query.where.$and
    }

    query.include = [
      {
        model: 'stages',
        required: true,
        include: [
          {
            model: 'cards',
            required: true,
            where: {
              ...(Object.keys(cardDateFilter).length ? { createdAt: cardDateFilter } : {}),
              isArchived: false,
            },
            attributes: [],
          },
        ],
      },
    ]

    const pipelines = await super.findMany(query)
    const response = []

    await queuedAsyncMap(pipelines ?? [], async (pipeline: PipelineInstance) => {
      response.push({ ...pipeline?.dataValues, totals: await pipelineRepository.getTotals(pipeline, cardDateFilter) })
    })

    return { data: response }
  }
}

export default new PipelineResource()
