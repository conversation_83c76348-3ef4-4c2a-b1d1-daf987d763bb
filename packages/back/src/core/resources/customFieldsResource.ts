import BaseResource, { Options } from './BaseResource'
import customFieldsRepository from '../dbSequelize/repositories/customFieldsRepository'
import { CustomFieldInstance } from '../dbSequelize/models/CustomField'
import customFieldValuesResource from './customFieldValuesResource'

export class CustomFieldsResource extends BaseResource<CustomFieldInstance> {
  constructor() {
    super(customFieldsRepository)
  }

  async destroy(model: CustomFieldInstance, options: Options<CustomFieldInstance> = {}) {
    await customFieldValuesResource.getRepository().destroyMany({ where: { customFieldId: model.id } }, {})
    return super.destroy(model, options)
  }
}

export default new CustomFieldsResource()
