import generatePdf from '../services/pdf'
import BaseResource from './BaseResource'
import accountResource from './accountResource'
import termRepository from '../dbSequelize/repositories/termRepository'
import { TermInstance } from '../dbSequelize/models/Term'
import { UserInstance } from '../dbSequelize/models/User'
import AgnusApi from '../services/agnus/AgnusApi'
import { getStorage } from '../services/storage'
import S3Storage from '../services/storage/S3Storage'
import Storage from '../services/storage/Storage'
import { sendAgreedTerms } from '../services/email'
import config from '../config'
import OracleStorage from '../services/storage/OracleStorage'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

export class TermResource extends BaseResource<TermInstance> {
  protected agnusApi: AgnusApi

  protected storage: Storage<S3Storage | OracleStorage>

  constructor() {
    super(termRepository)
    this.agnusApi = new AgnusApi()
    this.storage = getStorage()
  }

  async agree(user: UserInstance, termsIds: Array<string>): Promise<any> {
    const agnusTerms = await this.findManyByIds(termsIds, {
      where: { accountId: user.accountId },
    })
    const agreedTerms = await this.agnusApi.agree(
      user?.account?.agnusSignatureKey,
      agnusTerms.map((term) => term.agnusId),
    )
    await this.bulkUpdate(
      {
        userId: user.id,
        agreementDate: new Date(),
      },
      {
        where: {
          id: { $in: termsIds },
          accountId: user.accountId,
        },
      },
    )
    this.saveTermFile(agnusTerms, user)
    return agreedTerms
  }

  async saveTermFile(terms: Array<any>, user: UserInstance) {
    const urls = []
    await queuedAsyncMap(terms, async (term) => {
      urls.push(await this.uploadTerm(term, user))
    })
    this.sendEmail(user, urls)
  }

  getSignature(user: UserInstance) {
    const language = user?.language

    switch (language) {
      case 'en-US':
        return `My name is ${user.name} and I declare that I have read and accepted the terms of use`
      case 'es':
        return `Mi nombre es ${user.name} y declaro haber leído y aceptado las condiciones de uso.`
      case 'pt-BR':
      default:
        return `Eu, ${user.name}, declaro que li e aceitei o termo de uso`
    }
  }

  async uploadTerm(term: any, user: UserInstance): Promise<string> {
    const baseUrl = `terms/${user.accountId}`
    const footer = `<br /><br /><p><span>${this.getSignature(user)}</span></p>`
    const pdfBuffer = await generatePdf(term.text['pt-BR'] + footer, '', '')
    const file = await this.storage.write(`${baseUrl}/${term.id}.pdf`, pdfBuffer, {})

    if (!file.Location) {
      throw new Error('Storage upload error')
    }

    const fileUrl = `${config('publicUrl')}/terms/download/${user.accountId}/${term.id}`

    return `<a target="_blank" href='${fileUrl}'>${term.title['pt-BR']}</a>`
  }

  async sendEmail(user: UserInstance, urls: Array<string>) {
    const emailMessage = {
      to: { email: user.email },
      termsUrls: urls.join('<br />'),
    }

    await sendAgreedTerms(emailMessage)
  }

  async downloadTerm(accountId: string, termId: string): Promise<{ fileBuffer: AWS.S3.Body; fileName: string }> {
    const fileUrl = `terms/${accountId}/${termId}.pdf`
    const fileContents = await this.storage.read(fileUrl, {}, {})
    const term = await this.findById(termId)
    return { fileBuffer: fileContents.Body, fileName: `${term.title['pt-BR']}.pdf` }
  }

  async createOrUpdateTerm(term: TermInstance, signatureId: string) {
    const termToUpdate = await this.findOne({
      where: {
        agreementDate: { $eq: null },
        title: term.title['pt-BR'],
      },
    })
    if (termToUpdate) {
      return this.update(termToUpdate, term)
    }
    const account = await accountResource.findOne({ where: { agnusSignatureKey: signatureId } })
    if (account) {
      return this.create({ ...term, accountId: account?.id })
    }
  }
}

export default new TermResource()
