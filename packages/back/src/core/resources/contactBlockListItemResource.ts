import BaseResource from './BaseResource'
import contactBlockListItemRepository from '../dbSequelize/repositories/contactBlockListItemRepository'
import { ContactBlockListItemInstance } from '../dbSequelize/models/ContactBlockListItem'

export class ContactBlockListResource extends BaseResource<ContactBlockListItemInstance> {
  constructor() {
    super(contactBlockListItemRepository)
  }
}

export default new ContactBlockListResource()
