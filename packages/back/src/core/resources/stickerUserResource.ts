import BaseResource from './BaseResource'
import { StickerUserInstance } from '../dbSequelize/models/StickerUser'
import stickerUserRepository from '../dbSequelize/repositories/stickerUserRepository'

export class StickerUserResource extends BaseResource<StickerUserInstance> {
  constructor() {
    super(stickerUserRepository)
  }

  async createOrUpdate(stickerId: string, userId: string, accountId: string) {
    if (!stickerId || !userId || !accountId) throw new Error('Invalid sticker ID or user ID or account ID')

    const stickerUser = await this.findOne({
      where: {
        stickerId,
        userId,
        accountId,
      },
    })

    if (stickerUser) {
      return this.update(stickerUser, {
        lastSendAt: new Date(),
      })
    }

    return this.create({
      stickerId,
      userId,
      accountId,
      lastSendAt: new Date(),
    })
  }
}

export default new StickerUserResource()
