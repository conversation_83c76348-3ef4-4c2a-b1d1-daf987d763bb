import sortBy from 'lodash/sortBy'
import { format, addMinutes, addDays, subDays, set, differenceInMinutes } from 'date-fns'
import BaseResource, { EventTransaction } from './BaseResource'
import contactResource from './contactResource'
import messageResource from './messageResource'
import accountResource from './accountResource'
import { BlockMessageRuleInstance, WorkPlanDay } from '../dbSequelize/models/BlockMessageRule'
import { ContactInstance } from '../dbSequelize/models/Contact'
import blockMessageRuleRepository from '../dbSequelize/repositories/blockMessageRuleRepository'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import iteratePaginated from '../utils/iteratePaginated'
import config from '../config'

const WEEK_DAYS: { [key: number]: string } = {
  0: 'sun',
  1: 'mon',
  2: 'tue',
  3: 'wed',
  4: 'thu',
  5: 'fri',
  6: 'sat',
}

// Máximo de tentativas, 1 para cada dia da semana
const DEFAULT_MAX_ATTEMPTS = 7

export const BLOCKED_BY_MESSAGE_RULE = 'blocked-by-message-rule'

export type ContactBlockedByMessageRule = {
  contactId: string
} & BlockedByMessageRule

type BlockedByPhoneNumber = {
  isMatch: boolean
} & BlockedByMessageRule

type BlockedByMessageRule = {
  isBlockedToSendMessage: boolean
  nextBlockDate?: Date // Retorna a data do próximo bloqueio
  nextUnblockDate?: DatePeriod // Retorna um objeto com o período do próximo desbloqueio se isBlockedToSendMessage for true, caso contrário retorna null
  lastUnblockDate?: DatePeriod // Retorna um objeto com o período do último desbloqueio se isBlockedToSendMessage for true, caso contrário retorna null
}

type DatePeriod = {
  start: string
  end: string
}

export type EmitBlockedByMessageRuleData = {
  accountId: string
} & ContactBlockedByMessageRule

export class BlockMessageRuleResource extends BaseResource<BlockMessageRuleInstance> {
  constructor() {
    super(blockMessageRuleRepository)
    this.events = [...super.getEvents(), BLOCKED_BY_MESSAGE_RULE]
  }

  emitBlockedByMessageRule(
    data: EmitBlockedByMessageRuleData | EmitBlockedByMessageRuleData[],
    evenTransaction?: EventTransaction,
  ) {
    return this.emit(BLOCKED_BY_MESSAGE_RULE, data, evenTransaction)
  }

  onBlockedByMessageRule(listener) {
    return this.on(BLOCKED_BY_MESSAGE_RULE, listener)
  }

  findAllBlockMessageRules(accountId: string) {
    return this.findMany({
      where: {
        accountId,
      },
      order: [['priority', 'ASC']],
    })
  }

  async contactBlockedByMessageRule(contactId: string): Promise<ContactBlockedByMessageRule> {
    if (!contactId) {
      return { contactId, isBlockedToSendMessage: false }
    }

    // Busca o contato com a respectiva conta e conexão
    const contact = await contactResource.findOne({
      where: {
        id: contactId,
      },
      include: ['account', 'service'],
    })

    // Busca por todas as regras de bloqueio da conta
    const blockMessageRules = await this.findAllBlockMessageRules(contact.accountId)

    const result = this.contactBlockMessageRules(contact, blockMessageRules)
    return result
  }

  contactBlockMessageRules(
    contact: ContactInstance,
    blockMessageRules: BlockMessageRuleInstance[],
  ): ContactBlockedByMessageRule {
    // As feature flags precisam estar habilitadas
    if (
      !contact ||
      !contact?.account?.settings?.flags?.['use-block-message-rules-by-service'] ||
      !contact?.service?.settings?.blockMessageRulesActive
    ) {
      return { contactId: contact?.id, isBlockedToSendMessage: false }
    }

    const unblockUntilAt = contact.data?.blockMessageRules?.unblockUntilAt
    if (unblockUntilAt && new Date(unblockUntilAt) > new Date()) {
      return { contactId: contact.id, isBlockedToSendMessage: false }
    }

    // Verifica regra por regra, seguindo a prioridade
    for (const blockMessageRule of blockMessageRules) {
      if (blockMessageRule.type === 'phone-number') {
        const result = this.blockedByPhoneNumber(contact, blockMessageRule)

        if (result?.isMatch) {
          return {
            contactId: contact.id,
            isBlockedToSendMessage: result.isBlockedToSendMessage,
            nextBlockDate: result.nextBlockDate,
            nextUnblockDate: result.nextUnblockDate,
            lastUnblockDate: result.lastUnblockDate,
          }
        }
      }
    }

    // Se nenhuma regra deu match, não existe bloqueio
    return { contactId: contact.id, isBlockedToSendMessage: false }
  }

  blockedByPhoneNumber(contact: ContactInstance, blockMessageRule: BlockMessageRuleInstance): BlockedByPhoneNumber {
    if (!contact || !blockMessageRule) {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    // Se o tipo de regra não é por número de telefone
    if (blockMessageRule.type !== 'phone-number') {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    // Se for grupo, não aplica nenhuma regra por número de telefone
    if (contact.isGroup) {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    // Obter número de telefone (não parece haver unanimidade de onde esse valor é armazenado)
    let phoneNumber = contact?.data?.number || contact?.data?.validNumber || contact?.idFromService

    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    // Retirar o que não é um dígito do número de telefone
    phoneNumber = phoneNumber.replace(/\D/gim, '')

    if (blockMessageRule.data?.conditionType !== 'regex' || typeof blockMessageRule.data?.condition !== 'string') {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    const regex = new RegExp(blockMessageRule.data?.condition)

    if (!regex.test(phoneNumber)) {
      return { isMatch: false, isBlockedToSendMessage: false }
    }

    // A partir dessa linha, isMatch deve ser sempre true, pois passou no regex test

    const timezone = blockMessageRule.data?.options?.timezone || 0

    // Calcula o horário local da regra informada (considerando fuso horário e horário de verão)
    const currentLocalDate = this.calculateCurrentLocalDate(timezone, blockMessageRule.data?.options?.dayLight)

    // Obter dia atual (baseado no padrão do workPlan)
    const currentDay = WEEK_DAYS[currentLocalDate.getDay()]

    const workPlan = blockMessageRule.data?.workPlan || {}

    // Obter workPlan do dia atual
    const currentWorkPlan: WorkPlanDay = workPlan[currentDay]

    if (currentWorkPlan?.length) {
      // Obter o horário atual, no formato de hora:minuto. Exemplo: 08:19
      // O format do date-fns especifica que HH retorna o valor em horas entre 00h até 23h
      // O format do date-fns especifica que mm retorna o valor em minutos entre 00m até 59m
      const currentHour = format(currentLocalDate, 'HH:mm')

      // Se o horário atual está entre o começo e o fim de alguma regra do workPlan
      // Então, está em um horário valido e não está bloqueado
      const findWorkPlan = currentWorkPlan.find((wp) => wp.start <= currentHour && currentHour < wp.end)

      if (findWorkPlan) {
        // Calcular o horário de fim do atendimento atual
        const endFindWorkPlan = this.formatDatePeriod(currentLocalDate, timezone, findWorkPlan.start, findWorkPlan.end)

        // Calcular a próxima hora ou dia disponível para atendimento
        const nextUnblockDate = this.calculateNextUnblock(
          addMinutes(currentLocalDate, 1),
          workPlan,
          timezone,
          DEFAULT_MAX_ATTEMPTS,
        )

        return {
          isMatch: true,
          isBlockedToSendMessage: false,
          nextBlockDate: new Date(endFindWorkPlan.end),
          nextUnblockDate,
        }
      }
    }

    // Não encontrou um horário de atendimento para o dia atual
    // Calcular a próxima hora ou dia disponível para atendimento
    const nextUnblockDate = this.calculateNextUnblock(currentLocalDate, workPlan, timezone, DEFAULT_MAX_ATTEMPTS)

    // Calcular a última hora ou dia disponível para atendimento
    const lastUnblockDate = this.calculateLastUnblock(currentLocalDate, workPlan, timezone, DEFAULT_MAX_ATTEMPTS)

    return { isMatch: true, isBlockedToSendMessage: true, nextUnblockDate, lastUnblockDate }
  }

  calculateCurrentLocalDate(localTimezoneHours: number, localDayLightHours: number): Date {
    const currentDate = new Date()

    const localTimezoneMinutes = typeof localTimezoneHours === 'number' ? localTimezoneHours * 60 : 0
    const localDayLightMinutes = typeof localDayLightHours === 'number' ? localDayLightHours * 60 : 0

    // addMinutes com getTimezoneOffset faz a data ir para o horário UTC
    // addMinutes com localTimezoneMinutes faz a data ir para o horário local informado
    // addMinutes com localDayLightMinutes faz a data considerar o horário de verão
    const offset = currentDate.getTimezoneOffset() + localTimezoneMinutes + localDayLightMinutes

    return addMinutes(currentDate, offset)
  }

  calculateNextUnblock(
    localDate: Date,
    workPlan: BlockMessageRuleInstance['data']['workPlan'],
    timezone: number,
    maxAttempts: number,
  ): DatePeriod {
    // Função recursiva precisa de uma condição para parada
    if (!maxAttempts) return null

    // Obter dia (baseado no padrão do workPlan)
    const currentDay = WEEK_DAYS[localDate.getDay()]

    // Obter workPlan do dia
    const currentWorkPlan: WorkPlanDay = workPlan[currentDay]

    if (currentWorkPlan?.length) {
      // Ordena o workPlan por hora de início
      const sortCurrentWorkPlan = sortBy(currentWorkPlan, ['start'])

      const currentHour = format(localDate, 'HH:mm')

      const findNextUnblock = sortCurrentWorkPlan.find((wp) => currentHour <= wp.start)

      if (findNextUnblock) {
        // Encontrou a hora de desbloqueio para o dia, formata e retorna
        return this.formatDatePeriod(localDate, timezone, findNextUnblock.start, findNextUnblock.end)
      }
    }

    // Não encontrou um horário de atendimento para o dia
    // Define o próximo dia meia-noite, faz a chamada recursiva diminuindo uma tentativa
    const nextLocalMidnightDate = set(addDays(localDate, 1), { hours: 0, minutes: 0 })

    return this.calculateNextUnblock(nextLocalMidnightDate, workPlan, timezone, maxAttempts - 1)
  }

  calculateLastUnblock(
    localDate: Date,
    workPlan: BlockMessageRuleInstance['data']['workPlan'],
    timezone: number,
    maxAttempts: number,
  ): DatePeriod {
    // Função recursiva precisa de uma condição para parada
    if (!maxAttempts) return null

    // Obter dia (baseado no padrão do workPlan)
    const currentDay = WEEK_DAYS[localDate.getDay()]

    // Obter workPlan do dia
    const currentWorkPlan: WorkPlanDay = workPlan[currentDay]

    if (currentWorkPlan?.length) {
      // Ordena o workPlan por hora de fim
      const sortCurrentWorkPlan = sortBy(currentWorkPlan, ['end']).reverse()

      const currentHour = format(localDate, 'HH:mm')

      const findLastUnblock = sortCurrentWorkPlan.find((wp) => currentHour >= wp.end)

      if (findLastUnblock) {
        // Encontrou a hora de desbloqueio para o dia, formata e retorna
        return this.formatDatePeriod(localDate, timezone, findLastUnblock.start, findLastUnblock.end)
      }
    }

    // Não encontrou um horário de atendimento para o dia
    // Define o dia anterior 23h59, faz a chamada recursiva diminuindo uma tentativa
    const lastLocalEndDate = set(subDays(localDate, 1), { hours: 23, minutes: 59 })

    return this.calculateLastUnblock(lastLocalEndDate, workPlan, timezone, maxAttempts - 1)
  }

  formatDatePeriod(localDate: Date, timezone: number, start: string, end: string): DatePeriod {
    const isPositiveTimezone = timezone >= 0
    const formatTimezone = `0${isPositiveTimezone ? timezone : -timezone}`.slice(-2)

    // Formata para o padrão "2024-10-09T12:01:00-05:00"
    return {
      start: `${format(localDate, 'yyyy-MM-dd')}T${start}:00${isPositiveTimezone ? '+' : '-'}${formatTimezone}:00`,
      end: `${format(localDate, 'yyyy-MM-dd')}T${end}:00${isPositiveTimezone ? '+' : '-'}${formatTimezone}:00`,
    }
  }

  async unblockContactUntilNextWorkPlan(contactId: string): Promise<void> {
    if (!contactId) return

    // Busca o contato com a respectiva conta e conexão
    const contact = await contactResource.findOne({
      where: {
        id: contactId,
      },
      include: ['account', 'service'],
    })

    // Busca por todas as regras de bloqueio da conta
    const blockMessageRules = await this.findAllBlockMessageRules(contact.accountId)

    const result = this.contactBlockMessageRules(contact, blockMessageRules)

    if (
      !result?.isBlockedToSendMessage ||
      !result?.nextUnblockDate?.start ||
      !contact?.service?.settings?.unblockByReceiveMessage
    ) {
      // Não realizar o desbloqueio
      return
    }

    // Atualizar o desbloqueio no contato que enviou a mensagem
    await contactResource.updateById(
      contactId,
      {
        data: {
          blockMessageRules: {
            ...contact?.data?.blockMessageRules,
            unblockUntilAt: result?.nextUnblockDate?.start, // Manter o contato desbloqueado só até o início do próximo antedimento
          },
        },
      },
      {
        mergeJson: ['data'],
      },
    )

    // Emitir desbloqueio para o usuário do ticket
    this.emitBlockedByMessageRule({
      accountId: contact?.accountId,
      contactId,
      isBlockedToSendMessage: false,
      nextUnblockDate: null,
      lastUnblockDate: null,
    })

    // Criar a mensagem de desbloqueio
    await messageResource.create({
      type: 'unblock_message_rule',
      timestamp: new Date(),
      accountId: contact.accountId,
      serviceId: contact.serviceId,
      contactId,
      contact,
      // @ts-ignore
      data: {
        startBlockedAt: result?.lastUnblockDate?.end,
        endBlockedAt: result?.nextUnblockDate?.start,
        unblockUntilAt: result?.nextUnblockDate?.end,
      },
    })
  }

  async serviceContactBlockedByMessageRule(): Promise<void> {
    const currentDate = new Date()
    const blockMessageRuleMinutesBeforeToAlert: number = config('blockMessageRuleMinutesBeforeToAlert')

    // Busca as contas e as conexões habilitadas
    const accounts = await accountResource.findMany({
      attributes: ['id', 'settings'],
      where: {
        'settings.flags.use-block-message-rules-by-service': true,
      },
      include: [
        {
          model: 'services',
          attributes: ['id', 'settings', 'accountId'],
          where: {
            'settings.blockMessageRulesActive': true,
            archivedAt: null,
          },
        },
      ],
    })

    await queuedAsyncMap(accounts, async (account) => {
      if (!account.id || !account?.services?.length) return

      // Busca por todas as regras de bloqueio da conta
      const blockMessageRules = await this.findAllBlockMessageRules(account.id)

      // Processa cada uma das conexões da conta
      await queuedAsyncMap(account.services, async (service) => {
        const contacts = await iteratePaginated<ContactInstance, EmitBlockedByMessageRuleData>(
          ({ page }) =>
            contactResource.findManyPaginated({
              page,
              perPage: 100,
              where: {
                currentTicketId: { $ne: null },
                serviceId: service.id,
                accountId: account.id,
              },
            }),
          async (contact) => {
            contact.service = service
            contact.account = account

            const result = this.contactBlockMessageRules(contact, blockMessageRules)

            const contactNewBlockMessageRules: ContactInstance['data']['blockMessageRules'] = {}

            const unblockUntilAt = contact?.data?.blockMessageRules?.unblockUntilAt
            const alertToBlockUntilAt = contact?.data?.blockMessageRules?.alertToBlockUntilAt

            if (unblockUntilAt && new Date(unblockUntilAt) <= currentDate) {
              // Já terminou o desbloqueio do contato
              contactNewBlockMessageRules.unblockUntilAt = null
            }

            if (alertToBlockUntilAt && new Date(alertToBlockUntilAt) <= currentDate) {
              // Já terminou o alerta para o contato
              contactNewBlockMessageRules.alertToBlockUntilAt = null
              contactNewBlockMessageRules.nextUnblockAt = null
              contactNewBlockMessageRules.unreadAlertToBlock = false
            }

            if (
              result.nextUnblockDate &&
              result.nextBlockDate &&
              result.nextBlockDate > currentDate &&
              differenceInMinutes(result.nextBlockDate, currentDate, { roundingMethod: 'ceil' }) <=
                blockMessageRuleMinutesBeforeToAlert &&
              (!alertToBlockUntilAt || new Date(alertToBlockUntilAt) <= currentDate)
            ) {
              // Começou um novo período de alerta
              contactNewBlockMessageRules.alertToBlockUntilAt = result.nextBlockDate.toISOString()
              contactNewBlockMessageRules.nextUnblockAt = result.nextUnblockDate.start
              contactNewBlockMessageRules.unreadAlertToBlock = true
            }

            if (Object.keys(contactNewBlockMessageRules).length) {
              // Update apenas se algum campo foi adicionado no objeto para ser alterado no contato
              await contactResource.update(
                contact,
                {
                  data: {
                    blockMessageRules: {
                      ...contact?.data?.blockMessageRules,
                      ...contactNewBlockMessageRules,
                    },
                  },
                },
                {
                  mergeJson: ['data'],
                },
              )
            }

            return {
              accountId: account.id,
              contactId: contact.id,
              ...result,
            }
          },
          5,
          true,
        )

        // Emitir o resultado dos contatos da respectiva conexão
        this.emitBlockedByMessageRule(contacts)
      })
    })
  }
}

export default new BlockMessageRuleResource()
