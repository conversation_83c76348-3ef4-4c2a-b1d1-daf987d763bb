import { pick } from 'lodash'
import Container from 'typedi'
import BaseResource, { Options } from './BaseResource'
import contactBlockListsControlRepository from '../dbSequelize/repositories/contactBlockListsControlRepository'
import { ContactBlockListsControlInstance } from '../dbSequelize/models/ContactBlockListsControl'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import QueuedPrepareContactBlockOrUnblockJob from '../../microServices/workers/jobs/contactBlockLists/QueuedPrepareContactBlockOrUnblockJob'
import QueueJobsDispatcher from '../services/jobs/queue/QueueJobsDispatcher'

export class ContactBlockListsControlResource extends BaseResource<ContactBlockListsControlInstance> {
  constructor() {
    super(contactBlockListsControlRepository)
  }

  async create(
    data: {
      reason: string
      action: ContactBlockListsControlInstance['action']
      contactBlockListsIds: string[]
      servicesIds: string[]
      userId: string
      accountId: string
      revertExceptions: boolean
    },
    options?: Options<ContactBlockListsControlInstance>,
  ): Promise<any> {
    const { servicesIds, contactBlockListsIds } = data

    const jobsDispatcher = Container.get(QueueJobsDispatcher)

    await queuedAsyncMap(servicesIds, async (serviceId) => {
      await queuedAsyncMap(
        contactBlockListsIds,
        async (contactBlockListId) => {
          const createdContactBlockListControl = await super.create(
            {
              ...pick(data, ['reason', 'revertExceptions', 'action', 'userId', 'accountId']),
              serviceId,
              contactBlockListId,
              status: 'ready',
              updatedCount: 0,
              processCount: 0,
              totalCount: 0,
            },
            options,
          )

          await jobsDispatcher.dispatch<QueuedPrepareContactBlockOrUnblockJob>(
            'queued-prepare-contact-block-or-unblock',
            {
              contactBlockListControlId: createdContactBlockListControl.id,
            },
            {
              hashKey: serviceId,
            },
          )
        },
        1,
      ),
        10
    })
  }
}

export default new ContactBlockListsControlResource()
