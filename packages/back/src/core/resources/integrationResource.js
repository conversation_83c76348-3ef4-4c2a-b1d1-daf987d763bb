import BaseResource from './BaseResource'
import integrationRepository from '../dbSequelize/repositories/integrationRepository'

export class IntegrationResource extends BaseResource {
  constructor() {
    super(integrationRepository)
  }

  UNIQUE_TYPES = ['event_ticket_transfer', 'event_ticket_closed']

  ERROR_TYPE = 'Já existe uma integração desse tipo'

  async create(data, query) {
    if (this.UNIQUE_TYPES.includes(data.type)) {
      const response = await this.thereIsIntegration(data.type, query)
      if (response > 0) {
        return { success: false, message: this.ERROR_TYPE }
      }
    }
    return { success: true, data: await super.create(data, query) }
  }

  async update(model, data, query) {
    this.thereIsIntegration(data.type, query, model.id)
    if (this.UNIQUE_TYPES.includes(data.type)) {
      const response = await this.thereIsIntegration(data.type, query, model.id)
      if (response > 0) {
        return { success: false, message: this.ERROR_TYPE }
      }
    }
    return { success: true, data: await super.update(model, data, query) }
  }

  async thereIsIntegration(type, query, id = undefined) {
    query.where = { ...query.where, type }
    if (id) {
      query.where = { ...query.where, id: { $ne: id } }
    }
    return integrationRepository.count(query)
  }
}

export default new IntegrationResource()
