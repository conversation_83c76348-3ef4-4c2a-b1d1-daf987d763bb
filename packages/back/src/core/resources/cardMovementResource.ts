import { omit } from 'lodash'
import BaseResource from './BaseResource'
import { CardMovementInstance } from '../dbSequelize/models/CardMovement'
import { CardInstance } from '../dbSequelize/models/Card'
import cardMovementRepository from '../dbSequelize/repositories/cardMovementRepository'
import pipelineStageRepository from '../dbSequelize/repositories/pipelineStageRepository'
import pipelineStageStatusRepository from '../dbSequelize/repositories/pipelineStageStatusRepository'

export class CardMovementResource extends BaseResource<CardMovementInstance> {
  constructor() {
    super(cardMovementRepository)
  }

  async validate(pipelineId: string, pipelineStageId: string, stageStatusId?: string) {
    const stage = await pipelineStageRepository.findOne({ where: { id: pipelineStageId, pipelineId } })
    if (!stage) {
      throw new Error(`Stage was not found for this pipeline`)
    }
    if (stageStatusId) {
      const stageStatus = await pipelineStageStatusRepository.findOne({
        where: { id: stageStatusId, stageId: stage?.id },
      })
      if (!stageStatus) {
        throw new Error(`Status was not found for this stage`)
      }
    }
  }

  async move(card: CardInstance, newData: any) {
    if (!newData?.pipelineStageId && !newData?.statusId) return

    const lastMovement = await this.findOne({
      where: { cardId: card?.id },
      order: [['createdAt', 'desc']],
    })

    if (
      !lastMovement ||
      (newData?.pipelineId && lastMovement?.toPipelineId !== newData?.pipelineId) ||
      (newData?.pipelineStageId && lastMovement?.toPipelineStageId !== newData?.pipelineStageId) ||
      (newData?.statusId && lastMovement?.toStageStatusId !== newData?.statusId)
    ) {
      const { pipelineId, pipelineStageId, statusId } = newData
      await this.validate(pipelineId || card?.pipelineId, pipelineStageId || lastMovement?.toPipelineStageId, statusId)

      await super.create({
        ...omit(newData, ['id', 'createdAt']),
        cardId: card?.id,
        fromPipelineId: lastMovement?.toPipelineId || card?.pipelineId,
        toPipelineId: newData?.pipelineId || lastMovement?.toPipelineId || card?.pipelineId,
        fromPipelineStageId: lastMovement?.toPipelineStageId || card?.pipelineStageId,
        toPipelineStageId: newData?.pipelineStageId || lastMovement?.toPipelineStageId,
        accountId: lastMovement?.accountId || newData?.accountId || card?.accountId,
        pipelineId: newData?.toPipelineId || lastMovement?.toPipelineId || card?.pipelineId,
        fromStageStatusId: lastMovement?.toStageStatusId,
        toStageStatusId: newData?.statusId || lastMovement?.toStageStatusId,
      })
    }
  }
}

export default new CardMovementResource()
