import driverService from '../services/driverService'

export class GroupResource {
  async getGroups(req, res) {
    try {
      const groups = await driverService.getGroups(req.params.serviceId)
      return res.send(groups)
    } catch (e) {
      reportError(e)
    }
  }

  async getGroupParticipants(req, res) {
    try {
      const groups = await driverService.getGroupParticipants(req.params.serviceId, req.params.contactId)
      return res.send(groups)
    } catch (e) {
      reportError(e)
    }
  }

  async createGroup(req, res) {
    try {
      const { serviceId, name, contacts } = req.body
      const contactsArray = contacts.split(',', 256)
      const group = await driverService.createGroup(serviceId, name, contactsArray)
      return res.send(group)
    } catch (e) {
      reportError(e)
    }
  }

  async addParticipantGroup(req, res) {
    try {
      const { serviceId, idGroup, idParticipant } = req.body
      const group = await driverService.addParticipantGroup(serviceId, idGroup, idParticipant)
      return res.send(group)
    } catch (e) {
      reportError(e)
    }
  }

  async removeParticipantGroup(req, res) {
    try {
      const group = await driverService.removeParticipantGroup(
        req.params.serviceId,
        req.params.id,
        req.params.participantId,
      )
      return res.send(group)
    } catch (e) {
      reportError(e)
    }
  }

  async promoteParticipantAdminGroup(req, res) {
    try {
      const { serviceId, idGroup, idParticipant } = req.body
      const group = await driverService.promoteParticipantAdminGroup(serviceId, idGroup, idParticipant)
      return res.send(group)
    } catch (e) {
      reportError(e)
    }
  }

  async demoteParticipantAdminGroup(req, res) {
    try {
      const group = await driverService.demoteParticipantAdminGroup(
        req.params.serviceId,
        req.params.id,
        req.params.participantId,
      )
      return res.send(group)
    } catch (e) {
      reportError(e)
    }
  }
}

export default new GroupResource()
