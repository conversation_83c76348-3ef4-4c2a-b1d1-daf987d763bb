import { pick, differenceBy, sortBy, omit, isEmpty } from 'lodash'
import { Inject, Service } from 'typedi'
import { QueryTypes, Transaction } from 'sequelize'
import tagRepository from '../dbSequelize/repositories/tagRepository'
import CampaignModel, { CampaignInstance } from '../dbSequelize/models/Campaign'
import campaignRepository from '../dbSequelize/repositories/campaignRepository'
import campaignMessageProgressRepository from '../dbSequelize/repositories/campaignMessageProgressRepository'
import BaseResource, { EventTransaction } from './BaseResource'
import campaignMessageResource from './campaignMessageResource'
import fileResource from './fileResource'
import campaignMessageRepository from '../dbSequelize/repositories/campaignMessageRepository'
import messageRepository from '../dbSequelize/repositories/messageRepository'
import ContactService from '../services/contacts/ContactService'
import HttpError from '../utils/error/HttpError'
import sequelize from '../services/db/sequelize'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

@Service()
export class CampaignResource extends BaseResource<CampaignInstance> {
  @Inject()
  contactService: ContactService

  constructor() {
    super(campaignRepository)
  }

  async create(data, options: any = {}) {
    let file = null
    const campaignData = {
      totalContacts: 0,
      ...omit(data, ['messages', 'tags']),
      config: {
        ...data.config,
        nameField: data.nameField,
        numberField: data.numberField,
      },
    }

    const campaign = await super.create(campaignData, { dontEmit: true })

    if (data.fileId) {
      await fileResource.getRepository().updateByIdNoSelect(data.fileId, { attachedId: campaign.id })
    }

    if (data.tagIds && Array.isArray(data.tagIds)) {
      const tags = await tagRepository.findManyByIds(data.tagIds)
      await campaign.setTags(tags)

      // @TODO: criar thumbnail quando necessário
      if (!isEmpty(data.fileTemplate)) {
        file = await fileResource.create(
          {
            name: data.fileTemplate.name,
            base64: data.fileTemplate.base64Url.split('base64,')[1],
            mimetype: data.fileTemplate.mimetype,
            attachedId: null,
            accountId: data.accountId,
          },
          options,
        )
      }
    }

    const messagesData = (
      (data.hsm
        ? [
            {
              extraOptions: {
                parameters: data?.parameters || [],
                hsm: data.hsm,
              },
              hsmId: data.hsm?.id,
              hsmFileId: data.hsmFileId || file?.id,
            },
          ]
        : data.messages) || []
    ).map((message) => ({
      ...message,
      text: data?.text || message?.text,
      accountId: data.accountId,
      campaignId: campaign.id,
    }))

    const messages = await campaignMessageResource.createMany(messagesData, {
      dontEmit: true,
    })

    if (!options.dontEmit) this.emitCreated(campaign)

    if (!(messages || []).length) throw new HttpError(500, 'Unable to create messages!')

    return campaign
  }

  async sendById(id, query = {}, userId = null) {
    const campaign = await this.findById(id, {
      ...query,
    })

    if (campaign.status === 'import_error') {
      return this.internalUpdate(
        campaign,
        {
          status: 'importing_contacts',
        },
        query,
      )
    }

    return this.internalUpdate(
      campaign,
      {
        status: 'processing',
        sendsAt: new Date(),
        finishedAt: null,
        sentById: userId,
      },
      query,
    )
  }

  async duplicate(campaign, options = {}) {
    const campaignData = {
      status: 'ready',
      ...pick(campaign.dataValues, ['title', 'accountId', 'userId', 'serviceId', 'messages']),
    }

    const newCampaign = await this.create(campaignData, {
      dontEmit: true,
    })

    // messages
    const messagesData = (campaign.messages || []).map((m) => ({
      ...m.dataValues,
      campaignId: newCampaign.id,
    }))
    // TODO copy file
    await campaignMessageResource.createMany(messagesData, {
      dontEmit: true,
    })

    // tags
    await newCampaign.setTags(campaign.tags)

    if (!options.dontEmit) this.emitCreated(newCampaign)

    return newCampaign
  }

  async countReadyCampaignsByService(serviceId: string): Promise<number> {
    return this.getRepository().count({
      where: {
        serviceId,
        status: 'ready',
      },
    })
  }

  async duplicateById(id, options = {}) {
    const campaign = await this.findById(id, {
      include: ['messages', 'tags'],
    })

    return this.duplicate(campaign, options)
  }

  async update(model, data, options = {}) {
    const promises = []
    let file = null
    const campaignData = omit(data, ['messages', 'tags'])

    const getStatusCampaign = () => {
      if (model.isScheduled && !data.isScheduled && model.status !== 'importing_contacts') {
        return 'ready'
      }
      return model.status === 'paused' && campaignData.sendsAt ? 'queued' : data.status
    }

    if (!model.isScheduled && data.isScheduled && campaignData.startedAt) {
      campaignData.startedAt = null
    }

    campaignData.sendsAt = campaignData.isScheduled && campaignData.sendsAt ? campaignData.sendsAt : null
    campaignData.status = getStatusCampaign()

    if (!campaignData.mustOpenTicket) {
      campaignData.defaultDepartmentId = null
      campaignData.defaultUserId = null
    }
    let campaign = await campaignRepository.update(model, campaignData, options)

    const messages = await model.getMessages({
      include: ['file', 'hsmFile'],
    })

    const service = await model.getService()

    const messagesToCreate = differenceBy(data.messages, messages, 'id').map((m) => ({
      ...m,
      campaignId: campaign.id,
      accountId: campaign.accountId,
    }))
    const messagesToDelete = differenceBy(messages, data.messages, 'id')
    const sortedDataMessages = sortBy(data.messages, 'id')
    const messagesToUpdate = sortBy(differenceBy(messages, messagesToDelete), 'id')

    // Replacing the tags
    if (data.tagIds && Array.isArray(data.tagIds)) {
      const tags = await tagRepository.findManyByIds(data.tagIds)
      promises.push(campaign.setTags(tags))
    }

    // @TODO: criar thumbnail quando necessário
    if (!isEmpty(data.fileTemplate)) {
      // Whatsapp Business com template de mídia
      if (service.type === 'whatsapp-business' && messages[0]?.hsmFileId) {
        if (data.fileTemplate.fileName !== messages[0].hsmFile?.name) {
          // Replacing the file
          await fileResource.destroyById(messages[0].hsmFileId)

          file = await fileResource.create(
            {
              name: data.fileTemplate.name,
              base64Url: data.fileTemplate.base64Url,
              mimetype: data.fileTemplate.mimetype,
              attachedId: messages[0].id,
              accountId: data.accountId,
              attachedType: 'campaign_message',
            },
            options,
          )
        }
      }
    }

    // Adding the new messages
    if (messagesToCreate.length) {
      promises.push(campaignMessageResource.createMany(messagesToCreate))
    }

    // Deleting messages
    if (messagesToDelete.length) {
      promises.push(
        campaignMessageRepository.bulkDestroy({
          where: { id: { $in: messagesToDelete.map((m) => m.id) } },
        }),
      )
      promises.push(
        fileResource.bulkDestroy({
          where: { id: { $in: messagesToDelete.map((m) => m?.file?.id) } },
        }),
      )
    }

    // Updating messages
    if (messagesToUpdate.length) {
      messagesToUpdate.map((m, index) => {
        if (service.type === 'whatsapp-business') {
          sortedDataMessages[index].extraOptions = {
            ...sortedDataMessages[index]?.extraOptions,
            parameters: data.parameters,
            hsm: data.hsm,
          }
          if (sortedDataMessages[index].hsmId !== data.hsm?.id) {
            sortedDataMessages[index].hsmFileId = file?.id
          } else if (file?.id) {
            sortedDataMessages[index].hsmFileId = file?.id
          }
          if (data.hsm?.id && sortedDataMessages[index].hsmId !== data.hsm.id) {
            sortedDataMessages[index].hsmId = data.hsm.id
          }
        }

        if (service.type === 'whatsapp') {
          const messageData = sortedDataMessages.find((sm) => sm.id === m.id)
          if (messageData && !messageData.file) {
            if (!messageData?.base64Url) {
              if (m.file) {
                promises.push(fileResource.destroyById(m.file.id))
              }
            } else {
              if (messageData?.fileName && m.file?.name !== messageData?.fileName) {
                if (m.file) {
                  promises.push(fileResource.destroyById(m.file.id))
                }

                promises.push(
                  fileResource.create(
                    {
                      name: messageData.fileName,
                      base64Url: messageData.base64Url,
                      mimetype: messageData.mimetype,
                      attachedId: messageData.id,
                      accountId: messageData.accountId,
                      attachedType: 'campaign_message',
                    },
                    options,
                  ),
                )
              }
            }
          }
        }

        promises.push(campaignMessageResource.getRepository().update(m, sortedDataMessages[index]))
      })
    }

    await Promise.all(promises)

    if (!options.dontEmit) this.emitUpdated(campaign)
    if (options.include) campaign = await campaignRepository.reload(campaign, options)

    return campaign
  }

  // TODO remover
  internalUpdate(model: CampaignInstance, data, options = {}) {
    return super.update(model, data, options)
  }

  async getStats(campaign: CampaignInstance, forCampaignList = false): Promise<any> {
    const total = campaign.totalMessagesCount || 0
    const sent = campaign.sentMessagesCount || 0

    if (forCampaignList) {
      return {
        sent,
        viewed: await messageRepository.getAckCountByCampaignId(campaign.id, 3),
      }
    }

    const [
      error = 0,
      sending = 0,
      sentToServer = 0,
      received = 0,
      viewed = 0,
      played = 0,
      totalFiredCount = 0,
      firedFailedCount = 0,
      firedBlockedByMessageRuleCount = 0,
      firedSuccessCount = 0,
      answered = 0,
    ] = await Promise.all([
      messageRepository.getAckCountByCampaignId(campaign.id, 'error'),
      messageRepository.getAckCountByCampaignId(campaign.id, 0),
      messageRepository.getAckCountByCampaignId(campaign.id, 1),
      messageRepository.getAckCountByCampaignId(campaign.id, 2),
      messageRepository.getAckCountByCampaignId(campaign.id, 3),
      messageRepository.getAckCountByCampaignId(campaign.id, 4),
      campaignMessageProgressRepository.getTotalFiredCount(campaign.id),
      campaignMessageProgressRepository.getFiredFailedCount(campaign.id),
      campaignMessageProgressRepository.getFiredBlockedByMessageRuleCount(campaign.id),
      campaignMessageProgressRepository.getFiredSuccessCount(campaign.id),
      campaignMessageProgressRepository.getFiredAnsweredCount(campaign.id),
    ])

    const deprecated = {
      total,
      sent,
      failed: firedFailedCount,
      sending,
      sentToServer,
      received,
      viewed,
      played,
      answered,
      sentAndNotReceived: sentToServer,
    }

    return {
      ...deprecated, // Manter para retrocompatibilidade
      campaignInfo: {
        totalMessagesCount: campaign.totalMessagesCount || 0,
        sentMessagesCount: campaign.sentMessagesCount || 0,
        totalContacts: campaign.totalContacts || 0,
        totalContactsImported: campaign.totalContactsImported || 0,
        totalValidContacts: campaign.totalValidContacts || 0,
        totalFiredCount,
        firedFailedCount,
        firedBlockedByMessageRuleCount,
        firedSuccessCount,
      },
      messageInfo: {
        error,
        sending,
        sentToServer,
        received,
        viewed,
        played,
        answered,
      },
    }
  }

  /**
   * @param id {string}
   * @param options {Object}
   * @returns {Promise<void>}
   */
  async getStatsById(id) {
    const campaign = await this.findById(id)
    return this.getStats(campaign)
  }

  async getContactsPerCampaign(campaignId) {
    const campaign = await this.findById(campaignId, {
      include: ['tags'],
    })

    if (campaign?.totalContacts) return campaign.totalContacts

    if (!campaign?.tags?.length) return 0

    const count = await sequelize
      .query(
        `      
      select count(*) from contacts c inner join contact_tags ct on ct."contactId" = c.id
      inner join tags t on t.id = ct."tagId" 
      where c."serviceId" = :serviceId
      and t.id in (:tagIds)
    `,
        {
          replacements: {
            serviceId: campaign.serviceId,
            tagIds: campaign.tags.map((t) => t.id),
          },
          type: QueryTypes.SELECT,
        },
      )
      .then((r) => r?.[0]?.count)

    return count
  }

  async updateProgress(
    campaign: CampaignInstance,
    options: { dontEmit?: Boolean; transaction?: Transaction; eventTransaction?: EventTransaction } = {},
  ): Promise<CampaignInstance> {
    // @ts-ignore
    const updatedCampaign: CampaignInstance = await sequelize
      .query(
        `
      with subquery as (
        select
          coalesce(count(id),
          0)
        from
          campaign_messages_progress
        where
          "campaignId" = :campaignId
          and "accountId" = :accountId
          and "sentAt" is not null
          and ((failed is true)
            or ("messageId" is not null))
           )
            update
          campaigns
        set
          "sentMessagesCount" = (
          select
            *
          from
            subquery),
          status = case
            when (
            select
              *
            from
              subquery) >= coalesce("totalMessagesCount",
            0) then 'done'
            else status
          end,
          "finishedAt" = case
            when (
            select
              *
            from
              subquery) >= coalesce("totalMessagesCount",
            0) then now()
            else 
            "finishedAt"
          end
        where
          id = :campaignId
        returning  *;
  `,
        {
          replacements: { campaignId: campaign.id, accountId: campaign.accountId },
          mapToModel: true,
          model: CampaignModel,
          transaction: options.transaction,
        },
      )
      .then((r) => r?.[0])

    if (!options.dontEmit) {
      this.emitUpdated(updatedCampaign, options.eventTransaction)
    }

    return updatedCampaign
  }

  async findManyPaginated(query) {
    const response = await super.findManyPaginated(query)
    const data = await queuedAsyncMap(response?.data, async (campaign) => {
      const stats = await this.getStats(campaign, true)
      return { ...(campaign?.dataValues || {}), stats }
    })

    return { ...response, data }
  }

  async findManyPaginatedBase(query) {
    return super.findManyPaginated(query)
  }
}

export default new CampaignResource()
