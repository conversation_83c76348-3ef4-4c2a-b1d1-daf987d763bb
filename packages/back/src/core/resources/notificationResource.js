import notificationRepository from '../dbSequelize/repositories/notificationRepository'
import BaseResource from './BaseResource'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import sequelize from '../../core/services/db/sequelize'

class NotificationResource extends BaseResource {
  constructor() {
    super(notificationRepository)
  }

  async markAllNotifications(accountId, userId) {
    if (accountId && userId) {
      return sequelize.query(`
        update notifications set read = true where "userId" = '${userId}' and "accountId" = '${accountId}';
    `)
    }
  }

  async count(accountId, userId, whereCondition) {
    const results = await sequelize.query(
      `
        SELECT
          COUNT(DISTINCT n.id) as "total",
          COUNT(DISTINCT CASE WHEN nr."readAt" IS NOT NULL THEN n.id END) as "read",
          COUNT(DISTINCT CASE WHEN nr."readAt" IS NULL THEN n.id END) as "unread"
        FROM notifications n
               LEFT JOIN notification_reads nr ON
          n.id = nr."notificationId"
            AND nr."userId" = :userId
        WHERE
          n."accountId" = :accountId
          ${whereCondition ? '' : 'AND n."userId" = :filterUserId'}
          AND n."deletedAt" is null;
      `,
      {
        type: sequelize.QueryTypes.SELECT,
        replacements: {
          accountId,
          userId,
          filterUserId: userId,
        },
      },
    )

    return {
      count: results[0] || {},
    }
  }

  async markSingleNotificationAsRead(id) {
    return this.updateById(id, {
      read: true,
    })
  }

  async markMultipleNotificationsAsRead(ids) {
    return queuedAsyncMap(ids, async (id) =>
      this.updateById(id, {
        read: true,
      }),
    )
  }

  async markSingleNotificationAsUnread(id) {
    return this.updateById(id, {
      read: null,
    })
  }
}

export default new NotificationResource()
