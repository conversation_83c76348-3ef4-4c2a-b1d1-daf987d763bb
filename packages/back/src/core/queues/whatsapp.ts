import taskQueue from '../services/queue/redisTaskQueue'
import config from '../config'
import serviceResource from '../resources/serviceResource'
import { ServiceInstance } from '../dbSequelize/models/Service'

export const getReceivingQueue =
  (serviceId: string) =>
  (contactId: string = 'default') =>
    taskQueue(`whatsapp/${serviceId}/${contactId}/receiving-queue`, {
      concurrency: config('waReceivingQueueConcurrency'),
    })

export const getSendingQueue =
  (serviceId: string) =>
  (contactId: string = 'default', timeout: number = 60_000) =>
    taskQueue(`whatsapp/${serviceId}/${contactId}/sending-queue`, {
      concurrency: config('waSendingQueueConcurrency'),
      timeout,
    })

export const getDownloadMediaQueue =
  (serviceId: string) =>
  (messageId: string = 'default') =>
    taskQueue(`whatsapp/${serviceId}/${messageId}/download-media-queue`, {
      concurrency: 1,
    })

export const getUpdateServiceQueue = (serviceId: string) => taskQueue(`whatsapp/${serviceId}/update-service`)

export const getLogoutQueue = (serviceId: string) => taskQueue(`whatsapp/${serviceId}/logout`)

export const serviceUpdate = (
  serviceId: string,
  serviceData: (service: ServiceInstance, doNotUpdateSymbol: Symbol) => any,
  options = {},
) => {
  return getUpdateServiceQueue(serviceId).run(async () => {
    let service = await serviceResource.findById(serviceId)

    const doNotUpdateSymbol = Symbol('DO_NOT_UPDATE')
    const data = typeof serviceData === 'function' ? await serviceData(service, doNotUpdateSymbol) : serviceData

    if (data === doNotUpdateSymbol) return service

    service = await serviceResource.update(service, data, options)

    return service
  })
}
