import { Inject, Service } from 'typedi'
import <PERSON> from 'winston'
import Chalk from 'chalk'
import { ecsFields, ecsStringify } from '@elastic/ecs-winston-format'
import safeStableStringify from 'safe-stable-stringify'
import pick from 'lodash/pick'
import Config from '../config/Config'
import getCaller from '../../utils/getCaller'
import RequestContextCls from '../cls/RequestContextCls'
import { getLogType, obfuscateLgpdData, createSignature, getHostAttributes, hasLgpdData } from './loggerParamsUtils'
import configValues from '../../configValues'
import { isPlainObject, omit, pickBy } from 'lodash'
import { AxiosError } from 'axios'
import { Tracer, TracerToken } from '../tracer/Tracer'

export type Level = 'debug' | 'info' | 'warn' | 'error' | 'critical'

export type Event = { name?: string; params?: {}; reason?: string }

@Service()
export default class Logger {
  protected logger: Winston.Logger

  @Inject()
  protected requestContextCls: RequestContextCls

  @Inject(TracerToken)
  protected tracer: Tracer

  constructor(@Inject(() => Config) protected config: Config<typeof configValues>) {
    this.logger = this.createLogger()

    // default log level
    this.logger.level = 'debug'

    this.log = this.log.bind(this)
  }

  log(message = '', level: Level = 'debug', splat: any[] = [], options: { event?: Event; caller?: string } = {}) {
    if (typeof level !== 'string') {
      throw new Error('Attribute "level" is not a string.')
    }
    const { event } = options

    const caller = options.caller || getCaller()
    const jobTraces = this.requestContextCls.getContext()?.jobTraces || []
    const context = this.requestContextCls.getContext()
    const { hostname, protocol } = getHostAttributes()

    return this.logger.log({
      message,
      level,
      splat: this.formatedSplat(splat),
      ...pickBy(
        {
          event,
          type: getLogType(event),
          caller, // custom metadata
          jobTraces, // custom metadata
          lgpd: hasLgpdData(context?.requestPayload),
          product: this.config.get('product'),
          account_id: context?.accountId,
          user_id: context?.userId,
          hostname: hostname,
          port: this.config.get('port'),
          protocol: protocol,
          request_method: context.requestMethod,
          request_payload: obfuscateLgpdData(context?.requestPayload),
          request_uri: context.requestUri,
          request_status_code: context.requestStatusCode,
          useragent: context.userAgent,
          source_ip: context.sourceIp,
          ...this.tracer.getCurrentTraceIds(),
        },
        (i) => (Array.isArray(i) ? i.length : i),
      ),
    })
  }

  logEvent(
    name?: string,
    params?: {},
    reason?: string,
    level: Level = 'info',
    message: string = '',
    splat: any[] = [],
    options: any = {},
  ) {
    const caller = getCaller()
    this.log(message, level, splat, { ...options, event: { name, params, ...(reason && { reason }) }, caller })
  }

  protected createLogger() {
    return Winston.createLogger({
      silent: this.config.get('silentLogs'),
      ...(this.config.get('logFormat') === 'ecs'
        ? this.createOptionsForEcsFormat()
        : this.createOptionsForTextFormat()),
    })
  }

  protected createOptionsForEcsFormat() {
    const format = Winston.format.combine(
      Winston.format.splat(),
      ecsFields({ convertReqRes: true, apmIntegration: true, convertErr: true }),
      // @ts-ignore
      Winston.format((params) => {
        const cleanParams = omit(params, ['splat', 'level'])

        const stringfied = safeStableStringify(cleanParams)
        const signature = createSignature(stringfied, this.config.get('logSecretKey'))

        return { ...cleanParams, signature }
      })(),
      ecsStringify(),
    )

    const transports = [new Winston.transports.Console()]

    return { format, transports }
  }

  protected createOptionsForTextFormat() {
    const transports = [
      new Winston.transports.Console({
        format: Winston.format.combine(
          Winston.format.colorize({ all: true }),
          Winston.format.timestamp(),
          Winston.format.splat(),
          Winston.format.printf(({ level, message, timestamp, caller, jobsTrace, event }) => {
            const jobsTraceString = jobsTrace?.length ? [`${jobsTrace.join('->')}`] : ''

            return `${timestamp} [${level}] ${jobsTraceString}[${Chalk.cyan(caller)}]: ${
              message || JSON.stringify(event)
            }`
          }),
        ),
      }),
    ]

    return { transports }
  }

  protected formatedSplat(splat: any[] = []) {
    return splat.map((s) => {
      if (s instanceof AxiosError) {
        const error = new Error(s.message)
        error.stack = s.stack
        // @ts-ignore
        error.axios = {
          config: pick(s.config, ['url', 'method', 'data', 'headers']),
          data: s?.response?.data,
        }
        return error
      }

      if (isPlainObject(s)) {
        return obfuscateLgpdData(s)
      }

      return s
    })
  }
}
