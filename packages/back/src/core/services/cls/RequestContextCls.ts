import { Container, Service } from 'typedi'
import Cls from './Cls'
import { TracerToken } from '../tracer/Tracer'

const CONTEXT_KEY = 'context'
const HEADERS_KEY = 'headers'

export type Context = {
  userId?: string
  userEmail?: string
  userName?: string
  accountId?: string
  traceId?: string
  accessTokenId?: string
  jobTraces?: string[]
  impersonate?: boolean
  client?: 'app' | 'web'
  platform?: string
  requestUri: string
  requestMethod: string
  requestPayload: {}
  requestStatusCode?: number
  userAgent: string
  sourceIp: string
  geo: string
}

export type TraceIds = {
  traceparent?: string
  traceId?: string
  transactionId?: string
  spanId?: string
  parentId?: string
}

@Service()
export default class RequestContextCls extends Cls {
  getContext(): Context {
    return this.get(CONTEXT_KEY, {})
  }

  setContext(context: Partial<Context>, merge = false) {
    if (context?.userId)
      this.getTracer().setUserContext({
        id: context.userId,
        email: context.userEmail,
        username: context.userName,
      })
    if (context?.accountId) this.getTracer().setLabel('accountId', context.accountId)
    if (context?.accessTokenId) this.getTracer().setLabel('accessTokenId', context.accessTokenId)
    if (context?.impersonate) this.getTracer().setLabel('impersonate', context.impersonate)
    if (context?.client) this.getTracer().setLabel('client', context.client)
    if (context?.platform) this.getTracer().setLabel('platform', context.platform)

    return this.setObject(CONTEXT_KEY, context, merge)
  }

  getTraceIds(): TraceIds {
    return this.get(HEADERS_KEY, {})
  }

  setTraceIds(headers: TraceIds, merge = false) {
    return this.setObject(HEADERS_KEY, headers, merge)
  }

  protected setObject<T>(key: string, value: T, merge = false) {
    if (merge) {
      return this.set(key, {
        ...(this.get(key) || {}),
        ...value,
      })
    }

    return this.set(key, value)
  }

  // Cannot use @Inject() because of circular dependency with GenericTracer
  protected getTracer() {
    return Container.get(TracerToken)
  }
}
