import Container, { Service } from 'typedi'
import config from '../../config'
import creditMovementResource from '../../resources/creditMovementResource'
import userResource from '../../resources/userResource'
import notificationResource from '../../resources/notificationResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import accountResource from '../../resources/accountResource'
import Logger from '../../services/logs/Logger'

@Service()
export default class CsatService {
  private readonly csatType = 'limit-reached-csat'

  private readonly logger: Logger = Container.get(Logger)

  async accountIsEnable(accountId: string): Promise<boolean> {
    const account = await accountResource.findById(accountId, { attributes: ['settings', 'plan'] })
    if (!account?.plan?.renewDate) {
      this.logger.log(`There is not a plan renew date for account id ${accountId}`, 'warn')
      return false
    }
    return account?.settings?.flags?.['enable-smart-csat-score'] ? true : false
  }

  async limitReached(accountId: string): Promise<Boolean> {
    const accountEnable = await this.accountIsEnable(accountId)
    if (!config('blockAiConsumption') || !accountEnable) {
      return false
    }
    const credits = await creditMovementResource.balance(accountId, 'csat')
    return credits <= 0 ? true : false
  }

  async limitReachedNotification(accountId: string): Promise<void> {
    const users = await userResource.getUsersAdminNotNotified(accountId, this.csatType, ['id', 'accountId'])
    await queuedAsyncMap(users, async (user) => {
      await notificationResource.create({
        accountId: user.accountId,
        userId: user.id,
        read: false,
        text: '',
        type: this.csatType,
      })
    })
  }
}
