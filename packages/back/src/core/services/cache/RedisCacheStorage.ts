import { Inject, Service } from 'typedi'
import CacheStorage from './CacheStorage'
import RedisClientContainer from '../redis/RedisClientContainer'

@Service()
export default class RedisCacheStorage implements CacheStorage {
  @Inject()
  protected clientContainer: RedisClientContainer

  set<T>(key: string, value: T, ttl?: number) {
    if (ttl) {
      return this.client.set(key, JSON.stringify(value), 'PX', ttl)
    }
    return this.client.set(key, JSON.stringify(value))
  }

  get<T>(key: string): Promise<T> {
    return this.client.get(key).then(JSON.parse)
  }

  getOrSet<T>(key: string, value?: T): Promise<T> {
    return typeof value === 'undefined' ? this.get(key) : this.set(key, value)
  }

  async destroy(key: string): Promise<void> {
    await this.client.del(key)
  }

  getAllKeys(keyPattern: string): Promise<string[]> {
    return this.client.keys(keyPattern)
  }

  getAllByKeys<T>(keys: string[]): Promise<T[]> {
    return this.client.mget(keys).then((items) => items.map((item) => JSON.parse(item)))
  }

  setGet<T>(key: string): Promise<T> {
    return this.client.smembers(key).then((items) => items.map(JSON.parse))
  }

  async setRemove<T>(key: string, item: T): Promise<void> {
    await this.client.srem(key, JSON.stringify(item))
  }

  async setAdd<T>(key: string, item: T, ttl?: number): Promise<void> {
    if (!ttl || ttl < 0) {
      await this.client.sadd(key, JSON.stringify(item))
      return
    }

    const script = `
      if redis.call("EXISTS", KEYS[1]) == 0 then
        redis.call("SADD", KEYS[1], ARGV[1])
        redis.call("PEXPIRE", KEYS[1], tonumber(ARGV[2]))
      else
        redis.call("SADD", KEYS[1], ARGV[1])
      end
    `

    await this.client.eval(script, 1, key, JSON.stringify(item), ttl || 0)
  }

  async setHash<T>(key: string, item: T): Promise<void> {
    await this.client.hset(key, item)
  }

  getHash<T>(key: string): Promise<T> {
    return this.client.hgetall(key)
  }

  getHashField<T>(key: string, field: string): Promise<T> {
    return this.client.hget(key, field)
  }

  isHashFieldExists(key: string, field: string): Promise<boolean> {
    return this.client.hexists(key, field).then((value: number) => !!value)
  }

  async incrementHashFields<T>(key: string, fields: T): Promise<T> {
    const transaction = this.client.multi()

    Object.entries(fields).forEach(([field, value]) => {
      transaction.hincrby(key, field, value)
    })

    transaction.hgetall(key)

    return transaction.exec().then((result) => result.pop()[1])
  }

  async pushListItem<T>(key: string, item: T): Promise<number> {
    return this.client.rpush(key, JSON.stringify(item))
  }

  async getListLength(key: string): Promise<number> {
    return this.client.llen(key)
  }

  async getListRange<T>(key: string, start: number, end: number): Promise<T[]> {
    return this.client.lrange(key, start, end).then((items) => items.map(JSON.parse))
  }

  async trimList(key: string, start: number, end: number): Promise<void> {
    await this.client.ltrim(key, start, end)
  }

  async setListItem<T>(key: string, itemIndex: number, item: T): Promise<void> {
    await this.client.lset(key, itemIndex, JSON.stringify(item))
  }

  get client() {
    return this.clientContainer.getClient()
  }
}
