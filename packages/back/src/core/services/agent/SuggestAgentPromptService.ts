import { OpenAI } from 'openai'
import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import config from '../../config'
import { AgentConfig, VOICE_TONE, LANGUAGE_TYPE, AGENT_FUNCTION } from './utils'

@Service()
export default class SuggestAgentPromptService {
  @Inject()
  public logger: Logger

  async getPrompt(agentConfig: AgentConfig) {
    const apiKey = config('apiKeyOpenIA')

    if (!apiKey) {
      this.logger.log('API key not found', 'error')
      throw new Error('API key not found.')
    }

    if (!agentConfig.companySegment || !agentConfig.companySubject || !agentConfig.companyServices) {
      this.logger.log('Missing required fields in payload', 'error')
      throw new Error(
        `Missing required fields in payload: ${[
          !agentConfig.companySegment ? 'companySegment' : '',
          !agentConfig.companySubject ? 'companySubject' : '',
          !agentConfig.companyServices ? 'companyServices' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const agentFunction = AGENT_FUNCTION[agentConfig.function]
    const voiceTone = VOICE_TONE[agentConfig.voiceTone]
    const languageType = LANGUAGE_TYPE[agentConfig.languageType]

    if (!agentFunction || !voiceTone || !languageType) {
      this.logger.log('Invalid agent configuration', 'error')
      throw new Error(
        `Invalid agent configuration: ${[
          !agentFunction ? 'function' : '',
          !voiceTone ? 'voiceTone' : '',
          !languageType ? 'languageType' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const systemPrompt = `Você é um especialista em assistentes virtuais e chatbots.
      Você deve sugerir um prompt para a criação de um assistente virtual com as configurações fornecidas pelo usuário.
      Retorne o prompt sugerido em texto simples, sem formatação ou marcação.`

    const userPrompt = `Crie um prompt para um assistente virtual com as seguintes configurações:
      - Tom de voz: ${voiceTone}
      - Tipo de linguagem: ${languageType}
      - Função: ${agentFunction}
      - Segmento da empresa: ${agentConfig.companySegment}
      - Assunto da empresa: ${agentConfig.companySubject}
      - Serviços da empresa: ${agentConfig.companyServices}
      O prompt deve ser claro, conciso e fornecer orientações específicas sobre como o assistente deve interagir com os usuários.
      O prompt deve incluir exemplos de perguntas que os usuários podem fazer e como o assistente deve responder a essas perguntas.
      O prompt deve ser adaptado para o tom de voz e o tipo de linguagem especificados.
      O prompt deve orientar o assistente a não fugir do assunto da empresa especificado.`

    try {
      const openai = new OpenAI({
        apiKey: apiKey,
      })

      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
      })

      return response?.choices?.[0]?.message?.content
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error while suggesting an agent prompt.')
    }
  }
}
