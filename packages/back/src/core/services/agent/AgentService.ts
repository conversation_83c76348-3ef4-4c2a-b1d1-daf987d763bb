import Axios from 'axios'
import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import config from '../../config'
import { AgentConfig, AgentPayload, AgentResponse, VOICE_TONE, LANGUAGE_TYPE, AGENT_FUNCTION } from './utils'

const DEFAULT_AGENT_NAME = 'Assistente Virtual'
const DEFAULT_CREATIVITY = 0.5

@Service()
export default class AgentService {
  @Inject()
  public logger: Logger

  private validatePayload(payload: AgentConfig) {
    if (
      !payload.voiceTone ||
      !payload.languageType ||
      !payload.function ||
      !payload.companySegment ||
      !payload.companySubject ||
      !payload.companyServices ||
      !payload.prompt
    ) {
      throw new Error(
        `Missing required fields in payload: ${[
          !payload.voiceTone ? 'voiceTone' : '',
          !payload.languageType ? 'languageType' : '',
          !payload.function ? 'function' : '',
          !payload.companySegment ? 'companySegment' : '',
          !payload.companySubject ? 'companySubject' : '',
          !payload.companyServices ? 'companyServices' : '',
          !payload.prompt ? 'prompt' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    if (!payload.actions || payload.actions.length === 0) {
      throw new Error('Missing required fields in payload: actions')
    }

    if (payload.actions.some(({ action }) => !action.description)) {
      throw new Error('Missing required fields in payload: action.description')
    }
  }

  private formatDescription(prompt: string, services: string, actions: AgentConfig['actions']): string {
    return `${prompt.trim()}

      Serviços prestados pela empresa: ${services.trim()}.

      Ações do robô:
      ${actions
        .map(({ action }, i) => `${action.name.trim() || `Caminho ${i + 1}`}: ${action.description.trim()}`)
        .join('\n')}`.trim()
  }

  async createAgent(payload: AgentConfig, accountId: string): Promise<AgentResponse> {
    if (!accountId) {
      throw new Error('Missing required configuration: accountId')
    }

    const aiUrl = `${config('botAiUrl')}/v1/bot-profiles/`
    const aiToken = config('botAiToken')

    if (!config('botAiUrl') || !aiToken) {
      throw new Error('Missing required configuration: botAiUrl or botAiToken')
    }

    this.validatePayload(payload)

    const agentFunction = AGENT_FUNCTION[payload.function]
    const voiceTone = VOICE_TONE[payload.voiceTone]
    const languageType = LANGUAGE_TYPE[payload.languageType]

    if (!agentFunction || !voiceTone || !languageType) {
      throw new Error(
        `Invalid agent configuration: ${[
          !agentFunction ? 'function' : '',
          !voiceTone ? 'voiceTone' : '',
          !languageType ? 'languageType' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const agentPayload: AgentPayload = {
      nome: DEFAULT_AGENT_NAME,
      empresa: payload.companySubject,
      setor_atuacao: payload.companySegment,
      expertise: agentFunction,
      objetivo: agentFunction,
      comunicacao: `${voiceTone} e ${languageType}`,
      descricao: this.formatDescription(payload.prompt, payload.companyServices, payload.actions),
      criatividade: DEFAULT_CREATIVITY,
      account_id: accountId,
    }

    try {
      const res = await Axios.post<AgentResponse>(aiUrl, agentPayload, {
        headers: {
          Authorization: `Bearer ${aiToken}`,
        },
      })
      return res.data
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when creating agent')
    }
  }

  async updateAgent(agentId: string, payload: AgentConfig, accountId: string): Promise<AgentResponse> {
    if (!agentId || !accountId) {
      throw new Error('Missing required configuration: agentId or accountId')
    }

    const aiUrl = `${config('botAiUrl')}/v1/bot-profiles/${agentId}`
    const aiToken = config('botAiToken')

    if (!config('botAiUrl') || !aiToken) {
      throw new Error('Missing required configuration: botAiUrl or botAiToken')
    }

    this.validatePayload(payload)

    const agentFunction = AGENT_FUNCTION[payload.function]
    const voiceTone = VOICE_TONE[payload.voiceTone]
    const languageType = LANGUAGE_TYPE[payload.languageType]

    if (!agentFunction || !voiceTone || !languageType) {
      throw new Error(
        `Invalid agent configuration: ${[
          !agentFunction ? 'function' : '',
          !voiceTone ? 'voiceTone' : '',
          !languageType ? 'languageType' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const agentPayload: AgentPayload = {
      nome: DEFAULT_AGENT_NAME,
      empresa: payload.companySubject,
      setor_atuacao: payload.companySegment,
      expertise: agentFunction,
      objetivo: agentFunction,
      comunicacao: `${voiceTone} e ${languageType}`,
      descricao: this.formatDescription(payload.prompt, payload.companyServices, payload.actions),
      criatividade: DEFAULT_CREATIVITY,
      account_id: accountId,
    }

    try {
      const res = await Axios.put<AgentResponse>(aiUrl, agentPayload, {
        headers: {
          Authorization: `Bearer ${aiToken}`,
        },
      })
      return res.data
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when updating agent')
    }
  }
}
