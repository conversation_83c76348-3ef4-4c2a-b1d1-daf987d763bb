export interface AgentConfig {
  agentId?: string
  knowledgeBase: string
  voiceTone: string
  languageType: string
  function: string
  companySegment: string
  companySubject: string
  companyServices: string
  prompt: string
  actions: {
    context?: string
    action: { id: string; name: string; description: string }
  }[]
}

export interface AgentPayload {
  nome: string
  empresa: string
  setor_atuacao: string
  expertise: string
  objetivo: string
  comunicacao: string
  descricao: string
  criatividade: number
  account_id: string
}

export interface AgentResponse extends Partial<AgentPayload> {
  id: string
  system_prompt: string
}

export const VOICE_TONE = {
  formal: 'Formal',
  neutral: 'Neutro',
  informal: 'Informal',
}

export const LANGUAGE_TYPE = {
  simple: 'Simples',
  neutral: 'Neutro',
  technical: 'Técnica',
}

export const AGENT_FUNCTION = {
  triage: 'Fazer triagem',
  sales: 'Realizar vendas',
  'answer-questions': 'Responder dúvidas',
  support: 'Dar suporte',
}
