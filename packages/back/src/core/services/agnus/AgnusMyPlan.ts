import Container from 'typedi'
import AgnusApi from './AgnusApi'
import serviceResource from '../../resources/serviceResource'
import userResource from '../../resources/userResource'
import accountResource from '../../resources/accountResource'
import config from '../../config'
import BadRequestHttpError from '../../utils/error/BadRequestHttpError'
import CreditsControlCacheService from '../../services/creditMovement/CreditsControlCacheService'

export default class AgnusMyPlan {
  protected agnusApi = new AgnusApi()

  protected creditsControlCacheService = Container.get(CreditsControlCacheService)

  async getPlanUrl(accountId: string, username: string): Promise<string> {
    const account = await accountResource.findById(accountId)

    return this.agnusApi.getPlanUrl(account.agnusSignatureKey, username)
  }

  async updateAmounts(accountId: string) {
    const account = await accountResource.findById(accountId)

    return this.agnusApi.updateAgnus(account.agnusSignatureKey, await this.getAmounts(accountId))
  }

  isCreditsControlEnabled(accountId: string) {
    return this.creditsControlCacheService.isCreditsControlEnabled(accountId)
  }

  async getPlanUrl_v2(accountId: string, digisacToken: string) {
    if (!accountId || !digisacToken) {
      throw new BadRequestHttpError('Account ID or Digisac token is invalid')
    }

    const account = await accountResource.findById(accountId)

    if (!account || !account.agnusSignatureKey) {
      throw new BadRequestHttpError('Account not found')
    }

    const myPlan = await this.agnusApi.getPlanUrl_v2(account.agnusSignatureKey, await this.getAmounts(accountId))

    if (!myPlan || !myPlan.url || !myPlan.accessToken) {
      throw new BadRequestHttpError('MyPlan URL or access token is invalid')
    }

    const digisacApiUrl = config('publicUrl')
    const url = `${myPlan.url}?myplan_token=${myPlan.accessToken}&digisac_token=${digisacToken}&digisac_api_url=${digisacApiUrl}`

    return { url }
  }

  async updateAmounts_v2(accountId: string) {
    const account = await accountResource.findById(accountId)

    if (!account || !account.agnusSignatureKey) {
      return false
    }

    return this.agnusApi.updateAgnus_v2(account.agnusSignatureKey, await this.getAmounts(accountId))
  }

  protected async getAmounts(accountId: string) {
    const amounts = {}

    const services = await serviceResource.findMany({
      where: {
        accountId,
        archivedAt: { $eq: null },
      },
      attributes: ['type'],
    })

    services.forEach((item) => {
      if (!amounts[item.type]) {
        amounts[item.type] = 0
      }
      amounts[item.type] += 1
    })

    amounts.users = await userResource.count({
      where: {
        accountId,
        archivedAt: { $eq: null },
      },
    })

    return amounts
  }
}
