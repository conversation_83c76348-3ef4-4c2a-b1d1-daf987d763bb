import config from '../../config'
import formatDate from '../../utils/date/formatDate'
import AgnusEmail from '../agnus/AgnusEmail'

export const sendEmail = AgnusEmail.send

export const sendRegisterTrialEmail = (data: {
  name: string
  to: string
  token: string
  email: string
  password: string
  trialText: string
  trialDuration: number
  trialExpiresAt: Date
}) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.token) throw new Error('No user token was provided')
  if (!data.email) throw new Error('No email was provided')
  if (!data.password) throw new Error('No password was provided')

  const url = `${config('frontUrl')}`

  const registerEmailTemplateId = config('registerEmailTemplateIdMap')[config('domain')]
  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      name: data.name,
      email: data.email,
      password: data.password,
      url,
      trialText: data.trialText,
      trialDuration: data.trialDuration || '',
      trialExpiresAt: data.trialExpiresAt ? formatDate(data.trialExpiresAt) : '',
    },
  }

  return AgnusEmail.send(msg)
}

export const sendResetPasswordEmail = (data: {
  to: string
  token: string
  userName: string
  email?: string
  password?: string
}) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.token) throw new Error('No user token was provided')
  if (!data.userName) throw new Error('No userName was provided')

  let url = `${config('frontUrl')}/reset-password/${encodeURIComponent(data.token)}`

  const registerEmailTemplateId = config('sendResetPasswordEmail')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      email: data.email,
      password: data.password,
      url,
    },
  }

  if (!data.to) throw new Error('No receiver email was provided')

  return AgnusEmail.send(msg)
}

export const sendCreatePasswordEmail = (data: {
  to: string
  token: string
  userName: string
  email?: string
  password?: string
}) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.token) throw new Error('No user token was provided')
  if (!data.userName) throw new Error('No userName was provided')

  let url = `${config('frontUrl')}/reset-password/${encodeURIComponent(data.token)}?creating=1`

  const registerEmailTemplateId = config('sendCreatePasswordEmail')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      email: data.email,
      password: data.password,
      url,
    },
  }

  if (!data.to) throw new Error('No receiver email was provided')

  return AgnusEmail.send(msg)
}

export const sendDeactiveAccountEmail = (data: { to: string; expireDate: Date }) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.expireDate) throw new Error('No expireDate was provided')

  const registerEmailTemplateId = config('desactivationEmailTemplateIdMap')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      trialExpiresAt: formatDate(data.expireDate),
      formatDate,
    },
  }

  return AgnusEmail.send(msg)
}

export const sendExpirationNotificationEmail = (data: { to: string; expireDate: Date }) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.expireDate) throw new Error('No expireDate was provided')

  const registerEmailTemplateId = config('expirationEmailTemplateIdMap')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      trialExpiresAt: formatDate(data.expireDate),
    },
  }

  return AgnusEmail.send(msg)
}

export const sendWebhookInactivationNotificationEmail = (data: {
  username: string
  accountUrl: string
  to: string
  webhookName: string
  webhookId: string
  webhookUrl: string
  idle: string
  inactivationAt: string
}) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.accountUrl) throw new Error('No account url was provided')
  if (!data.username) throw new Error('No username was provided')
  if (!data.webhookName) throw new Error('No webhook name was provided')
  if (!data.webhookId) throw new Error('No webhook id was provided')
  if (!data.webhookUrl) throw new Error('No webhook url was provided')
  if (!data.idle) throw new Error('No idle was provided')
  if (!data.inactivationAt) throw new Error('No inactivationAt was provided')

  const invalidWebhooksInactivator = config('invalidWebhooksInactivator')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: invalidWebhooksInactivator,
    service_email_id: serviceEmailId,
    to: {
      name: data.username,
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      webhookName: data.webhookName,
      accountUrl: data.accountUrl,
      name: data.username,
      webhookId: data.webhookId,
      webhookUrl: data.webhookUrl,
      idle: data.idle,
      inactivationAt: data.inactivationAt,
    },
  }

  return AgnusEmail.send(msg)
}

export const sendGracePeriodExtendNotification = (data: { to: string; userName: string }) => {
  if (!data.to) throw new Error('No receiver email was provided')
  if (!data.userName) throw new Error('No userName was provided')

  const registerEmailTemplateId = config('gracePeriodEmailTemplateIdMap')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email: data.to,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      user: data.userName,
      date: formatDate(new Date()),
    },
  }

  return AgnusEmail.send(msg)
}

export const sendPlatformAccessNotification = (data: { days: number | string | Date }) => {
  if (!data.days) throw new Error('No receiver days was provided')

  const registerEmailTemplateId = config('checkPlatformAccess')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      name: 'Comercial',
      email: '<EMAIL>',
      bcc: config('bccAddresses'),
    },
    personalizations: {
      frontUrl: config('frontUrl'),
      days: data.days,
    },
  }

  return AgnusEmail.send(msg)
}

export const sendAgreedTerms = (data: { to: any; termsUrls: string }) => {
  if (!data.to) throw new Error('No email adress was provided')

  if (data.termsUrls.length === 0) throw new Error('No terms was provided')

  const registerEmailTemplateId = config('emailTermsTemplate')

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: data.to,
    personalizations: {
      termsUrls: data.termsUrls,
    },
  }

  return AgnusEmail.send(msg)
}

export const sendEmailToFollowUpUsersInWebchat = (contact) => {
  const { followUpRedirectLink, originAccessURL, contactUnread } = contact.data.webchat
  const email = contact.data.email

  if (!followUpRedirectLink) throw new Error('No url_redirect was provided')
  if (!originAccessURL) throw new Error('No url_site was provided')
  if (!contactUnread) throw new Error('No qtde_mensagens was provided')
  if (!email) throw new Error('No email was provided')

  const registerEmailTemplateId = config('sendEmailToFollowUpUsersInWebchat')[config('domain')]

  const serviceEmailId = config('serviceEmailIds')[config('domain')]

  const msg = {
    template_id: registerEmailTemplateId,
    service_email_id: serviceEmailId,
    to: {
      email,
      bcc: config('bccAddresses'),
    },
    personalizations: {
      url_redirect: followUpRedirectLink,
      url_site: originAccessURL,
      qtde_mensagens: contactUnread,
      hora: new Date().getHours,
      data: new Date(),
    },
  }

  return AgnusEmail.send(msg)
}

export const sendWebChatTicketClosed = (data: {
  to: string
  url_redirect: string
  url_site: string
  qtde_mensagens: number
  time: string
  date: number
}) => {
  const msg = {
    template_id: 'c32299f0-098d-11ec-bd28-9f24ffd00da3',
    service_email_id: 'b0890260-d104-11ea-be62-7d8beb5ce828',
    to: {
      email: data.to,
    },
    personalizations: {
      url_redirect: data.url_redirect,
      url_site: data.url_site,
      qtde_mensagens: data.qtde_mensagens,
      mensagens: '',
      hora: data.time,
      data: data.date,
    },
  }

  return AgnusEmail.send(msg)
}

/**
 * Envia e-mail informado os administradores sobre a desativação do MFA
 *
 * @type {{ to: string, titulo: string, texto: string, texto_final: string, texto_rodape: string}} data
 * @returns
 */
export const sendMFANotification = (data: {
  to: string
  titulo: string
  texto: string
  texto_final: string
  texto_rodape: string
}) => {
  const msg = {
    template_id: config('sendMFANotification')[config('domain')],
    service_email_id: 'b0890260-d104-11ea-be62-7d8beb5ce828',
    to: {
      email: data.to,
    },
    personalizations: {
      titulo: data.titulo,
      texto: data.texto,
      texto_final: data.texto_final,
      texto_rodape: data.texto_rodape,
    },
  }

  return AgnusEmail.send(msg)
}
