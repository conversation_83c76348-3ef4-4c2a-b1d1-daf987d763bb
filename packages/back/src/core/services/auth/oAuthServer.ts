import crypto from 'crypto'
import OAuthServer, { ServerOptions } from '@node-oauth/oauth2-server'
import oAuthAccessTokenResource from '../../resources/oAuthAccessTokenResource'
import roleResource from '../../resources/roleResource'
import oAuthRefreshTokenResource from '../../resources/oAuthRefreshTokenResource'
import oAuthClientResource from '../../resources/oAuthClientResource'
import userResource from '../../resources/userResource'
import hasher from '../../utils/crypt/hasher'
import { RoleInstance } from '../../dbSequelize/models/Role'
import roleRepository from '../../dbSequelize/repositories/roleRepository'
import reportError from '../logs/reportError'

const getFutureDate = () => {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 1)
  return date
}

const defaultUserInclude = ['account', 'departments', 'timetable']

const defaultUserWhere = {
  active: true,
  archivedAt: { $eq: null },
}

const defaultGrants = ['password', 'refresh_token']

const getUserRoles = (userId: string): Promise<RoleInstance[]> => {
  return roleRepository.findMany({
    include: [
      'permissions',
      {
        model: 'users',
        where: {
          id: { $eq: userId },
        },
        required: true,
        attributes: [],
      },
    ],
    cache: true,
  })
}

export const model: ServerOptions['model'] = {
  async getAccessToken(accessToken: string) {
    const token = await oAuthAccessTokenResource.findOne({
      where: { accessToken: { $eq: accessToken } },
      include: [
        {
          model: 'user',
          required: true,
          where: defaultUserWhere,
          include: defaultUserInclude,
        },
      ],
      cache: true,
    })

    if (!token) return null

    if (token.name && token.deletedAt) return null

    if (token.name && token.accessTokenExpiresAt) {
      const now = new Date().getTime()
      const expiresAt = token.accessTokenExpiresAt.getTime()
      if (now > expiresAt) {
        return null
      }
    }

    token.user.roles = await roleResource.findMany({
      include: [
        'permissions',
        {
          model: 'users',
          where: {
            id: token.user.id,
          },
          required: true,
          attributes: [],
        },
      ],
      cache: true,
    })

    token.user.roles = await getUserRoles(token.user.id)

    return {
      accessTokenId: token.id,
      accessToken: token.accessToken,
      accessTokenExpiresAt: token.accessTokenExpiresAt || getFutureDate(),
      scope: token.scope?.split(','),
      client: { id: token.clientId, grants: defaultGrants },
      user: token.user,
    }
  },

  async getRefreshToken(refreshTokenString: string) {
    const refreshToken = await oAuthRefreshTokenResource.findOne({
      where: { refreshToken: { $eq: refreshTokenString } },
      include: [
        {
          model: 'user',
          required: true,
          where: defaultUserWhere,
          include: defaultUserInclude,
        },
      ],
      cache: true,
    })

    if (!refreshToken) return null

    refreshToken.user.roles = await getUserRoles(refreshToken.user.id)

    return {
      refreshToken: refreshToken.refreshToken,
      refreshTokenExpiresAt: refreshToken.refreshTokenExpiresAt,
      client: { id: refreshToken.clientId, grants: defaultGrants },
      user: refreshToken.user,
    }
  },

  async revokeToken({ refreshToken }: OAuthServer.RefreshToken) {
    try {
      const refreshTokenInstance = await oAuthRefreshTokenResource.findOne({
        where: { refreshToken: { $eq: refreshToken } },
      })

      await oAuthRefreshTokenResource.destroy(refreshTokenInstance)
      return true
    } catch (error) {
      reportError(error)
      return false
    }
  },

  async saveToken(token: OAuthServer.Token, client: OAuthServer.Client, user: OAuthServer.User) {
    const [accessToken, refreshToken] = await Promise.all([
      oAuthAccessTokenResource.create({
        accessToken: token.accessToken,
        accessTokenExpiresAt: token.accessTokenExpiresAt,
        scope: Array.isArray(token.scope) ? token.scope.join(',') : token.scope,
        clientId: client.id,
        userId: user.id,
      }),
      oAuthRefreshTokenResource.create({
        refreshToken: token.refreshToken,
        refreshTokenExpiresAt: token.refreshTokenExpiresAt,
        clientId: client.id,
        userId: user.id,
      }),
    ])

    return {
      accessTokenId: accessToken.id,
      accessToken: accessToken.accessToken,
      accessTokenExpiresAt: accessToken.accessTokenExpiresAt,
      refreshToken: refreshToken.refreshToken,
      refreshTokenExpiresAt: refreshToken.refreshTokenExpiresAt,
      scope: token.scope,
      client,
      user,
    }
  },

  getClient(clientId: string, clientSecret: string) {
    return oAuthClientResource.findOne({
      where: { clientId: { $eq: clientId }, clientSecret: { $eq: clientSecret } },
    })
  },

  async getUser(username: string, password: string) {
    const [email, accountId] = username.split(':')

    const user = await userResource.findOne({
      where: {
        ...defaultUserWhere,
        email: { $eq: email || username },
        ...(accountId && { accountId: { $eq: accountId } }),
      },
      include: defaultUserInclude,
      cache: true,
    })

    if (!user) return null

    const same = await hasher.compare(password, user.password)

    if (!same) return null

    user.roles = await getUserRoles(user.id)

    return user
  },

  getUserFromClient(client: OAuthServer.Client) {
    return client.getUser()
  },

  async generateAccessToken() {
    const buffer = crypto.randomBytes(256)
    return crypto.createHash('sha1').update(buffer).digest('hex')
  },
}

export default new OAuthServer({
  model,
  allowBearerTokensInQueryString: true,
})
