import { get } from 'lodash'
import { Inject, Service } from 'typedi'
import Env from './Env'
import type configValues from '../../configValues'

export type ConfigValues = typeof configValues

@Service()
export default class Config<V extends ConfigValues> {
  @Inject()
  protected env: Env

  protected values: V = {} as V

  get<T extends keyof V>(key: T, defaults?: V[T]): V[T] {
    return get(this.values, key, defaults)
  }

  set<T extends keyof typeof this.values>(key: T | string, value: V[T]) {
    this.values = {
      ...this.values,
      [key]: value,
    }
    return this
  }

  setValues(value: any) {
    this.values = value
    return this
  }

  getValues() {
    return this.values
  }
}
