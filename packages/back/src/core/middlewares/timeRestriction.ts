import { Request, Response, NextFunction } from 'express'
import HttpError from '../utils/error/HttpError'
import getTimezoneMinutesOffset from '../../core/utils/getTimezoneMinutesOffset'
import isOpenByRulesAndDate from '../../core/utils/isOpenByRulesAndDate'
import { UserInstance } from '../dbSequelize/models/User'

export default () => (req: Request, res: Response, next: NextFunction) => {
  const user: UserInstance = res.locals.user

  if (!user?.timetable) {
    return next()
  }

  const canEnter = isOpenByRulesAndDate(
    user?.timetable?.workPlan,
    new Date(),
    getTimezoneMinutesOffset(user?.account?.settings?.timezone),
  )

  if (canEnter) {
    next()
    return
  }

  next(new HttpError(403, `Is out of office hours`))
}
