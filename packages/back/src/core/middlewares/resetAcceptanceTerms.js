import contactResource from '../resources/contactResource'
import botResource from '../resources/botResource'

const getAcceptanceTermIdFromNewBot = async ({ contexts }) => {
  let acceptanceTermId = ''

  await contexts?.map((context) => {
    context?.triggers.map((trigger) => {
      trigger?.rules.map((rule) => {
        rule?.actions.map((action) => {
          if (action?.data?.acceptanceTerms) {
            acceptanceTermId = action?.data?.acceptanceTerms.id
          }
        })
      })
    })
  })

  return acceptanceTermId
}

const getAcceptanceTermIdFromCurrentBot = async ({ contexts }) => {
  const rules = contexts['@INIT']?.triggers?.TICKET_OPENED

  let acceptanceTermId = ''

  if (rules) {
    await rules.map((rule) => {
      rule?.actions.map((action) => {
        if (action?.data?.acceptanceTerms) {
          acceptanceTermId = action?.data?.acceptanceTerms
        }
      })
    })
  }

  return acceptanceTermId
}

export default async (req, res, next) => {
  const newBot = req.body
  const botId = req.params.id

  const currentBot = await botResource.findById(botId)

  const acceptanceTermIdFromNewBot = await getAcceptanceTermIdFromNewBot(newBot)

  const acceptanceTermIdFromCurrentBot = await getAcceptanceTermIdFromCurrentBot(currentBot)

  if (!acceptanceTermIdFromNewBot || !acceptanceTermIdFromCurrentBot) {
    return next()
  }

  if (acceptanceTermIdFromNewBot !== acceptanceTermIdFromCurrentBot) {
    await contactResource.bulkUpdate(
      {
        acceptedTermAt: null,
        acceptanceTermId: null,
      },
      {
        where: {
          acceptanceTermId: acceptanceTermIdFromCurrentBot,
          acceptedTermAt: { $not: null },
          deletedAt: { $is: null },
        },
      },
    )

    return next()
  }

  return next()
}
