import { NextFunction, Request, Response } from 'express'
import customFieldsResource from '../../core/resources/customFieldsResource'

export const checkAndOrganizeCustomFieldsPayload = async (req: Request, res: Response, next: NextFunction) => {
  const accountId = res.locals.accountId
  const incomingCustomFields = req.body.customFields

  if (!incomingCustomFields || incomingCustomFields.length === 0) return next()

  const storageCustomFields = await customFieldsResource.findMany({
    attributes: ['id', 'type', 'required', 'settings'],
    where: {
      accountId,
      allowed: 'contacts',
      deletedAt: null,
    },
  })

  const organizedCustomFields = storageCustomFields.map((storageCustomField) => {
    const { id, type, required, settings } = storageCustomField
    const filteredIncomingCustomField = incomingCustomFields.find((incomingCustomField) => {
      const incomingCustomFieldId =
        incomingCustomField.fieldId ?? incomingCustomField.customFieldId ?? incomingCustomField.id
      return incomingCustomFieldId === id
    })

    const incomingCustomField = filteredIncomingCustomField || {
      customFieldId: id,
      value: '',
      type: null,
      required: false,
      settings: {},
      defaultValue: true,
    }

    let value = incomingCustomField.value

    if (typeof value === 'string' && value.trim() === '') {
      value = ''
    }

    return { ...incomingCustomField, value, type, required, settings }
  })

  const cleanedCustomFields = organizedCustomFields.filter(
    (customField) => customField.required || !customField.defaultValue,
  )

  req.body.customFields = cleanedCustomFields

  next()
}
