import { NextFunction, Request, Response } from 'express'
import HttpError from '../utils/error/HttpError'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import { UserInstance } from '../dbSequelize/models/User'

const logger = Container.get(Logger)

/**
 * Esse middleware verifica se a conta está ativa para realizar o acesso a plataforma
 */
export default async (req: Request, res: Response, next: NextFunction) => {
  const user = res.locals?.oauth?.token?.user as UserInstance

  if (!user || !user.account) {
    next(new HttpError(500, 'Should have a user with account set'))
    return
  }

  const account = user.account

  if (!account.isActive && !account.plan?.isOnGracePeriod) {
    logger.logEvent('authn_login_fail', { email: user.email }, 'account_inactivated', 'error')

    next(new HttpError(403, 'ACCOUNT_INACTIVE'))
    return
  }

  next()
}
