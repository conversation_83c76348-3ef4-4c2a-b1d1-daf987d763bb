import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import serviceResource from '../resources/serviceResource'

const checkCountServiceArchived = async (req, res, next) => {
  const { user } = res.locals
  const accountId = res.locals?.accountId
  const { archive } = req.body

  if (typeof archive !== 'boolean') {
    return next(new BadRequestHttpError('Field archive must be a boolean.'))
  }

  // Busca a conexão enviada
  const service = await serviceResource.findOne({
    where: {
      id: req.params.id,
      accountId,
    },
    attributes: ['type'],
  })

  if (!service) {
    return next(new BadRequestHttpError('Service not found.'))
  }

  // Conta a quantidade de conexões ativas do tipo da conexão enviada
  const activeServiceCount = await serviceResource.count({
    where: {
      accountId,
      type: service.type,
      archivedAt: null,
    },
  })

  // Verifica a quantidade contratada no plano do tipo da conexão enviada
  const planService = parseInt(user?.account?.plan?.services[service.type]) || 0
  const isActiveAllowed = activeServiceCount < planService

  if (!isActiveAllowed && !archive) {
    // Foi enviado a operação de ativação (archive false) e o plano atual não permite
    return next(new BadRequestHttpError('Plan limit reached.'))
  }

  return next()
}

export default checkCountServiceArchived
