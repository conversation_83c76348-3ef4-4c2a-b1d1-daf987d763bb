import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import RequestContextCls from '../services/cls/RequestContextCls'
import RedisCacheStorage from '../services/cache/RedisCacheStorage'
import { UserInstance } from '../dbSequelize/models/User'

export default () => (req: Request, res: Response, next: NextFunction) => {
  const requestContextCls = Container.get(RequestContextCls)

  const user: UserInstance = res.locals.oauth.token.user

  if (!user) {
    next(new Error('No user set.'))
    return
  }

  const service = Container.get(RedisCacheStorage)
  const now = new Date()
  const utc = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
  service.set(`lastActivity:${user.id}`, utc)

  // @ts-ignore
  res.locals.user = req.user = user
  res.locals.impersonate = req.headers.impersonate === 'true'

  requestContextCls.setContext(
    {
      userId: user.id,
      userEmail: user.email,
      userName: user.name,
      accountId: user.accountId,
      accessTokenId: res.locals.oauth.token.accessTokenId,
      impersonate: res.locals.impersonate,
      client: res.locals.client,
      platform: req.headers.platform as string,
    },
    true,
  )

  next()
}
