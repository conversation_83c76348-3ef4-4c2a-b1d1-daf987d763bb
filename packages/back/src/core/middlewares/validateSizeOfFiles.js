import getSize from 'image-size-from-base64'
import contactResource from '../resources/contactResource'
import serviceResource from '../resources/serviceResource'
import ValidationError from '../utils/error/ValidationError'

export default async (req, res, next) => {
  const { attachments, contactId, serviceId } = req.body
  let contact
  if (!serviceId) {
    contact = await contactResource.findById(contactId)
  }
  const service = await serviceResource.findById(serviceId || contact?.serviceId)

  if (!service) {
    return res.status(400).json({
      message: 'this service is not exist',
    })
  }

  if (!service.data?.status?.isConnected) {
    return res.status(400).json({
      message: 'this service is not connected',
    })
  }

  if (service.archivedAt) {
    return res.status(400).json({
      message: 'this service is archived',
    })
  }

  if (service.data.service === 'Hotmail') {
    if (attachments && attachments.length) {
      const filteredArray = []

      await attachments.map(async (attachment) => {
        const item = await getSize(attachment.base64)
        filteredArray.push(item)
      })

      const size = await filteredArray.reduce((a, b) => parseInt(a) + parseInt(b))
      if (size > 3000) {
        next(new ValidationError())
      }

      next()
      return
    }
  }

  next()
}
