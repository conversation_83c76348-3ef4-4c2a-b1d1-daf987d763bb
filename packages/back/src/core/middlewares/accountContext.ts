import { Request, Response, NextFunction } from 'express'
import isObjectLike from 'lodash/isObjectLike'
import { UserInstance } from '../dbSequelize/models/User'

export default () => (req: Request, res: Response, next: NextFunction) => {
  const { account } = res.locals.user as UserInstance

  if (!account) {
    next(new Error('Account not found on currentUser.'))
    return
  }

  req.query.where = req.query.where || {}

  res.locals.account = account
  res.locals.accountId = account.id
  req.query.where = {
    // @ts-ignore
    ...req.query.where,
    ...(req.query?.archive && { archivedAt: null }),
    accountId: account.id,
  }

  if (isObjectLike(req.body) && !Array.isArray(req.body)) {
    req.body.accountId = account.id
  }

  next()
}
