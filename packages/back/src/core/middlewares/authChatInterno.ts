import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'

const auth = (req: Request, res: Response, next: NextFunction) => {
  const { authorization } = req.headers
  const token = authorization ? authorization.split(' ')[1] : null

  if (!token) return res.sendStatus(401)

  try {
    const decoded: any = jwt.verify(token, process.env.INTERNAL_CHAT_JWT)

    if (!decoded) return res.sendStatus(401)
  } catch (error) {
    return res.sendStatus(401)
  }

  return next()
}

export default auth
