import { NextFunction, Request, Response } from 'express'
import accountRepository from '../dbSequelize/repositories/accountRepository'

const getAccountIdByCode = async (accountAlias: string): Promise<string | null> => {
  if (!accountAlias) return null

  const account = await accountRepository.findOne({
    attributes: ['id'],
    where: {
      alias: { $eq: accountAlias },
    },
    cache: true,
  })

  return account?.id || null
}

const oAuth2AddAccount = async (req: Request, res: Response, next: NextFunction) => {
  if (req.body.username && (req.body.accountId || req.body.account_alias)) {
    const accountId = req.body.accountId || (await getAccountIdByCode(req.body.account_alias))

    if (accountId) {
      req.body.username = `${req.body.username}:${accountId}`
    }
  }

  next()
}

export default oAuth2AddAccount
