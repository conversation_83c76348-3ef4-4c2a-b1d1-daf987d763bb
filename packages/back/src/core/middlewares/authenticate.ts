import { Request, Response, NextFunction } from 'express'
import * as OAuthServer from '@node-oauth/oauth2-server'
import oAuthServer from '../../core/services/auth/oAuthServer'
import type OAuth2Server from 'oauth2-server'
import { UserInstance } from '../dbSequelize/models/User'

export type OAuth = {
  token: OAuth2Server.Token & {
    user: UserInstance
  }
}

export default async function (req: Request, res: Response, next: NextFunction) {
  const request = new OAuthServer.Request(req)
  const response = new OAuthServer.Response(res)

  try {
    const token = await oAuthServer.authenticate(request, response)

    res.locals.oauth = { token }

    next()
  } catch (error) {
    next(error)
  }
}
