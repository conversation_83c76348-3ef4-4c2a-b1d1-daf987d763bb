import { Request, Response, NextFunction } from 'express'
import stackTrace from 'stack-trace'
import reportError from '../services/logs/reportError'
import config from '../config'
import HttpError from '../utils/error/HttpError'
import ValidationError from '../utils/error/ValidationError'
import BadRequestHttpError from '../utils/error/BadRequestHttpError'

const dontReportStatus = [404, 401, 402, 403, 400]

type PossibleErrors = Error | HttpError | ValidationError

const formatStack = (error: PossibleErrors) => {
  const stack = stackTrace.parse(error)

  return stack
    .filter((line) => line.getFileName())
    .map(
      (line) =>
        `${
          line.getFunctionName() || line.getMethodName()
        } (${line.getFileName()}:${line.getLineNumber()}:${line.getColumnNumber()})`,
    )
}

const isDev = config('env') === 'development'

type SerializedError = {
  error: string
  message: string
  status: number
  stack?: string[]
  cause?: unknown
  extra?: object
}

const serializeHttpError = (error: HttpError): SerializedError => ({
  error: error.name,
  message: error.message,
  status: error.status,
  extra: error.extra,
  ...(error instanceof ValidationError && {
    errors: error.errors,
  }),
  ...(isDev && {
    message: error.message,
    stack: formatStack(error),
    cause: error.cause,
  }),
})

const convertToHttpError = (error: Error | any): HttpError => {
  const httpError = error instanceof HttpError ? error : new HttpError(error.status || 500, error.message)
  httpError.stack = error.stack

  return httpError
}

export default (error: PossibleErrors, req: Request, res: Response, next: NextFunction) => {
  // @ts-ignore
  const status = error.status || 500
  if (!dontReportStatus.includes(status)) reportError(error, { req, res })

  res.status(status).json(serializeHttpError(convertToHttpError(error)))
}
