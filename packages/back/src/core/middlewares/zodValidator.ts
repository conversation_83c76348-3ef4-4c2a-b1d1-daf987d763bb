import { NextFunction, Request, Response } from 'express'
import { ZodSchema, z } from 'zod'

export const validateSchema = <T extends ZodSchema>(schema: T, mapError?: Function) => {
  return (req: Request<{}, {}, z.infer<T>>, res: Response, next: NextFunction): void => {
    const validation = schema.safeParse(req.body)

    if (!validation.success) {
      res.status(400).json({
        error: 'BadRequestHttpError',
        message: 'Invalid request body, check format or invalid values',
        status: 400,
        extra: mapError ? mapError(req.body, validation.error) : validation.error,
      })

      return
    }

    next()
  }
}
