import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import otpTokenIsValid from '../utils/otp/tokenIsValid'
import { decryptTextForAccount } from '../services/crypt/accountCryptor'
import HttpError from '../utils/error/HttpError'
import Logger from '../../core/services/logs/Logger'
import { ThrottleLogin } from './throttleLogin'
import { OAuth } from './oAuth2Authentication'
import UnauthorizedHttpError from '../utils/error/UnauthorizedHttpError'

const logger = Container.get(Logger)

type Locals = {
  throttleLogin: ThrottleLogin
  oauth: OAuth
}

const OTP_HEADER = 'x-digisac-otp'
const OTP_REQUIRED = 'required'
const OTP_INVALID = 'invalid'

export default async function (req: Request, res: Response<any, Locals>, next: NextFunction) {
  const user = res.locals?.oauth?.token?.user

  if (!user || !user.account) {
    next(new HttpError(500, 'User and account should be set'))
    return
  }

  try {
    const { stats, throttler } = res.locals.throttleLogin
    const account = user.account

    if (!account.twoFactorAuthMandatoryActive && !user?.otpAuthActive) {
      next()
      return
    }

    const otpToken = (req.headers[OTP_HEADER] as string) || ''

    if (!otpToken) {
      if (!stats.isOtpStage) await throttler.setOtpStage()

      res.setHeader(OTP_HEADER, OTP_REQUIRED)
      logger.logEvent('authn_login_fail', { email: user.email }, 'absence_otp_token', 'error')
      next(new UnauthorizedHttpError('OTP Token is required.'))
      return
    }

    const { otpSecretKey = '' } = user

    const secretKey = decryptTextForAccount(account, otpSecretKey)

    if (!otpTokenIsValid(otpToken, secretKey)) {
      res.setHeader(OTP_HEADER, OTP_INVALID)
      logger.logEvent('authn_login_fail', { email: user.email }, 'invalid_otp_token', 'error')
      next(new UnauthorizedHttpError('OTP Token is invalid.'))
      return
    }

    next()
  } catch (error) {
    logger.logEvent('authn_login_fail', { email: user.email }, 'error_otp_token', 'error')
    next(error)
  }
}
