import { Request, Response, NextFunction } from 'express'
import HttpError from '../utils/error/HttpError'
import jwt from 'jsonwebtoken'
import config from '../config'
import { UserInstance } from '../dbSequelize/models/User'
import userResource from '../resources/userResource'
import { sendResetPasswordEmail } from '../services/email'
import reportError from '../services/logs/reportError'

const handleSendmailToResetPassword = async (user, next) => {
  try {
    const secret = config('resetPasswordJwtSecret')

    const token = jwt.sign({ id: user.id }, secret, { expiresIn: '24h' })

    const updatedUser = await userResource.updateById(
      user.id,
      {
        resetPasswordToken: token,
        data: { sentResetPasswordAt: new Date() },
      },
      { mergeJson: ['data'] },
    )

    await sendResetPasswordEmail({
      to: updatedUser.email,
      token: encodeURIComponent(token),
      userName: updatedUser.name,
    })
  } catch (error) {
    reportError(error)
    next(new HttpError(401, `Password expired. But wasn't possible to send an email to change it.`))
  }
}

export default () => async (req: Request, res: Response, next: NextFunction) => {
  const user: UserInstance = res.locals.user

  //Necessário nova instância não cacheada do usuário
  const nonCachedUser = await userResource.findById(user.id)

  if (!nonCachedUser) {
    next(new HttpError(401, `User not found #${user.id}`))
    return
  }

  if (nonCachedUser.isSuperAdmin) {
    next()
    return
  }

  if (nonCachedUser.hasPasswordExpired) {
    const secret = config('resetPasswordJwtSecret')

    if (['false', null, undefined].includes(nonCachedUser.resetPasswordToken)) {
      await handleSendmailToResetPassword(nonCachedUser, next)
    } else {
      try {
        jwt.verify(nonCachedUser.resetPasswordToken, secret)
      } catch (e) {
        if (e.message === 'jwt expired') {
          await handleSendmailToResetPassword(nonCachedUser, next)
        }
      }
    }

    next(new HttpError(401, `Password has expired.`))
    return
  }

  next()
}
