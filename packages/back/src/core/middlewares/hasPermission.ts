import ForbiddenHttpError from '../utils/error/ForbiddenHttpError'
import { UserInstance } from '../dbSequelize/models/User'
import hasPermission from '../utils/hasPermission'

export default (permissions: string | string[], any = false) =>
  (req, res, next) => {
    const user: UserInstance = res.locals.user

    if (!user || !hasPermission(user.permissions, permissions, any)) {
      next(new ForbiddenHttpError())
      return
    }

    next()
  }
