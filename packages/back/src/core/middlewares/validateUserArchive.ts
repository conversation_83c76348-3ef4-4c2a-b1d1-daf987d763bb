import sequelize from 'sequelize'
import botResource from '../resources/botResource'
import userResource from '../resources/userResource'
import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import PaymentRequiredError from '../utils/error/PaymentRequiredError'
import ForbiddenHttpError from '../utils/error/ForbiddenHttpError'

export const checkUserBot = async (userId) => {
  const bot = await botResource.count({
    where: sequelize.literal(`contexts::text like '%${userId}%'`),
  })
  return !!bot
}

export const checkForPlanLimit = async (account, quantityUsers = 1) => {
  if (!account || !account.id || !account.plan || quantityUsers <= 0) {
    return true
  }

  const userCount = await userResource.getRepository().count({
    where: {
      accountId: account.id,
      archivedAt: null,
    },
  })

  const planUsers = parseInt(account.plan.users, 10) || 3

  return userCount + quantityUsers > planUsers
}

export default async (req, res, next) => {
  const { id } = req.params
  const { archive } = req.body

  if (!archive) {
    const user = await userResource.findById(id, { include: ['account'] })

    if (!user) {
      next(new BadRequestHttpError('User not found.'))
      return
    }

    if (await checkForPlanLimit(user.account)) {
      next(new PaymentRequiredError('Plan limit reached.'))
      return
    }
  }

  if (archive && (await checkUserBot(id))) {
    next(new ForbiddenHttpError('Cannot archive user because it is used in a bot.'))
    return
  }

  return next()
}
