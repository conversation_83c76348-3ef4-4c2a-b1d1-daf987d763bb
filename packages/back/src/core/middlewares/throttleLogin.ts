import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import { addMinutes } from 'date-fns'
import RedisCacheStorage from '../../core/services/cache/RedisCacheStorage'
import { getIpFromRequest } from './ipRestriction'
import TooManyRequestsHttpError from '../../core/utils/error/TooManyRequestsHttpError'
import HttpError from '../../core/utils/error/HttpError'
import reportError from '../services/logs/reportError'

export enum ThrottlingStages {
  LOGIN = 'login',
  OTP = 'otp',
}

const logger = Container.get(Logger)

export class Throttler {
  protected redisCache: RedisCacheStorage

  protected attemptsKey: string

  protected stageKey: string

  protected maxAttempts = 5

  protected windowTime = 60 //minutes

  protected TTL = this.windowTime * 60 * 1000

  constructor(username: string, originIP: string) {
    this.redisCache = Container.get(RedisCacheStorage)

    const parsedIp = originIP.replaceAll(':', '-')

    const baseKey = `throttlelogin:${parsedIp}-${username}`
    this.attemptsKey = `${baseKey}:attempts`
    this.stageKey = `${baseKey}:stage`
  }

  getStage = async (): Promise<ThrottlingStages> => {
    const stage = (await this.redisCache.get(this.stageKey)) as ThrottlingStages

    return stage || ThrottlingStages.LOGIN
  }

  clearThrottling = async () => {
    await this.redisCache.destroy(this.stageKey)
    await this.redisCache.destroy(this.attemptsKey)
  }

  getAttempts = async (): Promise<Date[]> => {
    const attempts = (await this.redisCache.setGet(this.attemptsKey)) as string[]

    if (!Array.isArray(attempts)) return []

    return attempts.slice(0, this.maxAttempts).map((a) => new Date(a))
  }

  addAttempt = async (date = new Date()) => {
    await this.redisCache.setAdd(this.attemptsKey, date, this.TTL)
  }

  setOtpStage = async () => {
    await this.redisCache.set(this.stageKey, ThrottlingStages.OTP)
  }

  getStats = async () => {
    const history: Date[] = await this.getAttempts()
    const currentStage = await this.getStage()
    const isOtpStage = currentStage === ThrottlingStages.OTP
    const firstAttempt = history[0]
    /* Ref: https://www.epochconverter.com/ */
    const resetsAt = Math.floor(addMinutes(firstAttempt, this.windowTime).getTime() / 1000.0)
    const remainingTime = resetsAt - Math.floor(new Date().getTime() / 1000.0)

    return {
      history,
      limit: this.maxAttempts,
      window: this.windowTime,
      attempts: history.length,
      firstAttempt,
      remaining: this.maxAttempts - history.length,
      resetsAt,
      remainingTime,
      currentStage,
      isOtpStage,
    }
  }
}

type Stats = Awaited<ReturnType<Throttler['getStats']>>

export type ThrottleLogin = {
  throttler: Throttler
  stats: Stats
}

function setRateLimitHeaders(res: Response, stats: Stats) {
  const { limit, window, resetsAt, remaining } = stats

  res.setHeader('X-RateLimit-Policy', `${limit};w=${window}`)
  res.setHeader('X-RateLimit-Limit', limit)
  res.setHeader('X-RateLimit-Remaining', remaining)
  res.setHeader('X-RateLimit-Reset', resetsAt)
}

async function handleThrottle(req: Request, res: Response<any, { throttleLogin: ThrottleLogin }>, next: NextFunction) {
  try {
    const { username = '' } = req.body
    const originIP = getIpFromRequest(req)

    const throttler = new Throttler(username, originIP)

    let stats = await throttler.getStats()
    const { remaining: lastRemaining, remainingTime, isOtpStage } = stats

    if (!lastRemaining) {
      if (remainingTime <= 0) {
        await throttler.clearThrottling()
      }

      logger.logEvent('authn_login_fail_max', { email: username }, 'login_blocked_by_to_exceeded_attempt', 'error')
      next(new TooManyRequestsHttpError())
      return
    }

    if (!isOtpStage || req.headers['x-digisac-otp']) {
      await throttler.addAttempt()
    }

    stats = await throttler.getStats()

    setRateLimitHeaders(res, stats)

    res.locals.throttleLogin = <ThrottleLogin>{
      stats,
      throttler,
    }

    res.once('finish', () => {
      // the login was successfully
      if (res.statusCode < 400) {
        res.locals.throttleLogin.throttler.clearThrottling().catch(reportError)
      }
    })
  } catch (error) {
    next(error)
    return
  }

  next()
}

async function throttleErrorHandler(
  err: HttpError,
  req: Request,
  res: Response<any, { throttleLogin: ThrottleLogin }>,
  next: NextFunction,
) {
  const stats = res.locals?.throttleLogin?.stats

  if (stats) {
    setRateLimitHeaders(res, stats)
  }

  next(err)
}

export default { handleThrottle, throttleErrorHandler }
