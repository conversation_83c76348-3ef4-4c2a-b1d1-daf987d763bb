import { Request, Response, NextFunction } from 'express'
import HttpProxy from 'http-proxy'
import { Container } from 'typedi'
import { UserInstance } from '../dbSequelize/models/User'
import Logger from '../services/logs/Logger'
import Config from '../services/config/Config'

const proxy = HttpProxy.createProxyServer({})

export default () => (req: Request, res: Response, next: NextFunction) => {
  const user: UserInstance = res.locals.user
  const account = user.account

  const logger = Container.get(Logger)
  const config = Container.get(Config)

  const branch = config.get<string>('branch')

  if (account.branch !== branch) {
    logger.log(`Wrong account API branch. Account: ${account.branch}, Instance: ${branch}.`)

    if (req.header('redirectedFromBranch')) {
      next(new Error('Request already proxied, terminating here to prevent infinite loop.'))
      return
    }

    const baseUrl = config.get('apiPublicBaseUrl')

    const target = `${baseUrl}${req.baseUrl}`

    proxy.web(req, res, {
      headers: {
        branch: account.branch,
        redirectedFrom: `${branch};${config.get<string>('version')}`,
      },
      target,
    })
    return
  }

  next()
}
