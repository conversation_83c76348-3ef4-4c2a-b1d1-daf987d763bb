import { Request, Response, NextFunction } from 'express'
import authHistoryResource from '../../core/resources/authHistoryResource'
import { getIpFromRequest, getUserAgentFromRequest } from './ipRestriction'
import config from '../../core/config'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import { ThrottleLogin } from './throttleLogin'
import { OAuth } from './oAuth2Authentication'

const logger = Container.get(Logger)

type Locals = {
  throttleLogin: ThrottleLogin
  oauth: OAuth
}

export default async function (req: Request, res: Response<any, Locals>, next: NextFunction) {
  const { token } = res.locals?.oauth || {}

  const { accessTokenId, user } = token

  try {
    const originIP = getIpFromRequest(req)

    await authHistoryResource.create({
      userId: user.id,
      accountId: user.accountId,
      event: 'auth',
      accessTokenId,
      passwordHash: user.password,
      branch: config('branch'),
      originIP,
      originUA: getUserAgentFromRequest(req),
    })

    next()
  } catch (error) {
    logger.logEvent('authn_login_fail', { email: user.email }, 'error_auth_history', 'error')
    next(error)
  }
}
