import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import * as OAuthServer from '@node-oauth/oauth2-server'
import oAuthServer from '../../core/services/auth/oAuthServer'
import Logger from '../../core/services/logs/Logger'
import type OAuth2Server from 'oauth2-server'
import { UserInstance } from '../dbSequelize/models/User'
import { ThrottleLogin } from './throttleLogin'
import UnauthorizedHttpError from '../utils/error/UnauthorizedHttpError'

const logger = Container.get(Logger)

export type OAuth = {
  token: OAuth2Server.Token & {
    user: UserInstance
  }
}

type Locals = {
  throttleLogin: ThrottleLogin
  oauth: OAuth
  oauthResponse: OAuth2Server.Response
}

async function generateToken(req: Request, res: Response<any, Locals>, next: NextFunction) {
  const request = new OAuthServer.Request(req)
  const response = new OAuthServer.Response(res)

  let token: OAuth['token']

  try {
    // @ts-ignore
    token = await oAuthServer.token(request, response, {
      accessTokenLifetime: 60 * 60 * 24 * 14,
    })

    res.locals.oauth = { token }
    res.locals.oauthResponse = response

    next()
  } catch (error) {
    logger.logEvent(
      'authn_login_fail',
      { email: token?.user?.email, error: error.toString() },
      'error_new_access_token',
      'error',
    )
    next(new UnauthorizedHttpError())
  }
}

async function sendSuccess(req: Request, res: Response<any, Locals>) {
  const token = res.locals.oauth.token
  const response = res.locals.oauthResponse

  logger.logEvent('authn_login_success', { userId: token.user.id })

  res.set(response.headers)
  res.status(response.status).send(response.body)
}

export default { generateToken, sendSuccess }
