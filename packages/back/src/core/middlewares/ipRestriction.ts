import { Request, Response, NextFunction } from 'express'
import HttpError from '../utils/error/HttpError'
import { UserInstance } from '../dbSequelize/models/User'
import config from '../config'

export const getIpFromRequest = (req: Request) => String(req.headers['x-real-ip'] || req.connection.remoteAddress)
export const getUserAgentFromRequest = (req: Request) => req.headers['user-agent']

const privateIpRange = config('privateIpRange')

export default () => (req: Request, res: Response, next: NextFunction) => {
  const user: UserInstance = res.locals.user
  const account = user.account

  if (!account.settings.ipRestriction) {
    next()
    return
  }

  if (user.isSuperAdmin) {
    next()
    return
  }

  const ip = getIpFromRequest(req)
  const allowedIps = account.settings.allowedIps

  if (allowedIps.includes(ip)) {
    next()
    return
  }

  // Para liberar conexões oriundas de serviços internos, por exemplo geração de PDF
  if (ip.startsWith(privateIpRange)) {
    next()
    return
  }

  // Por ultimo pra não gastar ciclos de CPU desnecessários,
  // visto que será a regra menos usada
  if (user.hasPermission('ignore-ip-restriction')) {
    next()
    return
  }

  next(new HttpError(403, `IP [${ip}] not allowed.`))
}
