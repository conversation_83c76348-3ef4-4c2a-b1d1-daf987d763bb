import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import PaymentRequiredError from '../utils/error/PaymentRequiredError'
import ForbiddenHttpError from '../utils/error/ForbiddenHttpError'
import accountResource from '../resources/accountResource'
import userResource from '../resources/userResource'
import { checkForPlanLimit, checkUserBot } from './validateUserArchive'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

export default async (req, res, next) => {
  const { archive, userIds } = req.body
  const accountId = res.locals?.accountId

  if (typeof archive !== 'boolean') {
    next(new BadRequestHttpError('Field archive must be a boolean.'))
    return
  }

  if (!userIds || !Array.isArray(userIds) || !userIds.length) {
    next(new BadRequestHttpError('Field userIds must be an array not empty.'))
    return
  }

  const account = await accountResource.getAccountPlan(accountId)

  if (!account) {
    next(new BadRequestHttpError('Account not found.'))
    return
  }

  const users = await userResource.findMany({
    attributes: ['id'],
    where: { id: userIds, accountId },
  })

  if (userIds.length !== users.length) {
    next(new BadRequestHttpError(`Requesting ${userIds.length} users but only found ${users.length} users`))
    return
  }

  if (!archive && (await checkForPlanLimit(account, users.length))) {
    next(new PaymentRequiredError('Not enough plan limit to activate all users.'))
    return
  }

  if (archive) {
    const userBots = await queuedAsyncMap(users, async (user) => checkUserBot(user.id))

    if (userBots.some(Boolean)) {
      next(new ForbiddenHttpError('Cannot archive user because it is used in a bot.'))
      return
    }
  }

  return next()
}
