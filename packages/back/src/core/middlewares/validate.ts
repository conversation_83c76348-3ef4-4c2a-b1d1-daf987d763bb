import isEmpty from 'lodash/isEmpty'
import { getErrors } from '../utils/validator'
import ValidationError from '../utils/error/ValidationError'
import { Rule } from '../utils/validator/rules'
import { Request, Response, NextFunction } from 'express'

const locations = ['body', 'params', 'query', 'headers', 'cookies'] as const
type RuleLocation = (typeof locations)[number]

export type Rules = {
  [K in RuleLocation]: { [P in K]: { [attribute: string]: Rule[] } }
}[RuleLocation]

const validate = (rules: Rules, options) => async (req: Request, res: Response, next: NextFunction) => {
  const mergedOptions = {
    currentUser: res.locals?.user,
    req,
    res,
    next,
    ...options,
  }

  const errors = {}

  for (const location of locations) {
    if (!(location in rules)) continue
    const locationErrors = await getErrors(req[location], rules[location], mergedOptions)
    if (isEmpty(locationErrors)) continue
    errors[location] = locationErrors
  }

  if (isEmpty(errors)) {
    next()
    return
  }

  if (!mergedOptions?.hasError) next(new ValidationError(errors))
}

const validateWithConditionalRules =
  (
    conditionFunction: (req: Request, rules: Rules, elseRules?: Rules) => Rules,
    thenRules: Rules,
    elseRules?: Rules,
    options?,
  ) =>
  (req: Request, res: Response, next: NextFunction) => {
    const result = conditionFunction(req, thenRules, elseRules)
    return validate((typeof result == 'boolean' ? thenRules : elseRules) || result, options)(req, res, next)
  }

export default validate
export { validateWithConditionalRules }
