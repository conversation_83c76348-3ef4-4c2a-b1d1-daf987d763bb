import { Container } from 'typedi'
import pick from 'lodash/pick'
import subscriptionTransformer from './subscriptionTransformer'
import Config from '../services/config/Config'
import { transformMany } from '../utils/resource/transformerHelpers'
import roleTransformer from './roleTransformer'

const config = Container.get(Config)

export default async (model, context) => {
  if (!model) return model
  return {
    ...pick(model, [
      'id',
      'name',
      'alias',
      'isCampaignActive',
      'isActive',
      'createdAt',
      'updatedAt',
      'deletedAt',
      'data',
      'defaultDepartment',
      'wizardProgress',
      'plan',
      'expiresAt',
      'integrations',
      'agnusSignatureKey',
      'promptAiTransfer',
      'promptAiFinalize',
      'promptAiCsat',
    ]),
    roles: await transformMany(roleTransformer, context)(model.roles),
    subscription: await subscriptionTransformer(model.subscription),
    settings: {
      ...model.settings,
      flags: {
        'absence-management': false,
        'bots-v2': false,
        'bots-v3': true,
        'by-user-and-by-department-tabs': false,
        'use-block-message-rules-by-service': false,
        'disable-hsm-limit': false,
        distribution: false,
        'enable-audio-transcription': false,
        'enable-smart-summary': false,
        'enable-chatgpt': false,
        'enable-sales-funnel': false,
        'enable-magic-text': false,
        'enable-smart-csat-score': false,
        'internal-chat': false,
        'invalid-webhooks-inactivator': false,
        'search-messages': false,
        'enable-audio-waveform': true,
        ...Object.entries(model.settings?.flags || {}).reduce((acc, [key, value]) => {
          acc[key] = ['true', true, '1', 1].includes(value)
          return acc
        }, {}),
      },
    },
    creditsControlEnabled: !!model?.creditsControl?.enabled,
    withoutCreditsAvailable: !!model?.creditsControl?.withoutCreditsAvailable,
    amountLoadSearchMessages: config.get('amountLoadSearchMessages'),
  }
}
