import mongoose, { Schema } from 'mongoose'
import mongoose_delete from 'mongoose-delete'

export type AuthInfoAppLogInstance = {
  _id?: string
  url: string
  username: string
  name: string
  accountId: string
}

const schema = new Schema(
  {
    url: { type: String },
    username: { type: String },
    name: { type: String },
    accountId: { type: String },
  },
  { timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' } },
)

schema.plugin(mongoose_delete, { deletedAt: true })
schema.index({ accountId: 1 }, { name: 'authinfoapplogs_accountid_index' })
schema.index({ createdAt: 1 }, { name: 'authinfoapplogs_createdat_index' })

export default mongoose.model('AuthInfoAppLog', schema, 'AuthInfoAppLogs')
