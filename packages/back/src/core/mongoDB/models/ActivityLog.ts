import mongoose, { Schema } from 'mongoose'
import mongoose_delete from 'mongoose-delete'

export type ActivityLogInstance = {
  _id?: string
  resourceType?: string
  resourceId?: string
  event?: string
  userId?: string
  accessTokenId?: string
  accountId?: string
  data?: any
}

const schema = new Schema(
  {
    resourceType: { type: String },
    resourceId: { type: String },
    event: { type: String },
    userId: { type: String },
    accessTokenId: { type: String },
    accountId: { type: String },
    data: { type: Schema.Types.Mixed },
  },
  { timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' } },
)

schema.plugin(mongoose_delete, { deletedAt: true })
schema.index({ accountId: 1 }, { name: 'activitylogs_accountid_index' })
schema.index({ userId: 1 }, { name: 'activitylogs_userid_index' })
schema.index({ createdAt: 1 }, { name: 'activitylogs_createdat_index' })
schema.index({ resourceType: 1 }, { name: 'activitylogs_resourcetype_index' })
export default mongoose.model('ActivityLog', schema, 'ActivityLogs')
