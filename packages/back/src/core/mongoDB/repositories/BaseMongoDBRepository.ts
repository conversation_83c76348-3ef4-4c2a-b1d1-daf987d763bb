import { Model } from 'mongoose'

export default class BaseMongoDBRepository<T> {
  private readonly model: any

  constructor(model: Model<any>) {
    this.model = model
  }

  async findMany(options: any): Promise<[any]> {
    return this.model.find(options.filters, null, {
      sort: options.order,
    })
  }

  async findManyPaginated(options: any): Promise<any> {
    const DEFAULT_PER_PAGE = 15
    // eslint-disable-next-line radix
    const page = parseInt(`${options.page || '1'}`, 10)
    // eslint-disable-next-line radix
    const perPage = parseInt(`${options.perPage || DEFAULT_PER_PAGE}`, 10)
    const from = (page - 1) * perPage
    const to = page * perPage
    const limit = to - from
    const count = await this.model.count(options.filters)
    const skip = page > 1 ? perPage * (page - 1) : 0
    const result = await this.model.find(options.filters, null, {
      skip,
      limit: perPage,
      sort: options.order,
    })

    const lastPage = Math.ceil(count / perPage) || 1

    return {
      data: result,
      total: count,
      limit,
      skip,
      currentPage: page,
      lastPage,
      from,
      to,
    }
  }

  async findById(_id: string): Promise<any> {
    return this.model.findById({ _id })
  }

  async create(data: T): Promise<any> {
    return this.model.create(data)
  }

  async update(_id: string, data: T): Promise<any> {
    const oldData = await this.model.findByIdAndUpdate(_id, data)
    return {
      oldData,
      newData: await this.findById(_id),
    }
  }

  async destroyById(_id: string) {
    await this.model.delete({ _id })
  }
}
