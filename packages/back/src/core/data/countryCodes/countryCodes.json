[{"countryCode": "7 840", "acronym": "AB", "countryName": "Abkhazia", "patterns": []}, {"countryCode": "93", "acronym": "AF", "countryName": "Afghanistan", "patterns": ["93-XXX-XXX-XXX"]}, {"countryCode": "358 18", "acronym": "AX", "countryName": "Aland Islands", "patterns": []}, {"countryCode": "355", "acronym": "AL", "countryName": "Albania", "patterns": ["355-XX-XXX-XXXX"]}, {"countryCode": "213", "acronym": "DZ", "countryName": "Algeria", "patterns": ["213-XXX-XX-XX-XX"]}, {"countryCode": "1 684", "acronym": "AS", "countryName": "American Samoa", "patterns": ["1684-XXX-XXXX"]}, {"countryCode": "376", "acronym": "AD", "countryName": "Andorra", "patterns": ["376-XX-XX-XX"]}, {"countryCode": "244", "acronym": "AO", "countryName": "Angola", "patterns": ["244-XXX-XXX-XXX"]}, {"countryCode": "1 264", "acronym": "AI", "countryName": "<PERSON><PERSON><PERSON>", "patterns": ["1264-XXX-XXXX"]}, {"countryCode": "1 268", "acronym": "AG", "countryName": "Antigua & Barbuda", "patterns": ["1268-XXX-XXXX"]}, {"countryCode": "54", "acronym": "AR", "countryName": "Argentina", "patterns": ["54-X-XX-XXXX-XXXX"]}, {"countryCode": "374", "acronym": "AM", "countryName": "Armenia", "patterns": ["374-XX-XXX-XXX"]}, {"countryCode": "297", "acronym": "AW", "countryName": "Aruba", "patterns": ["297-XXX-XXXX"]}, {"countryCode": "247", "acronym": "SH", "countryName": "Ascension", "patterns": ["290-XX-XXX"]}, {"countryCode": "61", "acronym": "AU", "countryName": "Australia", "patterns": ["61-XXX-XXX-XXX"]}, {"countryCode": "672", "acronym": "AU", "countryName": "Australian External Territories", "patterns": ["61-XXX-XXX-XXX"]}, {"countryCode": "43", "acronym": "AT", "countryName": "Austria", "patterns": []}, {"countryCode": "994", "acronym": "AZ", "countryName": "Azerbaijan", "patterns": ["994-XX-XXX-XX-XX"]}, {"countryCode": "1 242", "acronym": "BS", "countryName": "Bahamas", "patterns": ["1242-XXX-XXXX"]}, {"countryCode": "973", "acronym": "BH", "countryName": "Bahrain", "patterns": ["973-XXXX-XXXX"]}, {"countryCode": "880", "acronym": "BD", "countryName": "Bangladesh", "patterns": ["880-XXXX-XXXXXX"]}, {"countryCode": "1 246", "acronym": "BB", "countryName": "Barbados", "patterns": ["1246-XXX-XXXX"]}, {"countryCode": "1 268", "acronym": "AG", "countryName": "Barbuda", "patterns": ["1268-XXX-XXXX"]}, {"countryCode": "375", "acronym": "BY", "countryName": "Belarus", "patterns": ["375-XX-XXX-XXXX"]}, {"countryCode": "32", "acronym": "BE", "countryName": "Belgium", "patterns": ["32-XXX-XX-XX-XX"]}, {"countryCode": "501", "acronym": "BZ", "countryName": "Belize", "patterns": []}, {"countryCode": "229", "acronym": "BJ", "countryName": "Benin", "patterns": ["229-XX-XXX-XXX"]}, {"countryCode": "1 441", "acronym": "BM", "countryName": "Bermuda", "patterns": ["1441-XXX-XXXX"]}, {"countryCode": "975", "acronym": "BT", "countryName": "Bhutan", "patterns": []}, {"countryCode": "591", "acronym": "BO", "countryName": "Bolivia", "patterns": ["591-X-XXX-XXXX"]}, {"countryCode": "599 7", "acronym": "BQ", "countryName": "Caribbean Netherlands", "patterns": []}, {"countryCode": "387", "acronym": "BA", "countryName": "Bosnia & Herzegovina", "patterns": []}, {"countryCode": "267", "acronym": "BW", "countryName": "Botswana", "patterns": ["267-XX-XXX-XXX"]}, {"countryCode": "55", "acronym": "BR", "countryName": "Brazil", "patterns": ["55-XX-9XXXX-XXXX", "55-XX-XXXX-XXXX", "XX-9XXXX-XXXX", "XX-XXXX-XXXX"]}, {"countryCode": "246", "acronym": "IO", "countryName": "British Indian Ocean Territory", "patterns": ["246-XXX-XXXX"]}, {"countryCode": "1 284", "acronym": "VG", "countryName": "British Virgin Islands", "patterns": ["1284-XXX-XXXX"]}, {"countryCode": "673", "acronym": "BN", "countryName": "Brunei", "patterns": ["673-XXX-XXXX"]}, {"countryCode": "359", "acronym": "BG", "countryName": "Bulgaria", "patterns": []}, {"countryCode": "226", "acronym": "BF", "countryName": "Burkina Faso", "patterns": ["226-XX-XX-XX-XX"]}, {"countryCode": "95", "acronym": "MM", "countryName": "Myanmar (Burma)", "patterns": []}, {"countryCode": "257", "acronym": "BI", "countryName": "Burundi", "patterns": ["257-XX-XX-XXXX"]}, {"countryCode": "855", "acronym": "KH", "countryName": "Cambodia", "patterns": []}, {"countryCode": "237", "acronym": "CM", "countryName": "Cameroon", "patterns": ["237-XXXX-XXXX"]}, {"countryCode": "1", "acronym": "CA", "countryName": "Canada", "patterns": ["1-XXX-XXX-XXXX"]}, {"countryCode": "238", "acronym": "CV", "countryName": "Cape Verde", "patterns": ["238-XXX-XXXX"]}, {"countryCode": "1 345", "acronym": "KY", "countryName": "Cayman Islands", "patterns": ["1345-XXX-XXXX"]}, {"countryCode": "236", "acronym": "CF", "countryName": "Central African Republic", "patterns": ["236-XX-XX-XX-XX"]}, {"countryCode": "235", "acronym": "TD", "countryName": "Chad", "patterns": ["235-XX-XX-XX-XX"]}, {"countryCode": "56", "acronym": "CL", "countryName": "Chile", "patterns": ["56-X-XXXX-XXXX"]}, {"countryCode": "86", "acronym": "CN", "countryName": "China", "patterns": ["86-XXX-XXXX-XXXX"]}, {"countryCode": "61", "acronym": "CX", "countryName": "Christmas Island", "patterns": []}, {"countryCode": "61", "acronym": "CC", "countryName": "Cocos (Keeling) Islands", "patterns": []}, {"countryCode": "57", "acronym": "CO", "countryName": "Colombia", "patterns": ["57-XXX-XXX-XXXX"]}, {"countryCode": "269", "acronym": "KM", "countryName": "Comoros", "patterns": ["269-XXX-XXXX"]}, {"countryCode": "242", "acronym": "CG", "countryName": "Congo - Brazzaville", "patterns": ["242-XX-XXX-XXXX"]}, {"countryCode": "243", "acronym": "CD", "countryName": "Congo - Kinshasa", "patterns": ["243-XX-XXX-XXXX"]}, {"countryCode": "682", "acronym": "CK", "countryName": "Cook Islands", "patterns": []}, {"countryCode": "506", "acronym": "CR", "countryName": "Costa Rica", "patterns": []}, {"countryCode": "225", "acronym": "CI", "countryName": "Cote d’Ivoire", "patterns": ["225-XX-XXX-XXX"]}, {"countryCode": "385", "acronym": "HR", "countryName": "Croatia", "patterns": []}, {"countryCode": "53", "acronym": "CU", "countryName": "Cuba", "patterns": ["53-XXXX-XXXX"]}, {"countryCode": "599 9", "acronym": "CW", "countryName": "Curacao", "patterns": []}, {"countryCode": "357", "acronym": "CY", "countryName": "Cyprus", "patterns": ["357-XXXX-XXXX"]}, {"countryCode": "420", "acronym": "CZ", "countryName": "Czech Republic", "patterns": []}, {"countryCode": "45", "acronym": "DK", "countryName": "Denmark", "patterns": ["45-XXXX-XXXX"]}, {"countryCode": "246", "acronym": "DG", "countryName": "<PERSON>", "patterns": []}, {"countryCode": "253", "acronym": "DJ", "countryName": "Djibouti", "patterns": ["253-XX-XX-XX-XX"]}, {"countryCode": "1 767", "acronym": "DM", "countryName": "Dominica", "patterns": ["1767-XXX-XXXX"]}, {"countryCode": "1", "acronym": "DO", "countryName": "Dominican Republic", "patterns": ["1-809-XXX-XXXX", "1-829-XXX-XXXX"]}, {"countryCode": "670", "acronym": "TL", "countryName": "Timor-Leste", "patterns": []}, {"countryCode": "593", "acronym": "EC", "countryName": "Ecuador", "patterns": ["593-XX-XXX-XXXX"]}, {"countryCode": "20", "acronym": "EG", "countryName": "Egypt", "patterns": ["20-XX-XXX-XXXX", "20-XXX-XXX-XXXX"]}, {"countryCode": "503", "acronym": "SV", "countryName": "El Salvador", "patterns": ["503-XXXX-XXXX"]}, {"countryCode": "240", "acronym": "GQ", "countryName": "Equatorial Guinea", "patterns": ["240-XXX-XXX-XXX"]}, {"countryCode": "291", "acronym": "ER", "countryName": "Eritrea", "patterns": ["291-X-XXX-XXX"]}, {"countryCode": "372", "acronym": "EE", "countryName": "Estonia", "patterns": []}, {"countryCode": "251", "acronym": "ET", "countryName": "Ethiopia", "patterns": ["251-XX-XXX-XXXX"]}, {"countryCode": "500", "acronym": "FK", "countryName": "Falkland Islands", "patterns": []}, {"countryCode": "298", "acronym": "FO", "countryName": "Faroe Islands", "patterns": ["298-XXX-XXX"]}, {"countryCode": "679", "acronym": "FJ", "countryName": "Fiji", "patterns": []}, {"countryCode": "358", "acronym": "FI", "countryName": "Finland", "patterns": []}, {"countryCode": "33", "acronym": "FR", "countryName": "France", "patterns": ["33-X-XX-XX-XX-XX"]}, {"countryCode": "594", "acronym": "GF", "countryName": "French Guiana", "patterns": []}, {"countryCode": "689", "acronym": "PF", "countryName": "French Polynesia", "patterns": []}, {"countryCode": "241", "acronym": "GA", "countryName": "Gabon", "patterns": ["241-X-XX-XX-XX"]}, {"countryCode": "220", "acronym": "GM", "countryName": "Gambia", "patterns": ["220-XXX-XXXX"]}, {"countryCode": "995", "acronym": "GE", "countryName": "Georgia", "patterns": []}, {"countryCode": "49", "acronym": "DE", "countryName": "Germany", "patterns": ["49-XXX-XXXXXXXX", "49-XX-XXXXXXXX"]}, {"countryCode": "233", "acronym": "GH", "countryName": "Ghana", "patterns": []}, {"countryCode": "350", "acronym": "GI", "countryName": "Gibraltar", "patterns": ["350-XXXX-XXXX"]}, {"countryCode": "30", "acronym": "GR", "countryName": "Greece", "patterns": ["30-XX-XXXX-XXXX"]}, {"countryCode": "299", "acronym": "GL", "countryName": "Greenland", "patterns": ["299-XXX-XXX"]}, {"countryCode": "1 473", "acronym": "GD", "countryName": "Grenada", "patterns": ["1473-XXX-XXXX"]}, {"countryCode": "590", "acronym": "GP", "countryName": "Guadeloupe", "patterns": []}, {"countryCode": "1 671", "acronym": "GU", "countryName": "Guam", "patterns": ["1671-XXX-XXXX"]}, {"countryCode": "502", "acronym": "GT", "countryName": "Guatemala", "patterns": ["502-X-XXX-XXXX"]}, {"countryCode": "44", "acronym": "GG", "countryName": "Guernsey", "patterns": []}, {"countryCode": "224", "acronym": "GN", "countryName": "Guinea", "patterns": ["224-XXX-XXX-XXX"]}, {"countryCode": "245", "acronym": "GW", "countryName": "Guinea-Bissau", "patterns": ["245-XXX-XXXX"]}, {"countryCode": "592", "acronym": "GY", "countryName": "Guyana", "patterns": []}, {"countryCode": "509", "acronym": "HT", "countryName": "Haiti", "patterns": []}, {"countryCode": "504", "acronym": "HN", "countryName": "Honduras", "patterns": ["504-XXXX-XXXX"]}, {"countryCode": "852", "acronym": "HK", "countryName": "Hong Kong SAR China", "patterns": []}, {"countryCode": "36", "acronym": "HU", "countryName": "Hungary", "patterns": ["36-XX-XXX-XXXX"]}, {"countryCode": "354", "acronym": "IS", "countryName": "Iceland", "patterns": ["354-XXX-XXXX"]}, {"countryCode": "91", "acronym": "IN", "countryName": "India", "patterns": ["91-XXXXX-XXXXX"]}, {"countryCode": "62", "acronym": "ID", "countryName": "Indonesia", "patterns": []}, {"countryCode": "98", "acronym": "IR", "countryName": "Iran", "patterns": ["98-XXX-XXX-XXXX"]}, {"countryCode": "964", "acronym": "IQ", "countryName": "Iraq", "patterns": ["964-XXX-XXX-XXXX"]}, {"countryCode": "353", "acronym": "IE", "countryName": "Ireland", "patterns": ["353-XX-XXX-XXXX"]}, {"countryCode": "972", "acronym": "IL", "countryName": "Israel", "patterns": ["972-XX-XXX-XXXX"]}, {"countryCode": "39", "acronym": "IT", "countryName": "Italy", "patterns": ["39-XXX-XXX-XXXX"]}, {"countryCode": "1 876", "acronym": "JM", "countryName": "Jamaica", "patterns": ["1876-XXX-XXXX"]}, {"countryCode": "47 79", "acronym": "SJ", "countryName": "Svalbard & Jan <PERSON>", "patterns": []}, {"countryCode": "81", "acronym": "JP", "countryName": "Japan", "patterns": ["81-XX-XXXX-XXXX"]}, {"countryCode": "44", "acronym": "JE", "countryName": "Jersey", "patterns": []}, {"countryCode": "962", "acronym": "JO", "countryName": "Jordan", "patterns": ["962-X-XXXX-XXXX"]}, {"countryCode": "383", "acronym": "XK", "countryName": "Kosovo", "patterns": ["383-XXXX-XXXX"]}, {"countryCode": "7 7", "acronym": "KZ", "countryName": "Kazakhstan", "patterns": ["7-XXX-XXX-XX-XX"]}, {"countryCode": "254", "acronym": "KE", "countryName": "Kenya", "patterns": ["254-XXX-XXX-XXX"]}, {"countryCode": "686", "acronym": "KI", "countryName": "Kiribati", "patterns": []}, {"countryCode": "850", "acronym": "KP", "countryName": "North Korea", "patterns": []}, {"countryCode": "82", "acronym": "KR", "countryName": "South Korea", "patterns": []}, {"countryCode": "965", "acronym": "KW", "countryName": "Kuwait", "patterns": ["965-XXXX-XXXX"]}, {"countryCode": "996", "acronym": "KG", "countryName": "Kyrgyzstan", "patterns": []}, {"countryCode": "856", "acronym": "LA", "countryName": "Laos", "patterns": []}, {"countryCode": "371", "acronym": "LV", "countryName": "Latvia", "patterns": ["371-XXX-XXXXX"]}, {"countryCode": "961", "acronym": "LB", "countryName": "Lebanon", "patterns": []}, {"countryCode": "266", "acronym": "LS", "countryName": "Lesotho", "patterns": ["266-XX-XXX-XXX"]}, {"countryCode": "231", "acronym": "LR", "countryName": "Liberia", "patterns": []}, {"countryCode": "218", "acronym": "LY", "countryName": "Libya", "patterns": ["218-XX-XXX-XXXX"]}, {"countryCode": "423", "acronym": "LI", "countryName": "Liechtenstein", "patterns": []}, {"countryCode": "370", "acronym": "LT", "countryName": "Lithuania", "patterns": ["370-XXX-XXXXX"]}, {"countryCode": "352", "acronym": "LU", "countryName": "Luxembourg", "patterns": []}, {"countryCode": "853", "acronym": "MO", "countryName": "Macau SAR China", "patterns": []}, {"countryCode": "389", "acronym": "MK", "countryName": "Macedonia", "patterns": []}, {"countryCode": "261", "acronym": "MG", "countryName": "Madagascar", "patterns": ["261-XX-XX-XXX-XX"]}, {"countryCode": "265", "acronym": "MW", "countryName": "Malawi", "patterns": []}, {"countryCode": "60", "acronym": "MY", "countryName": "Malaysia", "patterns": []}, {"countryCode": "960", "acronym": "MV", "countryName": "Maldives", "patterns": []}, {"countryCode": "223", "acronym": "ML", "countryName": "Mali", "patterns": ["223-XXXX-XXXX"]}, {"countryCode": "356", "acronym": "MT", "countryName": "Malta", "patterns": ["356-XX-XX-XX-XX"]}, {"countryCode": "692", "acronym": "MH", "countryName": "Marshall Islands", "patterns": []}, {"countryCode": "596", "acronym": "MQ", "countryName": "Martinique", "patterns": []}, {"countryCode": "222", "acronym": "MR", "countryName": "Mauritania", "patterns": ["222-XXXX-XXXX"]}, {"countryCode": "230", "acronym": "MU", "countryName": "Mauritius", "patterns": []}, {"countryCode": "262", "acronym": "YT", "countryName": "Mayotte", "patterns": []}, {"countryCode": "52", "acronym": "MX", "countryName": "Mexico", "patterns": ["52-X-XXX-XXX-XXXX", "52-XXX-XXX-XXXX"]}, {"countryCode": "691", "acronym": "FM", "countryName": "Micronesia", "patterns": []}, {"countryCode": "373", "acronym": "MD", "countryName": "Moldova", "patterns": ["373-XX-XXX-XXX"]}, {"countryCode": "377", "acronym": "MC", "countryName": "Monaco", "patterns": ["377-XXXX-XXXX"]}, {"countryCode": "976", "acronym": "MN", "countryName": "Mongolia", "patterns": []}, {"countryCode": "382", "acronym": "ME", "countryName": "Montenegro", "patterns": []}, {"countryCode": "1 664", "acronym": "MS", "countryName": "Montserrat", "patterns": ["1664-XXX-XXXX"]}, {"countryCode": "212", "acronym": "MA", "countryName": "Morocco", "patterns": ["212-XX-XXX-XXXX"]}, {"countryCode": "258", "acronym": "MZ", "countryName": "Mozambique", "patterns": ["258-XX-XXX-XXXX"]}, {"countryCode": "264", "acronym": "NA", "countryName": "Namibia", "patterns": ["264-XX-XXX-XXXX"]}, {"countryCode": "674", "acronym": "NR", "countryName": "Nauru", "patterns": []}, {"countryCode": "977", "acronym": "NP", "countryName": "Nepal", "patterns": []}, {"countryCode": "31", "acronym": "NL", "countryName": "Netherlands", "patterns": ["31-X-XX-XX-XX-XX"]}, {"countryCode": "687", "acronym": "NC", "countryName": "New Caledonia", "patterns": []}, {"countryCode": "64", "acronym": "NZ", "countryName": "New Zealand", "patterns": []}, {"countryCode": "505", "acronym": "NI", "countryName": "Nicaragua", "patterns": ["505-XXXX-XXXX"]}, {"countryCode": "227", "acronym": "NE", "countryName": "Niger", "patterns": ["227-XX-XX-XX-XX"]}, {"countryCode": "234", "acronym": "NG", "countryName": "Nigeria", "patterns": []}, {"countryCode": "683", "acronym": "NU", "countryName": "Niue", "patterns": []}, {"countryCode": "672", "acronym": "NF", "countryName": "Norfolk Island", "patterns": []}, {"countryCode": "1 670", "acronym": "MP", "countryName": "Northern Mariana Islands", "patterns": ["1670-XXX-XXXX"]}, {"countryCode": "47", "acronym": "NO", "countryName": "Norway", "patterns": ["47-XXXX-XXXX"]}, {"countryCode": "968", "acronym": "OM", "countryName": "Oman", "patterns": ["968-XXXX-XXXX"]}, {"countryCode": "92", "acronym": "PK", "countryName": "Pakistan", "patterns": ["92-XXX-XXX-XXXX"]}, {"countryCode": "680", "acronym": "PW", "countryName": "<PERSON><PERSON>", "patterns": []}, {"countryCode": "970", "acronym": "PS", "countryName": "Palestinian Territories", "patterns": ["970-XXX-XX-XXXX"]}, {"countryCode": "507", "acronym": "PA", "countryName": "Panama", "patterns": ["507-XXXX-XXXX"]}, {"countryCode": "675", "acronym": "PG", "countryName": "Papua New Guinea", "patterns": []}, {"countryCode": "595", "acronym": "PY", "countryName": "Paraguay", "patterns": ["595-XXX-XXX-XXX"]}, {"countryCode": "51", "acronym": "PE", "countryName": "Peru", "patterns": ["51-XXX-XXX-XXX"]}, {"countryCode": "63", "acronym": "PH", "countryName": "Philippines", "patterns": ["63-XXX-XXX-XXXX"]}, {"countryCode": "64", "acronym": "PN", "countryName": "Pitcairn Islands", "patterns": []}, {"countryCode": "48", "acronym": "PL", "countryName": "Poland", "patterns": ["48-XXX-XXX-XXX"]}, {"countryCode": "351", "acronym": "PT", "countryName": "Portugal", "patterns": ["351-X-XXXX-XXXX", "351-XXX-XXX-XXX"]}, {"countryCode": "1", "acronym": "PR", "countryName": "Puerto Rico", "patterns": ["1-787-XXX-XXXX", "1-939-XXX-XXXX"]}, {"countryCode": "974", "acronym": "QA", "countryName": "Qatar", "patterns": []}, {"countryCode": "262", "acronym": "RE", "countryName": "Reunion", "patterns": ["262-XXX-XXX-XXX"]}, {"countryCode": "40", "acronym": "RO", "countryName": "Romania", "patterns": ["40-XXX-XXX-XXX"]}, {"countryCode": "7", "acronym": "RU", "countryName": "Russia", "patterns": ["7-XXX-XXX-XX-XX"]}, {"countryCode": "250", "acronym": "RW", "countryName": "Rwanda", "patterns": ["250-XXX-XXX-XXX"]}, {"countryCode": "590", "acronym": "BL", "countryName": "St. <PERSON>", "patterns": []}, {"countryCode": "290", "acronym": "SH", "countryName": "St. Helena", "patterns": ["290-XX-XXX"]}, {"countryCode": "1 869", "acronym": "KN", "countryName": "St. Kitts & Nevis", "patterns": ["1869-XXX-XXXX"]}, {"countryCode": "1 758", "acronym": "LC", "countryName": "St. Lucia", "patterns": ["1758-XXX-XXXX"]}, {"countryCode": "590", "acronym": "MF", "countryName": "<PERSON><PERSON> (France)", "patterns": []}, {"countryCode": "508", "acronym": "PM", "countryName": "St. Pierre and Miquelon", "patterns": []}, {"countryCode": "1 784", "acronym": "VC", "countryName": "St. Vincent and the Grenadines", "patterns": ["1784-XXX-XXXX"]}, {"countryCode": "685", "acronym": "WS", "countryName": "Samoa", "patterns": []}, {"countryCode": "378", "acronym": "SM", "countryName": "San Marino", "patterns": ["378-XXX-XXX-XXXX"]}, {"countryCode": "239", "acronym": "ST", "countryName": "São Tome & Principe", "patterns": ["239-XX-XXXXX"]}, {"countryCode": "966", "acronym": "SA", "countryName": "Saudi Arabia", "patterns": []}, {"countryCode": "221", "acronym": "SN", "countryName": "Senegal", "patterns": ["221-XX-XXX-XXXX"]}, {"countryCode": "381", "acronym": "RS", "countryName": "Serbia", "patterns": ["381-XX-XXX-XXXX"]}, {"countryCode": "248", "acronym": "SC", "countryName": "Seychelles", "patterns": ["248-X-XX-XX-XX"]}, {"countryCode": "232", "acronym": "SL", "countryName": "Sierra Leone", "patterns": ["232-XX-XXX-XXX"]}, {"countryCode": "65", "acronym": "SG", "countryName": "Singapore", "patterns": ["65-XXXX-XXXX"]}, {"countryCode": "599 3", "acronym": "BQ", "countryName": "Sint Eustatius", "patterns": []}, {"countryCode": "1 721", "acronym": "SX", "countryName": "Sint Maarten", "patterns": ["1721-XXX-XXXX"]}, {"countryCode": "421", "acronym": "SK", "countryName": "Slovakia", "patterns": []}, {"countryCode": "386", "acronym": "SI", "countryName": "Slovenia", "patterns": []}, {"countryCode": "677", "acronym": "SB", "countryName": "Solomon Islands", "patterns": []}, {"countryCode": "252", "acronym": "SO", "countryName": "Somalia", "patterns": ["252-XX-XXX-XXX"]}, {"countryCode": "27", "acronym": "ZA", "countryName": "South Africa", "patterns": ["27-XX-XXX-XXXX"]}, {"countryCode": "500", "acronym": "GS", "countryName": "South Georgia & South Sandwich Islands", "patterns": []}, {"countryCode": "995 34", "acronym": "", "countryName": "South Ossetia", "patterns": []}, {"countryCode": "211", "acronym": "SS", "countryName": "South Sudan", "patterns": ["211-XX-XXX-XXXX"]}, {"countryCode": "34", "acronym": "ES", "countryName": "Spain", "patterns": ["34-XXX-XXX-XXX", "34-XXX-XX-XX-XX"]}, {"countryCode": "94", "acronym": "LK", "countryName": "Sri Lanka", "patterns": ["94-XX-XXX-XXXX"]}, {"countryCode": "249", "acronym": "SD", "countryName": "Sudan", "patterns": ["249-XX-XXX-XXXX"]}, {"countryCode": "597", "acronym": "SR", "countryName": "Suriname", "patterns": ["597-XXX-XXXX"]}, {"countryCode": "47 79", "acronym": "SJ", "countryName": "Svalbard", "patterns": []}, {"countryCode": "268", "acronym": "SZ", "countryName": "Swaziland", "patterns": ["268-XXXX-XXXX"]}, {"countryCode": "46", "acronym": "SE", "countryName": "Sweden", "patterns": ["46-XX-XXX-XXXX"]}, {"countryCode": "41", "acronym": "CH", "countryName": "Switzerland", "patterns": ["41-XX-XXX-XXXX"]}, {"countryCode": "963", "acronym": "SY", "countryName": "Syria", "patterns": []}, {"countryCode": "886", "acronym": "TW", "countryName": "Taiwan", "patterns": []}, {"countryCode": "992", "acronym": "TJ", "countryName": "Tajikistan", "patterns": []}, {"countryCode": "255", "acronym": "TZ", "countryName": "Tanzania", "patterns": ["255-XX-XXX-XXXX"]}, {"countryCode": "66", "acronym": "TH", "countryName": "Thailand", "patterns": ["66-X-XXXX-XXXX"]}, {"countryCode": "228", "acronym": "TG", "countryName": "Togo", "patterns": ["228-XX-XXX-XXX"]}, {"countryCode": "690", "acronym": "TK", "countryName": "Tokelau", "patterns": []}, {"countryCode": "676", "acronym": "TO", "countryName": "Tonga", "patterns": []}, {"countryCode": "1 868", "acronym": "TT", "countryName": "Trinidad & Tobago", "patterns": ["1868-XXX-XXXX"]}, {"countryCode": "216", "acronym": "TN", "countryName": "Tunisia", "patterns": ["216-XX-XXX-XXX"]}, {"countryCode": "90", "acronym": "TR", "countryName": "Turkey", "patterns": ["90-XXX-XXX-XXXX"]}, {"countryCode": "993", "acronym": "TM", "countryName": "Turkmenistan", "patterns": ["993-XX-XXXXXX"]}, {"countryCode": "1 649", "acronym": "TC", "countryName": "Turks & Caicos Islands", "patterns": ["1649-XXX-XXXX"]}, {"countryCode": "688", "acronym": "TV", "countryName": "Tuvalu", "patterns": []}, {"countryCode": "256", "acronym": "UG", "countryName": "Uganda", "patterns": ["256-XX-XXX-XXXX"]}, {"countryCode": "380", "acronym": "UA", "countryName": "Ukraine", "patterns": ["380-XX-XXX-XX-XX"]}, {"countryCode": "971", "acronym": "AE", "countryName": "United Arab Emirates", "patterns": ["971-XX-XXX-XXXX"]}, {"countryCode": "44", "acronym": "GB", "countryName": "United Kingdom", "patterns": ["44-XXXX-XXXXXX"]}, {"countryCode": "1", "acronym": "US", "countryName": "United States", "patterns": ["1-XXX-XXX-XXXX"]}, {"countryCode": "598", "acronym": "UY", "countryName": "Uruguay", "patterns": ["598-XXXX-XXXX"]}, {"countryCode": "1 340", "acronym": "VI", "countryName": "U.S. Virgin Islands", "patterns": ["1340-XXX-XXXX"]}, {"countryCode": "998", "acronym": "UZ", "countryName": "Uzbekistan", "patterns": ["998-XX-XXXXXXX"]}, {"countryCode": "678", "acronym": "VU", "countryName": "Vanuatu", "patterns": []}, {"countryCode": "58", "acronym": "VE", "countryName": "Venezuela", "patterns": ["58-XXX-XXX-XXXX"]}, {"countryCode": "39 06 698", "acronym": "VA", "countryName": "Vatican City", "patterns": []}, {"countryCode": "84", "acronym": "VN", "countryName": "Vietnam", "patterns": []}, {"countryCode": "681", "acronym": "WF", "countryName": "Wallis & Futuna", "patterns": []}, {"countryCode": "967", "acronym": "YE", "countryName": "Yemen", "patterns": ["967-XXX-XXX-XXX"]}, {"countryCode": "260", "acronym": "ZM", "countryName": "Zambia", "patterns": ["260-XX-XXX-XXXX"]}, {"countryCode": "255", "acronym": "", "countryName": "Zanzibar", "patterns": []}, {"countryCode": "263", "acronym": "ZW", "countryName": "Zimbabwe", "patterns": ["263-XX-XXX-XXXX"]}]