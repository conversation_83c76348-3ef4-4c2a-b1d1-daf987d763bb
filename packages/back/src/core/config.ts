import { Container } from 'typedi'
import { isEmpty } from 'lodash'
import AppContextCls from './services/cls/AppContextCls'
import Config, { ConfigValues } from './services/config/Config'
import configValues from './configValues'

const getApp = () => Container.get(AppContextCls)

const getConfig = <V extends ConfigValues>() => {
  const defaultConfig = Container.get(Config<V>)
  const containerName = getApp().getContainer()
  const appConfig = Container.of(containerName).get(Config<V>)

  if (!isEmpty(appConfig.getValues())) return appConfig

  if (isEmpty(defaultConfig.getValues())) {
    if (configValues.env === 'development') {
      /* eslint-disable no-console */
      console.warn('Running on default AppContextCls container.')
    }
    defaultConfig.setValues(configValues)
  }

  return defaultConfig
}

export default <V extends ConfigValues, K extends keyof V>(key: K, defaults?: V[K]): V[K] =>
  getConfig<V>().get(key, defaults)

export const setValues = <V extends ConfigValues>(v: V) => getConfig<V>().setValues(v)

export const getValues = <V extends ConfigValues>() => getConfig<V>().getValues()

export const setValue = <V extends ConfigValues, K extends keyof V>(key: K, value: V[K]) =>
  getConfig<V>().set(key, value)
