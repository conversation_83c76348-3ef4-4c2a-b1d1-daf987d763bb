import { Container } from 'typedi'
// eslint-disable-next-line import/no-cycle
import contactResource from '../resources/contactResource'
// eslint-disable-next-line import/no-cycle
import messageResource from '../resources/messageResource'
import { MessageInstance } from '../dbSequelize/models/Message'
import { ContactInstance } from '../dbSequelize/models/Contact'
import base64ToBuffer from '../utils/base64/base64ToBuffer'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import type WhatsappRemoteClientRpcJob from '../../microServices/workers/jobs/whatsapp/WhatsappRemoteClientRpcJob'

export const start = () => {}
export const stop = () => {}

export type WhatsappWorkerRpcInterface = WhatsappRemoteClientRpcJob['getClientMethods']

type Weaken<T, K extends keyof T> = { [P in keyof T]: P extends K ? any : T[P] }

export interface WhatsappWorkerRpcConsumer extends Weaken<WhatsappWorkerRpcInterface, 'screenshot'> {
  screenshot: (serviceId: string) => Promise<Buffer>
  refresh: () => Promise<void>
}

const rebuild = (resource, modelData) => {
  const model = resource.build(modelData, { isNewRecord: false })

  // eslint-disable-next-line no-underscore-dangle
  Object.keys(model._changed).forEach((key) => {
    model.changed(key, false)
  })

  return model
}

export const createConsumer = async (): Promise<WhatsappWorkerRpcConsumer> => {
  const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

  const methodNames = [
    'syncGroupById',
    'syncGroupParticipants',
    'syncContact',
    'loadEarlierMessages',
    'contactExists',
    'sendAndSave',
    'send',
    'sendGroupInviteMessage',
    'start',
    'takeover',
    'shutdown',
    'restart',
    'isConnected',
    'screenshot',
    'logout',
    'ping',
    'kill',
    'getMeta',
    'revokeMessageById',
    'sendReactionByMessage',
    'revokeReactionByMessage',
    'syncMessageFileById',
    'getGroups',
    'getGroupParticipants',
    'getValidId',
    'editMessage',

    'wplvForwardMessages',
    'wplvGetContacts',
    'wplvCreateGroup',
    'wplvAddGroupParticipants',
    'wplvRemoveGroupParticipants',
    'wplvPromoteGroupParticipants',
    'wplvDemoteGroupParticipants',
    'wplvLeaveGroup',
    'wplvDeleteChat',
    'wplvGetChat',
    'wplvRevokeMessage',
    'wplvMarkChatSeen',
    'wplvSetChatTyping',
    'wplvRemoveAllGroupParticipants',

    'webhook',
    'wplvOnlyAdminCanSendMsgGroup',
    'wplvOnlyAdminCanEditProperties',
    'wplvSetAdminApproval',
    'wplvSetMemberAddMode',
    'wplvSetGroupDescription',
    'wplvGetGroupConfig',
    'wplvSendVCardContactMessage',
  ]

  // @ts-ignore
  const proxy: WhatsappWorkerRpcInterface = methodNames.reduce((p, methodName) => {
    p[methodName] = (...params) =>
      httpJobsDispatcher.dispatch<WhatsappRemoteClientRpcJob>('whatsapp-remote-client-rpc', {
        method: methodName,
        params,
      })
    return p
  }, {})

  const syncGroupParticipants = (serviceId: string, contactId: string): Promise<ContactInstance[]> =>
    proxy
      .syncGroupParticipants(serviceId, contactId)
      .then((results) => Promise.all(results.map((model) => rebuild(contactResource, model))))

  const sendAndSave = (serviceId: string, data, options): Promise<MessageInstance> =>
    proxy.sendAndSave(serviceId, data, options).then((model) => rebuild(messageResource, model))

  const screenshot = (serviceId: string): Promise<Buffer> => proxy.screenshot(serviceId).then(base64ToBuffer)

  return {
    ...proxy,
    syncGroupParticipants,
    sendAndSave,
    screenshot,
    refresh: async () => null,
  }
}
