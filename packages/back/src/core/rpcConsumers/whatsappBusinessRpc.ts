import { Container } from 'typedi'
import messageResource from '../resources/messageResource'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import type WhatsappBusinessRpcJob from '../../microServices/workers/jobs/whatsappBusiness/WhatsappBusinessRpcJob'

const rebuild = (resource, modelData) => {
  const model = resource.build(modelData, { isNewRecord: false })

  Object.keys(model._changed).forEach((key) => {
    model.changed(key, false)
  })

  return model
}

export const createConsumer = async () => {
  const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

  const methodNames = [
    'contactExists',
    'loadEarlierMessages',
    'sendAndSave',
    'webhook',
    'syncMessageFileById',
    'start',
    'refresh',
    'processReceivedFile',
    'processSentFile',
    'sendMessageToBroker',
    'newToken',
    'refreshTemplates',
    'createTemplate',
    'deleteTemplate',
    'logout',
    'restart',
    'shutdown',
    'sendReactionByMessage',
    'revokeReactionByMessage',
  ]

  const proxy = methodNames.reduce((p, methodName) => {
    p[methodName] = (...params) =>
      httpJobsDispatcher.dispatch<WhatsappBusinessRpcJob>('whatsapp-business-rpc', {
        method: methodName,
        params,
      })
    return p
  }, {})

  const sendAndSave = (serviceId, data, options) =>
    proxy.sendAndSave(serviceId, data, options).then((model) => rebuild(messageResource, model))

  return {
    ...proxy,
    sendAndSave,
    getValidId: async (serviceId, idFromService) => idFromService,
    isConnected: async () => true,
    syncContact: async () => null,
    loadEarlierMessagesById: async () => true,
  }
}
