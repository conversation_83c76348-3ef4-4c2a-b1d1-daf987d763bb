import { Container } from 'typedi'
import type WhatsappRemoteClientRpcJob from '../../microServices/workers/jobs/whatsapp/WhatsappRemoteClientRpcJob'
// eslint-disable-next-line import/no-cycle
import * as whatsappRemoteRpc from './whatsappRemoteRpc'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
// eslint-disable-next-line import/no-cycle
import QueuedWhatsappRemoteRpcJob from '../../microServices/workers/jobs/whatsapp/QueuedWhatsappRemoteRpcJob'

export const start = () => {}
export const stop = () => {}

export type WhatsappWorkerRpcInterface = ReturnType<WhatsappRemoteClientRpcJob['getClientMethods']>

type Weaken<T, K extends keyof T> = { [P in keyof T]: P extends K ? any : T[P] }

export interface WhatsappWorkerRpcConsumer extends Weaken<WhatsappWorkerRpcInterface, 'screenshot'> {
  screenshot: (serviceId: string) => Promise<Buffer>
  refresh: () => Promise<void>
}

export const createConsumer = async (): Promise<WhatsappWorkerRpcConsumer> => {
  const jobsDispatcher = Container.get(HttpJobsDispatcher)

  const superProxy = await whatsappRemoteRpc.createConsumer()

  const methodNames = [
    'send',
    'wplvForwardMessages',
    // 'wplvCreateGroup',
    // 'wplvAddGroupParticipants',
    // 'wplvRemoveGroupParticipants',
    // 'wplvPromoteGroupParticipants',
    // 'wplvDemoteGroupParticipants',
    // 'wplvLeaveGroup',
    // 'wplvDeleteChat',
    // 'wplvRevokeMessage',
    // 'wplvMarkChatSeen',
  ]

  // @ts-ignore
  const proxy: WhatsappWorkerRpcInterface = methodNames.reduce((p, methodName) => {
    p[methodName] = (serviceId: string, ...rest: any[]) =>
      jobsDispatcher.dispatch<QueuedWhatsappRemoteRpcJob>('queued-whatsapp-remote-rpc', {
        method: methodName,
        params: [serviceId, ...rest],
        serviceId,
      })
    return p
  }, {})

  // @ts-ignore
  return {
    ...superProxy,
    ...proxy,
  }
}
