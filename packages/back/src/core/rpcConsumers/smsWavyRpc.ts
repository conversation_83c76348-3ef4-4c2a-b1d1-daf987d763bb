import { Container } from 'typedi'
import messageResource from '../resources/messageResource'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import type SmsWavyRpcJob from '../../microServices/workers/jobs/smsWavy/SmsWavyRpcJob'

const rebuild = (resource, modelData) => {
  const model = resource.build(modelData, { isNewRecord: false })

  Object.keys(model._changed).forEach((key) => {
    model.changed(key, false)
  })

  return model
}

export const createConsumer = async () => {
  const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

  const methodNames = [
    'sendAndSave',
    'webhook',
    'contactExists',
    'getValidId',
    'isConnected',
    'syncContact',
    'loadEarlierMessages',
    'start',
    'syncMessageFileById',
    'revokeMessageById',
    'shutdown',
    'restart',
    'destroy',
    'kill',
    'logout',
  ]

  const proxy = methodNames.reduce((p, methodName) => {
    p[methodName] = (...params) =>
      httpJobsDispatcher.dispatch<SmsWavyRpcJob>('sms-wavy-rpc', {
        method: methodName,
        params,
      })
    return p
  }, {})

  const sendAndSave = (serviceId, data, options) =>
    proxy.sendAndSave(serviceId, data, options).then((model) => rebuild(messageResource, model))

  return {
    ...proxy,
    sendAndSave,
    getValidId: async (serviceId, idFromService) => idFromService,
    contactExists: async () => true,
    isConnected: async () => true,
    syncContact: async () => null,
    refresh: async () => null,
  }
}
