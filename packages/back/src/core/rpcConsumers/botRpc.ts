import { Container } from 'typedi'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import type BotRpcJob from '../../microServices/workers/jobs/bot/BotRpcJob'

let consumer

const createConsumer = async () => {
  const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

  const methodNames = ['handleTicketBeforeClose', 'handleSurveyAnswer']

  const proxy = methodNames.reduce((p, methodName) => {
    p[methodName] = (...params) =>
      httpJobsDispatcher.dispatch<BotRpcJob>('bot-rpc', {
        method: methodName,
        params,
      })
    return p
  }, {})

  return proxy
}

// singleton
export default async () => {
  if (!consumer) {
    consumer = await createConsumer()
  }

  return consumer
}
