import { Inject, Service } from 'typedi'
import { ContactInstance } from '../dbSequelize/models/Contact'
import { MessageInstance } from '../dbSequelize/models/Message'
import contactResource from '../resources/contactResource'
import messageResource from '../resources/messageResource'
import base64ToBuffer from '../utils/base64/base64ToBuffer'
import type WhatsappRemoteClientRpcJob from '../../microServices/workers/jobs/whatsapp/WhatsappRemoteClientRpcJob'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'

export type WhastappWorkerRpcInterface = {
  syncGroupParticipants: (serviceId: string, contactId: string) => Promise<ContactInstance[]>
  contactExists: (serviceId: string, contactId: string) => Promise<boolean>
  syncContact: (serviceId: string, contactId: string) => Promise<boolean>
  loadEarlierMessages: (serviceId: string, contactId: string, timestamp: number) => Promise<boolean>
  sendAndSave: (serviceId: string, data, options?) => Promise<MessageInstance>
  start: (serviceId: string) => Promise<boolean>
  takeover: (serviceId: string) => Promise<boolean>
  shutdown: (serviceId: string) => Promise<boolean>
  restart: (serviceId: string) => Promise<boolean>
  isConnected: (serviceId: string) => Promise<boolean>
  logout: (serviceId: string) => Promise<boolean>
  ping: () => Promise<{ pong: boolean }>
  kill: (serviceId: string) => Promise<void>
  getMeta: () => Promise<{ version: string }>
  revokeMessageById: (serviceId: string, messageId: string) => Promise<boolean>
  syncMessageFileById: (serviceId: string, messageId: string) => Promise<boolean>
  getGroups: (serviceId: string) => Promise<any>
  screenshot: (serviceId: string) => Promise<Buffer>
}

const rebuild = (resource, modelData) => {
  const model = resource.build(modelData, { isNewRecord: false })

  Object.keys(model._changed).forEach((key) => {
    model.changed(key, false)
  })

  return model
}

const proxyRpcCalls = () => (obj) =>
  new Proxy(obj, {
    get: (target: HasProxy, methodName) => {
      if (obj.proxyMethods.includes(methodName)) {
        return (...params) => target.proxyCall(methodName, params)
      }

      return target[methodName]
    },
  })

interface HasProxy<T = any> {
  proxyMethods: string[]
  proxyCall(methodName: string | number | symbol, params: any[]): T
}

@Service()
@proxyRpcCalls()
// @ts-ignore
export default class WhatsappRemoteRpc2 implements WhastappWorkerRpcInterface, HasProxy {
  @Inject()
  protected jobsDispatcher: HttpJobsDispatcher

  proxyMethods = [
    'syncGroupParticipants',
    'syncContact',
    'loadEarlierMessages',
    'contactExists',
    'sendAndSave',
    'send',
    'start',
    'takeover',
    'shutdown',
    'restart',
    'isConnected',
    'screenshot',
    'logout',
    'ping',
    'kill',
    'getMeta',
    'revokeMessageById',
    'syncMessageFileById',
    'getGroups',
    'getValidId',
  ]

  // Any method not present is proxied to RPC by @proxyRpcCalls annotation
  proxyCall(methodName: string, params: any[]) {
    return this.jobsDispatcher.dispatch<WhatsappRemoteClientRpcJob>('whatsapp-remote-client-rpc', {
      method: methodName,
      params,
    })
  }

  syncGroupParticipants = (serviceId: string, contactId: string): Promise<ContactInstance[]> =>
    this.proxyCall('syncGroupParticipants', [serviceId, contactId]).then((results) =>
      Promise.all(results.map((model) => rebuild(contactResource, model))),
    )

  sendAndSave = (serviceId: string, data, options): Promise<MessageInstance> => {
    return this.proxyCall('sendAndSave', [serviceId, data, options]).then((model) => rebuild(messageResource, model))
  }

  screenshot = (serviceId: string): Promise<Buffer> =>
    // @ts-ignore
    this.screenshot(serviceId).then(base64ToBuffer)
}
