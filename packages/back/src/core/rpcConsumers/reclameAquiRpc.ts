import { Container } from 'typedi'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import ReclameAquiRpcJobs from '../../microServices/workers/jobs/reclameAqui/ReclameAquiRpcJob'
import messageResource from '../resources/messageResource'

const rebuild = (resource, modelData) => {
  const model = resource.build(modelData, { isNewRecord: false })

  Object.keys(model._changed).forEach((key) => {
    model.changed(key, false)
  })

  return model
}

export const createConsumer = async () => {
  const httpJobDispatcher = Container.get(HttpJobsDispatcher)

  const methodNames = [
    'sendAndSave',
    'webhook',
    'contactExists',
    'getValidId',
    'isConnected',
    'syncContact',
    'loadEarlierMessages',
    'start',
    'syncMessageFileById',
    'revokeMessageById',
    'shutdown',
    'restart',
    'refresh',
    'logout',
    'processReceivedFile',
    'processSentFile',
    'sendMessageToBroker',
  ]

  const proxy = methodNames.reduce((p, methodName): any => {
    p[methodName] = (...params) =>
      httpJobDispatcher.dispatch<ReclameAquiRpcJobs>('reclame-aqui-rpc', {
        method: methodName,
        params,
      })
    return p
  }, {})

  const sendAndSave = (serviceId, data, options) =>
    proxy.sendAndSave(serviceId, data, options).then((model) => rebuild(messageResource, model))

  return {
    ...proxy,
    sendAndSave,
    getValidId: async (serviceId, idFromService) => idFromService,
    contactExists: async () => true,
    isConnected: async () => true,
    syncContact: async () => true,
    shutdown: async () => true,
  }
}
