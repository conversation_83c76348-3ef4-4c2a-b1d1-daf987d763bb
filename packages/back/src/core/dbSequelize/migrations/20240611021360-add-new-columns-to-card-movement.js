module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'fromStageStatusId',
      {
        type: Sequelize.UUID,
        references: {
          model: 'stage_status',
          key: 'id',
        },
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'toStageStatusId',
      {
        type: Sequelize.UUID,
        references: {
          model: 'stage_status',
          key: 'id',
        },
      },
    )
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'fromStageStatusId',
    )
    await queryInterface.removeColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'toStageStatusId',
    )
  },
}
