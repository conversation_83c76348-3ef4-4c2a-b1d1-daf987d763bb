'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const indexes = await queryInterface.showIndex('bot_versions')
    const updatedAtHasIndex = indexes.find((index) => index?.fields?.some((field) => field?.attribute === 'updatedAt'))

    if (!updatedAtHasIndex) {
      await queryInterface.addIndex('bot_versions', [{ attribute: 'updatedAt', order: 'DESC' }])
    }
  },

  async down(queryInterface) {
    const indexes = await queryInterface.showIndex('bot_versions')
    const updatedAtHasIndex = indexes.find((index) => index?.fields?.some((field) => field?.attribute === 'updatedAt'))

    if (updatedAtHasIndex) {
      await queryInterface.removeIndex('bot_versions', ['updatedAt'])
    }
  },
}
