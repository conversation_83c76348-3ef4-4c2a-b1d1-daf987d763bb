module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize
    return queryInterface.describeTable('services').then((services) => {
      if (!services.archivedAt) {
        return queryInterface.addColumn('services', 'archivedAt', {
          type: DataTypes.DATE,
          allowNull: true,
          defaultValue: null,
        })
      }
      return Promise.resolve(true)
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('services', 'archivedAt')
  },
}
