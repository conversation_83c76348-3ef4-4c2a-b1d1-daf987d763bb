module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('contacts', 'hsmExpirationTime', {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
      after: 'unsubscribed',
    })

    await queryInterface.addIndex('contacts', {
      fields: ['serviceId', 'idFromService', 'hsmExpirationTime'],
      name: 'contacts_hsmExpirationTime_index',
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('contacts', 'hsmExpirationTime')

    await queryInterface.removeIndex('contacts', 'contacts_hsmExpirationTime_index')
  },
}
