import accountResource from '../../resources/accountResource'
import setupSequelize from '../../services/db/setupSequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const accounts = await accountResource.findMany({
      attributes: ['id', 'settings'],
    })

    await queuedAsyncMap(accounts, async (account) => {
      await accountResource.updateById(
        account.id,
        {
          settings: {
            isQueueNotificationActive: false,
          },
        },
        { mergeJson: ['settings'] },
      )
    })
  },

  down: async (queryInterface, Sequelize) => {
    // await queryInterface.removeColumn('accounts', 'settings.emailRequired')
  },
}
