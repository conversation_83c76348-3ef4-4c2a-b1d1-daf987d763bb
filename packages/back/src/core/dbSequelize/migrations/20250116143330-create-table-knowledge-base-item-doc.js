module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('knowledge_base_item_doc', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      knowledgeBaseItemId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'knowledge_base_item',
          key: 'id',
        },
      },
      docId: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    })
  },

  down: (queryInterface) => queryInterface.dropTable('knowledge_base_item_doc'),
}
