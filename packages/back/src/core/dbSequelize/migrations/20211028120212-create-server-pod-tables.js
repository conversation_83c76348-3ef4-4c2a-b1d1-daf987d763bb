module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.createTable('server_pod_types', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      cloud: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      serverSize: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      maxPerPod: {
        type: DataTypes.INTEGER,
        unique: false,
        allowNull: false,
        defaultValue: 100,
      },
      min: {
        type: DataTypes.INTEGER,
        unique: false,
        allowNull: false,
        defaultValue: 0,
      },
      desiredAvailable: {
        type: DataTypes.INTEGER,
        unique: false,
        allowNull: false,
        defaultValue: 0,
      },
      startPriority: {
        type: DataTypes.INTEGER,
        unique: false,
        allowNull: false,
        defaultValue: 0,
      },
      accountIdWhitelist: {
        type: DataTypes.JSONB,
        defaultValue: [],
        allowNull: false,
      },
      serviceIdWhitelist: {
        type: DataTypes.JSONB,
        defaultValue: [],
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.createTable('server_pods', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      address: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      version: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: true,
      },
      serviceIds: {
        type: DataTypes.JSONB,
        defaultValue: [],
        allowNull: false,
      },
      serverPodTypeId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'server_pod_types',
          key: 'id',
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('server_pods', ['name'])
    await queryInterface.addIndex('server_pods', ['serverPodTypeId'])
  },

  down: async (queryInterface) => {
    await queryInterface.removeIndex('server_pods', ['serverPodTypeId'])
    await queryInterface.removeIndex('server_pods', ['name'])
    await queryInterface.dropTable('server_pods')

    await queryInterface.dropTable('server_pod_types')
  },
}
