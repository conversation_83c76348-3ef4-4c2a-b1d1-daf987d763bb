'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const archivedUserIdsQuery = `
        SELECT id FROM "users" WHERE "archivedAt" IS NOT NULL
      `

      await queryInterface.sequelize.query(
        `
        UPDATE "contacts"
        SET "defaultUserId" = NULL
        WHERE "defaultUserId" IN (${archivedUserIdsQuery});
        `,
        { transaction },
      )
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
