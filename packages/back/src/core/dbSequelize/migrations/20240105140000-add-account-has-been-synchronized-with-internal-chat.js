import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
    UPDATE accounts
    SET settings = jsonb_set(settings, '{hasBeenSynchronizedWithInternalChat}', 'true', TRUE)
    WHERE settings->'flags'->>'internal-chat' is not null;
  `)
  },

  down: async (queryInterface, Sequelize) => {
    await sequelize.query(`
    UPDATE accounts
    SET settings = jsonb_set(settings, '{hasBeenSynchronizedWithInternalChat}', 'false', TRUE)
    WHERE settings->'flags'->>'internal-chat' is not null;
  `)
  },
}
