'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await queryInterface.sequelize.query(`
      UPDATE services
      SET data = jsonb_set(
        data,
        '{isManuallyDisconnected}',
        'true',
        true
      )
      WHERE type = 'whatsapp'
        AND "deletedAt" IS NULL
        AND (
          data->'status'->>'isConnected' IS NULL
          OR (data->'status'->>'isConnected')::boolean IS DISTINCT FROM true
        )
        AND (data->>'isManuallyDisconnected' IS NULL)
    `)
  },

  async down(queryInterface) {
    await queryInterface.sequelize.query(`
      UPDATE services
      SET data = data - 'isManuallyDisconnected'
      WHERE type = 'whatsapp'
        AND "deletedAt" IS NULL
        AND (
          data->'status'->>'isConnected' IS NULL
          OR (data->'status'->>'isConnected')::boolean IS DISTINCT FROM true
        )
        AND (data->>'isManuallyDisconnected' = 'true')
    `)
  },
}
