module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE tickets t
      SET "isOpen" = false
      FROM contacts c
      WHERE t."contactId" = c.id
        AND c."archivedAt" IS NOT NULL
        AND t."isOpen" = true;
    `)

    await queryInterface.sequelize.query(`
      UPDATE contacts c
      SET "currentTicketId" = null
      WHERE c."archivedAt" IS NOT NULL;
    `)
  },
}
