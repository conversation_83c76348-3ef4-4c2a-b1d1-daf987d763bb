import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.transaction(async (transaction) => {
      await sequelize.query(
        `
        create table if not exists caching.tickets_count (
          "accountId" uuid not null,
          "departmentId" uuid null,
          "userId" uuid null,
          "mineCount" bigint,
          "queueCount" bigint,
          "updatedAt" timestamptz
        );`,
        { transaction },
      )

      await sequelize.query(
        `
        create unique index if not exists "tickets_count_accountId_departmentId_userId"
          on caching.tickets_count("accountId", "departmentId", "userId");
        
        create index if not exists "tickets_count_accountId"
          on caching.tickets_count("accountId");

        create index if not exists "tickets_count_departmentId"
          on caching.tickets_count("departmentId");

        create index if not exists "tickets_count_userId"
          on caching.tickets_count("userId");`,
        { transaction },
      )

      await sequelize.query(
        `
        insert into caching.tickets_count( \
          "accountId", "departmentId", "userId", "mineCount", "queueCount", "updatedAt"
        )
        select
          c."accountId" as "accountId",
          t."departmentId" as "departmentId",
          t."userId" as "userId",
          sum(case when t."userId" is not null then 1 else 0 end) as "mineCount",
          sum(case when t."userId" is null then 1 else 0 end) as "queueCount",
          current_timestamp "updatedAt"
        from contacts c
        inner join tickets t
          on t.id = c."currentTicketId"
          and t."accountId" = c."accountId"
          and t."isOpen" = true
        where c."deletedAt" is null
          and c."visible" = true
          and not exists (
            select 1
            from caching.tickets_count tc
            where tc."accountId" = t."accountId"
            and coalesce(tc."departmentId",'********-0000-0000-0000-********0000') = coalesce(t."departmentId",'********-0000-0000-0000-********0000')
            and coalesce(tc."userId",'********-0000-0000-0000-********0000') = coalesce(t."userId",'********-0000-0000-0000-********0000')
            limit 1
          )
        group by c."accountId", t."departmentId", t."userId", current_timestamp;`,
        { transaction },
      )
    })
  },

  down: async (queryInterface, Sequelize) => {
    await sequelize.query(`drop table if exists caching.tickets_count;`)
  },
}
