import sequelize from '../../services/db/sequelize'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
      UPDATE campaigns
      SET status = 'done', "updatedAt" = now()
      WHERE status = 'processing'
      and "totalContactsImported" > "totalContacts";


      UPDATE campaigns
      SET status = 'import_error', "updatedAt" = now()
      WHERE status = 'importing_contacts'
      and "totalContactsImported" > "totalContacts";
      `)
  },

  down: async (queryInterface, Sequelize) => {},
}
