import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface) => {
    console.log(`=>Getting wrong services in messages`)
    const items = await queryInterface.sequelize.query(
      `
      select distinct m."contactId", c."serviceId"
      from messages m
      inner join contacts c 
        on c.id = m."contactId"
      left join services s 
        on s.id = m."serviceId" 
        and s.id = c."serviceId" 
      where s.id is null;
    `,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
        raw: true,
      },
    )

    console.log(`=>Fixing ${items.length} wrong contact/service relations`)
    await queuedAsyncMap(
      items,
      ({ contactId, serviceId }) =>
        queryInterface.sequelize.query(
          `
          update messages set "serviceId" = :serviceId
          where "contactId" = :contactId 
          and ("serviceId" <> :serviceId or "serviceId" is null);
        `,
          {
            replacements: {
              serviceId,
              contactId,
            },
          },
        ),
      20,
    )

    await queryInterface.sequelize.transaction(async (transaction) => {
      console.log(`=>Creating unique index`)
      await queryInterface.sequelize.query(
        `
        create unique index "contacts_id_serviceId_index" on contacts (id, "serviceId");
      `,
        { transaction },
      )

      console.log(`=>Creating composite foreign key`)
      await queryInterface.sequelize.query(
        `
        alter table messages add constraint "messages_contactId_serviceId_fk"
        foreign key ("contactId", "serviceId")
        references contacts("id", "serviceId") on update cascade on delete set null;
      `,
        { transaction },
      )

      console.log(`=>Creating check constraint to avoid null serviceId and contactId`)
      await queryInterface.sequelize.query(
        `
        alter table messages add constraint "messages_contactId_serviceId_check" 
        check (("serviceId" is not null and "contactId" is not null) or "createdAt" < :cutoffAt);
      `,
        {
          replacements: {
            cutoffAt: new Date(),
          },
          transaction,
        },
      )
    })
  },

  down: (queryInterface) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      console.log(`=>Dropping messages_contactId_serviceId_check constraint`)
      await queryInterface.sequelize.query(
        `
        alter table messages drop constraint "messages_contactId_serviceId_check";
      `,
        { transaction },
      )
      console.log(`=>Dropping messages_contactId_serviceId_fk constraint`)
      await queryInterface.sequelize.query(
        `
        alter table messages drop constraint "messages_contactId_serviceId_fk";
      `,
        { transaction },
      )
      console.log(`=>Dropping contacts_id_serviceId_index index`)
      await queryInterface.sequelize.query(
        `
        drop index "contacts_id_serviceId_index";
      `,
        { transaction },
      )
    })
  },
}
