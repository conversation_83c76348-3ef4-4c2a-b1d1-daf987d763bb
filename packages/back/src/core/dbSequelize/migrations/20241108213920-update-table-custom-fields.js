'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('custom_fields', 'showOnRegister', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    })
    await queryInterface.addColumn('custom_fields', 'required', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    })
    await queryInterface.addColumn('custom_fields', 'settings', {
      type: Sequelize.JSON,
      allowNull: true,
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('custom_fields', 'showOnRegister')
    await queryInterface.removeColumn('custom_fields', 'required')
    await queryInterface.removeColumn('custom_fields', 'settings')
  },
}
