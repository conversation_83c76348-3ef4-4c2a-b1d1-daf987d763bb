module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('questions', 'reasonMessage', {
      type: DataTypes.TEXT,
      allowNull: true,
    })

    const query = "ALTER TYPE enum_questions_type ADD VALUE 'csat'"
    await queryInterface.sequelize.query(query)
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('questions', 'reasonMessage')

    const query = "DELETE FROM pg_enum where enumlabel = 'csat'"
    await queryInterface.sequelize.query(query)
  },
}
