'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    queryInterface
      .describeTable({
        tableName: 'card_comments',
        schema: 'pipeline',
      })
      .then((tb) => {
        if (!tb.userId) {
          return queryInterface.addColumn(
            {
              tableName: 'card_comments',
              schema: 'pipeline',
            },
            'userId',
            { type: Sequelize.UUID },
          )
        }
      })
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      {
        tableName: 'card_comments',
        schema: 'pipeline',
      },
      'userId',
    )
  },
}
