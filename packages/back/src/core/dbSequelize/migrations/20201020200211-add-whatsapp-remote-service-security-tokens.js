import serviceResource from '../../resources/serviceResource'
import setupSequelize from '../../services/db/setupSequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import createToken from '../../services/random/createToken'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const services = await serviceResource.findMany({
      attributes: ['id', 'internalData'],
      where: {
        type: 'whatsapp-remote',
        'internalData.securityToken': { $eq: null },
      },
    })

    await queuedAsyncMap(services, async (service) => {
      return serviceResource.updateById(service.id, {
        internalData: {
          ...service.internalData,
          securityToken: await createToken(),
        },
      })
    })
  },

  down: (queryInterface, Sequelize) => {},
}
