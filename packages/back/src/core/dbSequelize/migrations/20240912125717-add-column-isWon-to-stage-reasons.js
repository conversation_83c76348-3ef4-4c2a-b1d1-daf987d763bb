'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    queryInterface
      .describeTable({
        tableName: 'stage_reasons',
        schema: 'pipeline',
      })
      .then((tb) => {
        if (!tb.isWon) {
          return queryInterface.addColumn(
            {
              tableName: 'stage_reasons',
              schema: 'pipeline',
            },
            'isWon',
            {
              type: Sequelize.BOOLEAN,
              default: false,
            },
          )
        }
      })
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      {
        tableName: 'stage_reasons',
        schema: 'pipeline',
      },
      'isWon',
    )
  },
}
