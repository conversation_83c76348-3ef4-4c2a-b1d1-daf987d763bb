module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'pipeline_automations',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        pipelineId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipelines',
            key: 'id',
          },
        },
        type: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        isArchived: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
        },
        archivedAt: {
          type: Sequelize.DATE,
        },
        config: {
          type: Sequelize.JSONB,
          allowNull: true,
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: (queryInterface) => queryInterface.dropTable({ tableName: 'pipeline_automations', schema: 'pipeline' }),
}
