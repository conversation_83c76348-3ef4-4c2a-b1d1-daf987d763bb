module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('contact_block_lists', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM,
        values: ['ready', 'processing', 'done', 'error'],
        defaultValue: 'ready',
        allowNull: false,
      },
      defaultDDI: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      saveCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      validCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      totalCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      userId: {
        type: Sequelize.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        allowNull: false,
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      createdAt: Sequelize.DATE,
      updatedAt: Sequelize.DATE,
      deletedAt: Sequelize.DATE,
    })
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.dropTable('contact_block_lists')
  },
}
