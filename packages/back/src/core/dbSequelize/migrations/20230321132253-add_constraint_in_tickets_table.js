module.exports = {
  up: async (queryInterface) => {
    const date = new Date().toISOString()

    const ddl = `alter table tickets 
    add constraint "ck_departmentId_not_null"
    check ("departmentId" is not null or "updatedAt" < '${date}')`

    await queryInterface.sequelize.query(ddl, { raw: true })
  },

  down: async (queryInterface) => {
    const ddl = 'alter table tickets drop constraint "ck_departmentId_not_null"'
    await queryInterface.sequelize.query(ddl, { raw: true })
  },
}
