module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      {
        tableName: 'pipelines',
        schema: 'pipeline',
      },
      'type',
      {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'sales',
      },
    )
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn(
      {
        tableName: 'pipelines',
        schema: 'pipeline',
      },
      'type',
    )
  },
}
