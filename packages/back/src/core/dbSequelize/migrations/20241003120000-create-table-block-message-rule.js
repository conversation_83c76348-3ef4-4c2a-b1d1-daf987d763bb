'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const dtBlockMessageRules = await queryInterface.tableExists('block_message_rules')

    if (!dtBlockMessageRules) {
      // Adicionar nova tabela de block_message_rules
      // Responsável por registrar cada regra do bloqueio de mensagens
      await queryInterface.createTable('block_message_rules', {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        type: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        priority: {
          // Prioridade (zero é a maior prioridade)
          type: DataTypes.INTEGER,
          defaultValue: 0,
          allowNull: false,
        },
        data: {
          // Informações sobre a regra de bloqueio
          type: DataTypes.JSONB,
          defaultValue: {},
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        accountId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
      })

      await Promise.all([
        queryInterface.addIndex('block_message_rules', ['priority']), // Index para ordenação
        queryInterface.addIndex('block_message_rules', ['accountId']), // Index na chave estrangeira
      ])
    }
  },

  async down(queryInterface) {
    const dtBlockMessageRules = await queryInterface.tableExists('block_message_rules')

    if (dtBlockMessageRules) {
      // Remove a tabela de block_message_rules
      await queryInterface.dropTable('block_message_rules')
    }
  },
}
