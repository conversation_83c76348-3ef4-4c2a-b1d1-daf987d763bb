import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    await sequelize.query(`
      delete from files
        where "attachedType" = 'contact.thumbnail'`)

    await sequelize.query(`
      create unique index if not exists "files_attachedId_attachedType_is_thumbAvatar" 
        on public.files using btree ("attachedId", "attachedType")
        where (("attachedType")::text = 'contact.thumbnail'::text)
        and "deletedAt" is null;`)
  },

  down: async (queryInterface, Sequelize) => {},
}
