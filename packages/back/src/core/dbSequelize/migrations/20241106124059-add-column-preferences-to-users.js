'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('users')

    if (!tableInfo?.preferences) {
      // Adicionar nova coluna de preferences na tabela de users
      // Responsável por armazenar as preferências de cada usuário na plataforma
      await queryInterface.addColumn('users', 'preferences', {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('users')

    if (tableInfo?.preferences) {
      // Remove a coluna de preferences na tabela de users
      await queryInterface.removeColumn('users', 'preferences')
    }
  },
}
