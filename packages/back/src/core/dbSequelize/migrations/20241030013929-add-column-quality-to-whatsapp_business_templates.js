module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable('whatsapp_business_templates')
    if (!tableInfo.quality) {
      await queryInterface.addColumn('whatsapp_business_templates', 'quality', {
        type: Sequelize.STRING,
        allowNull: true,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('whatsapp_business_templates')
    if (tableInfo.quality) {
      await queryInterface.removeColumn('whatsapp_business_templates', 'quality')
    }
  },
}
