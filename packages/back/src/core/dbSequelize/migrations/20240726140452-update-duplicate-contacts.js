'use strict'
const { QueryTypes } = require('sequelize')

module.exports = {
  up: async (queryInterface, _Sequelize) => {
    await queryInterface.sequelize.query(`
      CREATE INDEX IF NOT EXISTS contacts_lastContactMessageAt_idx ON contacts ("lastContactMessageAt" DESC NULLS LAST);
      CREATE INDEX IF NOT EXISTS contacts_accountid_visible_lastmessageat_idx ON contacts ("accountId", visible, "lastMessageAt" DESC NULLS LAST);
      CREATE INDEX IF NOT EXISTS messages_contactId_serviceId_idx ON messages ("contactId", "serviceId");
      CREATE INDEX IF NOT EXISTS files_deletedat_attachedtype_idx ON public.files ("deletedAt","attachedType");
    `)

    // Arquiva duplicados do whatsapp e sms
    await queryInterface.sequelize.query(`
     WITH
        phone_numbers as (
            WITH
                phone_prep AS (
                    SELECT
                        id,
                        SPLIT_PART ("idFromService", '@', 1) AS idFromServicePart
                    FROM
                        contacts
                ),
                whatsapp_services AS (
                    SELECT
                        id
                    FROM
                        services
                    WHERE
                        "type" LIKE 'whatsapp%' or "type" = 'sms-wavy'
                )
            SELECT
                c1.id,
                CASE
                    WHEN LEFT (idFromServicePart, 4) = '5555'
                    AND LENGTH (idFromServicePart) in (14, 15) THEN SUBSTRING(
                        idFromServicePart
                        FROM
                            3
                    )
                    ELSE idFromServicePart
                END AS phone_number,
                '55' AS ddi,
                SUBSTRING(
                    CASE
                        WHEN LEFT (idFromServicePart, 4) = '5555'
                        AND LENGTH (idFromServicePart) in (14, 15) THEN SUBSTRING(
                            idFromServicePart
                            FROM
                                3
                        )
                        ELSE idFromServicePart
                    END
                    FROM
                        3 FOR 2
                ) AS ddd,
                CASE
                    WHEN LENGTH (
                        SUBSTRING(
                            idFromServicePart
                            FROM
                                5
                        )
                    ) = 9 THEN '9'
                    ELSE NULL
                END AS ninth_digit,
                CASE
                    WHEN LENGTH (
                        SUBSTRING(
                            idFromServicePart
                            FROM
                                5
                        )
                    ) = 9 THEN SUBSTRING(
                        idFromServicePart
                        FROM
                            6
                    )
                    ELSE SUBSTRING(
                        idFromServicePart
                        FROM
                            5
                    )
                END AS phone_without_ninth,
                c1."serviceId",
                c1."accountId",
                c1."lastContactMessageAt",
                c1."lastMessageAt",
                c1."createdAt"
            FROM
                contacts c1
                JOIN phone_prep p ON c1.id = p.id
            WHERE
                c1."deletedAt" IS NULL
                AND c1."archivedAt" IS NULL
                AND (
                    LEFT (idFromServicePart, 2) = '55'
                    OR LEFT (idFromServicePart, 4) = '5555'
                )
                AND c1."serviceId" IN (
                    SELECT
                        id
                    FROM
                        whatsapp_services
                )
        ),
        MarkedForDeletion AS (
            SELECT
                id
            FROM
                (
                    SELECT
                        id,
                        "serviceId",
                        "accountId",
                        ddi,
                        ddd,
                        phone_without_ninth,
                        "lastContactMessageAt",
                        "lastMessageAt",
                        "createdAt",
                        ROW_NUMBER() OVER (
                            PARTITION BY
                                "serviceId",
                                "accountId",
                                ddi,
                                ddd,
                                phone_without_ninth
                            ORDER BY
                                "lastContactMessageAt" DESC NULLS LAST,
                                "lastMessageAt" DESC NULLS LAST,
                                "createdAt" DESC
                        ) AS rn
                    FROM
                        phone_numbers
                ) subquery
            WHERE
                subquery.rn > 1
        )
    UPDATE contacts
    SET
        "archivedAt" = NOW ()
    WHERE
        id IN (
            SELECT
                id
            FROM
                MarkedForDeletion
        );
    `)

    // Arquiva duplicados que não são whatsapp nem sms
    await queryInterface.sequelize.query(`
        with not_whatsapp_services as (
        select
            id
        from
            services
        where
            "type" not like 'whatsapp%' and "type" != 'sms-wavy'
        ),
        ranked_contacts as (
        select
            c.id,
            row_number() over (
                    partition by c."idFromService",
            "serviceId",
            "accountId"
        order by
            "lastMessageAt" desc nulls last,
            "lastContactMessageAt" desc nulls last,
            "createdAt" desc
                ) as rn
        from
            contacts c
        inner join not_whatsapp_services s
                on
            s.id = c."serviceId"
        where
            "archivedAt" is null
            and "deletedAt" is null
        )

        update
            contacts
        set
            "archivedAt" = NOW()
        where
            id in (
            select
                id
            from
                ranked_contacts
            where
                rn > 1
        );
    `)

    await queryInterface.sequelize.query(`
      DROP INDEX IF EXISTS contacts_idfromservice_without_nine_idx;
      
      DROP INDEX IF EXISTS contacts_id_from_service;

      DROP INDEX IF EXISTS public.contacts_idfromservice_idx;

      DROP INDEX IF EXISTS "contacts_idFromService_serviceId_deletedAt_unique";

      DROP INDEX IF EXISTS contacts_last_message_at;

      DROP INDEX IF EXISTS contacts_current_ticked_id;

      DROP INDEX IF EXISTS public.contacts_deletedat_index;
    `)

    await queryInterface.sequelize.query(
      `
       CREATE UNIQUE INDEX contacts_idfromservice_without_nine_idx
      ON public.contacts
      (
        (CASE
          WHEN LEFT("idFromService", 2) = '55' AND (LENGTH(regexp_replace("idFromService", ${queryInterface.sequelize.escape(
            '\\D',
          )}, '', 'g' )) = 12 OR (LENGTH(regexp_replace("idFromService", ${queryInterface.sequelize.escape(
        '\\D',
      )}, '', 'g' )) = 13 AND SUBSTRING("idFromService", 5, 1) = '9'))
          THEN (LEFT("idFromService", 4) || '%' || RIGHT(REGEXP_REPLACE("idFromService", ${queryInterface.sequelize.escape(
            '\\D',
          )}, '', 'g'), 8))
          ELSE "idFromService"
        END),
        "serviceId",
        "accountId"
      )
      WHERE "archivedAt" IS NULL AND "deletedAt" IS NULL;
    `,
      { type: QueryTypes.RAW },
    )
  },

  down: async (queryInterface, _Sequelize) => {},
}
