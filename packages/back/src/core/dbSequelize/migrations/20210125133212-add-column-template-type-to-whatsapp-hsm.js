module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface.describeTable('whatsapp_business_hsm').then(async (hsm) => {
      if (!hsm.templateType) {
        await queryInterface.addColumn('whatsapp_business_hsm', 'templateType', {
          type: Sequelize.DataTypes.STRING,
          defaultValue: 'text',
          allowNull: false,
        })
      }
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('whatsapp_business_hsm', 'templateType')
  },
}
