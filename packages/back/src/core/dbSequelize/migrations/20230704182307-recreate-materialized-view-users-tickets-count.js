module.exports = {
  up: async (queryInterface) => {
    await queryInterface.sequelize.query(`DROP MATERIALIZED VIEW if exists caching.users_tickets_count;`)

    await queryInterface.sequelize.query(`
      CREATE MATERIALIZED VIEW caching.users_tickets_count AS
      SELECT
        u.id as "userId",
        u.language,
        u."accountId",
        ud."departmentId",
        ARRAY(SELECT ur."roleId" FROM user_roles as ur WHERE ur."userId" = u.id) AS roles,
        tc."mineCount"
      FROM
        users u
      INNER JOIN
        user_departments AS ud
      ON
        ud."userId" = u.id
      LEFT JOIN
        caching.tickets_count tc
      ON
        tc."userId" = u.id
        AND tc."departmentId" = ud."departmentId"
      WHERE
        u."archivedAt" IS NULL
        AND u."deletedAt" IS NULL
        AND u.status IN ('online', 'away')
      GROUP BY
        u.id,
        ud."departmentId",
        tc."mineCount",
        u.language,
        u."accountId",
        "roles"
      ORDER BY
        tc."mineCount" desc;
    `)

    await queryInterface.sequelize.query(`refresh materialized view caching.tickets_count;`)
    await queryInterface.sequelize.query(`refresh materialized view caching.users_tickets_count;`)
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.query(`drop materialized view if exists caching.users_tickets_count;`)
  },
}
