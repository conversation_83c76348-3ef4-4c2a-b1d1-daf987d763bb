import serviceResource from '../../resources/serviceResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const allServicesSmsWavy = await serviceResource.findMany({
      attributes: ['id', 'data'],
      where: {
        type: 'sms-wavy',
      },
    })

    await queuedAsyncMap(allServicesSmsWavy, async (item) => {
      await serviceResource.updateById(item.id, {
        data: {
          ...item.data,
          smsWavy: {
            sync: true,
          },
        },
      })
    })
  },

  down: (queryInterface, Sequelize) => {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.dropTable('users');
    */
  },
}
