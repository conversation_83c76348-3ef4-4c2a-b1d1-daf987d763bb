module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(
      'card_comments',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        comment: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        cardId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'cards',
            key: 'id',
          },
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'card_comments', schema: 'pipeline' })
  },
}
