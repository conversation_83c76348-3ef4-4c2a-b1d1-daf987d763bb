'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await queryInterface.sequelize.query(
      `CREATE INDEX IF NOT EXISTS bots_contexts_bot_inactive_idx ON bots ((contexts->'@EVERY'->'triggers'->'BOT_INACTIVE'));`,
    )

    await queryInterface.sequelize.query('ANALYZE')
  },

  async down(queryInterface) {
    await queryInterface.sequelize.query(`DROP INDEX IF EXISTS bots_contexts_bot_inactive_idx;`)
  },
}
