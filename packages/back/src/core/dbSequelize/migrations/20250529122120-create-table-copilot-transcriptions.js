const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const existsCopilotTranscriptions = await queryInterface.tableExists('copilot_transcriptions')

    if (!existsCopilotTranscriptions) {
      await queryInterface.createTable('copilot_transcriptions', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
        ticketId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'tickets',
            key: 'id',
          },
        },
        userId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        finishedAt: {
          type: Sequelize.DataTypes.DATE,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DataTypes.DATE,
          allowNull: true,
        },
      })

      return queryInterface.addIndex('copilot_transcriptions', ['ticketId'])
    }
  },

  down: (queryInterface) => queryInterface.dropTable('copilot_transcriptions'),
}
