module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.createTable('jobs', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      key: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      data: {
        type: DataTypes.JSONB,
        defaultValue: {},
        allowNull: false,
      },
      attempts: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
      runsAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('jobs', ['accountId'])
    await queryInterface.addIndex('jobs', ['name'])
    await queryInterface.addIndex('jobs', ['key'])
    await queryInterface.addIndex('jobs', ['runsAt'])
    await queryInterface.addIndex('jobs', ['expiresAt'])
  },

  down: async (queryInterface) => {
    await queryInterface.removeIndex('jobs', ['expiresAt'])
    await queryInterface.removeIndex('jobs', ['runsAt'])
    await queryInterface.removeIndex('jobs', ['key'])
    await queryInterface.removeIndex('jobs', ['name'])
    await queryInterface.removeIndex('jobs', ['accountId'])
    await queryInterface.dropTable('jobs')
  },
}
