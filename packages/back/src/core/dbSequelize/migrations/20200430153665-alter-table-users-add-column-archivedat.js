module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize
    return queryInterface.describeTable('users').then((users) => {
      if (!users.archivedAt) {
        return queryInterface.addColumn('users', 'archivedAt', {
          type: DataTypes.DATE,
          allowNull: true,
          defaultValue: null,
        })
      }
      return Promise.resolve(true)
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'archivedAt')
  },
}
