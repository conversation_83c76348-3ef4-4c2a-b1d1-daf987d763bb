module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable(
      'pipeline_stages',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        position: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        pipelineId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'pipeline_stages', schema: 'pipeline' })
  },
}
