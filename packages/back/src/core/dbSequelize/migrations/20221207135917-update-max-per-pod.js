import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    await sequelize.query(`
        UPDATE server_pod_types SET "maxPerPod" = 100 WHERE "name" = 'local'`)
  },

  down: async (queryInterface, Sequelize) => {
    await setupSequelize()
    await sequelize.query(`
        UPDATE server_pod_types SET "maxPerPod" = 1 WHERE "name" = 'local'`)
  },
}
