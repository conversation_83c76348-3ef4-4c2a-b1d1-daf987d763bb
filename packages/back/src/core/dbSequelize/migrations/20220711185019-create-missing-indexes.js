import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const indexes = [
      ['messages_hsmid_index', 'messages', '("hsmId")'],
      ['contacts_acceptancetermid_index', 'contacts', '("acceptanceTermId")'],
      ['services_webhook_fails_contactid_index', 'services_webhook_fails', '("contactId")'],
      ['services_webhook_fails_accountid_index', 'services_webhook_fails', '("accountId")'],
      ['services_webhook_fails_serviceid_index', 'services_webhook_fails', '("serviceId")'],
      ['absences_accountid_index', 'absences', '("accountId")'],
      ['absences_userid_index', 'absences', '("userId")'],
      ['holidays_accountid_index', 'holidays', '("accountId")'],
      ['acceptance_terms_accountid_index', 'acceptance_terms', '("accountId")'],
      ['acceptance_terms_fileId_index', 'acceptance_terms', '("fileId")'],
      [
        'files_attachedId_attachedType_is_messageThumbnail',
        'files',
        '("attachedId", "attachedType", \
        COALESCE("deletedAt", to_timestamp(0::double precision))) \
        where (("attachedType")::text = \'message.thumbnail\'::text)',
      ],
      [
        'files_attachedId_attachedType_is_messageFiles',
        'files',
        '("attachedId", "attachedType", \
        COALESCE("deletedAt", to_timestamp(0::double precision))) \
        where (("attachedType")::text = \'message.files\'::text)',
      ],
      [
        'files_attachedId_attachedType_is_hsmFileTemplate',
        'files',
        '("attachedId", "attachedType", \
        COALESCE("deletedAt", to_timestamp(0::double precision))) \
        where (("attachedType")::text = \'hsm.file\'::text)',
      ],
      ['whatsapp_business_hsm_serviceId_index', 'whatsapp_business_hsm', '("serviceId")'],
      ['departments_distributionId_index', 'departments', '("distributionId")'],
      ['campaigns_defaultDepartmentId_index', 'campaigns', '("defaultDepartmentId")'],
      ['campaigns_defaultUserId_index', 'campaigns', '("defaultUserId")'],
    ]

    await queuedAsyncMap(indexes, (item) => {
      const query = `create index if not exists "${item[0]}" on "${item[1]}" ${item[2]};`
      queryInterface.sequelize.query(query, { raw: true })
    })
  },
  down: async (queryInterface, Sequelize) => {},
}
