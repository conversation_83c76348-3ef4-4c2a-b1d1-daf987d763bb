import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
    UPDATE accounts
    SET "promptAiFinalize" = 'Resuma a conversa de forma clara e objetiva, destacando apenas os pontos discutidos e conclusões. Não inicie com "resumo" ou introduções. Não inclua informações ou diálogos fora da conversa. Limite o resumo a 400 caracteres.'
     , "promptAiTransfer" = 'Resuma a conversa de forma clara e objetiva, destacando apenas os pontos discutidos e conclusões. Não inicie com "resumo" ou introduções. Não inclua informações ou diálogos fora da conversa. Limite o resumo a 200 caracteres.'
    WHERE settings->'flags'->>'enable-smart-summary' is not null;
  `)
  },
}
