import accountResource from '../../resources/accountResource'
import setupSequelize from '../../services/db/setupSequelize'
import iteratePaginated from '../../utils/iteratePaginated'

module.exports = {
  up: async () => {
    await setupSequelize()

    await iteratePaginated(
      ({ page }) =>
        accountResource.findManyPaginated({
          attributes: ['id'],
          page: 1,
          perPage: 500,
          noAccountId: true,
        }),
      async (account) =>
        accountResource.updateById(
          account.id,
          {
            settings: {
              changeUserPasswordOnFirstAccess: false,
              userPasswordCreationMethod: 'manual',
            },
          },
          { mergeJson: ['settings'] },
        ),
    )
  },

  down: async () => {},
}
