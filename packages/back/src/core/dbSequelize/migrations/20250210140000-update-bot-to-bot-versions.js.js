'use strict'

import setupSequelize from '../../services/db/setupSequelize'
import botResource from '../../resources/botResource'
import botVersionResource from '../../resources/botVersionResource'
import iteratePaginated from '../../utils/iteratePaginated'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    await setupSequelize()

    // Criar registro inicial de versionamento do bot
    await iteratePaginated(
      ({ page }) =>
        botResource.findManyPaginated({
          page,
          perPage: 10,
          noAccountId: true,
          where: {
            currentBotVersionId: null,
          },
        }),
      async (model) => {
        const result = await botVersionResource.create({
          status: 'published',
          contexts: model.contexts || {},
          flowJson: model.flowJson || {},
          settings: model.settings || {},
          publishedAt: new Date(),
          botId: model.id,
          accountId: model.accountId,
        })

        await model.update({
          currentBotVersionId: result.id,
        })
      },
    )
  },

  async down() {
    // Não realizar ação
  },
}
