module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface
      .createTable('interactive_message_departments', {
        interactiveMessageId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'interactive_messages',
            key: 'id',
          },
        },
        departmentId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'departments',
            key: 'id',
          },
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      })
      .then(() =>
        Promise.all([
          queryInterface.addIndex('interactive_message_departments', ['interactiveMessageId']),
          queryInterface.addIndex('interactive_message_departments', ['departmentId']),
        ]),
      )
  },

  down: (queryInterface) => {
    return queryInterface.dropTable('interactive_message_departments')
  },
}
