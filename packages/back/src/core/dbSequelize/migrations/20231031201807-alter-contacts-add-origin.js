'use strict'

const { ContactOrigin } = require('../models/Contact')

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.describeTable('contacts').then((contacts) => {
      if (!contacts.origin) {
        return queryInterface.addColumn('contacts', 'origin', {
          type: Sequelize.ENUM,
          values: Object.values(ContactOrigin),
          allowNull: true,
        })
      }
      return true
    })
  },

  async down(queryInterface) {
    return queryInterface.describeTable('contacts').then((contacts) => {
      if (contacts.origin) {
        return queryInterface.removeColumn('contacts', 'origin')
      }
      return true
    })
  },
}
