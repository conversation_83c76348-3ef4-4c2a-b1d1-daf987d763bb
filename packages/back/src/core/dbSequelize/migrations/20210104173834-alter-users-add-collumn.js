module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.describeTable('users').then((tableDefinition) => {
      if (!tableDefinition.data) {
        queryInterface.addColumn('users', 'data', {
          type: DataTypes.JSONB,
          allowNull: true,
          defaultValue: {},
        })
      }
      return true
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'data')
  },
}
