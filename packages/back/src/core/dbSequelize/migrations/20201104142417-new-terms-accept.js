module.exports = {
  up: async (queryInterface, Sequelize) =>
    await queryInterface.createTable('acceptanceTerms', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      fileId: {
        type: Sequelize.UUID,
        references: {
          model: 'files',
          key: 'id',
        },
      },
      name: {
        type: Sequelize.STRING,
      },
      textField: {
        type: Sequelize.TEXT,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    }),

  down: async (queryInterface, Sequelize) => await queryInterface.dropTable('acceptanceTerms'),
}
