import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'
import messageRepository from '../repositories/messageRepository'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    await sequelize
      .query(
        ` with activedup_messages as (
          select m.id,
               row_number() over (
                 partition by m."contactId", m."idFromService"
                 order by m."updatedAt" desc
               ) as "rowId"
          from messages m
          inner join (
            select 
              "contactId",
              "idFromService"
            from messages
            where "deletedAt" is null
            and "idFromService" is not null
            group by "contactId", "idFromService"
            having count(1) > 1
          ) dm on dm."contactId" = m."contactId"
            and dm."idFromService" = m."idFromService" 
          where m."deletedAt" is null
        )
        select distinct dm.id
        from activedup_messages dm
        where dm."rowId" > 1`,
      )
      .then((result) => {
        const messages = Object.values(result[0]).map((msg) => msg.id)
        if (!messages.length) return null
        console.log(`=> ${messages.length} duplicate messages were found.`)
        console.log(`=> Destroying duplicate messages.`)
        return messageRepository.bulkDestroy({
          where: {
            id: { $in: messages },
          },
        })
      })
      .then(() => {
        console.log(`=> Creating unique index messages_idfromservice_index.`)
        return sequelize.query(
          `create unique index if not exists "messages_idfromservice_index" 
            on messages ("idFromService", "contactId")
            where "deletedAt" is null
            and "idFromService" is not null
          `,
        )
      })
  },

  down: async (queryInterface, Sequelize) => {
    await setupSequelize()
    console.log(`=> Destroying unique index messages_idfromservice_index.`)
    await sequelize.query(`drop index "messages_idfromservice_index"`)
  },
}
