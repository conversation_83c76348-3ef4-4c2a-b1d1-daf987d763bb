'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`create extension if not exists pg_trgm;`)

    await queryInterface.sequelize.query(
      `
      create index if not exists contacts_filter_name_index on contacts using gin (
        ("accountId"::text) gin_trgm_ops, name gin_trgm_ops, "internalName" gin_trgm_ops,
        "alternativeName" gin_trgm_ops, ("data"#>>'{number}') gin_trgm_ops)
      where "deletedAt" is null and visible = true;
    `,
    )
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`drop index if exists contacts_filter_name_index`)
  },
}
