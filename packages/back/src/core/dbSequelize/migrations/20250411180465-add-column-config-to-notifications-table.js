'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('notifications')

    if (!tableInfo?.config) {
      await queryInterface.addColumn('notifications', 'config', {
        type: DataTypes.JSONB,
        allowNull: true,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('notifications')

    if (tableInfo?.config) {
      await queryInterface.removeColumn('notifications', 'config')
    }
  },
}
