module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('terms', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      title: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      text: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      expirationDate: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      version: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      agreementDate: {
        type: Sequelize.DATEONLY,
        allowNull: true,
      },
      agnusId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        allowNull: true,
      },
      createdAt: Sequelize.DATE,
      updatedAt: Sequelize.DATE,
    })

    return queryInterface.addIndex('terms', ['agnusId'])
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable('terms')
  },
}
