'use strict'

import { Op } from 'sequelize'
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date()

    await queryInterface.addIndex('campaign_messages_progress', ['contactId', 'campaignMessageId', 'accountId'], {
      where: {
        createdAt: {
          [Op.gt]: now,
        },
      },
      unique: true,
    })
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('campaign_messages_progress', ['contactId', 'campaignMessageId', 'accountId'], {
      unique: true,
    })
  },
}
