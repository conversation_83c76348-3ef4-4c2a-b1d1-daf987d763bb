module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('contact_block_list_items', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      idFromService: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      contactBlockListId: {
        type: Sequelize.UUID,
        references: {
          model: 'contact_block_lists',
          key: 'id',
        },
        allowNull: false,
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      createdAt: Sequelize.DATE,
      updatedAt: Sequelize.DATE,
    })
    await queryInterface.sequelize.query(`
      CREATE UNIQUE INDEX contact_block_list_items_idfromservice_idx ON public.contact_block_list_items ("idFromService","contactBlockListId","accountId");
    `)
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('contact_block_list_items')
  },
}
