module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('contacts', 'acceptedTermAt', {
      type: DataTypes.DATE,
      allowNull: true,
    })

    await queryInterface.addColumn('contacts', 'acceptanceTermId', {
      type: Sequelize.UUID,
      references: {
        model: 'acceptanceTerms',
        key: 'id',
      },
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('contacts', 'acceptedTermAt')
    await queryInterface.removeColumn('contacts', 'acceptanceTermId')
  },
}
