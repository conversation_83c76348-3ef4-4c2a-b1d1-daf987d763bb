'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('accounts', 'alias', {
      type: Sequelize.STRING,
      allowNull: true,
    })

    await queryInterface.addIndex('accounts', ['alias'])
  },

  async down(queryInterface) {
    await queryInterface.removeIndex('accounts', ['alias'])
    await queryInterface.removeColumn('accounts', 'alias')
  },
}
