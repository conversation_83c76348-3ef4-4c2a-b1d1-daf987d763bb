module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE contacts c
      SET "archivedAt" = now()
      WHERE c."idFromService" IS NULL
        AND c."deletedAt" IS NULL
        AND c.data->>'number' IS NOT NULL
        AND c.data->>'number' <> ''
        AND c.data->>'number' IN (
          SELECT c2.data->>'number'
          FROM contacts c2
          WHERE c2."idFromService" IS NOT NULL
            AND c2."serviceId" = c."serviceId"
            AND c2."deletedAt" IS NULL
            AND c2.data->>'number' IS NOT NULL
            AND c2.data->>'number' <> ''
        );
    `)

    await queryInterface.sequelize.query(`
      WITH dups AS (
        SELECT
          c.id,
          c.data->>'number' AS number,
          ROW_NUMBER() OVER (
            PARTITION BY c.data->>'number', c."serviceId"
            ORDER BY c."lastMessageAt" DESC NULLS LAST, c.id ASC
          ) AS rn
        FROM contacts c
        WHERE c."isGroup" = false
          AND c."deletedAt" IS NULL
          AND c.data->>'number' IS NOT NULL
          AND c.data->>'number' <> ''
          AND c."idFromService" IS NULL
      )
      UPDATE contacts t
      SET "archivedAt" = now()
      FROM dups
      WHERE t.id = dups.id
        AND dups.rn > 1;
    `)

    await queryInterface.sequelize.query(`
      WITH principal AS (
        SELECT
          c.id,
          c.data->>'number' AS number,
          ROW_NUMBER() OVER (
            PARTITION BY c.data->>'number', c."serviceId"
            ORDER BY c."lastMessageAt" DESC NULLS LAST, c.id ASC
          ) AS rn,
          c."lastMessageAt"
        FROM contacts c
        WHERE c."isGroup" = false
          AND c."deletedAt" IS NULL
          AND c."archivedAt" IS NULL
          AND c."idFromService" IS NULL
          AND c.data->>'number' IS NOT NULL
          AND c.data->>'number' <> ''
      )
      UPDATE contacts t
      SET
        "archivedAt" = CASE
          WHEN principal."lastMessageAt" IS NULL
            THEN now()
          WHEN EXISTS (
            SELECT 1
            FROM contacts x
            WHERE x."id" <> t."id"
              AND x."serviceId" = t."serviceId"
              AND x."deletedAt" IS NULL
              AND x."archivedAt" IS NULL
              AND x."idFromService" = (principal.number || '@c.us')
          )
          THEN now()
          ELSE t."archivedAt"
        END,
        "idFromService" = CASE
          -- Só atualiza se:
          -- 1) principal."lastMessageAt" NÃO é nulo
          -- 2) NÃO existe outro contato com o mesmo idFromService
          WHEN principal."lastMessageAt" IS NOT NULL
            AND NOT EXISTS (
              SELECT 1
              FROM contacts x
              WHERE x."serviceId" = t."serviceId"
                AND x."accountId" = t."accountId"
                AND x."deletedAt" IS NULL
                AND x."archivedAt" IS NULL
				AND x."idFromService" LIKE (
  					left(principal.number, 4) || '%' || right(principal.number, 8) || '@c.us')
            )
          THEN principal.number || '@c.us'
          ELSE t."idFromService"
        END
      FROM principal
      WHERE t.id = principal.id
        AND principal.rn = 1;
    `)

    await queryInterface.sequelize.query(`
      WITH group_dups AS (
        SELECT
          c.id,
          c.data->>'number' AS number,
          ROW_NUMBER() OVER (
            PARTITION BY c.data->>'number', c."serviceId"
            ORDER BY c."lastMessageAt" DESC NULLS LAST, c.id ASC
          ) AS rn
        FROM contacts c
        WHERE c."isGroup" = true
          AND c."deletedAt" IS NULL
          AND c.data->>'number' IS NOT NULL
          AND c.data->>'number' <> ''
          AND c."idFromService" IS NULL
      )
      UPDATE contacts t
      SET "archivedAt" = now()
      FROM group_dups
      WHERE t.id = group_dups.id
        AND group_dups.rn > 1;
    `)

    await queryInterface.sequelize.query(`
      WITH group_principal AS (
        SELECT
          c.id,
          c.data->>'number' AS number,
          ROW_NUMBER() OVER (
            PARTITION BY c.data->>'number', c."serviceId"
            ORDER BY c."lastMessageAt" DESC NULLS LAST, c.id ASC
          ) AS rn,
          c."lastMessageAt"
        FROM contacts c
        WHERE c."isGroup" = true
          AND c."deletedAt" IS NULL
          AND c."archivedAt" IS NULL
          AND c."idFromService" IS NULL
          AND c.data->>'number' IS NOT NULL
          AND c.data->>'number' <> ''
      )
      UPDATE contacts t
      SET
        "idFromService" = CASE
          WHEN group_principal."lastMessageAt" IS NOT NULL
          THEN group_principal.number || '@g.us'
          ELSE NULL
        END,
        "archivedAt" = CASE
          WHEN group_principal."lastMessageAt" IS NULL
            THEN now()
          ELSE t."archivedAt"
        END
      FROM group_principal
      WHERE t.id = group_principal.id
        AND group_principal.rn = 1;
    `)
    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1
            FROM information_schema.table_constraints
           WHERE constraint_name = 'ck_contacts_idfromservice_isnull'
             AND table_name = 'contacts'
        ) THEN
          ALTER TABLE "contacts"
          ADD CONSTRAINT "ck_contacts_idfromservice_isnull"
          CHECK ("updatedAt" < '${new Date().toISOString()}' OR "idFromService" IS NOT NULL);
        END IF;
      END
      $$;
    `)
  },

  async down(queryInterface) {
    await queryInterface.sequelize.query(`
      ALTER TABLE "contacts"
      DROP CONSTRAINT IF EXISTS "ck_contacts_idfromservice_isnull";
    `)
  },
}
