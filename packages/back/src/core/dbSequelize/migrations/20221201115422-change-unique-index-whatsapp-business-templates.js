import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
      alter table "whatsapp_business_templates"
      drop constraint if exists "whatsapp_business_templates_name_serviceId_accountId_key";
    `)

    await sequelize.query(`
      create unique index if not exists "whatsapp_business_templates_name_serviceId_accountId_index" 
      on whatsapp_business_templates ("name", "serviceId", "accountId")
      where "archivedAt" is null and "deletedAt" is null;    
    `)
  },

  down: async (queryInterface, Sequelize) => {},
}
