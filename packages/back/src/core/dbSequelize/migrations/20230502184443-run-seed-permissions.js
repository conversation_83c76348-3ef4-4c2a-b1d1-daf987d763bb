import { execSync } from 'child_process'

const root = process.env.NODE_ENV === 'production' ? 'dist' : 'src'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await execSync(`node ${root}/scripts seed:permissions`, {
      stdio: 'inherit',
    })

    await execSync(`node ${root}/scripts attach-all-permissions-to-admin`, {
      stdio: 'inherit',
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
