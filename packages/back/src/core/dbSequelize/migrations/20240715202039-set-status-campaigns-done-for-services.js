import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
    UPDATE campaigns c
    SET status = 'done'
    WHERE c.id IN
        (SELECT c2.id
         FROM campaigns c2
         INNER JOIN services s ON s.id = c2."serviceId"
         WHERE c2.status = 'processing'
           AND c2."createdAt" < now() - interval '1' DAY
           AND s."archivedAt" is null 
           AND s."deletedAt" is null 
           );
  `)
  },

  down: async (queryInterface, Sequelize) => {},
}
