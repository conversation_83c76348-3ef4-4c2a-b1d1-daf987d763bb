import sequelize from '../../services/db/sequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import answersResource from '../../resources/answersResource'

module.exports = {
  up: async () => {
    //Essa query busca todos os registros que possuem duplicação na base mas recebendo somente o que possui a última alteração. Esse registro será mantido na base de dados.
    let duplicateAnswers = await sequelize.query(
      `
        SELECT "ticketId", "questionId", max("updatedAt") AS "updatedAt"
        FROM answers
        WHERE "deletedAt" IS NULL
        GROUP BY "ticketId", "questionId"
        HAVING count(1) > 1
      `,
    )

    await queuedAsyncMap(duplicateAnswers?.[0], async (answer) => {
      await answersResource.bulkDestroy({
        where: {
          updatedAt: { $ne: answer?.updatedAt },
          ticketId: answer?.ticketId,
          questionId: answer?.questionId,
        },
      })
    })

    //Essa segunda query busca todos os registros que possuem duplicação na base, inclusive com a mesma data de alteração. Foi criada duas queries devido ao processamento
    //da query utilizando having ser muita mais rápida, assim já elimina a maior parte dos registros.
    duplicateAnswers = await sequelize.query(
      `
        SELECT A."id", A."ticketId", A."questionId"
        FROM answers A 
        WHERE A."deletedAt" IS NULL
        AND (SELECT COUNT(1) FROM answers B WHERE A."ticketId" = B."ticketId" AND A."questionId" = B."questionId" AND B."deletedAt" IS NULL) > 1
      `,
    )

    if (duplicateAnswers?.[0].length > 0) {
      let deleted = []
      await queuedAsyncMap(
        duplicateAnswers?.[0],
        async (answer) => {
          if (!deleted.includes(answer.ticketId + '_' + answer.questionId)) {
            deleted.push(answer.ticketId + '_' + answer.questionId)
            await answersResource.bulkDestroy({
              where: {
                id: { $ne: answer?.id },
                questionId: answer?.questionId,
                ticketId: answer?.ticketId,
              },
            })
          }
        },
        1,
      )
    }

    await sequelize.query(`
      create unique index if not exists "answer_ticket_id_question_id" 
      on answers ("ticketId", "questionId")
      where "deletedAt" is null;    
    `)
  },

  down: async () => {
    await sequelize.query(`drop index if exists "answer_ticket_id_question_id"`)
  },
}
