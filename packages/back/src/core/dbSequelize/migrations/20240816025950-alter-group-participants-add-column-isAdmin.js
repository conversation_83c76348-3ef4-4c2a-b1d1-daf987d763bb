'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('group_participants', 'isAdmin', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    })

    await queryInterface.addColumn('group_participants', 'isSuperAdmin', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('group_participants', 'isAdmin')
    await queryInterface.removeColumn('group_participants', 'isSuperAdmin')
  },
}
