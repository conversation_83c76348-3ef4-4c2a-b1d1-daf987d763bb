import sequelize from 'sequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import roleResource from '../../resources/roleResource'
import permissionResource from '../../resources/permissionResource'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const permission = await permissionResource.findOneByName('contacts.view.number')

    const roles = await roleResource.findMany()

    await queuedAsyncMap(roles, async ({ id }) => {
      const hasPermission = Number(
        (
          await queryInterface.sequelize.query(
            `select count(*) from role_permissions where "roleId" = '${id}' and "permissionId" = '${permission.id}'`,
            { type: sequelize.QueryTypes.SELECT },
          )
        )[0].count,
      )

      const date = new Date().toISOString()

      if (!hasPermission) {
        await queryInterface.sequelize.query(`
        insert into role_permissions ("roleId", "permissionId", "createdAt", "updatedAt") values('${id}','${permission.id}', '${date}', '${date}') 
        `)
      }
    })
  },

  down: (queryInterface, Sequelize) => {},
}
