'use strict'

import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import iteratePaginated from '../../utils/iteratePaginated'
import pipelineRepository from '../../dbSequelize/repositories/pipelineRepository'
import pipelineStageRepository from '../../dbSequelize/repositories/pipelineStageRepository'
import cardRepository from '../../dbSequelize/repositories/cardRepository'

module.exports = {
  async up(queryInterface) {
    const pipelines = await pipelineRepository.findMany({ attributes: ['id', 'accountId'], paranoid: false })

    await queuedAsyncMap(
      pipelines,
      async (pipeline) => {
        const stages = await pipelineStageRepository.findMany({
          attributes: ['id'],
          where: { pipelineId: pipeline?.id },
          paranoid: false,
        })

        await queuedAsyncMap(
          stages,
          async (stage) => {
            let order = 1

            await iteratePaginated(
              ({ page }) =>
                cardRepository.findManyPaginated({
                  where: {
                    pipelineId: pipeline?.id,
                    pipelineStageId: stage?.id,
                    accountId: pipeline?.accountId,
                    isArchived: false,
                  },
                  page: 1,
                  perPage: 50,
                }),
              async (model) => {
                await cardRepository.update(model, { order })
                order++
              },
              1,
            )
          },
          1,
        )
      },
      1,
    )
  },

  async down() {},
}
