module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('campaigns', 'totalContacts', {
      type: DataTypes.INTEGER,
      allowNull: true,
    })

    await queryInterface.addColumn('campaigns', 'totalContactsImported', {
      type: DataTypes.INTEGER,
      allowNull: true,
    })

    await queryInterface.addColumn('campaigns', 'totalValidContacts', {
      type: DataTypes.INTEGER,
      allowNull: true,
    })

    await queryInterface.addColumn('campaigns', 'mustOpenTicket', {
      type: DataTypes.BOOLEAN,
      default: false,
    })

    await queryInterface.addColumn('campaigns', 'defaultDepartmentId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id',
      },
    })

    await queryInterface.addColumn('campaigns', 'defaultUserId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('campaigns', 'totalContacts')
    await queryInterface.removeColumn('campaigns', 'totalContactsImported')
    await queryInterface.removeColumn('campaigns', 'totalValidContacts')
    await queryInterface.removeColumn('campaigns', 'mustOpenTicket')
    await queryInterface.removeColumn('campaigns', 'defaultDepartmentId')
    await queryInterface.removeColumn('campaigns', 'defaultUserId')
  },
}
