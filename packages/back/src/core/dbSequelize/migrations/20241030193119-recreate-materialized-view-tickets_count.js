module.exports = {
  up: async (queryInterface) => {
    await queryInterface.sequelize.query(`DROP MATERIALIZED VIEW if exists caching.users_tickets_count;`)
    await queryInterface.sequelize.query(`DROP MATERIALIZED VIEW if exists caching.tickets_count;`)

    await queryInterface.sequelize.query(`
      create materialized view caching.tickets_count as 
      select
        c."accountId",
        t."departmentId",
        t."userId",
        sum(
              case
                  when t."userId" is not null then 1
                  else 0
              end) as "mineCount",
        sum(
              case
                  when t."userId" is null then 1
                  else 0
              end) as "queueCount",
        CURRENT_TIMESTAMP as "updatedAt",
        c."isGroup"
      from
        tickets t
      join contacts c on
        c."currentTicketId" = t.id
        and c."accountId" = t."accountId"
      join services s on
        s.id = c."serviceId"
        and s."deletedAt" is null
        and s."archivedAt" is null
      where
        c."accountId" = t."accountId"
        and c."deletedAt" is null
        and c.visible = true
        and t."isOpen" = true
      group by
        c."accountId",
        t."departmentId",
        t."userId",
        (CURRENT_TIMESTAMP),
        c."isGroup";
    `)

    await queryInterface.sequelize.query(`
      CREATE MATERIALIZED VIEW caching.users_tickets_count AS
      SELECT
        u.id as "userId",
        u.language,
        u."accountId",
        ud."departmentId",
        ARRAY(SELECT ur."roleId" FROM user_roles as ur WHERE ur."userId" = u.id) AS roles,
        SUM(tc."mineCount") as "mineCount"
      FROM
        users u
      INNER JOIN
        user_departments AS ud
      ON
        ud."userId" = u.id
      LEFT JOIN
        caching.tickets_count tc
      ON
        tc."userId" = u.id
        AND tc."departmentId" = ud."departmentId"
      WHERE
        u."archivedAt" IS NULL
        AND u."deletedAt" IS NULL
        AND u.status IN ('online', 'away')
      GROUP BY
        u.id,
        u.language,
        u."accountId",
        ud."departmentId",
        tc."mineCount"
      ORDER BY
        tc."mineCount" desc;
    `)

    await queryInterface.sequelize.query(`refresh materialized view caching.tickets_count;`)
    await queryInterface.sequelize.query(`refresh materialized view caching.users_tickets_count;`)
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.query(`DROP MATERIALIZED VIEW if exists caching.users_tickets_count;`)
    await queryInterface.sequelize.query(`DROP MATERIALIZED VIEW if exists caching.tickets_count;`)

    await queryInterface.sequelize.query(`
       create materialized view caching.tickets_count as 
        select
          c."accountId" as "accountId",
          t."departmentId" as "departmentId",
          t."userId" as "userId",
          sum(case when t."userId" is not null then 1 else 0 end) as "mineCount",
          sum(case when t."userId" is null then 1 else 0 end) as "queueCount",
          current_timestamp "updatedAt"
        from
          tickets t
        inner join 
          contacts c
        on
          c."currentTicketId" = t."id"
          and c."accountId" = t."accountId"
        inner join
          services s
        on
          s.id = c."serviceId"
          and s."deletedAt" is null
          and s."archivedAt" is null
        where
          c."accountId" = t."accountId"
          and c."deletedAt" is null
          and c."visible" = true
          and t."isOpen" = true
        group by
          c."accountId",
          t."departmentId",
          t."userId",
          current_timestamp;
  `)

    await queryInterface.sequelize.query(`
      CREATE MATERIALIZED VIEW caching.users_tickets_count AS
      SELECT
        u.id as "userId",
        u.language,
        u."accountId",
        ud."departmentId",
        ARRAY(SELECT ur."roleId" FROM user_roles as ur WHERE ur."userId" = u.id) AS roles,
        tc."mineCount"
      FROM
        users u
      INNER JOIN
        user_departments AS ud
      ON
        ud."userId" = u.id
      LEFT JOIN
        caching.tickets_count tc
      ON
        tc."userId" = u.id
        AND tc."departmentId" = ud."departmentId"
      WHERE
        u."archivedAt" IS NULL
        AND u."deletedAt" IS NULL
        AND u.status IN ('online', 'away')
      GROUP BY
        u.id,
        u.language,
        u."accountId",
        ud."departmentId",
        tc."mineCount"
      ORDER BY
        tc."mineCount" desc;
    `)

    await queryInterface.sequelize.query(`refresh materialized view caching.tickets_count;`)
    await queryInterface.sequelize.query(`refresh materialized view caching.users_tickets_count;`)
  },
}
