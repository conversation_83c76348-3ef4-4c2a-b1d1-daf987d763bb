import accountResource from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const accounts = await accountResource.findMany({
      attributes: ['id', 'settings'],
    })

    return queuedAsyncMap(accounts, async (account) => {
      await accountResource.updateById(
        account.id,
        {
          settings: {
            campaign: {
              ...account.settings.campaign,
              'whatsapp-business': account.settings.campaign['whatsapp-business'] || false,
            },
          },
        },
        { mergeJson: ['settings'] },
      )
    })
  },
  down: async (queryInterface, Sequelize) => {},
}
