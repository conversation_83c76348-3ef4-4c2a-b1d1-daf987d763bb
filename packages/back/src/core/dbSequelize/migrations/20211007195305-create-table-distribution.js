const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    return queryInterface
      .createTable('distribution', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          allowNull: false,
          primaryKey: true,
        },
        accountId: {
          type: Sequelize.UUID,
          references: {
            model: 'accounts',
            key: 'id',
          },
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
        },
        maxNum: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        archivedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
      })
      .then(() => queryInterface.addIndex('distribution', ['accountId']))
  },

  down: (queryInterface, Sequelize) => queryInterface.dropTable('distribution'),
}
