import sequelize from '../../services/db/sequelize'

module.exports = {
  async up(queryInterface) {
    // exclui o restrigimento de criação de usuário apenas com um e-mail
    await sequelize.query('drop index if exists "users_email_key";')

    // cria o novo index com email e accountId
    await sequelize.query(
      'create unique index if not exists "users_email_account_index" on "users" ("email", "accountId") where "deletedAt" is null;',
    )
  },

  async down(queryInterface) {
    await sequelize.query('drop index if exists "users_email_account_index";')
    await sequelize.query(
      'create unique index if not exists "users_email_key" on "users" ("email", COALESCE("deletedAt", to_timestamp(0::double precision)));',
    )
  },
}
