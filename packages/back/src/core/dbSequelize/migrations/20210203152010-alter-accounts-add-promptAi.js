'use strict'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    const table = await queryInterface.describeTable('accounts')

    if (!table.promptAiTransfer) {
      await queryInterface.addColumn('accounts', 'promptAiTransfer', {
        type: DataTypes.STRING,
        allowNull: true,
      })
    }

    if (!table.promptAiFinalize) {
      await queryInterface.addColumn('accounts', 'promptAiFinalize', {
        type: DataTypes.STRING,
        allowNull: true,
      })
    }
  },

  down: async (queryInterface, _Sequelize) => {
    await queryInterface.removeColumn('accounts', 'promptAiTransfer')
    await queryInterface.removeColumn('accounts', 'promptAiFinalize')
  },
}
