module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('whatsapp_business_templates_history', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      wabaTemplateId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'whatsapp_business_templates',
          key: 'id',
        },
      },
      statusFrom: {
        type: Sequelize.DataTypes.STRING,
      },
      statusTo: {
        type: Sequelize.DataTypes.STRING,
      },
      qualityFrom: {
        type: Sequelize.DataTypes.STRING,
      },
      qualityTo: {
        type: Sequelize.DataTypes.STRING,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
    })
  },

  down: (queryInterface) => queryInterface.dropTable('whatsapp_business_templates_history'),
}
