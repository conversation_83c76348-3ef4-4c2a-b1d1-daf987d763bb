import userResource from '../../resources/userResource'
import setupSequelize from '../../services/db/setupSequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async () => {
    await setupSequelize()

    const users = await userResource.findMany({
      attributes: ['id', 'name', 'email', 'accountId', 'password'],
      include: ['roles'],
    })

    await queuedAsyncMap(users, async (user) => {
      await userResource.generateTokenFromInternalChat({
        user,
        roles: user.roles,
      })
    })
  },

  down: async () => {},
}
