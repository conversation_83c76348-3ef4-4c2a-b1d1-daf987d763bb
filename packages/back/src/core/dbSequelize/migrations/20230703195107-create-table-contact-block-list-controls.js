module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('contact_block_lists_controls', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      reason: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM,
        values: ['ready', 'processing', 'done', 'error'],
        defaultValue: 'ready',
        allowNull: false,
      },
      action: {
        type: Sequelize.ENUM,
        values: ['block', 'unblock'],
        allowNull: false,
      },
      revertExceptions: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: true,
      },
      updatedCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      processCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      totalCount: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      contactBlockListId: {
        type: Sequelize.UUID,
        references: {
          model: 'contact_block_lists',
          key: 'id',
        },
        allowNull: false,
      },
      serviceId: {
        type: Sequelize.UUID,
        references: {
          model: 'services',
          key: 'id',
        },
        allowNull: false,
      },
      userId: {
        type: Sequelize.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      createdAt: Sequelize.DATE,
      updatedAt: Sequelize.DATE,
      deletedAt: Sequelize.DATE,
    })
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('contact_block_lists_controls')
  },
}
