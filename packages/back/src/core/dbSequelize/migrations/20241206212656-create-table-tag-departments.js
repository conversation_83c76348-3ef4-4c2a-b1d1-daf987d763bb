'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tag_departments', {
      tagId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tags',
          key: 'id',
        },
      },
      departmentId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id',
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('tag_departments', ['tagId', 'departmentId'], { unique: true })
  },

  async down(queryInterface) {
    queryInterface.dropTable('tag_departments')
  },
}
