module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createSchema('pipeline')

    return queryInterface.createTable(
      'pipelines',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        goBack: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
        deletedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'pipelines', schema: 'pipeline' })
  },
}
