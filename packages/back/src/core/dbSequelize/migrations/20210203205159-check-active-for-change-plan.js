import accountResource from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const accounts = await accountResource.findMany({
      attributes: ['id', 'plan'],
      where: {
        isActive: false,
      },
    })
    await queuedAsyncMap(accounts, async (account) => {
      await accountResource.updateById(account.id, {
        plan: {
          ...account.plan,
          isOnGracePeriod: true,
          isGracePeriodOnHold: false,
          gracePeriodExtendTimesRemaining: 3,
          gracePeriodEndsAt: new Date(),
        },
      })
    })
  },

  down: (queryInterface, Sequelize) => {},
}
