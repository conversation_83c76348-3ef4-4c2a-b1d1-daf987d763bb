module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(
      'cards',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        title: {
          type: Sequelize.STRING,
        },
        description: {
          type: Sequelize.TEXT,
        },
        order: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        isArchived: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
        },
        contactId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        pipelineId: {
          type: Sequelize.UUID,
          references: {
            model: 'pipelines',
            key: 'id',
          },
          allowNull: false,
        },
        pipelineStageId: {
          type: Sequelize.UUID,
          references: {
            model: 'pipeline_stages',
            key: 'id',
          },
          allowNull: false,
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        archivedAt: {
          type: Sequelize.DATE,
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )

    queryInterface.addIndex('pipeline.cards', ['contactId', 'pipelineId'], { unique: true })
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'cards', schema: 'pipeline' })
  },
}
