import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    return queryInterface
      .createTable('interactive_messages', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        interactive: {
          type: Sequelize.JSONB,
          defaultValue: {},
          allowNull: false,
        },
        archivedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        deletedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
        accountId: {
          type: Sequelize.UUID,
          references: {
            model: 'accounts',
            key: 'id',
          },
          allowNull: false,
        },
      })
      .then(() =>
        Promise.all([
          sequelize.query(`
        create unique index interactive_messages_name_accountId_key on interactive_messages(name, "accountId") where "deletedAt" is null;`),
        ]),
      )
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('interactive_messages')
  },
}
