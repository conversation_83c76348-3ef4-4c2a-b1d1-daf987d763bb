module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.renameTable('acceptanceTerms', 'acceptance_terms')
    } catch (err) {
      console.warn(err.message)
    }
  },
  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.renameTable('acceptance_terms', 'acceptanceTerms')
    } catch (err) {
      console.warn(err.message)
    }
  },
}
