'use strict'

import setupSequelize from '../../services/db/setupSequelize'
import rolesResource from '../../resources/roleResource'
import permissionResource from '../../resources/permissionResource'
import iteratePaginated from '../../utils/iteratePaginated'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await setupSequelize()

    const permissions = await permissionResource.findMany({
      where: {
        name: {
          $like: 'groups.%',
        },
      },
    })

    await iteratePaginated(
      ({ page }) =>
        rolesResource.findManyPaginated({
          perPage: 50,
          noAccountId: true,
          include: [
            {
              model: 'permissions',
              require: true,
            },
          ],
        }),
      async (model) => {
        const hasPermission = permissions.some((p) => model.permissions.some((p2) => p2.name === p.name))
        if (hasPermission) return
        await model.addPermissions(permissions)
      },
    )
  },

  async down(queryInterface, Sequelize) {
    await setupSequelize()

    const permissions = await permissionResource.findMany({
      where: {
        name: {
          $like: 'groups.%',
        },
      },
    })

    await iteratePaginated(
      ({ page }) =>
        rolesResource.findManyPaginated({
          perPage: 50,
          noAccountId: true,
          include: [
            {
              model: 'permissions',
              where: {
                name: {
                  $like: 'groups.%',
                },
              },
              require: true,
            },
          ],
        }),
      async (model) => model.removePermissions(permissions),
    )
  },
}
