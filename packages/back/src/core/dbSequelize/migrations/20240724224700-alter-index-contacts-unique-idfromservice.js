'use strict'

const { QueryTypes } = require('sequelize')

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`drop index if exists contacts_idfromservice_without_nine_idx;`)

    const now = new Date()

    await queryInterface.sequelize.query(
      `
      CREATE UNIQUE INDEX contacts_idfromservice_without_nine_idx
      ON public.contacts
      (
        (CASE
          WHEN LEFT("idFromService", 2) = '55' AND (LENGTH(regexp_replace("idFromService", ${queryInterface.sequelize.escape(
            '\\D',
          )}, '', 'g' )) IN (12) OR (LENGTH(regexp_replace("idFromService", ${queryInterface.sequelize.escape(
        '\\D',
      )}, '', 'g' )) IN (13) AND SUBSTRING("idFromService", 5, 1) = '9'))
          THEN (LEFT("idFromService", 4) || '%' || RIGHT(REGEXP_REPLACE("idFromService", ${queryInterface.sequelize.escape(
            '\\D',
          )}, '', 'g'), 8))
          ELSE "idFromService"
        END),
        "serviceId",
        "accountId"
      )
      WHERE "createdAt" > :now AND "deletedAt" IS NULL;
    `,
      {
        type: QueryTypes.RAW,
        replacements: {
          now,
        },
      },
    )
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`drop index contacts_idfromservice_without_nine_idx`)
  },
}
