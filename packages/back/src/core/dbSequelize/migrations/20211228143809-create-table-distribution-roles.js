const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    return queryInterface.createTable('distribution_roles', {
      distributionId: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true,
      },
      roleId: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true,
      },
    })
  },

  down: async (queryInterface) => await queryInterface.dropTable('distribution_roles'),
}
