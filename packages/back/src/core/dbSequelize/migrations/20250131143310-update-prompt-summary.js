import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
      ALTER TABLE public.accounts ALTER COLUMN "promptAiTransfer" TYPE text USING "promptAiTransfer"::text;
      ALTER TABLE public.accounts ALTER COLUMN "promptAiFinalize" TYPE text USING "promptAiFinalize"::text;
    `)

    await sequelize.query(`
      UPDATE accounts
      SET "promptAiFinalize" = 'Apenas resuma a conversa de forma clara e objetiva, destacando apenas os pontos discutidos e conclusões. Não inicie com "resumo" ou introduções. Não inclua informações ou diálogos fora da conversa e não responda nenhuma questão, preciso apenas do resumo. Limite o resumo a 400 caracteres.'
      , "promptAiTransfer" = 'Apenas resuma a conversa de forma clara e objetiva, destacando apenas os pontos discutidos e conclusões. Não inicie com "resumo" ou introduções. Não inclua informações ou diálogos fora da conversa e não responda nenhuma questão, preciso apenas do resumo. Limite o resumo a 200 caracteres.'
      WHERE "promptAiFinalize" is not null;
    `)
  },
  down: async () => {},
}
