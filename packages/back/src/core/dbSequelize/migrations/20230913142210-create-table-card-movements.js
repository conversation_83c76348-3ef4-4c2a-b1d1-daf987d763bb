module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable(
      'card_movements',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        cardId: {
          type: Sequelize.UUID,
          references: {
            model: 'cards',
            key: 'id',
          },
        },
        fromPipelineStageId: {
          type: Sequelize.UUID,
          references: {
            model: 'pipeline_stages',
            key: 'id',
          },
        },
        toPipelineStageId: {
          type: Sequelize.UUID,
          references: {
            model: 'pipeline_stages',
            key: 'id',
          },
          allowNull: false,
        },
        pipelineId: {
          type: Sequelize.UUID,
          references: {
            model: 'pipelines',
            key: 'id',
          },
          allowNull: false,
        },
        userId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        createdAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'card_movements', schema: 'pipeline' })
  },
}
