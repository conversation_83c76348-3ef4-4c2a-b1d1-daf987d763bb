'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('credit_movements')

    if (!tableInfo?.serviceId) {
      await queryInterface.addColumn('credit_movements', 'serviceId', {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'services',
          key: 'id',
        },
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('credit_movements')

    if (tableInfo?.serviceId) {
      await queryInterface.removeColumn('credit_movements', 'serviceId')
    }
  },
}
