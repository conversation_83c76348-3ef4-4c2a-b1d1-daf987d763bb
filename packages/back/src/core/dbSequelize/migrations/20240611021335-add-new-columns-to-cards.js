import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'finishedAt',
      {
        type: Sequelize.DATE,
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'success',
      {
        type: Sequelize.BOOLEAN,
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'statusId',
      {
        type: Sequelize.UUID,
        references: {
          model: 'stage_status',
          key: 'id',
        },
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'reasonId',
      {
        type: Sequelize.UUID,
        references: {
          model: 'stage_reasons',
          key: 'id',
        },
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'organizationId',
      {
        type: Sequelize.UUID,
      },
    )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'organizationSegment',
      {
        type: Sequelize.STRING,
      },
    ),
      await queryInterface.addColumn(
        {
          tableName: 'cards',
          schema: 'pipeline',
        },
        'originChannel',
        {
          type: Sequelize.STRING,
        },
      )
    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'originCampaign',
      {
        type: Sequelize.STRING,
      },
    )
  },

  down: async (queryInterface) => {
    const columnsToRemove = [
      'finishedAt',
      'success',
      'statusId',
      'reasonId',
      'organizationId',
      'organizationSegment',
      'originChannel',
      'originCampaign',
    ]
    await queuedAsyncMap(columnsToRemove, async (column) => {
      await queryInterface.removeColumn(
        {
          tableName: 'cards',
          schema: 'pipeline',
        },
        column,
      )
    })
  },
}
