module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('files')

    if (!tableInfo.storage) {
      await queryInterface.addColumn('files', 'storage', {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 's3',
      })
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('files', 'storage')
  },
}
