import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
      drop trigger if exists update_tickets_count_trigger on tickets;
      drop trigger if exists update_contacts_tickets_count_trigger on contacts;
      drop function if exists caching.update_tickets_count;
      drop table if exists caching.tickets_count;
    `)

    await sequelize.query(`
      create materialized view caching.tickets_count as 
      select
        c."accountId" as "accountId",
        t."departmentId" as "departmentId",
        t."userId" as "userId",
        sum(case when t."userId" is not null then 1 else 0 end) as "mineCount",
        sum(case when t."userId" is null then 1 else 0 end) as "queueCount",
        current_timestamp "updatedAt"
      from
        tickets t
      inner join contacts c
          on
        c."currentTicketId" = t."id"
        and c."accountId" = t."accountId"
      where
        c."accountId" = t."accountId"
        and c."deletedAt" is null
        and c."visible" = true
        and t."isOpen" = true
      group by
        c."accountId",
        t."departmentId",
        t."userId",
        current_timestamp;
    `)
  },

  down: async () => {
    await sequelize.query(`drop materialized view if exists caching.tickets_count;`)
  },
}
