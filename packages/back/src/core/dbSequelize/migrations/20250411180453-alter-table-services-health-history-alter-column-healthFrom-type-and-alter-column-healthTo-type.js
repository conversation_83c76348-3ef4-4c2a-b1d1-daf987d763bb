module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('services_health_history', 'healthFrom', {
      type: Sequelize.TEXT,
    })
    await queryInterface.changeColumn('services_health_history', 'healthTo', {
      type: Sequelize.TEXT,
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('services_health_history', 'healthFrom', {
      type: Sequelize.STRING,
    })
    await queryInterface.changeColumn('services_health_history', 'healthTo', {
      type: Sequelize.STRING,
    })
  },
}
