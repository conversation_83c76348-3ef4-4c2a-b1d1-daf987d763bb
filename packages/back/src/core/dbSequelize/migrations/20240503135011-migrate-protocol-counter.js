import iteratePaginated from '../../utils/iteratePaginated'
import accountResource from '../../resources/accountResource'
import ticketResource from '../../resources/ticketResource'
import setupSequelize from '../../services/db/setupSequelize'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await setupSequelize()

    await queryInterface.addColumn('tickets', 'count', {
      type: Sequelize.INTEGER,
      allowNull: true,
    })

    await iteratePaginated(
      ({ page }) =>
        accountResource.findManyPaginated({
          attributes: ['id'],
          noAccountId: true,
          page,
          perPage: 100,
        }),
      async (account) => {
        const ticket = await ticketResource.findOne({
          where: {
            accountId: account.id,
          },
          order: [['createdAt', 'desc']],
        })

        if (!ticket) return

        const protocolCount = await queryInterface.sequelize
          .query(
            `
          select max(count) from protocol_counters where "accountId" = :accountId and "deletedAt" is null; 
        `,
            {
              replacements: {
                accountId: account.id,
              },
            },
          )
          .then((r) => r?.[0]?.[0]?.max)

        if (!protocolCount) return

        await ticketResource.update(ticket, {
          count: protocolCount,
        })
      },
    )

    await queryInterface.sequelize.query(
      `
      CREATE UNIQUE INDEX tickets_count_idx ON public.tickets (count,"accountId");
      `,
    )

    // count is not null para considerar apenas novos tickets que tem count
    await queryInterface.sequelize.query(
      `
      CREATE UNIQUE INDEX tickets_protocol_idx ON public.tickets (protocol,"accountId") where count is not null;
      `,
    )
  },

  async down(queryInterface, Sequelize) {},
}
