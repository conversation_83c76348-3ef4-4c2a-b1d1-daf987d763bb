module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('messages', 'hsmId', {
      type: Sequelize.DataTypes.UUID,
      defaultValue: null,
      allowNull: true,
      references: {
        model: 'whatsapp_business_hsm',
        key: 'id',
      },
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('messages', 'hsmId')
  },
}
