'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('bots')

    if (!tableInfo?.currentBotVersionId) {
      // Adicionar nova coluna de currentBotVersionId na tabela de bots
      // Responsável por vincular o bot com a versão atual em uso
      await queryInterface.addColumn('bots', 'currentBotVersionId', {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'bot_versions',
          key: 'id',
        },
      })

      await queryInterface.addIndex('bots', ['currentBotVersionId']) // Index na chave estrangeira
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('bots')

    if (tableInfo?.currentBotVersionId) {
      // Remove a coluna de currentBotVersionId na tabela de bots
      await queryInterface.removeColumn('bots', 'currentBotVersionId')
    }
  },
}
