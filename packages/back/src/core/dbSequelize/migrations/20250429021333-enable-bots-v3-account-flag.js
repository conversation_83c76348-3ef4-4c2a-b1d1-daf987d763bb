import accountRepository from '../repositories/accountRepository'
import iteratePaginated from '../../utils/iteratePaginated'

module.exports = {
  up: async () => {
    await iteratePaginated(
      ({ page }) =>
        accountRepository.findManyPaginated({
          where: {
            settings: {
              flags: {
                'bots-v3': { $is: null },
              },
            },
          },
          page: 1,
          perPage: 500,
          noAccountId: true,
        }),
      async (account) =>
        accountRepository.update(
          account,
          {
            settings: {
              flags: {
                ...account.settings.flags,
                'bots-v3': true,
              },
            },
          },
          { mergeJson: ['settings'] },
        ),
    )
  },

  down: async () => {},
}
