module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('quick_reply_categories', {
      quickReplyId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'quick_replies',
          key: 'id',
        },
      },
      categoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'categories',
          key: 'id',
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('quick_reply_categories', ['quickReplyId', 'categoryId'], { unique: true })
  },

  down: (queryInterface) => queryInterface.dropTable('quick_reply_categories'),
}
