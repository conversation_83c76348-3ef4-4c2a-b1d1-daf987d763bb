module.exports = {
  up: async (queryInterface, Sequelize) =>
    queryInterface.sequelize.query(
      `
      drop index if exists contacts_lastmessageat_idx;
      
      create index contacts_lastmessageat_idx on public.contacts using btree ("lastMessageAt" desc nulls last);
      
      create index if not exists  messages_hsmfileid_idx ON public.messages ("hsmFileId");
      
      create index if not exists whatsapp_business_templates_accountid_idx ON public.whatsapp_business_templates ("accountId");

      create index if not exists distribution_roles_roleid_idx ON public.distribution_roles ("roleId");
      
      create index if not exists campaign_messages_hsmid_idx ON public.campaign_messages ("hsmId");
      
      create index if not exists campaign_messages_hsmfileid_idx ON public.campaign_messages ("hsmFileId");
      `,
    ),
  down: async (queryInterface, Sequelize) => {},
}
