import questionResource from '../../resources/questionResource'
import answersResource from '../../resources/answersResource'
import userResource from '../../resources/userResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import setupSequelize from '../../services/db/setupSequelize'

const getAccount = async (questionId) => {
  const answer = await answersResource.findOne({
    attributes: [],
    where: {
      questionId,
      ticketId: { $ne: null },
    },
    include: ['ticket.account'],
  })

  if (answer) {
    return answer.ticket.account
  }

  return null
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    const { DataTypes } = Sequelize

    const questions = await questionResource.findMany({
      attributes: ['id'],
    })
    const devUser = '<EMAIL>'
    const user = await userResource.findOne({
      attributes: ['id', 'accountId'],
      where: { email: { $ne: devUser } },
      include: [
        {
          model: 'account',
          attributes: ['id'],
        },
      ],
    })

    await queuedAsyncMap(questions, async (question) => {
      const account = (await getAccount(question.id)) || user.account
      await queryInterface.sequelize.query(
        `
        update questions set "accountId" = :accountId where id = :questionId
      `,
        {
          replacements: { accountId: account.id, questionId: question.id },
          type: queryInterface.sequelize.QueryTypes.UPDATE,
        },
      )
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('questions', 'accountId')
  },
}
