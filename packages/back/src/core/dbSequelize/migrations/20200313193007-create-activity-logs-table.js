module.exports = {
  up: async (queryInterface, Sequelize, retry = true) => {
    const { DataTypes } = Sequelize
    const createTable = () =>
      queryInterface.createTable('activity_logs', {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        resourceType: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        resourceId: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        event: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        data: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        accountId: {
          type: DataTypes.UUID,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
      })
    await createTable().catch(async (error) => {
      if (retry) {
        retry = false
        await queryInterface.sequelize
          .query(
            `
            delete from users 
            where ctid in (
              select t.ctid from (
                select ctid, row_number() over( partition by id order by "updatedAt" desc) as seq 
                from users
                where id in (
                  select u.id from users u
                  group by u.id
                  having count(1) > 1
                )
              ) t where t.seq > 1
            );
            alter table users add constraint users_pkey primary key (id);
          `,
          )
          .catch(() => true)
        return createTable()
      }
      throw error
    })

    await queryInterface.addIndex('activity_logs', ['accountId'])
    await queryInterface.addIndex('activity_logs', ['userId'])
    await queryInterface.addIndex('activity_logs', ['event'])
    await queryInterface.addIndex('activity_logs', ['resourceType', 'resourceId'])
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('activity_logs', ['resourceType', 'resourceId'])
    await queryInterface.removeIndex('activity_logs', ['event'])
    await queryInterface.removeIndex('activity_logs', ['userId'])
    await queryInterface.removeIndex('activity_logs', ['accountId'])
    await queryInterface.dropTable('activity_logs')
  },
}
