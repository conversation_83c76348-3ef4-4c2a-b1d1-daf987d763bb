'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    // Adicionar nova tabela de contracted_credits
    // Responsável por registrar os créditos contratados para o sistema de créditos de mensagens
    await queryInterface.createTable('contracted_credits', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      type: {
        // Tipo de crédito contratado
        type: DataTypes.ENUM,
        values: ['credit-system-plan', 'credit-system-additional'],
        allowNull: false,
      },
      startedAt: {
        // Data de início da vigência do crédito contratado
        type: DataTypes.DATE,
        allowNull: false,
      },
      expiredAt: {
        // Data de expiração da vigência do crédito contratado
        // Crédito do tipo plan não tem data de expiração predefinida
        type: DataTypes.DATE,
        allowNull: true,
      },
      contractedQuantity: {
        // Quantidade total contratada
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      consumedQuantity: {
        // Quantidade total consumida
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
      isRevoked: {
        // Flag para indicar se a contratação foi revogada
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      consumedServiceTypes: {
        // Quantidade consumida para cada tipo de conexão
        type: DataTypes.JSONB,
        defaultValue: {},
        allowNull: false,
      },
      contractedEventId: {
        // ID do evento de contratação (referência do ERP)
        type: DataTypes.STRING,
        allowNull: true,
      },
      contractedProductId: {
        // ID do produto contratado (referência do ERP)
        type: DataTypes.STRING,
        allowNull: true,
      },
      contractedProductName: {
        // Nome do produto contratado (referência do ERP)
        type: DataTypes.STRING,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        // Tabela com soft delete
        type: DataTypes.DATE,
        allowNull: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
    })

    await Promise.all([
      queryInterface.addIndex('contracted_credits', ['type']), // Index para busca e ordenação
      queryInterface.addIndex('contracted_credits', ['startedAt']), // Index para busca e ordenação
      queryInterface.addIndex('contracted_credits', ['expiredAt']), // Index para busca e ordenação
      queryInterface.addIndex('contracted_credits', ['isRevoked']), // Index para busca
      queryInterface.addIndex('contracted_credits', ['accountId']), // Index na chave estrangeira
    ])
  },

  async down(queryInterface) {
    // Remove a tabela de contracted_credits
    await queryInterface.dropTable('contracted_credits')
  },
}
