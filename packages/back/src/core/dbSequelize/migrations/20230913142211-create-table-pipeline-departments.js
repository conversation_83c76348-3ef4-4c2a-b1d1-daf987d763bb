module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable(
      'pipeline_departments',
      {
        pipelineId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipelines',
            key: 'id',
          },
        },
        departmentId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'pipeline_departments', schema: 'pipeline' })
  },
}
