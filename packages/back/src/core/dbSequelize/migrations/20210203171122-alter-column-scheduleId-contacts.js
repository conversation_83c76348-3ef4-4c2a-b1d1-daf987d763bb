import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import schedulesResource from '../../resources/scheduleResource'

const { Op } = require('sequelize')
const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const schedules = await schedulesResource.findMany({
      include: [
        {
          model: 'contact',
          attributes: ['id', 'deletedAt'],
          where: {
            [Op.not]: { deletedAt: null },
          },
          required: true,
        },
      ],
    })

    await queuedAsyncMap(schedules, async (schedule) => await schedule.destroy(schedule.id))
  },
  down: (queryInterface, Sequelize) => {},
}
