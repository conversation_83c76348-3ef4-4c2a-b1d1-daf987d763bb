'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.describeTable('campaigns').then(async (campaign) => {
      if (!campaign.createdById) {
        await queryInterface.addColumn('campaigns', 'createdById', {
          type: Sequelize.UUID,
          references: {
            model: 'users',
            key: 'id',
          },
          allowNull: true,
        })
      }
      if (!campaign.sentById) {
        await queryInterface.addColumn('campaigns', 'sentById', {
          type: Sequelize.UUID,
          references: {
            model: 'users',
            key: 'id',
          },
          allowNull: true,
        })
      }
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('campaigns', 'createdById')
    await queryInterface.removeColumn('campaigns', 'sentById')
  },
}
