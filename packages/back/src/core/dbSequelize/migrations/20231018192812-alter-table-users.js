'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('users', 'otpSecretKey', {
      type: DataTypes.STRING,
      allowNull: true,
    })
    await queryInterface.addColumn('users', 'otpAuthActive', {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('users', 'otpSecretKey')
    await queryInterface.removeColumn('users', 'otpAuthActive')
  },
}
