module.exports = {
  up: async (queryInterface, Sequelize, retry = true) => {
    const createTable = () =>
      queryInterface.createTable('user_contacts', {
        userId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        contactId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'contacts',
            key: 'id',
          },
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      })

    await createTable().catch(async (error) => {
      if (retry) {
        retry = false
        await queryInterface.sequelize
          .query(
            `
          delete from contacts 
          where ctid in (
            select t.ctid from (
              select ctid, row_number() over( partition by id order by "updatedAt" desc) as seq 
              from contacts
              where id in (
                select u.id from contacts u
                group by u.id
                having count(1) > 1
              )
            ) t where t.seq > 1
          );
          alter table contacts add constraint contacts_pkey primary key (id);
        `,
          )
          .catch(() => true)
        return createTable()
      }
      throw error
    })
  },

  down: async (queryInterface) => await queryInterface.dropTable('user_contacts'),
}
