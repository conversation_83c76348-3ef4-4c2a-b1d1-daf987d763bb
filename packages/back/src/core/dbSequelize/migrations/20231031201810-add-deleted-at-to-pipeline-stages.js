import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface
      .describeTable({
        tableName: 'pipeline_stages',
        schema: 'pipeline',
      })
      .then((tb) => {
        if (!tb.deletedAt) {
          sequelize.query(`ALTER TABLE pipeline.pipeline_stages ADD COLUMN "deletedAt" timestamptz`)
        }
      })
  },

  down: async () => {
    await sequelize.query(`ALTER TABLE pipeline.pipeline_stages DROP COLUMN "deletedAt"`)
  },
}
