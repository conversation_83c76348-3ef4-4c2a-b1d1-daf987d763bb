module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('plan_ai_history')

    if (!tableInfo?.copilot) {
      await queryInterface.addColumn('plan_ai_history', 'copilot', {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('plan_ai_history')

    if (tableInfo?.copilot) {
      await queryInterface.removeColumn('plan_ai_history', 'copilot')
    }
  },
}
