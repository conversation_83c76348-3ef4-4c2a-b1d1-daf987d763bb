import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import contactResource from '../../resources/contactResource'

const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const contacts = await contactResource.findMany({
      attributes: ['id', 'data', 'serviceId', 'idFromService'],
      include: [
        {
          model: 'service',
          attributes: [],
          where: {
            type: 'email',
          },
          required: true,
        },
      ],
      where: {
        $or: [
          { 'data.email': { $or: [{ $eq: '' }, { $eq: null }] } },
          { 'data.number': { $and: [{ $ne: null }, { $ne: '' }] } },
        ],
      },
    })

    await queuedAsyncMap(contacts, async (contact) =>
      contactResource.update(contact, {
        ...contact,
        data: {
          ...contact.data,
          number: '',
          email: contact.idFromService || contact.data.number || '',
        },
      }),
    )
  },

  down: async (queryInterface, Sequelize) => {},
}
