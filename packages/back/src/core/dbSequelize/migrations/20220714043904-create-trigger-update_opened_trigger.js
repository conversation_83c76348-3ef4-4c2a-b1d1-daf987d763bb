import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
      create or replace function caching.update_tickets_count()
      returns trigger as $$
      declare default_uuid uuid := '********-0000-0000-0000-************';
      begin
        if( TG_TABLE_NAME = 'tickets') then
          insert into caching.tickets_count
            ("accountId", "departmentId", "userId", "mineCount", "queueCount", "updatedAt")
          select
            c."accountId" as "accountId",
            t."departmentId" as "departmentId",
            t."userId" as "userId",
            sum(case when t."userId" is not null then 1 else 0 end) as "mineCount",
            sum(case when t."userId" is null then 1 else 0 end) as "queueCount",
            current_timestamp "updatedAt"
          from contacts c
          inner join tickets t
            on t."contactId" = c.id
            and t."accountId" = c."accountId"
            and t."isOpen" = true
          where c."deletedAt" is null
            and c."visible" = true
            and c."accountId" = coalesce(new."accountId", old."accountId")
            and not exists (
              select 1
              from caching.tickets_count tc
              where tc."accountId" = t."accountId"
              and coalesce(tc."departmentId",default_uuid) = coalesce(t."departmentId",default_uuid)
              and coalesce(tc."userId",default_uuid) = coalesce(t."userId",default_uuid)
              limit 1
            )
          group by c."accountId", t."departmentId", t."userId", current_timestamp;
             
          update caching.tickets_count as tc 
            set "mineCount" = tc2."mineCount",
            "queueCount" = tc2."queueCount",
            "updatedAt" = current_timestamp 
          from (
            select
              vt."accountId" as "accountId",
              vt."departmentId" as "departmentId",
              vt."userId" as "userId",
              sum(case when t.id is not null and vt."userId" is not null then 1 else 0 end) as "mineCount",
              sum(case when t.id is not null and vt."userId" is null then 1 else 0 end) as "queueCount",
              current_timestamp "updatedAt"
            from (
              select new."accountId" as "accountId", new."departmentId" as "departmentId", new."userId" as "userId"
              union
              select old."accountId", old."departmentId", old."userId"
            ) as vt
            inner join contacts c
              on c."accountId" = vt."accountId"
              and c."deletedAt" is null
              and c."visible" = true
            left join tickets t
              on t."contactId" = c.id
              and t."accountId" = c."accountId"
              and t."isOpen" = true
              and coalesce(t."departmentId",default_uuid) = coalesce(vt."departmentId",default_uuid)
              and coalesce(t."userId",default_uuid) = coalesce(vt."userId",default_uuid)
            where vt."accountId" is not null
            group by vt."accountId", vt."departmentId", vt."userId", current_timestamp
          ) as tc2
          where tc2."accountId" = tc."accountId"
              and coalesce(tc2."departmentId",default_uuid) = coalesce(tc."departmentId",default_uuid)
              and coalesce(tc2."userId",default_uuid) = coalesce(tc."userId",default_uuid);
        
        elsif( TG_TABLE_NAME = 'contacts') then
          update caching.tickets_count as tc 
            set "mineCount" = tc2."mineCount",
            "queueCount" = tc2."queueCount",
            "updatedAt" = current_timestamp 
          from (
            select
              vt."accountId" as "accountId",
              vt."departmentId" as "departmentId",
              vt."userId" as "userId",
              sum(case when t.id is not null and vt."userId" is not null then 1 else 0 end) as "mineCount",
              sum(case when t.id is not null and vt."userId" is null then 1 else 0 end) as "queueCount",
              current_timestamp "updatedAt"
            from (
              select ct."accountId" as "accountId", ct."departmentId" as "departmentId", ct."userId" as "userId"
              from tickets ct
              where ct.id = coalesce(old."currentTicketId", new."currentTicketId")
            ) as vt
            inner join contacts c
              on c."accountId" = vt."accountId"
              and c."deletedAt" is null
              and c."visible" = true
            left join tickets t
              on t."contactId" = c.id
              and t."accountId" = c."accountId"
              and t."isOpen" = true
              and coalesce(t."departmentId",default_uuid) = coalesce(vt."departmentId",default_uuid)
              and coalesce(t."userId",default_uuid) = coalesce(vt."userId",default_uuid)
            where vt."accountId" is not null
            group by vt."accountId", vt."departmentId", vt."userId", current_timestamp
          ) as tc2
          where tc2."accountId" = tc."accountId"
              and coalesce(tc2."departmentId",default_uuid) = coalesce(tc."departmentId",default_uuid)
              and coalesce(tc2."userId",default_uuid) = coalesce(tc."userId",default_uuid);
        end if;

        return null;
      end
      $$ language plpgsql;`)

    await sequelize.query(`
      create trigger update_tickets_count_trigger
      after insert or update of "isOpen","userId","departmentId" or delete on tickets
      for each row
      execute procedure caching.update_tickets_count();`)

    await sequelize.query(`
      create trigger update_contacts_tickets_count_trigger
      after insert or update of "deletedAt" or delete on contacts
      for each row
      execute procedure caching.update_tickets_count();`)
  },

  down: async (queryInterface, Sequelize) => {
    await sequelize.query(`
      drop trigger if exists update_tickets_count_trigger on tickets;
      drop trigger if exists update_contacts_tickets_count_trigger on contacts;
      drop function if exists caching.update_tickets_count;
    `)
  },
}
