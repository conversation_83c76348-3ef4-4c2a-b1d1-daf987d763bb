module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    return queryInterface.describeTable('contacts').then(async (contacts) => {
      if (!contacts.dataBlock) {
        await queryInterface.addColumn('contacts', 'dataBlock', {
          type: DataTypes.JSONB,
          allowNull: true,
          defaultValue: {},
        })
      }
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('contacts', 'dataBlock')
  },
}
