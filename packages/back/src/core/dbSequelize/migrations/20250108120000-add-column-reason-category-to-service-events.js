'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('service_events')

    if (!tableInfo?.reasonCategory) {
      // Adicionar nova coluna de reasonCategory na tabela de service_events
      // Responsável por categorizar os motivos dos eventos nas conexões
      await queryInterface.addColumn('service_events', 'reasonCategory', {
        type: DataTypes.STRING,
      })
    }

    await queryInterface.sequelize.query(`
      UPDATE service_events se
      SET "reasonCategory" = 'service_connection'
      WHERE se."reasonCategory" IS NULL
    `)
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('service_events')

    if (tableInfo?.reasonCategory) {
      // Remove a coluna de reasonCategory na tabela de service_events
      await queryInterface.removeColumn('service_events', 'reasonCategory')
    }
  },
}
