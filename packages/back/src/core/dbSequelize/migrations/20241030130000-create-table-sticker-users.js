'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const dtStickerUsers = await queryInterface.tableExists('sticker_users')

    if (!dtStickerUsers) {
      // Adicionar nova tabela de sticker_users
      // Responsável por registrar o último envio de stickers por usuário
      await queryInterface.createTable('sticker_users', {
        stickerId: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'stickers',
            key: 'id',
          },
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        accountId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
        lastSendAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
      })

      await Promise.all([
        queryInterface.addIndex('sticker_users', ['stickerId']), // Index na chave estrangeira
        queryInterface.addIndex('sticker_users', ['userId']), // Index na chave estrangeira
        queryInterface.addIndex('sticker_users', ['accountId']), // Index na chave estrangeira
        queryInterface.addIndex('sticker_users', [{ attribute: 'lastSendAt', order: 'DESC' }]), // Index para ordenação
      ])
    }
  },

  async down(queryInterface) {
    const dtStickerUsers = await queryInterface.tableExists('sticker_users')

    if (dtStickerUsers) {
      // Remove a tabela de sticker_users
      await queryInterface.dropTable('sticker_users')
    }
  },
}
