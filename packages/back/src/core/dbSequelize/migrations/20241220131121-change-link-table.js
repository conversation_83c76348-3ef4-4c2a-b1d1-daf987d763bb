'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize
      .query(
        `
    ALTER TABLE public.links ADD CONSTRAINT "links_messageId_fkey" 
    FOREIGN KEY ("messageId") REFERENCES public.messages(id)
    `,
      )
      .catch((error) => console.log(error.message))

    await queryInterface.addIndex('links', ['messageId']).catch((error) => console.log(error.message))
  },

  async down(queryInterface) {},
}
