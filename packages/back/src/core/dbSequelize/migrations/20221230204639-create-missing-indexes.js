module.exports = {
  up: async (queryInterface, Sequelize) => {
    queryInterface.sequelize.query(`
    CREATE INDEX IF NOT EXISTS messages_contactid_timestamp_idx ON public.messages USING btree ("contactId", "timestamp" DESC);

    DROP INDEX IF EXISTS public.ticket_transfers_ticketid;
    
    ALTER TABLE ticket_transfers DROP COLUMN IF EXISTS ticketid;

    DROP INDEX IF EXISTS public.ticket_transfers_ticketid_todepartmentid_index;
    `)
  },

  down: async (queryInterface, Sequelize) => {},
}
