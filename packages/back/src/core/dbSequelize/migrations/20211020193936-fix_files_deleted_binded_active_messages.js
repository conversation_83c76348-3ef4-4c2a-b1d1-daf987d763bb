import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(
      `update files f
      set "deletedAt" = null
    where f."attachedId" in (
        select m.id 
        from messages m 
        inner join contacts c 
            on c.id = m."contactId" 
        inner join services s 
            on s.id = m."serviceId" 
        where c."deletedAt" is null
        and s."deletedAt" is null
    )
    and f.id = (
      select f2.id
      from files f2
      where f2."attachedId" = f."attachedId" 
        and f2."attachedType" = f."attachedType"
      order by coalesce("deletedAt",current_timestamp) desc, "createdAt" desc
      limit 1	
    ) 
    and not exists (
      select f2.id
      from files f2
      where f2."attachedId" = f."attachedId" 
        and f2."attachedType" = f."attachedType"
        and f2."deletedAt" is null		
    )`,
    )
  },
}
