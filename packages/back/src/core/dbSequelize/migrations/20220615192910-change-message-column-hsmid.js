import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    await sequelize.query('alter table messages drop constraint if exists "hsmId_foreign_idx"')

    await sequelize.query('alter table messages drop constraint if exists "messages_hsmId_fkey"')

    await queryInterface.changeColumn('messages', 'hsmId', {
      type: Sequelize.DataTypes.UUID,
      references: {
        model: 'whatsapp_business_templates',
        key: 'id',
      },
    })
  },
  down: async (queryInterface) => {},
}
