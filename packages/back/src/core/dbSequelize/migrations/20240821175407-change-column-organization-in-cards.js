'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'organizationId',
    )

    await queryInterface.addColumn(
      {
        tableName: 'cards',
        schema: 'pipeline',
      },
      'organization',
      {
        type: Sequelize.STRING,
      },
    )
  },

  async down(queryInterface, Sequelize) {},
}
