module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable('quick_replies')
    if (!tableInfo.title) {
      await queryInterface.addColumn('quick_replies', 'title', {
        type: Sequelize.STRING,
        allowNull: true,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('quick_replies')
    if (tableInfo.title) {
      await queryInterface.removeColumn('quick_replies', 'title')
    }
  },
}
