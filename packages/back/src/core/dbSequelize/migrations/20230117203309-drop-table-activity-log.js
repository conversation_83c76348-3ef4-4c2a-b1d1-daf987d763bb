module.exports = {
  up: async (queryInterface) => queryInterface.dropTable('activity_logs'),
  down: async (queryInterface) => {
    const ddl = `
    CREATE TABLE activity_logs (
      id uuid NOT NULL,
      "resourceType" varchar(255) NULL,
      "resourceId" uuid NULL,
      "event" varchar(255) NOT NULL,
      "data" jsonb NULL,
      "userId" uuid NULL,
      "accountId" uuid NULL,
      "createdAt" timestamptz NOT NULL,
      "updatedAt" timestamptz NOT NULL,
      "accessTokenId" uuid NULL,
      CONSTRAINT activity_logs_pkey PRIMARY KEY (id)
    );

    --  activity_logs indexes

    CREATE INDEX activity_logs_access_token_id ON activity_logs USING btree ("accessTokenId");
    CREATE INDEX activity_logs_account_id ON activity_logs USING btree ("accountId");
    CREATE INDEX activity_logs_event ON activity_logs USING btree (event);
    CREATE INDEX activity_logs_resource_type_resource_id ON activity_logs USING btree ("resourceType", "resourceId");
    CREATE INDEX activity_logs_user_id ON activity_logs USING btree ("userId");


    -- activity_logs foreign keys

    ALTER TABLE activity_logs ADD CONSTRAINT "activity_logs_accessTokenId_fkey" FOREIGN KEY ("accessTokenId") REFERENCES oauth_access_tokens(id);
    ALTER TABLE activity_logs ADD CONSTRAINT "activity_logs_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES accounts(id);
    ALTER TABLE activity_logs ADD CONSTRAINT "activity_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES users(id);
    `

    queryInterface.sequelize.query(ddl, { raw: true })
  },
}
