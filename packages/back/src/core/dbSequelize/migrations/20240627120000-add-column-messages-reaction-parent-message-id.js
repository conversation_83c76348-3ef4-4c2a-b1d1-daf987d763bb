'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const dtMessages = await queryInterface.describeTable('messages')

    if (!dtMessages?.reactionParentMessageId) {
      // Adicionar nova coluna de reactionParentMessageId na tabela de messages
      // Responsável por vincular uma reação com uma mensagem do chat
      await queryInterface.addColumn('messages', 'reactionParentMessageId', {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'messages',
          key: 'id',
        },
      })

      // Adicionar index para busca
      await queryInterface.addIndex('messages', ['reactionParentMessageId'])
    }
  },

  async down(queryInterface) {
    const dtMessages = await queryInterface.describeTable('messages')

    if (dtMessages?.reactionParentMessageId) {
      // Remove a coluna de reactionParentMessageId na tabela de messages
      await queryInterface.removeColumn('messages', 'reactionParentMessageId')
    }
  },
}
