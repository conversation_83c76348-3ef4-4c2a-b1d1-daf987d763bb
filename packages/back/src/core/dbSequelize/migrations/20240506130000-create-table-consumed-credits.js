'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    // Adicionar nova tabela de consumed_credits
    // Responsável por registrar os créditos consumidos para o sistema de créditos de mensagens
    await queryInterface.createTable('consumed_credits', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      consumed: {
        // Quantidade consumida
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      serviceType: {
        // Tipo da conexão utilizada no consumo
        type: DataTypes.STRING,
        allowNull: false,
      },
      messageIds: {
        // Array com os IDs das mensagens que foram enviadas neste respectivo consumo de créditos
        type: DataTypes.ARRAY(DataTypes.UUID),
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        // Tabela com soft delete
        type: DataTypes.DATE,
        allowNull: true,
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      serviceId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'services',
          key: 'id',
        },
      },
      contractedCreditId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'contracted_credits',
          key: 'id',
        },
      },
    })

    await Promise.all([
      queryInterface.addIndex('consumed_credits', ['serviceType']), // Index para busca
      queryInterface.addIndex('consumed_credits', ['accountId']), // Index na chave estrangeira
      queryInterface.addIndex('consumed_credits', ['serviceId']), // Index na chave estrangeira
      queryInterface.addIndex('consumed_credits', ['contractedCreditId']), // Index na chave estrangeira
    ])
  },

  async down(queryInterface) {
    // Remove a tabela de consumed_credits
    await queryInterface.dropTable('consumed_credits')
  },
}
