module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.describeTable('contacts').then(async (contacts) => {
      if (!contacts.block) {
        await queryInterface.addColumn('contacts', 'block', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
        })
      }
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('contacts', 'block')
  },
}
