import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import serviceRepository from '../repositories/serviceRepository'

module.exports = {
  up: async () => {
    const services = await serviceRepository.findMany({
      attributes: ['id', 'settings'],
      where: { type: 'whatsapp' },
    })

    await queuedAsyncMap(services, async (service) => {
      service.settings = {
        ...service.settings,
        legacyMessageIds: true,
      }
      await service.save()
    })
  },

  down: async () => {},
}
