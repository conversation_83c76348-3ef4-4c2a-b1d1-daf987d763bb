'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE "accounts"
      SET "settings" = jsonb_set(
        "settings",
        '{expirationPasswordTime}',
        '120',
        true
      )
      WHERE ("settings"->>'isPasswordExpirationActive')::boolean = true
        AND ("settings"->>'expirationPasswordTime')::int > 120;
    `)
  },

  async down(queryInterface, Sequelize) {
    // Não temos acesso a informação anterior para reverter
  },
}
