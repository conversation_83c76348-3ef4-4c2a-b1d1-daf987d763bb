'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable('contacts')

    if (!tableInfo.archivedAt) {
      await queryInterface.addColumn('contacts', 'archivedAt', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      })
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('contacts', 'archivedAt')
  },
}
