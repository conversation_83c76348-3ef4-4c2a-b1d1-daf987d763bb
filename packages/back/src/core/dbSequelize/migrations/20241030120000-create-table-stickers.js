'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const dtStickers = await queryInterface.tableExists('stickers')

    if (!dtStickers) {
      // Adicionar nova tabela de stickers
      // Responsável por registrar a biblioteca de stickers disponíveis
      await queryInterface.createTable('stickers', {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        type: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        originFileChecksum: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        originFilehash: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        originMessageId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'messages',
            key: 'id',
          },
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        accountId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
      })

      await Promise.all([
        queryInterface.addIndex('stickers', ['type']), // Index para busca
        queryInterface.addIndex('stickers', ['originFileChecksum']), // Index para busca
        queryInterface.addIndex('stickers', ['originFilehash']), // Index para busca
        queryInterface.addIndex('stickers', [{ attribute: 'updatedAt', order: 'DESC' }]), // Index para ordenação
        queryInterface.addIndex('stickers', ['originMessageId']), // Index na chave estrangeira
        queryInterface.addIndex('stickers', ['userId']), // Index na chave estrangeira
        queryInterface.addIndex('stickers', ['accountId']), // Index na chave estrangeira
      ])
    }
  },

  async down(queryInterface) {
    const dtStickers = await queryInterface.tableExists('stickers')

    if (dtStickers) {
      // Remove a tabela de stickers
      await queryInterface.dropTable('stickers')
    }
  },
}
