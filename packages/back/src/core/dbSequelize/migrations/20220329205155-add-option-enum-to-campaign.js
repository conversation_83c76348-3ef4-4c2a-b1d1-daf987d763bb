const { intersection } = require('lodash')

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const [checkEnumValue] = await queryInterface.sequelize.query(
      "select count(1) \"exists\" FROM pg_enum \
       where enumtypid = ( \
        select oid from pg_type  \
         where typName like 'enum_campaigns_status' \
        limit 1) \
        and enumlabel like 'importing_contacts'",
      {
        raw: true,
        type: Sequelize.QueryTypes.SELECT,
      },
    )

    if (!Number(checkEnumValue?.exists)) {
      await queryInterface.sequelize.query(`alter type enum_campaigns_status add value 'importing_contacts'`)
    }
  },
  down: async (queryInterface, Sequelize) => {},
}
