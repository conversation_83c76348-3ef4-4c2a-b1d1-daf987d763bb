'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const existsBotVersions = await queryInterface.tableExists('bot_versions')

    if (!existsBotVersions) {
      // Adicionar nova tabela de bot_versions
      // Responsável por registrar cada uma das versões salvas no respectivo bot
      await queryInterface.createTable('bot_versions', {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        status: {
          // Status da versão
          type: DataTypes.ENUM,
          values: ['draft', 'published'],
          allowNull: false,
        },
        contexts: {
          // Mesmo campo da tabela de bots (armazenar os contextos para percorrer no BotService)
          type: DataTypes.JSONB,
          allowNull: false,
          defaultValue: {},
        },
        flowJson: {
          // Mesmo campo da tabela de bots (armazenar os blocos da visualização em fluxograma)
          type: DataTypes.JSONB,
          allowNull: false,
          defaultValue: {},
        },
        settings: {
          // Mesmo campo da tabela de bots (armazenar as configurações do bot)
          type: DataTypes.JSONB,
          allowNull: false,
          defaultValue: {},
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        publishedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        deletedAt: {
          // Tabela com soft delete
          type: DataTypes.DATE,
          allowNull: true,
        },
        botId: {
          // Relacionamento 1:N (1 Bot : N BotVersion)
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'bots',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        createdById: {
          // Usuário que criou a versão
          // Relacionamento 1:N (1 User : N BotVersion)
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        publishedById: {
          // Usuário que publicou a versão
          // Relacionamento 1:N (1 User : N BotVersion)
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        deletedById: {
          // Usuário que deletou a versão
          // Relacionamento 1:N (1 User : N BotVersion)
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        accountId: {
          // Relacionamento 1:N (1 Account : N BotVersion)
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
      })

      await Promise.all([
        queryInterface.addIndex('bot_versions', ['status']), // Index para busca
        queryInterface.addIndex('bot_versions', [{ attribute: 'createdAt', order: 'DESC' }]), // Index para ordenação
        queryInterface.addIndex('bot_versions', ['botId']), // Index na chave estrangeira
        queryInterface.addIndex('bot_versions', ['createdById']), // Index na chave estrangeira
        queryInterface.addIndex('bot_versions', ['publishedById']), // Index na chave estrangeira
        queryInterface.addIndex('bot_versions', ['deletedById']), // Index na chave estrangeira
        queryInterface.addIndex('bot_versions', ['accountId']), // Index na chave estrangeira
      ])
    }
  },

  async down(queryInterface) {
    const existsBotVersions = await queryInterface.tableExists('bot_versions')

    if (existsBotVersions) {
      // Remove a tabela de bot_versions
      await queryInterface.dropTable('bot_versions')
    }
  },
}
