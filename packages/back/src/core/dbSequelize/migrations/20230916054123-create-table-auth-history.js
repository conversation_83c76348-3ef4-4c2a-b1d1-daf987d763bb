const setupSequelize = require('../../services/db/setupSequelize').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    return queryInterface
      .createTable('auth_history', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        event: {
          type: Sequelize.ENUM,
          values: ['auth', 'password_change'],
          allowNull: false,
        },
        userId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        originIP: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        originUA: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        passwordHash: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        accessTokenId: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'oauth_access_tokens',
            key: 'id',
          },
          onDelete: 'set null',
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'accounts',
            key: 'id',
          },
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      })
      .then(() => {
        queryInterface.addIndex('auth_history', ['accountId'])
        queryInterface.addIndex('auth_history', ['event'])
        queryInterface.addIndex('auth_history', ['userId'])
        queryInterface.addIndex('auth_history', ['accessTokenId'])
      })
  },

  down: (queryInterface) => queryInterface.dropTable('auth_history'),
}
