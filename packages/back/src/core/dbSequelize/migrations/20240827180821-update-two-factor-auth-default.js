'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
        UPDATE "accounts"
        SET "settings" = jsonb_set("settings", '{twoFactorAuthActive}', 'true'::jsonb)
      `)
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE "accounts"
      SET "settings" = jsonb_set("settings", '{twoFactorAuthActive}', 'false'::jsonb)
      WHERE "settings"->>'twoFactorAuthMandatory' = 'false';
    `)
  },
}
