module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('distribution_roles', 'distributionId', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'distribution',
        key: 'id',
      },
    })
    await queryInterface.changeColumn('distribution_roles', 'roleId', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id',
      },
    })

    await queryInterface.addColumn('distribution_roles', 'createdAt', {
      type: Sequelize.DATE,
    })

    await queryInterface.addColumn('distribution_roles', 'updatedAt', {
      type: Sequelize.DATE,
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
