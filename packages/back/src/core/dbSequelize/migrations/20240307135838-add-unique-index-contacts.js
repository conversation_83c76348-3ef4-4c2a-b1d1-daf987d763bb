'use strict'

const { QueryTypes } = require('sequelize')

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date()

    await queryInterface.sequelize.query(
      `
    create unique index contacts_idfromservice_without_nine_idx
    on
    public.contacts
    ((case
    	when left("idFromService", 2) = '55'
    	then (left("idFromService", 4) || '%' || right(regexp_replace("idFromService", ${queryInterface.sequelize.escape(
        '\\D',
      )}, '', 'g' ), 8))
    	else "idFromService"
    end
    ),
    "serviceId", "accountId") where "createdAt" > :now and "deletedAt" is null
    `,
      {
        type: QueryTypes.RAW,
        replacements: {
          now,
        },
      },
    )
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`drop index contacts_idfromservice_without_nine_idx`)
  },
}
