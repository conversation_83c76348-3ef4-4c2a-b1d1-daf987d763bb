import sequelize from '../../services/db/sequelize'
import setupSequelize from '../../services/db/setupSequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const permissionsToRename = ['view', 'create', 'update', 'destroy']

    await sequelize.transaction(async (transaction) => {
      await queuedAsyncMap(permissionsToRename, async (permission) => {
        console.log(`=> Remove duplicate permission for holidays.${permission}`)
        await sequelize.query(
          ' \
          delete from "permissions" \
          where "name" = :toPermission \
          and exists( \
            select "id" \
            from "permissions" \
            where "name" = :fromPermission \
          limit 1 \
          );',
          {
            replacements: {
              toPermission: `holidays.${permission}`,
              fromPermission: `holiday.${permission}`,
            },
            transaction,
          },
        )

        console.log(`=> Rename permission, from: holiday.${permission} to: holidays.${permission}`)
        await sequelize.query(
          ' \
          update "permissions" \
          set "name" = :toPermission, \
              "type" = :toType \
          where "deletedAt" is null \
          and "name" like :fromPermission \
          ',
          {
            replacements: {
              toPermission: `holidays.${permission}`,
              toType: 'holidays',
              fromPermission: `holiday%.${permission}`,
            },
            transaction,
          },
        )
      })
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
