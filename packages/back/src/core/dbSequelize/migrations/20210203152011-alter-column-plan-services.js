import accountResources from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const accounts = await accountResources.findMany({
      attributes: ['id', 'plan'],
    })
    await queuedAsyncMap(accounts, async (account) => {
      await accountResources.updateById(
        account.id,
        {
          plan: {
            ...account.plan,
            services: {
              ...[
                'whatsapp',
                'whatsapp-business',
                'whatsapp-remote',
                'webchat',
                'telegram',
                'sms-wavy',
                'email',
              ].reduce(
                (aggr, item) => ({
                  ...aggr,
                  [item]: parseInt(account.plan.services[item], 10),
                }),
                {},
              ),
            },
          },
        },
        5,
      )
    })
  },
  down: async (queryInterface, Sequelize) => {},
}
