import timetableResource from '../../resources/timetableResource'
import userResource from '../../resources/userResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface) => {
    await setupSequelize()

    const timetables = await timetableResource.findMany({
      attributes: ['id'],
      include: [
        {
          model: 'users',
          attributes: ['accountId'],
        },
      ],
    })

    const devUser = '<EMAIL>'
    const user = await userResource.findOne({
      attributes: ['accountId'],
      where: { email: { $ne: devUser } },
    })

    await queuedAsyncMap(timetables, async (timetable) => {
      const accountId = timetable?.users?.[0]?.accountId || user.accountId
      await queryInterface.sequelize.query(`update timetables set "accountId" = :accountId where id = :timetableId;`, {
        replacements: { accountId, timetableId: timetable.id },
        type: queryInterface.sequelize.QueryTypes.UPDATE,
      })
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('timetables', 'accountId')
  },
}
