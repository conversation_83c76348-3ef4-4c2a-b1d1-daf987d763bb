import campaignResource from '../../resources/campaignResource'
import campaignMessageResource from '../../resources/campaignMessageResource'
import whatsappBusinessTemplateResource from '../../resources/whatsappBusinessTemplateResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    const campaigns = await campaignResource
      .findMany({
        attributes: ['id'],
        include: [
          {
            model: 'messages',
            where: {
              'extraOptions.hsm': {
                $ne: null,
              },
            },
            limit: 1,
          },
        ],
      })
      .then((r) =>
        r.filter(
          (c) => 'hsm' in (c.messages?.[0]?.extraOptions || {}) && typeof c.messages[0].extraOptions.hsm === 'string',
        ),
      )

    await queuedAsyncMap(campaigns, async (campaign) => {
      const hsm = await whatsappBusinessTemplateResource.findById(campaign.messages[0]?.extraOptions?.hsm, {
        include: [
          {
            model: 'fileExample',
            order: [['createdAt', 'DESC']],
            limit: 1,
          },
        ],
      })

      if (!hsm) return

      const components = hsm.components
      const header = components.find((c) => c.type === 'HEADER')
      const body = components.find((c) => c.type === 'BODY')

      // só tem header com imagem antes dessa feature
      const parameters = [
        ...(header
          ? [
              {
                type: 'header',
                parameters: [
                  {
                    type: 'image',
                    image: {
                      link: campaign.messages[0]?.extraOptions?.fileTemplate?.url || hsm.fileExample?.[0]?.url,
                    },
                  },
                ],
              },
            ]
          : []),
        ...(body?.params?.length
          ? [
              {
                type: 'body',
                parameters: body.params.map((bp, index) => ({
                  type: 'text',
                  text: campaign.messages[0]?.extraOptions?.parameters?.[index] || '',
                })),
              },
            ]
          : []),
      ]

      await campaignMessageResource.bulkUpdate(
        {
          extraOptions: {
            hsm,
            parameters,
          },
          hsmFileId: hsm?.fileExample?.[0]?.id,
          hsmId: hsm.id,
        },
        {
          where: {
            campaignId: campaign.id,
          },
        },
      )
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
