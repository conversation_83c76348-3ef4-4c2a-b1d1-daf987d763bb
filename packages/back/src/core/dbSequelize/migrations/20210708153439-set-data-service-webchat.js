import { omit } from 'lodash'

import serviceResource from '../../resources/serviceResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const services = await serviceResource.findMany({
      attributes: ['id', 'data', 'internalData'],
      where: { type: 'webchat', deletedAt: null },
    })

    await queuedAsyncMap(services, async (service) => {
      await serviceResource.updateById(service.id, {
        data: {
          ...omit(service.data, [
            'nameWebchat',
            'nameTelegram',
            'idUserWebChat',
            'phoneWhatsapp',
            'filePerfilWebchat',
            'idUserWebChat',
          ]),
          webchat: {
            id: service.internalData.idUserWebChat,
            name: service.data?.nameWebchat,
            phone: service.data?.phoneWhatsapp,
            telegram: service.data?.nameTelegram,
            file: service.internalData?.filePerfilWebchat,
          },
        },
        internalData: {
          ...omit(service.internalData, [
            'nameWebchat',
            'nameTelegram',
            'idUserWebChat',
            'phoneWhatsapp',
            'filePerfilWebchat',
            'idUserWebChat',
          ]),
          webchat: {
            id: service.internalData.idUserWebChat,
            token: service.internalData.token,
            username: service.internalData.username,
            password: service.internalData.password,
          },
        },
      })
    })
  },

  down: async (queryInterface, Sequelize) => {
    // await queryInterface.removeColumn('services', 'settings.emailRequired')
  },
}
