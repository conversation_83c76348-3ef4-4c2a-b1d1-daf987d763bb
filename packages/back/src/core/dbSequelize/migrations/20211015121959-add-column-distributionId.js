module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('departments', 'distributionId', {
      type: DataTypes.UUID,
      defaultValue: null,
      references: {
        model: 'distribution',
        key: 'id',
      },
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('departments', 'distributionId')
  },
}
