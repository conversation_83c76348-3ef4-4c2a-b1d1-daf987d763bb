import serviceResource from '../../resources/serviceResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const services = await serviceResource.findMany({
      attributes: ['id', 'data'],
      where: {
        'data.providerType': null,
        type: 'whatsapp-business',
      },
    })
    await queuedAsyncMap(services, async (service) => {
      await serviceResource.updateById(service.id, {
        data: {
          ...service.data,
          providerType: 'positus',
        },
      })
    })
  },

  down: (queryInterface, Sequelize) => {},
}
