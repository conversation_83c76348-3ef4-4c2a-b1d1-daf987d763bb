module.exports = {
  up: (queryInterface, Sequelize) =>
    queryInterface.createTable('service_events', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      online: {
        type: Sequelize.BOOLEAN,
      },
      reason: {
        type: Sequelize.STRING,
      },
      serviceId: {
        type: Sequelize.UUID,
        references: {
          model: 'services',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      userId: {
        type: Sequelize.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      accountId: {
        type: Sequelize.UUID,
        references: {
          model: 'accounts',
          key: 'id',
        },
        allowNull: false,
      },
      startedAt: {
        type: Sequelize.DATE,
      },
      endedAt: {
        type: Sequelize.DATE,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    }),

  down: (queryInterface, Sequelize) => queryInterface.dropTable('service_events'),
}
