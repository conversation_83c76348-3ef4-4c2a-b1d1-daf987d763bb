module.exports = {
  async up(queryInterface) {
    /**
     * Extension required for gen_random_uuid() function
     */
    await queryInterface.sequelize.query(`
      CREATE EXTENSION IF NOT EXISTS "pgcrypto"; 
    `)

    await queryInterface.sequelize.query(`
      INSERT INTO public.notifications (
        id,
        "accountId",
        "userId",
        "scheduleId",
        "attachedId",
        "attachedType",
        text,
        type,
        read,
        "createdAt",
        "updatedAt",
        "deletedAt",
        "contactId",
        image,
        label
      )
      SELECT
        gen_random_uuid(),       -- id
        a.id,                    -- accountId
        u.id,                    -- userId
        NULL,                    -- scheduleId
        NULL,                    -- attachedId
        NULL,                    -- attachedType
        NULL,                    -- text
        'smart-summary',         -- type
        false,                   -- read
        now(),                   -- createdAt
        now(),                   -- updatedAt
        NULL,                    -- deletedAt
        NULL,                    -- contactId
        NULL,                    -- image
        NULL                     -- label
      FROM public.accounts a
      JOIN public.users u ON u."accountId" = a.id
      JOIN public.user_roles ur ON ur."userId" = u.id 
      JOIN public.roles r ON r.id = ur."roleId"
      WHERE r."isAdmin" = TRUE AND a.settings->'flags'->>'enable-smart-summary' = 'true';
    `)
  },

  async down(queryInterface) {
    await queryInterface.sequelize.query(`
    DELETE FROM public.notifications
    WHERE
      type = 'smart-summary'
    `)
  },
}
