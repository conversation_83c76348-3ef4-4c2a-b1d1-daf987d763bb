module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'pipeline_notifications',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        pipelineId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipelines',
            key: 'id',
          },
        },
        pipelineAutomationId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipeline_automations',
            key: 'id',
          },
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: (queryInterface) => queryInterface.dropTable({ tableName: 'pipeline_notifications', schema: 'pipeline' }),
}
