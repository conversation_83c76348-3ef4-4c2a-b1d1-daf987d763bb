'use strict'

import accountRepository from '../repositories/accountRepository'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import config from '../../config'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    const accounts = await accountRepository.findMany({ paranoid: false })

    const publicUrl = config('publicUrl')

    let subdomain = new URL(publicUrl).hostname.split('.')[0]

    if (subdomain === '127') {
      subdomain = 'local'
    }

    let indexAdmin = 0
    let indexOthers = 0

    await queuedAsyncMap(accounts, async (account) => {
      if (account.name === 'MandeUmZap') {
        const alias = indexAdmin === 0 ? 'mandeumzap' : `mandeumzap-${indexAdmin}`
        indexAdmin += 1

        await accountRepository.update(account, { alias })
        return
      }

      const alias = indexOthers === 0 ? subdomain : `${subdomain}-${indexOthers}`
      indexOthers += 1

      await accountRepository.update(account, { alias })
    })
  },

  async down() {
    // Não realizar ação
  },
}
