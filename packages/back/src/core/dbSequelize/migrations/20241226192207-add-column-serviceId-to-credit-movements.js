'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const creditMovements = await queryInterface.describeTable('credit_movements')

    if (!creditMovements.serviceId) {
      await queryInterface.addColumn('credit_movements', 'serviceId', {
        type: Sequelize.UUID,
        references: {
          model: 'services',
          key: 'id',
        },
        allowNull: true,
      })
    }
  },

  async down(queryInterface, Sequelize) {
    const creditMovements = await queryInterface.describeTable('credit_movements')

    if (creditMovements.serviceId) {
      await queryInterface.removeColumn('credit_movements', 'serviceId')
    }
  },
}
