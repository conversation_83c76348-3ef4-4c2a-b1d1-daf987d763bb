module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('contacts', 'contactBlockListControlId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'contact_block_lists_controls',
        key: 'id',
      },
    })
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('contacts', 'contactBlockListControlId')
  },
}
