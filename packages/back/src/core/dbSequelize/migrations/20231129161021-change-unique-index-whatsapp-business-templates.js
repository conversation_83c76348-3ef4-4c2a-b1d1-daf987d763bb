import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface) => {
    await sequelize.query(`
      drop index if exists "whatsapp_business_templates_name_serviceId_accountId_index";
    `)

    await sequelize.query(`
      create unique index if not exists "whatsapp_business_templates_name_language_serviceId_accountId" 
      on whatsapp_business_templates ("name", "language", "serviceId", "accountId")
      where "archivedAt" is null and "deletedAt" is null;    
    `)
  },

  down: async (queryInterface, Sequelize) => {
    await sequelize.query(`
      drop index if exists "whatsapp_business_templates_name_language_serviceId_accountId";
      `)

    await sequelize.query(`
      create unique index if not exists "whatsapp_business_templates_name_serviceId_accountId_index" 
      on whatsapp_business_templates ("name", "serviceId", "accountId")
      where "archivedAt" is null and "deletedAt" is null;    
      `)
  },
}
