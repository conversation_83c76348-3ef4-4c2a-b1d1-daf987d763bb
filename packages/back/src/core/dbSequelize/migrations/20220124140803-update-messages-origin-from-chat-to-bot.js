import messageResource from '../../resources/messageResource'
import setupSequelize from '../../services/db/setupSequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const affectedRows = (
      await messageResource.getRepository().bulkUpdate(
        {
          origin: 'bot',
        },
        {
          where: {
            origin: 'chat',
            botId: { $ne: null },
          },
          paranoid: false,
        },
      )
    )?.[0]

    console.log(`=>${affectedRows} records had column: origin, fixed from 'chat' to 'bot'.`)
  },

  down: async (queryInterface, Sequelize) => {},
}
