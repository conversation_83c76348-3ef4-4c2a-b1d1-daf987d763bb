import { omit } from 'lodash'
import setupSequelize from '../../services/db/setupSequelize'
import iteratePaginated from '../../utils/iteratePaginated'
import userResource from '../../resources/userResource'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    await iteratePaginated(
      ({ page }) =>
        userResource.findManyPaginated({
          attributes: ['data'],
          where: {
            'data.isLoggedInWeb': {
              $ne: null,
            },
          },
          page: 1,
          perPage: 500,
        }),
      async (user) =>
        userResource.update(
          user,
          { data: omit(user.data, 'isLoggedInWeb') || {} },
          { dontEmit: true, noAccountId: true },
        ),
    )
  },

  down: async (queryInterface, Sequelize) => {},
}
