module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(
      'card_products',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        ammount: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        value: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        cardId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'cards',
            key: 'id',
          },
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'card_products', schema: 'pipeline' })
  },
}
