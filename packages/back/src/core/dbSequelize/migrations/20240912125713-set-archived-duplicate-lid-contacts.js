import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
    UPDATE contacts c
    SET "archivedAt" = now(), visible = false
    WHERE c."idFromService" like '%@lid%';
  `)
  },

  down: async () => {
    await sequelize.query(`
    UPDATE contacts c
    SET "archivedAt" = null, visible = true
    WHERE c."idFromService" like '%@lid%';
  `)
  },
}
