'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('messages', 'isTranscribing', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    })
    await queryInterface.addColumn('messages', 'transcribeError', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    })
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('messages', 'isTranscribing')
    await queryInterface.removeColumn('messages', 'transcribeError')
  },
}
