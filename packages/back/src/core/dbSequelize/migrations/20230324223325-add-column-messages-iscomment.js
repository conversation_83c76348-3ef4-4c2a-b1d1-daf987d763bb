module.exports = {
  up: (queryInterface, Sequelize) =>
    queryInterface.describeTable('messages').then((td) => {
      if (!td.isComment) {
        return queryInterface.addColumn('messages', 'isComment', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: true,
        })
      }
    }),

  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('messages', 'isComment')
  },
}
