module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      {
        tableName: 'pipelines',
        schema: 'pipeline',
      },
      'archivedAt',
      {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null,
      },
    )
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn(
      {
        tableName: 'pipelines',
        schema: 'pipeline',
      },
      'archivedAt',
    )
  },
}
