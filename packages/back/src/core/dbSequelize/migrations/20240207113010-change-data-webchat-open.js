import serviceRepository from '../repositories/serviceRepository'
import setupSequelize from '../../services/db/setupSequelize'
import iteratePaginated from '../../utils/iteratePaginated'

module.exports = {
  up: async () => {
    await setupSequelize()

    await iteratePaginated(
      ({ page }) =>
        serviceRepository.findManyPaginated({
          attributes: ['id', 'data'],
          where: { type: 'webchat' },
          page,
          perPage: 10,
          noAccountId: true,
        }),
      async (service) => {
        const webchatData = {
          ...service?.data,
          webchat: {
            ...service?.data?.webchat,
            desktopChatOpen: service?.data?.webchat?.isOpenChat || false,
            mobileChatOpen: service?.data?.webchat?.isOpenChat || false,
            getClientData: !service?.data?.webchat?.isOpenForm,
          },
        }
        await serviceRepository.updateById(service.id, { data: webchatData })
      },
    )
  },

  down: async () => {},
}
