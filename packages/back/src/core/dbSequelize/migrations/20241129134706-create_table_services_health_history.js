module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('services_health_history', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      serviceId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'services',
          key: 'id',
        },
      },
      healthFrom: {
        type: Sequelize.DataTypes.STRING,
      },
      healthTo: {
        type: Sequelize.DataTypes.STRING,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
    })
  },

  down: (queryInterface) => queryInterface.dropTable('services_health_history'),
}
