import omit from 'lodash/omit'
import setupSequelize from '../../services/db/setupSequelize'
import contactResource from '../../resources/contactResource'
import iteratePaginated from '../../utils/iteratePaginated'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    await iteratePaginated(
      ({ page }) =>
        contactResource.findManyPaginated({
          attributes: ['id', 'data'],
          where: {
            'data.whatsapp': {
              $ne: null,
            },
          },
          page: 1,
          perPage: 500,
        }),
      async (model) =>
        contactResource.update(model, { data: omit(model.data, ['whatsapp']) }, { dontEmit: true, noAccountId: true }),
    )
  },

  down: async (queryInterface, Sequelize) => {},
}
