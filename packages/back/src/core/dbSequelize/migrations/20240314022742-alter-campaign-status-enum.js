'use strict'

/** @type {import('sequelize-cli').Migration} */

module.exports = {
  up: async (queryInterface) => {
    await queryInterface.sequelize.query(`ALTER TYPE "enum_campaigns_status" ADD VALUE if not exists 'import_error'`)
    await queryInterface.sequelize.query(
      `ALTER TYPE "enum_campaigns_status" ADD VALUE if not exists 'hsm_limit_exceeded'`,
    )
  },

  down: async (queryInterface) => {
    return queryInterface.sequelize.query(`
    DELETE 
    FROM
        pg_enum
    WHERE
        enumlabel in('import_error', 'hsm_limit_exceeded') AND
        enumtypid = (
            SELECT
                oid
            FROM
                pg_type
            WHERE
                typname = 'enum_campaigns_status'
        )
    `)
  },
}
