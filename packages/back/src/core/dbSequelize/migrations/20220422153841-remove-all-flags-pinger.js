import omit from 'lodash/omit'
import setupSequelize from '../../services/db/setupSequelize'
import accountResource from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const accounts = await accountResource.findMany({
      attributes: ['id', 'settings'],
      where: { 'settings.flags.whatsapp-use-phone-pinger': true },
    })

    await queuedAsyncMap(accounts, async (account) => {
      await accountResource.updateById(
        account.id,
        {
          settings: {
            ...account.settings,
            flags: omit(account.settings.flags, 'whatsapp-use-phone-pinger'),
          },
        },
        { mergeJson: ['settings'] },
      )
    })
  },

  down: (queryInterface, Sequelize) => {},
}
