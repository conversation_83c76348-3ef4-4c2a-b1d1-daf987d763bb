'use strict'

const creditsControl = {
  enabled: false,
  useCreditsSystemAt: null,
  lastUpdateContractFromAgnus: null,
  lastRenewalDate: null,
  lastRenewalAmount: 0,
  nextRenewalDate: null,
  nextRenewalAmount: 0,
  allowExceedLimit: false,
  withoutCreditsAvailable: false,
  contractedPlanEventId: null,
  contractedPlanProductId: null,
  contractedPlanProductName: null,
  notificationRanges: [
    { name: '50%', percent: 50, modes: ['push'] },
    { name: '75%', percent: 75, modes: ['push', 'email'] },
    { name: '90%', percent: 90, modes: ['email', 'modal'] },
    { name: '100%', percent: 100, modes: ['email', 'modal'] },
  ],
  notificationsSent: [],
  creditsSystemType: null,
  closingDay: null,
}

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize
    const dtAccounts = await queryInterface.describeTable('accounts')
    if (!dtAccounts?.creditsControl) {
      // Adicionar nova coluna de creditsControl na tabela de accounts
      // Responsável por controlar toda a parte de créditos relacionados ao envio e recebimento de mensagens
      await queryInterface.addColumn('accounts', 'creditsControl', {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
      })

      // Atualiza todos os registros na tabela de accounts com o valor default de creditsControl
      await queryInterface.bulkUpdate('accounts', { creditsControl })
    }
  },

  async down(queryInterface) {
    const dtAccounts = await queryInterface.describeTable('accounts')
    if (dtAccounts.creditsControl) {
      // Remove a coluna de creditsControl na tabela de accounts
      await queryInterface.removeColumn('accounts', 'creditsControl')
    }
  },
}
