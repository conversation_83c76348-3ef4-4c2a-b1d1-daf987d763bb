import setupSequelize from '../../services/db/setupSequelize'
import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(
      'whatsapp_business_templates',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        internalName: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        category: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        components: {
          type: Sequelize.JSONB,
          allowNull: false,
        },
        language: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        namespace: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        rejectedReason: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        status: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        messageType: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        serviceId: {
          type: Sequelize.UUID,
          references: {
            model: 'services',
            key: 'id',
          },
          allowNull: false,
        },
        accountId: {
          type: Sequelize.UUID,
          references: {
            model: 'accounts',
            key: 'id',
          },
          allowNull: false,
        },
        archivedAt: {
          type: Sequelize.DATE,
          defaultValue: null,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        deletedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
      },
      {
        uniqueKeys: {
          unique_tag: {
            customIndex: true,
            fields: ['name', 'serviceId', 'accountId'],
          },
        },
      },
    )

    await setupSequelize()

    return sequelize.query(`
      CREATE INDEX IF NOT EXISTS 
      whatsapp_business_templates_serviceid_accountid_index 
      ON whatsapp_business_templates("serviceId", "accountId");
    `)
  },

  down: async (queryInterface) => {},
}
