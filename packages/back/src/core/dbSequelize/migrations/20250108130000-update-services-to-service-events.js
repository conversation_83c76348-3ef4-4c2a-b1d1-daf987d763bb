'use strict'

import setupSequelize from '../../services/db/setupSequelize'
import serviceEventsResource from '../../resources/serviceEventsResource'
import serviceResource from '../../resources/serviceResource'
import iteratePaginated from '../../utils/iteratePaginated'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    await setupSequelize()

    // Criar registro de evento nas conexões que possuem a flag blockMessageRulesActive habilitada
    // addEventsBlockMessageRules irá criar os registros iniciais de service_block_message na tabela de service_events
    await iteratePaginated(
      ({ page }) =>
        serviceResource.findManyPaginated({
          page,
          perPage: 10,
          noAccountId: true,
          where: {
            'settings.blockMessageRulesActive': true,
            archivedAt: null,
          },
        }),
      async (model) => {
        if (!model?.settings?.blockMessageRulesActive) return

        await serviceEventsResource.addEventsBlockMessageRules(model)
      },
    )
  },

  async down() {
    // Não realizar ação
  },
}
