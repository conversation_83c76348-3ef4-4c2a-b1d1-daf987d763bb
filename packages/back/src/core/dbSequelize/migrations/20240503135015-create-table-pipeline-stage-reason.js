module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(
      'stage_reasons',
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        position: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        pipelineId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipelines',
            key: 'id',
          },
        },
        stageId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'pipeline_stages',
            key: 'id',
          },
        },
        accountId: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        createdAt: Sequelize.DATE,
        updatedAt: Sequelize.DATE,
      },
      {
        schema: 'pipeline',
      },
    )

    await queryInterface.addIndex(
      {
        tableName: 'stage_reasons',
        schema: 'pipeline',
      },
      ['stageId', 'pipelineId', 'name'],
      { unique: true },
    )
  },

  down: async (queryInterface) => {
    return queryInterface.dropTable({ tableName: 'stage_reasons', schema: 'pipeline' })
  },
}
