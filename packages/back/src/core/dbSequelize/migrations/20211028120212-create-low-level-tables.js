module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.createTable('low_level_contacts', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      idFromService: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      data: {
        type: DataTypes.JSONB,
        defaultValue: {},
        allowNull: false,
      },
      serviceId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'services',
          key: 'id',
        },
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('low_level_contacts', ['serviceId'])
    await queryInterface.addIndex('low_level_contacts', ['accountId'])
    await queryInterface.addIndex('low_level_contacts', ['idFromService'])
    await queryInterface.addIndex('low_level_contacts', ['serviceId', 'idFromService'], { unique: true })

    await queryInterface.createTable('low_level_messages', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      idFromService: {
        type: DataTypes.STRING,
        unique: false,
        allowNull: false,
      },
      data: {
        type: DataTypes.JSONB,
        defaultValue: {},
        allowNull: false,
      },
      serviceId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'services',
          key: 'id',
        },
      },
      accountId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    })

    await queryInterface.addIndex('low_level_messages', ['serviceId'])
    await queryInterface.addIndex('low_level_messages', ['accountId'])
    await queryInterface.addIndex('low_level_messages', ['idFromService'])
    await queryInterface.addIndex('low_level_messages', ['serviceId', 'idFromService'], { unique: true })
  },

  down: async (queryInterface) => {
    await queryInterface.removeIndex('low_level_contacts', ['serviceId', 'idFromService'], { unique: true })
    await queryInterface.removeIndex('low_level_contacts', ['idFromService'])
    await queryInterface.removeIndex('low_level_contacts', ['accountId'])
    await queryInterface.dropTable('low_level_contacts')

    await queryInterface.removeIndex('low_level_messages', ['serviceId', 'idFromService'], { unique: true })
    await queryInterface.removeIndex('low_level_messages', ['idFromService'])
    await queryInterface.removeIndex('low_level_messages', ['accountId'])
    await queryInterface.dropTable('low_level_messages')
  },
}
