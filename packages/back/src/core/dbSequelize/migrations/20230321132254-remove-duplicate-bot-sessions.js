import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async () => {
    await sequelize.query(`
      delete from bots_sessions
      where "botId" IS NULL
    `)

    await sequelize.query(`
      delete from bots_sessions
      where exists(
        select 1 from (
          SELECT row_number() over(partition by "contactId", "botId" order by "updatedAt" desc) seq, id
          FROM bots_sessions
        ) as exclude_items
        where exclude_items.seq > 1
        and exclude_items.id = bots_sessions.id
        limit 1
      )
    `)
  },

  down: async () => {},
}
