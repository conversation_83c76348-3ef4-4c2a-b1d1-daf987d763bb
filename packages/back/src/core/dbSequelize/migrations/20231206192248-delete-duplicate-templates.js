import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
    UPDATE whatsapp_business_templates
    SET "deletedAt" = now()
    WHERE id in
        (SELECT id
         FROM
           (
            SELECT row_number() 
            OVER (
              PARTITION BY name,
                          "accountId",
                           language,
                          "serviceId"
              ORDER BY "updatedAt" DESC) 
                AS r,
                wbt.*
            FROM whatsapp_business_templates wbt) x
         WHERE x.r != 1
           AND "deletedAt" IS NULL);
  `)
  },

  down: async (queryInterface, Sequelize) => {},
}
