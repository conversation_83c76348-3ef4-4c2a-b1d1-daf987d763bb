const setupSequelize = require('../../services/db/setupSequelize').default
const createInitial = require('../../../scripts/commands/helpers/createInitial').default
const clusterResource = require('../../resources/clusterResource').default
const planResource = require('../../resources/planResource').default

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    await createInitial()

    await clusterResource.create({ name: 'default' })
    await planResource.create({
      name: 'Standard',
      data: { limits: { services: 1, users: 3 } },
    })
  },

  down: (queryInterface, Sequelize) => {},
}
