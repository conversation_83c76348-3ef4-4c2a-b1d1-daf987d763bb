'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`ALTER TYPE "enum_auth_history_event" ADD VALUE if not exists 'logout'`)
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      DELETE 
      FROM
          pg_enum
      WHERE
          enumlabel in('logout') AND
          enumtypid = (
              SELECT
                  oid
              FROM
                  pg_type
              WHERE
                  typname = 'enum_auth_history_event'
          )
      `)
  },
}
