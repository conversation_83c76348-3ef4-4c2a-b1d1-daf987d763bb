module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('activity_logs', 'accessTokenId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'oauth_access_tokens',
        key: 'id',
      },
    })
    await queryInterface.addIndex('activity_logs', ['accessTokenId'])
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('activity_logs', ['accessTokenId'])
    await queryInterface.removeColumn('activity_logs', 'accessTokenId')
  },
}
