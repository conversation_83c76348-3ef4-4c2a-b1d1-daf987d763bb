import setupSequelize from '../../services/db/setupSequelize'
import roleResource from '../../resources/roleResource'
import permissionResource from '../../resources/permissionResource'

module.exports = {
  up: async () => {
    await setupSequelize()
    const permission = await permissionResource.findOne({
      where: {
        name: 'chat.view.contacts',
      },
    })

    const roles = await roleResource.findMany({
      where: {
        displayName: { $ne: 'Administrador' },
      },
    })

    await Promise.all(roles.map((role) => role.addPermission(permission)))

    console.log('Attached permissions.')
  },

  down: async () => {
    await setupSequelize()
    const permission = await permissionResource.findOne({
      where: {
        name: 'chat.view.contacts',
      },
    })

    const roles = await roleResource.findMany({
      where: {
        displayName: { $ne: 'Administrador' },
      },
    })

    await Promise.all(roles.map((role) => role.removePermission(permission)))

    console.log('Permissions removed.')
  },
}
