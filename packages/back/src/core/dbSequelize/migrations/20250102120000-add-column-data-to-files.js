'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('files')

    if (!tableInfo?.data) {
      // Adicionar nova coluna de data na tabela de files
      // Responsável por armazenar os dados de cada arquivo da plataforma
      await queryInterface.addColumn('files', 'data', {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('files')

    if (tableInfo?.data) {
      // Remove a coluna de data na tabela de files
      await queryInterface.removeColumn('files', 'data')
    }
  },
}
