import { execSync } from 'child_process'

const root = process.env.NODE_ENV === 'production' ? 'dist' : 'src'

module.exports = {
  up: async () => {
    // Cadastrar as novas permissões: stickers.management e stickers.send
    await execSync(`node ${root}/scripts seed:permissions`, {
      stdio: 'inherit',
    })

    // Vincular as novas permissões nos usuários admins: stickers.management e stickers.send
    await execSync(`node ${root}/scripts attach-all-permissions-to-admin`, {
      stdio: 'inherit',
    })

    // Vincular as novas permissões nos usuários não admins: stickers.send
    await execSync(`node ${root}/scripts attach-permissions-to-not-admin --permissionsName=stickers.send`, {
      stdio: 'inherit',
    })
  },

  down: async () => {},
}
