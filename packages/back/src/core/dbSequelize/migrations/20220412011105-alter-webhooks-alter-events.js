module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.changeColumn('webhooks', 'events', {
      type: DataTypes.JSONB,
      defaultValue: [],
      allowNull: false,
    })

    await queryInterface.sequelize.query(
      "create index webhooks_events_service_updated_index on webhooks(events) where events ? 'service.updated'",
    )
  },

  down: async (queryInterface, Sequelize) => {
    const { DataTypes } = Sequelize

    await queryInterface.sequelize.query('drop index webhooks_events_service_updated_index')

    await queryInterface.changeColumn('webhooks', 'events', {
      type: DataTypes.JSON,
      defaultValue: [],
      allowNull: false,
    })
  },
}
