module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('contact_block_list_items', ['accountId'])
    await queryInterface.addIndex('contact_block_list_items', ['contactBlockListId'])
    await queryInterface.addIndex('contacts', ['contactBlockListControlId'])
    await queryInterface.addIndex('contact_block_lists', ['userId'])
    await queryInterface.addIndex('contact_block_lists', ['accountId'])
    await queryInterface.addIndex('contact_block_lists_controls', ['userId'])
    await queryInterface.addIndex('contact_block_lists_controls', ['accountId'])
    await queryInterface.addIndex('contact_block_lists_controls', ['contactBlockListId'])
    await queryInterface.addIndex('contact_block_lists_controls', ['serviceId'])
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('contact_block_list_items', ['accountId'])
    await queryInterface.removeIndex('contact_block_list_items', ['contactBlockListId'])
    await queryInterface.removeIndex('contacts', ['contactBlockListId'])
    await queryInterface.removeIndex('contact_block_lists', ['userId'])
    await queryInterface.removeIndex('contact_block_lists', ['accountId'])
    await queryInterface.removeIndex('contact_block_lists_controls', ['userId'])
    await queryInterface.removeIndex('contact_block_lists_controls', ['accountId'])
    await queryInterface.removeIndex('contact_block_lists_controls', ['contactBlockListId'])
    await queryInterface.removeIndex('contact_block_lists_controls', ['serviceId'])
  },
}
