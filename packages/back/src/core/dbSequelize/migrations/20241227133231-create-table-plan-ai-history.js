module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('plan_ai_history', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      accountId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      activity: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      magicText: {
        type: Sequelize.DataTypes.BOOLEAN,
        allowNull: false,
      },
      summary: {
        type: Sequelize.DataTypes.BOOLEAN,
        allowNull: false,
      },
      transcription: {
        type: Sequelize.DataTypes.BOOLEAN,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
    })
  },

  down: (queryInterface) => queryInterface.dropTable('plan_ai_history'),
}
