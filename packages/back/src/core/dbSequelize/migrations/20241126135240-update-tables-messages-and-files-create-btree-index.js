'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      create index if not exists "messages_contactId_idx" 
      on messages using btree ("contactId");    
    `)

    await queryInterface.sequelize.query(`
       create index if not exists "messages_type_idx" 
       on messages using btree ("type");    
     `)

    await queryInterface.sequelize.query(`
       create index if not exists "files_createdAt_idx" 
       on files using btree ("createdAt");    
     `)
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      drop index if exists messages_contactId_idx;    
    `)

    await queryInterface.sequelize.query(`
      drop index if exists messages_type_idx;  
    `)

    await queryInterface.sequelize.query(`
      drop index if exists files_createdAt_idx;   
    `)
  },
}
