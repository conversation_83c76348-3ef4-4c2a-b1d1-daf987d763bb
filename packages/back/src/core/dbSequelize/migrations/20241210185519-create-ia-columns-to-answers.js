module.exports = {
  async up(queryInterface, Sequelize) {
    const { Datatypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('answers')
    if (!tableInfo.aiText) {
      await queryInterface.addColumn('answers', 'aiText', {
        type: Sequelize.TEXT,
        allowNull: true,
      })
    }

    if (!tableInfo.aiGenerated) {
      await queryInterface.addColumn('answers', 'aiGenerated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('answers')
    if (tableInfo.aiText) await queryInterface.removeColumn('answers', 'aiText')
    if (tableInfo.aiGenerated) await queryInterface.removeColumn('answers', 'aiGenerated')
  },
}
