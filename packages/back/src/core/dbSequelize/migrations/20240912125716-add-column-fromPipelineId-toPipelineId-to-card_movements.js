'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    queryInterface
      .describeTable({
        tableName: 'card_movements',
        schema: 'pipeline',
      })
      .then((tb) => {
        if (!tb.fromPipelineId) {
          queryInterface.addColumn(
            {
              tableName: 'card_movements',
              schema: 'pipeline',
            },
            'fromPipelineId',
            {
              type: Sequelize.UUID,
              references: {
                model: 'pipelines',
                key: 'id',
              },
            },
          )
        }
        if (!tb.toPipelineId) {
          queryInterface.addColumn(
            {
              tableName: 'card_movements',
              schema: 'pipeline',
            },
            'toPipelineId',
            {
              type: Sequelize.UUID,
              references: {
                model: 'pipelines',
                key: 'id',
              },
            },
          )
        }
      })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'fromPipelineId',
    )
    await queryInterface.removeColumn(
      {
        tableName: 'card_movements',
        schema: 'pipeline',
      },
      'toPipelineId',
    )
  },
}
