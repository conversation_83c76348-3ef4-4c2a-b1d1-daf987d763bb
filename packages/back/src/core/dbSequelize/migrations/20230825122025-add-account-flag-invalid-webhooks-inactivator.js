import accountRepository from '../repositories/accountRepository'
import setupSequelize from '../../services/db/setupSequelize'
import iteratePaginated from '../../utils/iteratePaginated'

module.exports = {
  up: async () => {
    await setupSequelize()

    await iteratePaginated(
      ({ page }) =>
        accountRepository.findManyPaginated({
          attributes: ['id', 'settings'],
          page: 1,
          perPage: 500,
          noAccountId: true,
        }),
      async (account) =>
        accountRepository.update(
          account,
          {
            settings: {
              flags: {
                ...account.settings.flags,
                'invalid-webhooks-inactivator': true,
              },
            },
          },
          { mergeJson: ['settings'] },
        ),
    )
  },

  down: async () => {},
}
