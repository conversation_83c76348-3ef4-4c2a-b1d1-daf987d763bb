import { v4 as uuid } from 'uuid'
import validator from 'validator'
import whatsappBusinessTemplateResource from '../../resources/whatsappBusinessTemplateResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import setupSequelize from '../../services/db/setupSequelize'
import sequelize from '../../services/db/sequelize'
import Adapter from '../../../microServices/workers/jobs/whatsappBusiness/driver/Adapter'
import fileResource from '../../resources/fileResource'

const removeDuplicatedHsms = async (serviceId) => {
  const uniqueHsmsMap = new Map()

  const duplicatedHsms = await sequelize.query(
    `
  with query as (
    select count(id) qtd, "templateName" from whatsapp_business_hsm wbh
    where wbh."serviceId" = '${serviceId}'
    group by "templateName"
    having count(id) > 1
  )

  select wbh.* from whatsapp_business_hsm wbh
  inner join query q
  on q."templateName" = wbh."templateName"
  where wbh."serviceId" = '${serviceId}'
  order by wbh."templateName"
  `,
    {
      type: sequelize.QueryTypes.SELECT,
    },
  )

  await queuedAsyncMap(duplicatedHsms, async (duplicatedHsm) => {
    const uniqueHsm = uniqueHsmsMap.get(duplicatedHsm.templateName)

    if (!uniqueHsm) {
      uniqueHsmsMap.set(duplicatedHsm.templateName, duplicatedHsm)
      return
    }

    await sequelize.query(`
      update messages
      set "hsmId" = '${uniqueHsm.id}'
      where "hsmId" = '${duplicatedHsm.id}'
    `)

    await sequelize.query(`delete from whatsapp_business_hsm where id = '${duplicatedHsm.id}'`)
  })

  uniqueHsmsMap.clear()
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()

    const services = (
      await sequelize.query(`
    select * from services
    where type = 'whatsapp-business'
    and (data->>'providerType' = '360Dialog'
    or data->>'providerType' = 'positus')
    `)
    )[0]

    await queuedAsyncMap(
      services,
      async (service) => {
        const adapter = new Adapter(service.id)

        let templates = []
        if (!service.deletedAt && service.internalData.token && service.data.status?.isConnected) {
          templates = await adapter.refreshTemplates(service.id).catch((e) => [])
        }

        await removeDuplicatedHsms(service.id)

        const hsms = (
          await sequelize.query(`select * from whatsapp_business_hsm where "serviceId" = '${service.id}'`)
        )[0]

        await queuedAsyncMap(
          hsms,
          async (hsm) => {
            const template = templates.find((t) => t.name === hsm.templateName)

            if (!template) {
              const createdTemplate = await whatsappBusinessTemplateResource.create({
                internalName: hsm.name || hsm.templateName,
                name: hsm.templateName,
                namespace: validator.isUUID((hsm?.namespace || '').replace(new RegExp('_', 'g'), '-'), '4')
                  ? hsm.namespace
                  : uuid(),
                category: 'TRANSACTIONAL',
                rejectedReason: null,
                components: [
                  {
                    text: hsm.templateText,
                    type: 'BODY',
                    params: (hsm.parameters || '').split(',').filter(Boolean),
                  },
                ],
                serviceId: service.id,
                accountId: service.accountId,
                messageType: 'text_only',
                status: service.data?.providerType === 'positus' ? 'APPROVED' : '',
                language: hsm.language,
                deletedAt: hsm.deletedAt,
              })

              await sequelize.query(
                `update whatsapp_business_templates set id = '${hsm.id}' where id = '${createdTemplate.id}'`,
              )

              return
            }

            await fileResource.bulkUpdate(
              {
                attachedId: template.id,
              },
              {
                where: {
                  attachedId: hsm.id,
                  attachedType: 'file.hsm',
                  accountId: hsm.accountId,
                },
                limit: 1,
              },
            )

            const newComponents = template.components
            const index = newComponents.findIndex((c) => c.type === 'BODY')

            if (index == -1) return

            newComponents[index].params = hsm.parameters.split(',').filter(Boolean)

            await whatsappBusinessTemplateResource.update(template, {
              internalName: hsm.name,
              components: newComponents,
            })

            // Mais leve do que atualizar o hsmId das mensagens
            await sequelize.query(`update whatsapp_business_templates set id = '${hsm.id}' where id = '${template.id}'`)
          },
          1,
        )
      },
      1,
    )
  },

  down: async (queryInterface, Sequelize) => {},
}
