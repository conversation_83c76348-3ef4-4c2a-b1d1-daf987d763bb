import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import setupSequelize from '../../services/db/setupSequelize'
import accountResource from '../../resources/accountResource'

const hourTONumber = (hour) => Number(hour.replace(':', '.'))

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await setupSequelize()
    const accounts = await accountResource.findMany({
      attributes: ['id', 'settings'],
      where: {
        $not: {
          'settings.workPlan': null,
        },
      },
    })

    await queuedAsyncMap(accounts, async (account) => {
      const { settings } = account

      if ((settings.workPlan || []).some((wp) => hourTONumber(wp.end) <= hourTONumber(wp.start))) {
        await accountResource.updateById(
          account.id,
          {
            settings: {
              workPlan: settings.workPlan.map((workPlan) => {
                const endIsZero =
                  hourTONumber(workPlan.end) < hourTONumber(workPlan.start) &&
                  workPlan.end === '00:00' &&
                  workPlan.start !== '23:59'

                return {
                  ...workPlan,
                  ...(endIsZero
                    ? { end: '23:59' }
                    : hourTONumber(workPlan.end) <= hourTONumber(workPlan.start) && {
                        start: '00:00',
                        end: '23:59',
                      }),
                }
              }),
            },
          },
          { mergeJson: ['settings'] },
        )
      }
    })
  },

  down: async (queryInterface, Sequelize) => {},
}
