import sequelize from '../../services/db/sequelize'

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await sequelize.query(`
      update
          contacts
      set
          data = jsonb_set(data, '{number}', ('"' || replace("idFromService", '@c.us', '') || '"')::jsonb,true),
          "updatedAt" = current_timestamp
      where "deletedAt" is null
          and "isBroadcast" = false
          and "isGroup" = false
          and "idFromService" is not null
          and rtrim(replace("idFromService", '@c.us', '')) <> ''
          and exists (
          select
              id
          from
              services s
          where s.id = contacts."serviceId"
              and s.type in ('whatsapp', 'whatsapp-business', 'sms-wavy')
              and s."deletedAt" is null
          limit 1
      )`)
  },

  down: async (queryInterface, Sequelize) => {},
}
