import Summary, { SummaryInstance } from '../models/Summary'
import BaseRepository from './BaseRepository'
import Logger from '../../services/logs/Logger'
import Container from 'typedi'

const logger = Container.get(Logger)

class SummaryRepository extends BaseRepository<SummaryInstance> {
  constructor() {
    // @ts-ignore
    super(Summary)
  }

  async inQueue(ticketId: string): Promise<boolean> {
    try {
      if (!ticketId) return false

      const count = await this.count({
        where: {
          ticketId,
          isProcessing: true,
        },
      })

      return count > 0
    } catch (error) {
      logger.log(`Falha ao verificar se ticket está na fila de resumo: ${(error as Error).message}`, 'error')
      return false
    }
  }

  async markAsFinished(summaryId: string): Promise<boolean> {
    try {
      if (!summaryId) return false

      await this.updateById(summaryId, {
        finishedAt: new Date(),
        isProcessing: false,
      })

      return true
    } catch (error) {
      logger.log(`Falha ao marcar summary como finalizado: ${(error as Error).message}`, 'error')
      return false
    }
  }

  async markTicketSummariesAsFinished(ticketId: string): Promise<boolean> {
    try {
      if (!ticketId) return false

      const summaries = await this.findMany({
        where: {
          ticketId,
          finishedAt: null,
        },
      })

      if (!summaries || summaries.length === 0) return false

      await this.bulkUpdate(
        {
          finishedAt: new Date(),
          isProcessing: false,
        },
        {
          where: {
            id: { $in: summaries.map((summary) => summary.id) },
          },
        },
      )

      return true
    } catch (error) {
      logger.log(`Falha ao marcar summaries do ticket como finalizados: ${(error as Error).message}`, 'error')
      return false
    }
  }

  async markAsProcessing(summaryId: string): Promise<boolean> {
    try {
      if (!summaryId) return false

      await this.updateById(summaryId, {
        isProcessing: true,
      })

      return true
    } catch (error) {
      logger.log(`Falha ao marcar summary como em processamento: ${(error as Error).message}`, 'error')
      return false
    }
  }
}

export default new SummaryRepository()
