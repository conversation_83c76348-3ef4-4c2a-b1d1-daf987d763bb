import { QueryTypes } from 'sequelize'
import Tag from '../models/Tag'
import BaseRepository from './BaseRepository'
import sequelize from '../../../core/services/db/sequelize'

class TagRepository extends BaseRepository {
  constructor() {
    super(Tag)
  }

  async countContactsByTagId(tagId) {
    return sequelize
      .query(
        `
      select count(*) from contact_tags ct
      inner join contacts c on c.id = ct."contactId" and c."deletedAt" is null
      inner join tags t on t.id = ct."tagId" and t."deletedAt" is null
      where ct."tagId" = :tagId
      `,
        { replacements: { tagId }, type: QueryTypes.SELECT },
      )
      .then(([{ count = 0 }]) => count)
      .catch(() => 0)
  }
}

export default new TagRepository()
