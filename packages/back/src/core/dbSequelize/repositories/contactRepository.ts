import sequelize from '../../services/db/sequelize'
import Contact, { ContactInstance } from '../models/Contact'
import BaseRepository from './BaseRepository'
import { Options } from './BaseSequelizeRepository'
import { Container } from 'typedi'
import Logger from '../../services/logs/Logger'
import comparableIdFromService from '../../utils/whatsapp/comparableIdFromService'
import serviceResource from '../../resources/serviceResource'

const logger = Container.get(Logger)

export class ContactRepository extends BaseRepository<ContactInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(Contact, defaultOptions)
    this.findMany = this.customFindMany
    this.findManyPaginated = this.customFindManyPaginated
    this.findManyWithTotal = this.customFindManyWithTotal
    this.count = this.customCount
  }

  customBuildOptions(
    options: Options<ContactInstance> & {
      customFilter?: { [key: string]: any }
      noTag?: boolean
    } = {},
  ) {
    if (options.customFilter?.tags || options.noTag) {
      if (!options.where) options.where = {}

      if (options.where.id) {
        logger.log(
          `There was already a where.id and it was override by tags sub-query. Where.id: $s`,
          'warn',
          options.where.id,
        )
      }

      options.where = {
        ...options.where,
        $and: sequelize.literal(`(${this.makeTagFilterSubQuery(options?.customFilter?.tags || [], options.noTag)})`),
      }
    }
    return options
  }

  makeTagFilterSubQuery = (tagIds: string[], notTag?: boolean): string => {
    if (notTag) {
      return `not exists (select ct."contactId" from contact_tags ct where ct."contactId" = "Contact".id limit 1)`
    }
    tagIds = (tagIds || []).reduce((acc, curr) => {
      if (!acc[curr]) acc.push(curr)
      return acc
    }, [])

    const inTagIds = tagIds.map((id, i) => `'${id}'`)
    return `(select count(distinct ct."tagId" ) from contact_tags ct where ct."tagId" in (${inTagIds.join(
      ',',
    )}) and ct."contactId" = "Contact".id) = ${tagIds?.length}`
  }

  async numberIsBlockedBySomeBlocklist(
    idFromService,
    serviceId,
  ): Promise<{
    id: string
    reason: string
    userId: string
    contactBlockListId: string
    action: 'block' | 'unblock'
    createdAt: Date
  } | null> {
    const query = `
      with subquery as (
      select
          "id",
          "reason",
          "userId",
          "contactBlockListId",
          "action",
          "createdAt",
          row_number() over (
              partition by "contactBlockListId"
              order by
                  "createdAt" desc
          ) as idx
      from
          contact_block_lists_controls cblc2
      where
          "serviceId" = :serviceId
          and cblc2."createdAt" >= coalesce((
            select max(cblc3."createdAt")
            from contact_block_lists_controls cblc3
            where cblc3."serviceId" = cblc2."serviceId"
            and cblc3."revertExceptions" = true
       ), cblc2."createdAt")
      )
      select
          id, reason, "userId", "contactBlockListId", action, "createdAt"
      from
          subquery
      where
          idx = 1
          and action = 'block'
      order by
          "createdAt" desc
    `

    const [contactBlockListsControls = []] = await sequelize.query(query, {
      replacements: {
        serviceId,
      },
    })

    if (!contactBlockListsControls?.length) return

    const ids = contactBlockListsControls.map((cbl) => cbl.contactBlockListId)

    const service = await serviceResource.findById(serviceId, {
      attributes: ['type'],
    })

    const query2 = `
      select
          id, "contactBlockListId"
      from
          contact_block_list_items cbli
      where
          "contactBlockListId" in (:ids)
          and "idFromService" in (:comparableIds)
      limit
          1;
    `
    //IdFromService na Blocklist corresponde a números de telefone apenas
    //Para números whatsapp precisa remover o server do id "server é o que fica depois do @. Ex: c.us"
    const comparableIds = comparableIdFromService(idFromService, service.type).map((item) => (item || '').split('@')[0])
    const [response = []] = await sequelize.query(query2, {
      replacements: {
        ids,
        comparableIds,
      },
    })

    if (!response.length) return

    const contactBlockListsControl = contactBlockListsControls.find(
      (i) => i.contactBlockListId === response[0].contactBlockListId,
    )

    return contactBlockListsControl
  }

  customFindManyPaginated(
    options?: Options<ContactInstance> & { page?: string | number; perPage?: string | number; noAccountId?: boolean },
  ): Promise<{
    data: any
    total: any
    limit: any
    skip: any
    currentPage: number
    lastPage: number
    from: number
    to: number
  }> {
    return super.findManyPaginated(this.customBuildOptions(options))
  }

  customFindMany(options?: Options<ContactInstance>): Promise<ContactInstance[]> {
    return super.findMany(this.customBuildOptions(options))
  }
  customFindManyWithTotal<ContactInstance>(
    options?: Options<ContactInstance>,
  ): Promise<{ data: ContactInstance[]; total: number }> {
    return super.findManyWithTotal(this.customBuildOptions(options))
  }

  customCount(options: Options<ContactInstance>) {
    return super.count(this.customBuildOptions(options))
  }
}

export default new ContactRepository()
