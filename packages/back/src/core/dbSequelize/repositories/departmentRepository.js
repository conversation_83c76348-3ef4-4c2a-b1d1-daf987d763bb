import sequelize from '../../../core/services/db/sequelize'
import Department from '../models/Department'
import BaseRepository from './BaseRepository'

export class DepartmentRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Department, defaultQuery)
  }

  async getTotalTicketsByDepartment(userId, departmentsIds) {
    const ticketsSubQuery = `(
      SELECT COUNT(1) 
      FROM tickets AS t 
      WHERE t."isOpen" = true 
      AND t."departmentId" = "Departments".id
      AND t."userId" = '${userId}'
    )`

    return this.findMany({
      attributes: ['id', [sequelize.literal(ticketsSubQuery), 'countTickets']],
      where: { id: { $in: departmentsIds } },
    })
  }
}

export default new DepartmentRepository()
