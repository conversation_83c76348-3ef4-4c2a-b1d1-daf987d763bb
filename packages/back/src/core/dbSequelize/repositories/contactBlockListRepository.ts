import BaseRepository from './BaseRepository'
import Contact<PERSON>lockList, { ContactBlockListInstance } from '../models/ContactBlockList'
import sequelize from '../../services/db/sequelize'

class ContactBlockListRepository extends BaseRepository<ContactBlockListInstance> {
  constructor() {
    // @ts-ignore
    super(ContactBlockList)
  }

  async removeItems(id) {
    return sequelize.query(`delete from contact_block_list_items where "contactBlockListId" = :id`, {
      replacements: {
        id,
      },
    })
  }
}

export default new ContactBlockListRepository()
