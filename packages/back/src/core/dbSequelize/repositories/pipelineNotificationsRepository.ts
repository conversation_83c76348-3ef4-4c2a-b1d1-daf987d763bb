import PipelineNotifications, { PipelineNotificationsInstance } from '../models/PipelineNotifications'
import BaseRepository from './BaseRepository'

export class PipelineNotificationsRepository extends BaseRepository<PipelineNotificationsInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(PipelineNotifications, defaultOptions)
  }
}

export default new PipelineNotificationsRepository()
