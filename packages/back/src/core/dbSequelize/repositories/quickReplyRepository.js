import QuickReply from '../models/QuickReply'
import BaseRepository from './BaseRepository'
import sequelize from '../../../core/services/db/sequelize'

class QuickReplyRepository extends BaseRepository {
  constructor() {
    super(QuickReply)
  }

  async findManyWithTotal(query, departmentIds) {
    return super.findManyWithTotal({
      ...query,
      where: {
        $and: [
          {
            $or: [
              sequelize.literal(`
                (SELECT COUNT(1) FROM quick_reply_departments AS qrd WHERE qrd."quickReplyId" = "QuickReply".id) = 0
              `),
              sequelize.literal(`
                (SELECT COUNT(1) FROM quick_reply_departments AS qrd WHERE qrd."quickReplyId" = "QuickReply".id and qrd."departmentId" in ('${departmentIds.join(
                  "', '",
                )}')) > 0
              `),
            ],
          },
        ],
        ...query.where,
      },
    })
  }
}

export default new QuickReplyRepository()
