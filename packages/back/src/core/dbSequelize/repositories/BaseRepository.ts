import { Model } from 'sequelize'
// eslint-disable-next-line import/no-cycle
import { Instance } from '../models'
// eslint-disable-next-line import/no-cycle
import BaseSequelizeRepository from './BaseSequelizeRepository'
// eslint-disable-next-line import/no-cycle
import CachedBaseRepository from './CachedBaseRepository'
import ResourceCache from '../../services/redis/ResourceCache'
import config from '../../config'

export default class BaseRepository<T extends Instance> extends BaseSequelizeRepository<T> {
  getCache: () => ResourceCache

  getCached: () => CachedBaseRepository<T>

  constructor(model: Model<T, any>, options: {}) {
    super(model, options)

    const cached = new CachedBaseRepository<T>(model, options)
    const sequelize = new BaseSequelizeRepository(model, options)

    const wrap = (methodName, position = 0) => {
      return function (...args) {
        const useCache = args[position]?.cache || config('useCacheForAllQueries')

        if (useCache) return cached[methodName](...args)

        return sequelize[methodName](...args)
      }
    }

    this.findMany = wrap('findMany')
    this.findManyPaginated = wrap('findManyPaginated')
    this.findOne = wrap('findOne')
    this.findById = wrap('findById', 1)
    this.existsById = wrap('existsById', 1)
    this.exists = wrap('exists')
    this.updateByIdNoSelect = wrap('updateByIdNoSelect', 2)
    this.create = cached.create
    this.update = cached.update
    this.bulkCreate = cached.bulkCreate
    this.bulkUpdate = cached.bulkUpdate
    // @ts-ignore
    this.bulkDestroy = cached.bulkDestroy
    this.updateById = cached.updateById
    this.destroy = cached.destroy
    // @ts-ignore
    this.destroyById = cached.destroyById
    this.getCache = cached.getCache
    this.getCached = () => cached
  }
}
