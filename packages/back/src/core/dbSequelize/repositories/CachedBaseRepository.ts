import { flattenDeep, pick } from 'lodash'
import { Container } from 'typedi'
// eslint-disable-next-line import/no-cycle
import BaseSequelizeRepository, { Include, parseInclude, prepareInclude } from './BaseSequelizeRepository'
import ResourceCache from '../../services/redis/ResourceCache'
// eslint-disable-next-line import/no-cycle
import { Instance } from '../models'
import Logger from '../../services/logs/Logger'

const getAccountIdsFromData = (data: Instance | Instance[]): string[] =>
  // @ts-ignore
  (Array.isArray(data) ? data.map(getAccountIdsFromData) : [data?.accountId]) || []

const getAccountIdsFromQuery = (
  query: { where?: { accountId?: string } } | { where?: [{ accountId?: string }] },
): string => {
  if (Array.isArray(query.where)) {
    return query.where.find((q) => q?.accountId)?.accountId
  }

  return query?.where?.accountId
}

const makeListKeyWithAccountId = (accountId?: string) => `${accountId ? `${accountId}:` : 'no-account:'}list`

export function makeIncludeTags(
  include: Include[],
  data: Instance | Instance[],
  whitelist?: string[],
  accountId?: string,
): string[] {
  if (Array.isArray(data)) {
    return flattenDeep(data.map((model) => makeIncludeTags(include, model, whitelist, accountId))).filter(Boolean)
  }

  return flattenDeep(
    (include || [])
      .map((innerInclude: Include) => {
        if (!data) return null

        const relationName =
          innerInclude.as ||
          (typeof innerInclude.model === 'string' ? innerInclude.model : innerInclude.model?.name) ||
          innerInclude

        // @ts-ignore
        if (Array.isArray(whitelist) && !whitelist.includes(relationName)) {
          return null
        }

        // @ts-ignore
        const relationObjOrArr = data[relationName]
        const relationAccountId = getAccountIdsFromData(relationObjOrArr)?.[0] || accountId

        // null
        if (!relationObjOrArr) return []

        // array
        if (Array.isArray(relationObjOrArr)) {
          return relationObjOrArr.map((model) => [
            `${model.constructor.options.name.singular}:${makeListKeyWithAccountId(relationAccountId)}`,
            `${model.constructor.options.name.singular}:${model.id}`,
            ...makeIncludeTags(
              innerInclude.include,
              // @ts-ignore
              model[relationName],
              whitelist,
              relationAccountId,
            ),
          ])
        }

        // single
        return [
          `${relationObjOrArr.constructor.options.name.singular}:${makeListKeyWithAccountId(relationAccountId)}`,
          `${relationObjOrArr.constructor.options.name.singular}:${relationObjOrArr.id}`,
          ...makeIncludeTags(innerInclude.include, relationObjOrArr, whitelist, relationAccountId),
        ]
      })
      .filter(Boolean),
  )
}

const isCachedObject = (obj) => {
  return obj && typeof obj === 'object' && !obj.sequelize
}

const makeIncludeKeysWithQueryAndData = (query, data: Instance[]) =>
  query.include && data
    ? makeIncludeTags(
        prepareInclude(query.include),
        data,
        query.cacheIncludeWhitelist,
        getAccountIdsFromData(data)?.[0],
      )
    : []

const logger = Container.get(Logger)

// TODO handle raw SQL queries
export default class CachedBaseRepository<T extends Instance> extends BaseSequelizeRepository<T> {
  constructor(repository, ...rest) {
    super(repository, ...rest)

    this.findMany = this.findMany.bind(this)
    this.findManyPaginated = this.findManyPaginated.bind(this)
    this.findOne = this.findOne.bind(this)
    this.findById = this.findById.bind(this)
    this.create = this.create.bind(this)
    this.update = this.update.bind(this)
    this.bulkCreate = this.bulkCreate.bind(this)
    this.bulkUpdate = this.bulkUpdate.bind(this)
    this.bulkDestroy = this.bulkDestroy.bind(this)
    this.updateById = this.updateById.bind(this)
    this.destroy = this.destroy.bind(this)
    this.destroyById = this.destroyById.bind(this)
    this.existsById = this.existsById.bind(this)
    this.exists = this.exists.bind(this)
  }

  protected getResourceName() {
    return this.getModel().name
  }

  protected makeKey(key: string) {
    return `${this.getResourceName()}:${key}`
  }

  protected makeListKeyFromQuery(query) {
    const accountId = getAccountIdsFromQuery(query)
    return this.makeKey(makeListKeyWithAccountId(accountId))
  }

  protected makeListKeyFromData(data: T | T[]): string[] {
    const accountIds = getAccountIdsFromData(data)
    const ids = (Array.isArray(data) ? data.map((d) => d.id) : [data?.id]) || []
    return [
      ...accountIds.map((accountId) => this.makeKey(makeListKeyWithAccountId(accountId))),
      ...ids.map((id) => this.makeKey(id)),
    ]
  }

  protected parseQueryToKey(query) {
    return pick(query, [
      'attributes',
      'where',
      'include',
      'fields',
      'order',
      'subQuery',
      'through',
      'offset',
      'limit',
      'raw',
      'page',
      'perPage',
    ])
  }

  protected buildWithIncludes(data: any, include: any[] = []) {
    return this.build(data, {
      isNewRecord: false,
      // @ts-ignore
      include: parseInclude(this.getModel(), include),
    })
  }

  protected buildWithIncludesIfCached(data, query) {
    return isCachedObject(data) && !query.raw ? this.buildWithIncludes(data, query.include) : data
  }

  protected buildListTagsFromDataAndQuery(data, query) {
    // console.log('this.makeListKeyFromQuery(query)', query, this.makeListKeyFromQuery(query))
    return [
      // every model id
      ...data.map((m) => this.makeKey(m.id)),
      // every relation id and account id
      ...makeIncludeKeysWithQueryAndData(query, data),
      // accountId
      this.makeListKeyFromQuery(query),
    ]
  }

  protected buildSingleTagsFromDataAndQuery(id: string, data, query) {
    return [this.makeKey(id), ...makeIncludeKeysWithQueryAndData(query, data)]
  }

  findManyPaginated(query) {
    return this.getCache()
      .remember(
        [this.getResourceName(), this.parseQueryToKey(query)],
        () => super.findManyPaginated(query),
        (val) => this.buildListTagsFromDataAndQuery(val.data, query),
      )
      .then((res) =>
        res.data.length
          ? {
              ...res,
              data: res.data.map((m) => this.buildWithIncludesIfCached(m, query)),
            }
          : res,
      )
  }

  findMany(query) {
    return this.getCache()
      .remember(
        [this.getResourceName(), this.parseQueryToKey(query)],
        () => super.findMany(query),
        (data = []) => this.buildListTagsFromDataAndQuery(data, query),
      )
      .then((res) => res.map((m) => this.buildWithIncludesIfCached(m, query)))
  }

  // @ts-ignore
  findById(id, query) {
    return this.getCache()
      .remember(
        [this.getResourceName(), id, this.parseQueryToKey(query)],
        () => super.findById(id, query),
        (data) => this.buildSingleTagsFromDataAndQuery(id, data, query),
      )
      .then((res) => this.buildWithIncludesIfCached(res, query))
  }

  // @ts-ignore
  findOne(query) {
    return this.getCache()
      .remember(
        [this.getResourceName(), this.parseQueryToKey(query)],
        () => super.findOne(query),
        (data) => this.buildSingleTagsFromDataAndQuery(data.id, data, query),
      )
      .then((res) => this.buildWithIncludesIfCached(res, query))
  }

  // @ts-ignore
  async bulkCreate(data, options = {}) {
    const res = await super.bulkCreate(data, options)
    await this.forgetCacheListFromData(res)

    return res
  }

  async create(data, options = {}) {
    const res = await super.create(data, options)
    await this.forgetCacheListFromData(res)

    return res
  }

  async update(model, data, options = {}) {
    const res = await super.update(model, data, options)
    await this.forgetCacheListFromData(res)

    return res
  }

  // @ts-ignore
  async bulkUpdate(data, options = {}) {
    const [, res] = await super.bulkUpdate(data, options)
    await this.forgetCacheListFromData(res)

    return res
  }

  // @ts-ignore
  async bulkDestroy(options = {}) {
    const res = await super.bulkDestroy(options)
    // await this.forgetCacheListFromData(res)
    logger.log('bulkDestroy cant invalidate cache: %o', 'warn', [options])

    return res
  }

  async updateById(id, data, options) {
    const res = await super.findById(id, options).then((model) => super.update(model, data, options))
    await this.forgetCacheListFromData(res)

    return res
  }

  async updateByIdNoSelect(id, data, options) {
    const res = await super.updateByIdNoSelect(id, data, options)
    await this.forgetCacheListFromData(res)

    return res
  }

  async destroy(model, options = {}) {
    const res = await super.destroy(model, options)
    await this.forgetCacheById(model.id)

    return res
  }

  async destroyMany(query, options = {}) {
    const res = await super.destroyMany(query, options)
    await this.forgetCacheById(res.map((m) => m.id))

    return res
  }

  // @ts-ignore
  async destroyById(id, options = {}) {
    const res = await super.destroy(id, options)

    await this.forgetCacheListAndId(id)

    return res
  }

  forgetCacheListFromData(data: T | T[]) {
    return this.getCache().forgetByTags([...this.makeListKeyFromData(data), this.makeKey(`no-account:list`)])
  }

  forgetCacheById(id: string | string[]) {
    const ids = Array.isArray(id) ? id : [id]
    return this.getCache().forgetByTags(ids.map((id) => this.makeKey(id)))
  }

  forgetCacheList() {
    return this.getCache().forgetByTags([this.makeKey('no-account:list')])
  }

  forgetCacheListAndId(id: string) {
    return this.getCache().forgetByTags([this.makeKey(id), this.makeKey('no-account:list')])
  }

  getCache() {
    return Container.get(ResourceCache)
  }
}
