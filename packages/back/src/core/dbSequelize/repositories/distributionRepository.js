import { Container } from 'typedi'
import Distribution from '../models/Distribution'
import BaseRepository from './BaseRepository'
import sequelize from '../../services/db/sequelize'
import Logger from '../../services/logs/Logger'
import ticketResource from '../../resources/ticketResource'

export class DistributionRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Distribution, defaultQuery)
    this.logger = Container.get(Logger)
  }

  // Conta quantos tickets estão atualmente em processo de distribuição
  async totalTicketsDistributing() {
    try {
      const count = await ticketResource.count({
        where: {
          isDistributing: true,
          isOpen: true,
          endedAt: { $eq: null },
        },
      })
      return count
    } catch (error) {
      this.logger.log(`Falha ao contar tickets em distribuição: ${error.message}`, 'error')
      return 0
    }
  }

  // Verifica se um ticket está em processo de distribuição
  async isTicketDistributing(ticketId) {
    try {
      const ticket = await ticketResource.findById(ticketId, {
        attributes: ['isDistributing'],
      })
      return ticket?.isDistributing || false
    } catch (error) {
      this.logger.log(`Falha ao verificar se ticket está em distribuição: ${error.message}`, 'error')
      return false
    }
  }

  // Marca um ticket como não estando mais em distribuição
  async markTicketAsNotDistributing(ticketId) {
    try {
      await ticketResource.updateById(ticketId, { isDistributing: false }, { dontEmit: true })
      return true
    } catch (error) {
      this.logger.log(`Falha ao marcar ticket como não distribuindo: ${error.message}`, 'error')
      return false
    }
  }

  // Marca múltiplos tickets como não estando mais em distribuição
  async markTicketsAsNotDistributing(ticketIds) {
    if (!ticketIds || !ticketIds.length) return true

    try {
      await ticketResource.bulkUpdate(
        { isDistributing: false },
        {
          where: {
            id: { $in: ticketIds },
          },
          dontEmit: true,
        },
      )
      return true
    } catch (error) {
      this.logger.log(`Falha ao marcar tickets como não distribuindo: ${error.message}`, 'error')
      return false
    }
  }

  // Obtém os tickets que estão travados em distribuição por muito tempo
  async getBlockedTickets(departmentIds, accountId, minutes = 10) {
    try {
      const timeHasBlocked = new Date()
      timeHasBlocked.setMinutes(timeHasBlocked.getMinutes() - minutes)

      const blockedTickets = await ticketResource.findMany({
        where: {
          accountId,
          departmentId: { $in: departmentIds },
          isDistributing: true,
          updatedAt: { $lte: timeHasBlocked },
        },
      })

      return blockedTickets
    } catch (error) {
      this.logger.log(`Falha ao buscar tickets bloqueados: ${error.message}`, 'error')
      return []
    }
  }
}

export default new DistributionRepository()
