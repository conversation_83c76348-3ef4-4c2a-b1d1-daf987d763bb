import { QueryTypes } from 'sequelize'
import CardMovement, { CardMovementInstance } from '../models/CardMovement'
import BaseRepository from './BaseRepository'
import sequelize from '../../services/db/sequelize'

export class CardMovementRepository extends BaseRepository<CardMovementInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(CardMovement, defaultOptions)
  }

  async getAllStoppedCards(pipelineId: string, mediumDate: string, highDate?: string): Promise<number> {
    const getDateCondition = () => {
      if (!highDate)
        return 'and (select max(cm2."createdAt") from pipeline.card_movements cm2 where cm2."cardId" = cm."cardId") <= :mediumDate'
      return 'and (select max(cm2."createdAt") from pipeline.card_movements cm2 where cm2."cardId" = cm."cardId") between :highDate and :mediumDate'
    }

    const query = `
      select count(distinct cm."cardId") as count
      from pipeline.card_movements as cm
      inner join pipeline.cards c 
      on c.id = cm."cardId"
      and c."finishedAt" is null
      and c."archivedAt" is null
      where cm."pipelineId" = :pipelineId
      ${getDateCondition()}
    `

    const result = await sequelize.query(query, {
      replacements: {
        pipelineId,
        mediumDate,
        highDate,
      },
      type: QueryTypes.SELECT,
    })
    const { count = 0 } = result?.[0]
    return parseInt(count)
  }
}

export default new CardMovementRepository()
