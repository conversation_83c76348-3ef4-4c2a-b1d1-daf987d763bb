import Person from '../models/Person'
import BaseRepository from './BaseRepository'

export class PersonRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Person, defaultQuery)
  }

  getLastContact = async (id) => {
    const person = await this.findOne({
      where: { id },
      include: {
        model: 'contacts',
        order: [['lastMessageAt', 'DESC']],
        limit: 1,
        include: ['service'],
      },
    })
    return person.contacts[0]
  }
}

export default new PersonRepository()
