import { v4 as uuid } from 'uuid'
import BaseRepository from './BaseRepository'
import LowLevelContact, { LowLevelContactInstance } from '../models/LowLevelContact'
import sequelize from '../../services/db/sequelize'

class LowLevelContactRepository extends BaseRepository<LowLevelContactInstance> {
  constructor() {
    // @ts-ignore
    super(LowLevelContact)
  }

  customUpsert(data, options = {}): PromiseLike<LowLevelContactInstance> {
    const replacements = {
      ...data,
      data: JSON.stringify(data.data),
      id: uuid(),
    }

    return sequelize.query(
      `INSERT INTO low_level_contacts as l (id, "idFromService", data, "serviceId", "accountId", "createdAt", "updatedAt")
VALUES (:id, :idFromService, :data, :serviceId, :accountId, now(), now())
ON CONFLICT ("idFromService", "serviceId") DO UPDATE
SET data = (l.data || :data), "updatedAt" = now()`,
      { ...options, replacements, type: sequelize.QueryTypes.INSERT },
    )
  }
}

export default new LowLevelContactRepository()
