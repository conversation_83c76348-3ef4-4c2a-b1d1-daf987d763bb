/* eslint-disable class-methods-use-this */
import { pick, groupBy, union, identity, isObject } from 'lodash'
import { pipe } from 'lodash/fp'
import assignDeep from 'assign-deep'
import { Model, Sequelize } from 'sequelize'
import config from '../../config'
import sequelize from '../../services/db/sequelize'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
// eslint-disable-next-line import/no-cycle
import models, { Instance } from '../models'
import { Container } from 'typedi'
import Logger from '../../services/logs/Logger'

// @TODO remover no futuro
const DEBUG_PAGINATION_COUNT = config('debugPaginationCount')

type Source = {
  name: string
  associations: { [model: string]: { target: Source } }
}

export type Include = {
  model: Source
  as?: string
  include?: Include[]
  required?: boolean
  where?: object
}

type RawInclude =
  | string
  | {
      model: string | Source
      as?: string
      include?: RawInclude | RawInclude[]
      required?: boolean
      where?: object
    }

export const expandInclude = (include: RawInclude | RawInclude[]) => {
  if (!include) return []

  // Array
  if (Array.isArray(include)) {
    return include.map((inc) => expandInclude(inc))
  }

  // String
  if (typeof include === 'string') {
    if (!include.includes('.')) {
      return {
        model: include,
        as: include,
      }
    }

    const [head, ...tail] = include.split('.')

    return expandInclude({
      model: head,
      include: [tail.join('.')],
    })
  }

  // Object { model: 'string' }
  let { model } = include

  if (typeof include.model === 'string') {
    model = include.model
  }

  const as = include.as || include.model

  return {
    ...include,
    model,
    as,
    include: expandInclude(include.include),
  }
}

export const dedupeInclude = (includes: Include[]) => {
  const grouped = groupBy(includes, (i) => i.model)

  return Object.entries(grouped).reduce((accum, [includeName, includeObjs]) => {
    const includeIncludes = union(...includeObjs.map((i) => i.include).filter(identity))

    accum.push({
      ...includeObjs.reduce((curr, obj) => ({ ...obj, ...curr }), {}),
      model: includeName,
      as: includeName,
      ...(includeIncludes.length && {
        include: dedupeInclude(includeIncludes),
      }),
    })

    return accum
  }, [])
}

export const prepareInclude = pipe(expandInclude, dedupeInclude)

export const parseInclude = (source: Source, include: RawInclude | RawInclude[]) => {
  if (!include) return []

  if (Array.isArray(include)) {
    return include.map((inc) => parseInclude(source, inc))
  }

  if (typeof include === 'string') {
    if (!include.includes('.')) {
      if (!source.associations[include]) {
        throw new Error(`Model ${source.name} has no association with name ${include}.`)
      }

      return {
        model: source.associations[include].target,
        as: include,
      }
    }

    const [head, ...tail] = include.split('.')

    return parseInclude(source, {
      model: head,
      include: [tail.join('.')],
    })
  }

  let { model } = include

  if (typeof include.model === 'string') {
    if (!source.associations[include.model]) {
      throw new Error(`Model ${source.name} has no association with name ${include.model}.`)
    }

    model = source.associations[include.model].target
  }

  const as = include.as || include.model

  const associationType = models[source.name]?.associations?.[as]?.associationType

  const shouldUseSeparate =
    associationType === 'HasMany' && !include.required && !(Object.keys(include.where || {}).length > 0)

  if (shouldUseSeparate) {
    console.log('Association', source.name, '->', associationType, '->', as, 'using separate:true')
  }

  return {
    ...include,
    model,
    as,
    include: parseInclude(model as Source, include.include),
    separate: shouldUseSeparate,
  }
}

export const parseOrder = (source, order) => {
  if (!Array.isArray(order)) return order

  const [modelOrColumn, ...rest] = order

  if (typeof modelOrColumn !== 'object' || typeof modelOrColumn.model !== 'string') {
    return order
  }

  if (!source.associations[modelOrColumn.model]) {
    throw new Error(`Model ${source.name} has no association with name ${modelOrColumn.model}.`)
  }

  const association = source.associations[modelOrColumn.model]

  return [association, ...rest]
}
export const parseOrders = (source, orders) => {
  return typeof orders?.val // Literals
    ? orders
    : orders.map((order) => parseOrder(source, order))
}

export const filterIncludeForCountQuery = (includes = []) => {
  if (!Array.isArray(includes) && isObject(includes)) {
    // eslint-disable-next-line no-param-reassign
    includes = [includes]
  }

  return includes.reduce((final, include) => {
    const innerInclude = include.include ? filterIncludeForCountQuery(include.include) : []
    if (include.where || include.required || innerInclude.length) {
      final.push({
        ...include,
        include: innerInclude,
      })
    }

    return final
  }, [])
}

// Foi implementado para remover joins desnecessários da query de count.
// Em uma instancia do banco com 300k contatos, uma query com diversos joins (list de contacts do chat),
// teve melhoria significativa, caindo de ~30s para 1s.
export const filterCountQuery = (query) =>
  pick(
    {
      ...query,
      include: filterIncludeForCountQuery(query.include),
    },
    ['include', 'where', 'distinct'],
  )

export type Options<T> = {
  attributes?: string[] | object
  where?: any
  include?: any[]
  fields?: any[]
  order?: any
  offset?: number
  limit?: number
  transaction?: any
  returning?: boolean
  plain?: boolean
  subQuery?: boolean
  ignoreDuplicates?: boolean
  logging?: Console['log'] | boolean
}

type BuildOptions<T> = Options<T> & {
  isNewRecord?: boolean
}

type PaginatedOptions<T> = Options<T> & {
  page?: number | string
  perPage?: number | string
  noAccountId?: boolean
}

const logger = Container.get(Logger)

export default class BaseSequelizeRepository<T extends Instance> {
  protected readonly model: Model<T, any>

  protected readonly options: any

  constructor(model: Model<T, any>, options = {}) {
    this.model = model
    this.options = options

    this.getModel = this.getModel.bind(this)
    this.findManyPaginated = this.findManyPaginated.bind(this)
    this.findMany = this.findMany.bind(this)
    this.findManyByIds = this.findManyByIds.bind(this)
    this.findOne = this.findOne.bind(this)
    this.findById = this.findById.bind(this)
    this.exists = this.exists.bind(this)
    this.existsById = this.existsById.bind(this)
    this.reload = this.reload.bind(this)
    this.create = this.create.bind(this)
    this.update = this.update.bind(this)
    this.bulkCreate = this.bulkCreate.bind(this)
    this.bulkUpdate = this.bulkUpdate.bind(this)
    this.bulkDestroy = this.bulkDestroy.bind(this)
    this.destroy = this.destroy.bind(this)
    this.destroyById = this.destroyById.bind(this)
    this.destroyMany = this.destroyMany.bind(this)
    this.updateById = this.updateById.bind(this)
    this.findOrCreate = this.findOrCreate.bind(this)
    this.transaction = this.transaction.bind(this)
    this.touch = this.touch.bind(this)
    this.ensureCorrectType = this.ensureCorrectType.bind(this)
    this.build = this.build.bind(this)
  }

  ensureCorrectType(model: T) {
    // @ts-ignore
    if (model instanceof this.model) return

    if (!model) throw new Error(`Model is ${model}.`)

    throw new Error(
      `Wrong model type for this repository.
      Want's ${this.model.constructor.name}, received ${model.constructor.name}.`,
    )
  }

  getModel(): Model<T, any> {
    return this.model
  }

  transaction<TR>(fn: (transaction: any) => TR): TR {
    // @ts-ignore
    return sequelize.transaction(fn)
  }

  nestedTransaction<TR>(fn: (transaction: any) => TR, transaction): TR {
    // this is a workaround as real nested transactions from sequelize
    // wasnt working, it timesout on taskQueue
    if (transaction) return fn(transaction)
    // @ts-ignore
    return sequelize.transaction(fn)
  }

  buildOptions(options: Options<T> = {}) {
    options.logging = options.logging ? console.log : false

    // @ts-ignore
    const include = parseInclude(this.model, options.include)
    const order = options.order && parseOrders(this.model, options.order)

    return {
      ...this.options,
      ...options,
      include,
      order,
    }
  }

  build(modelData: any, options: BuildOptions<T> = { isNewRecord: false }): T {
    return this.model.build(modelData, options)
  }

  async findManyPaginated(options: PaginatedOptions<T> = {}) {
    const DEFAULT_PER_PAGE = 15

    const page = parseInt(`${options.page || '1'}`, 10)
    const perPage = parseInt(`${options.perPage || DEFAULT_PER_PAGE}`, 10)
    const from = (page - 1) * perPage
    const to = page * perPage
    const limit = to - from

    const q = this.buildOptions({
      offset: from,
      limit,
      ...pick(options, [
        'attributes',
        'where',
        'order',
        'include',
        'transaction',
        'logging',
        'subQuery',
        'through',
        'group',
      ]),
    })

    if (!options.noAccountId && !q?.where?.accountId) {
      console.trace('Missing accountId on query.')
    }

    const count = await this.doCountQuery(q)

    const result = await this.model.findAll(q)

    const lastPage = Math.ceil(count / perPage) || 1

    return {
      data: result,
      total: count,
      limit: q.limit,
      skip: q.offset,
      currentPage: page,
      lastPage,
      from,
      to,
    }
  }

  findMany(options): Promise<T[]> {
    return this.model.findAll(this.buildOptions(options))
  }

  async findManyWithTotal<T>(options: PaginatedOptions<T>): Promise<{ data: T[]; total: number }> {
    const q = this.buildOptions(options)

    const count = await this.doCountQuery(q)

    const result = await this.model.findAll(q)

    return {
      data: result,
      total: count,
    }
  }

  findManyByIds(ids: string[], options: Options<T> = {}) {
    return this.findMany({
      ...options,
      where: {
        ...options.where,
        id: { $in: ids },
      },
    })
  }

  findOne(options) {
    return this.model.findOne(this.buildOptions(options))
  }

  exists(options: Options<T> = {}) {
    options.fields = ['id']
    options.include = undefined
    return this.findOne(options).then(Boolean)
  }

  findById(id, options?) {
    // Tomar cuidado com a alteração -> { where: { id: { $eq: id } } }
    // Por algum motivo, utilizar o $eq causa problema de column not exist em query com required true
    // Esse problema foi detectado no QueueApplyDistributionRuleJob ao tentar fazer a distribuição de chamados
    // O problema reportado foi: "department->distribution.id does not exist"
    return this.findOne(assignDeep({ where: { id } }, options))
  }

  existsById(id, options: Options<T> = {}) {
    options.fields = ['id']
    options.include = undefined
    return this.findById(id, options).then(Boolean)
  }

  reload(model, options?: Options<T>) {
    this.ensureCorrectType(model)
    return this.findById(model.id, this.buildOptions(options))
  }

  create(data, options: Options<T> = {}): Promise<T> {
    return this.model.create(data, this.buildOptions(options))
  }

  upsert(data, options: Options<T> = {}): Promise<[T, boolean]> {
    return this.model.upsert(data, { returning: true, ...options })
  }

  destroy(model, options: Options<T>) {
    this.ensureCorrectType(model)
    return model.destroy(options)
  }

  destroyById(id, options: Options<T>) {
    return this.findById(id, options).then((model) => this.destroy(model, options))
  }

  destroyMany(query, options: Options<T>): Promise<T[]> {
    // @ts-ignore
    return this.transaction(options, (transaction) => {
      const destroyMany = (models) =>
        queuedAsyncMap(models, (model) => this.destroy(model, { transaction, ...options }))

      return this.findMany(query).then(destroyMany)
    })
  }

  update(model, data, options: Options<T> & { mergeJson?: string[] } = {}): Promise<T> {
    this.ensureCorrectType(model)

    if (options?.mergeJson?.length) {
      options.mergeJson.forEach((attr) => {
        if (data[attr]) {
          data[attr] = Sequelize.literal(`"${attr}" || ${sequelize.escape(JSON.stringify(data[attr]))}`)
        }
      })
      options.returning = true
      options.plain = true
    }

    return model.update(data, this.buildOptions(options))
  }

  bulkCreate(data, options: Options<T>) {
    return this.model.bulkCreate(data, options)
  }

  bulkUpdate(data, options: Options<T>) {
    // @ts-ignore
    return this.model.update(data, options)
  }

  bulkDestroy(options: Options<T>) {
    return this.model.destroy(options)
  }

  updateById(id: string, data, options: Options<T> & { mergeJson?: string[] } = {}): Promise<T> {
    return this.findById(id, options).then((model) => this.update(model, data, options))
  }

  async updateByIdNoSelect(id: string, data, options: Options<T> & { mergeJson?: string[] } = {}): Promise<T> {
    // @ts-ignore
    return this.bulkUpdate(data, {
      ...options,
      where: { ...data?.where, id },
    })
  }

  findOrCreate(options: Options<T> & { defaults: any }) {
    return this.findOne({ where: options.where }).then((model) => {
      if (model) return model
      return this.create(options.defaults, options)
    })
  }

  touch(model, options: Options<T>) {
    this.ensureCorrectType(model)
    model.changed('updatedAt', true)
    return model.save(options)
  }

  count(options: Options<T>) {
    return this.doCountQuery(this.buildOptions(options))
  }

  max(key: string, options: Options<T>) {
    return this.model.max(key, options)
  }

  protected async doCountQuery(query) {
    // @TODO remover no futuro
    let countSqlLog = ''
    let oldCountSqlLog = ''

    const filteredCountQuery = filterCountQuery(query)

    const count = await this.model.count({
      ...filteredCountQuery,
      distinct: !!filteredCountQuery?.include?.length,
      // @TODO remover no futuro
      logging:
        (DEBUG_PAGINATION_COUNT &&
          ((sql) => {
            countSqlLog = sql
          })) ||
        query.logging,
    })

    // @TODO remover no futuro
    // Implementado para facilitar debug em produção caso identificado problemas
    if (DEBUG_PAGINATION_COUNT) {
      const oldCount = await this.model.count({
        ...query,
        distinct: !!query?.include?.length,
        logging: (sql) => {
          oldCountSqlLog = sql
        },
      })
      logger.log(
        `DEBUG_PAGINATION_COUNT active, ran second count query and results where: ${
          oldCount === count ? 'equal.' : 'not equal.'
        }`,
        'warn',
      )
      if (oldCount !== count) {
        logger.log(
          `Counts are different, oldCount:${oldCount}, count:${oldCount}\nquery: %o, \ncountSqlLog: %s, \noldCountSqlLog: %s`,
          'warn',
          [query, countSqlLog, oldCountSqlLog],
        )
      }
    }

    return count
  }
}
