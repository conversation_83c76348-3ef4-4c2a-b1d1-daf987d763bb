import { QueryTypes } from 'sequelize'
import Container from 'typedi'
import { random } from 'lodash'
import Ticket from '../models/Ticket'
import BaseRepository from './BaseRepository'
import sequelize from '../../services/db/sequelize'
import ResourceCache from '../../services/redis/ResourceCache'

const cache = Container.get(ResourceCache)

export class TicketRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Ticket, defaultQuery)
  }

  getFirstWaitingTime = async (ticketId) => {
    const sql = `
      select 
        to_char(
          (
            select
              min(messages."createdAt")
            from
              messages
            where
              messages."ticketId" = tickets.id
              and messages."isFromMe" = true
          ) - (
            select
              min(messages."createdAt")
            from
              messages
            where
              messages."ticketId" = tickets.id
              and messages."isFromMe" = false
          ), 'HH24:MI:SS'
        ) as tpr
      from 
        tickets 
      where id = $ticketId
    `
    return sequelize.query(sql, {
      bind: {
        ticketId,
      },
    })
  }

  getAverageOpenTicketTime = async (filters) => {
    const whereUser = filters.userId ? `and tickets."userId" = any ($userId)` : ''
    const whereDepartment = filters.departmentId ? `and tickets."departmentId" = any ($departmentId)` : ''
    const sql = `
      select
        avg(
          cast(
            coalesce(
              tickets.metrics->>'ticketTime',
              tickets.metrics->>'messagingTime'
            ) as integer
          )
        ) as avg
      from tickets
      inner join contacts 
        on tickets."contactId" = contacts.id
        and contacts."deletedAt" is null 
      inner join services
        on services.id = contacts."serviceId"
        and services."deletedAt" is null
      where
        tickets."accountId" = $accountId
        and tickets."isOpen" = true
        ${whereUser}
        ${whereDepartment}
    `
    return sequelize.query(sql, {
      bind: filters,
      type: QueryTypes.SELECT,
    })
  }

  async getAverageOpenTicketsFirstWaitTime(filters) {
    const whereUser = filters.userId ? `and tickets."userId" = any ($userId)` : ''
    const whereDepartment = filters.departmentId ? `and tickets."departmentId" = any ($departmentId)` : ''
    const sql = `
      select 
        avg(
          cast(
            metrics->>'waitingTime' as integer
          )
        ) as avg
      from 
        tickets
        inner join contacts 
          on contacts.id = tickets."contactId"
          and contacts."deletedAt" is null
        inner join services
          on services.id = contacts."serviceId"
          and services."deletedAt" is null
      where
        tickets."accountId" = $accountId
        and tickets."isOpen" = true
        ${whereUser}
        ${whereDepartment}
    `
    return sequelize.query(sql, {
      bind: filters,
      type: QueryTypes.SELECT,
    })
  }

  async getAttrs(field, condition = '') {
    return `
      avg(
        coalesce(
          cast(metrics ->> 'ticketTime' as integer), 0
        )
      )::numeric(10,2) as tma,
      avg(
        coalesce(
          cast(metrics ->> 'waitingTime' as integer), 0
        )
      )::numeric(10,2) as tme,
      (
        select
          count(1)
        from
          messages
        where
          messages."ticketId" = ${field}
          and messages."isFromMe" = true
          and messages.type <> 'ticket'
          ${condition}
      ) as "amountMessageSent",
      (
        select
          count(1)
        from
          messages
        where
          messages."ticketId" = ${field}
          and messages."isFromMe" = false
          and messages.type <> 'ticket'
          ${condition}
      ) as "amountMessageReceived",
    `
  }

  async statisticsByDeparment(query, filters, departmentId) {
    const filterDepart =
      departmentId || query.departmentId ? 'and "toDepartmentId" = $departmentId' : 'and "toDepartmentId" IS NULL'

    const subQuery = `
      select
        count(1)
      from
        tickets
      inner join contacts on contacts.id = tickets."contactId" and contacts."deletedAt" is null
      left join contact_tags on contact_tags."contactId" = contacts.id
      left join ticket_ticket_topics on ticket_ticket_topics."ticketId" = tickets.id
      where
        ${filters}
        and tickets.id = ticket_transfers."ticketId"
        ${filterDepart}
    `

    const deptCondition = departmentId
      ? `and messages."ticketDepartmentId" = $departmentId`
      : `and messages."ticketDepartmentId" IS NULL`

    const sql = `
      select
        ticket_transfers."ticketId",
        ${await this.getAttrs('ticket_transfers."ticketId"', deptCondition)}
        case 
          when (
            select
              count(1)
            from
              ticket_ticket_topics
            where
              ticket_ticket_topics."ticketId" = ticket_transfers."ticketId"
          ) > 0 then 1
          else 0 
        end as "amountTotalTopics"
      from ticket_transfers
      where (${subQuery}) > 0
        ${filterDepart}
      group by
        ticket_transfers."toDepartmentId",
        ticket_transfers."ticketId"
    `
    const statistics = await sequelize.query(sql, {
      bind: {
        ...query,
        startPeriod: query.startPeriod?.toISOString ? query.startPeriod?.toISOString() : query.startPeriod,
        endPeriod: query.endPeriod?.toISOString ? query.endPeriod.toISOString() : query.endPeriod,
        departmentId: departmentId || query.departmentId,
      },
      type: QueryTypes.SELECT,
    })

    const ticketIds = statistics.map((item) => item.ticketId)
    return [await this.getTotalContacts(ticketIds), statistics]
  }

  async statisticsByUser(query, filters, userId) {
    const filterUser = userId || query.userId ? 'and "toUserId" = $userId' : 'and "toUserId" IS NULL'

    const subQuery = `
      select
        count(1)
      from
        tickets
      inner join contacts on contacts.id = tickets."contactId" and contacts."deletedAt" is null
      left join contact_tags on contact_tags."contactId" = contacts.id
      left join ticket_ticket_topics on ticket_ticket_topics."ticketId" = tickets.id
      where
        ${filters}
        and tickets.id = ticket_transfers."ticketId"
        ${filterUser}
    `

    const userCondition = userId ? `and messages."ticketUserId" = $userId` : `and messages."ticketUserId" IS NULL`

    const sql = `
      select
        ticket_transfers."ticketId",
        ${await this.getAttrs('ticket_transfers."ticketId"', userCondition)}
        case 
          when (
            select
              count(1)
            from
              ticket_ticket_topics
            where
              ticket_ticket_topics."ticketId" = ticket_transfers."ticketId"
          ) > 0 then 1
          else 0 
        end as "amountTotalTopics"
      from ticket_transfers
      where (${subQuery}) > 0
        ${filterUser}
      group by
        ticket_transfers."toUserId",
        ticket_transfers."ticketId"
    `

    const statistics = await sequelize.query(sql, {
      bind: {
        ...query,
        startPeriod: query.startPeriod,
        endPeriod: query.endPeriod,
        userId: userId || query.userId,
      },
      type: QueryTypes.SELECT,
    })

    const ticketIds = statistics.map((item) => item.ticketId)
    return [await this.getTotalContacts(ticketIds), statistics]
  }

  async getTotalContacts(ticketIds) {
    if (!ticketIds || ticketIds.length == 0) {
      return 0
    }

    const sqlContacts = `
      select
        count(distinct "contactId")
      from
        tickets
      inner join contacts 
        on contacts.id = tickets."contactId" 
       and contacts."deletedAt" is null
      where
        tickets.id in ('${ticketIds.join("','")}')
    `

    const response = await sequelize.query(sqlContacts, {
      type: QueryTypes.SELECT,
    })

    return response[0].count || 0
  }

  async statisticsByTopic(query, filters, topicId) {
    const filterTopic = topicId || query.topicId ? 'and ticket_topics.id = $topicId' : 'and ticket_topics.id IS NULL'

    const sql = `
      select
        tickets.id,
        ${await this.getAttrs('tickets.id')}
        coalesce(ticket_topics.name, 'Sem assunto') as name
      from tickets
      inner join contacts on contacts.id = tickets."contactId" and contacts."deletedAt" is null
      left join contact_tags on contact_tags."contactId" = contacts.id
      left join ticket_ticket_topics on ticket_ticket_topics."ticketId" = tickets.id
      left join ticket_topics on ticket_topics.id = ticket_ticket_topics."ticketTopicId" 
      where
        ${filters}
        ${filterTopic}
      group by
        tickets.id,
        ticket_topics.name
    `

    const topics = await sequelize.query(sql, {
      bind: {
        ...query,
        startPeriod: query.startPeriod.toISOString ? query.startPeriod.toISOString() : query.startPeriod,
        endPeriod: query.startPeriod.toISOString ? query.endPeriod.toISOString() : query.startPeriod,
        topicId: topicId || query.topicId,
      },
      type: QueryTypes.SELECT,
    })

    const ticketIds = topics.map((topic) => topic.id)
    return [await this.getTotalContacts(ticketIds), topics]
  }

  async statisticsByService(query, filters, serviceId) {
    const filterService =
      serviceId || query.serviceId ? 'and contacts."serviceId" = $serviceId' : 'and contacts."serviceId" IS NULL'

    const subQueryServiceName = `
      select name
      from services
      where services.id = contacts."serviceId"
    `

    const sql = `
      select
        tickets.id,
        (${subQueryServiceName}) as name,
        ${await this.getAttrs('tickets.id')}
        case 
          when (select count(1) from ticket_ticket_topics where ticket_ticket_topics."ticketId" = tickets.id) > 0 then 1
          else 0 
        end as "amountTotalTopics"
      from tickets
      inner join contacts on contacts.id = tickets."contactId" and contacts."deletedAt" is null
      left join contact_tags on contact_tags."contactId" = contacts.id
      left join ticket_ticket_topics on ticket_ticket_topics."ticketId" = tickets.id
      left join ticket_topics on ticket_topics.id = ticket_ticket_topics."ticketTopicId" 
      where
        ${filters}
        ${filterService}
      group by
        tickets.id,
        contacts."serviceId"
    `

    const services = await sequelize.query(sql, {
      bind: {
        ...query,
        startPeriod: query.startPeriod.toISOString ? query.startPeriod.toISOString() : query.startPeriod,
        endPeriod: query.startPeriod.toISOString ? query.endPeriod.toISOString() : query.startPeriod,
        serviceId: serviceId || query.serviceId,
      },
      type: QueryTypes.SELECT,
    })

    const ticketIds = services.map((item) => item.id)
    return [await this.getTotalContacts(ticketIds), services]
  }

  async getTicketsCount(
    accountId: string,
    options: {
      canViewAll: boolean
      canViewAllDepartments: boolean
      canViewGroups: boolean
      departmentIds: string[]
      userId: string
    },
  ): Promise<
    {
      accountId: string
      departmentId: string
      userId: string
      mineCount: number
      queueCount: number
    }[]
  > {
    const { canViewAll = false, canViewAllDepartments = false, canViewGroups = false, departmentIds, userId } = options

    const queryFrom = `
        select
        c."accountId",
        t."departmentId",
        t."userId",
        sum(
              case
                  when t."userId" is not null then 1
                  else 0
              end) as "mineCount",
        sum(
              case
                  when t."userId" is null then 1
                  else 0
              end) as "queueCount",
        CURRENT_TIMESTAMP as "updatedAt",
        c."isGroup"
      from
        tickets t
      join contacts c on
        c."currentTicketId" = t.id
        and c."accountId" = t."accountId"
        and c."deletedAt" is null
        and c.visible = true
      join services s on
        s.id = c."serviceId"
        and s."deletedAt" is null
        and s."archivedAt" is null
      where
        c."accountId" = :accountId
        and t."isOpen" = true
        ${(!canViewGroups && 'and c."isGroup" = false') || ''}
      group by
        c."accountId",
        t."departmentId",
        t."userId",
        (CURRENT_TIMESTAMP),
        c."isGroup"`

    const queryAllDepartments =
      (canViewAllDepartments &&
        (departmentIds?.length
          ? `and t."departmentId" in (:departmentIds)`
          : `and exists(
          select 1 from user_departments ud
          where ud."userId" = :userId
          and ud."departmentId" = t."departmentId"
          limit 1)`)) ||
      ''

    const queryOnlyMine =
      (!canViewAll &&
        !canViewAllDepartments &&
        `and (t."userId" = :userId or (t."userId" is null
        ${
          departmentIds?.length
            ? `and t."departmentId" in (:departmentIds)`
            : `and exists(
            select 1 from user_departments ud
            where ud."userId" = :userId
            and ud."departmentId" = t."departmentId"
            limit 1)`
        }))`) ||
      ''

    const queryCheckChanges = `
    select
    sum(t."queueCount") as "queueCount",
    sum(t."mineCount") as "mineCount"
    from (
    ${queryFrom}
    ) as t
    where t."accountId" = :accountId
   ${(!canViewGroups && 'and t."isGroup" = false') || ''}
   ${queryAllDepartments}
   ${queryOnlyMine}
   `
    const query = () =>
      sequelize.query(queryCheckChanges, {
        replacements: {
          accountId,
          userId,
          departmentIds: [...departmentIds],
        },
        //logging: true,
        type: QueryTypes.SELECT,
      })

    return cache
      .remember(['TicketsCount', { accountId, userId }], query, () => null, {
        dontHashKey: false,
        timeout: random(3, 12, false),
      })
      .then((data) => {
        return data
      })
  }
}

export default new TicketRepository()
