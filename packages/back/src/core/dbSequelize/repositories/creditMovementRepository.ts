import sequelize from '../../services/db/sequelize'
import CreditMovement, { CreditMovementInstance } from '../models/CreditMovement'
import BaseRepository from './BaseRepository'

export class CreditMovementRepository extends BaseRepository<CreditMovementInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(CreditMovement, defaultOptions)
  }

  async totalServices(
    from: string,
    to: string,
    orders: Array<string>,
    servicesIds: Array<string>,
    serviceType: string,
    page: number,
    accountId: string,
  ) {
    if (!from || !to || !serviceType || !accountId || page < 1) {
      throw new Error('Invalid param')
    }
    if (servicesIds?.some((id) => !id.match(/^[a-zA-Z0-9-]+$/))) {
      throw new Error('The services ids are invalid')
    }

    const perPage = 5
    const fromPage = (page - 1) * perPage
    const toPage = page * perPage
    const limit = toPage - fromPage
    const order = orders?.length > 0 ? `ORDER BY ${orders?.join(', ')}` : ''
    const filterServices = servicesIds?.length > 0 ? `AND cm."serviceId" IN ('${servicesIds.join("','")}')` : ''

    const replacements = { from, to, serviceType, accountId, limit, fromPage }

    const sql = `
      SELECT s.id, s.name, sum(amount) AS total
      FROM credit_movements AS cm
      INNER JOIN services AS s
      ON s.id = cm."serviceId"
      WHERE cm.type='out'
      AND cm.origin='single'
      AND cm."createdAt" >= :from
      AND cm."createdAt" <= :to
      AND cm."accountId" = :accountId
      AND cm."serviceType" = :serviceType
      ${filterServices}
      GROUP BY s.id
      ${order}
      LIMIT :limit
      OFFSET :fromPage
    `

    const sqlCount = `
      SELECT COUNT(DISTINCT cm."serviceId") AS count
      FROM credit_movements AS cm
      WHERE cm.type='out'
      AND cm.origin='single'
      AND cm."createdAt" >= :from
      AND cm."createdAt" <= :to
      AND cm."accountId" = :accountId
      AND cm."serviceType" = :serviceType
      ${filterServices}
    `

    try {
      const result = await sequelize.query(sql, { replacements, type: sequelize.QueryTypes.SELECT })
      const count = await sequelize.query(sqlCount, { replacements, type: sequelize.QueryTypes.SELECT })
      const lastPage = Math.ceil(count?.[0]?.count / perPage) || 1
      return {
        data: result,
        total: count?.[0]?.count,
        limit: limit,
        skip: fromPage,
        currentPage: page,
        lastPage,
        from: fromPage,
        to: toPage,
      }
    } catch (error) {
      const dbError = error instanceof Error ? error : new Error('Erro desconhecido')
      throw new Error(`Erro na consulta de movimentações: ${dbError.message}. Query: ${sql}`)
    }
  }
}

export default new CreditMovementRepository()
