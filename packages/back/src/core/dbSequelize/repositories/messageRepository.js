import Message from '../models/Message'
import BaseRepository from './BaseRepository'

export class MessageRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Message, defaultQuery)

    this.getAckCountByCampaignId = this.getAckCountByCampaignId.bind(this)
  }

  getAckCountByCampaignId(campaignId, ack, query) {
    return this.count(
      this.buildOptions({
        where: {
          'data.ack': {
            $eq: String(ack),
          },
        },
        include: [
          {
            model: 'campaignMessageProgress',
            where: { campaignId },
          },
        ],
        ...query,
      }),
    )
  }
}

export default new MessageRepository({
  include: ['file', 'preview', 'thumbnail', 'account'],
})
