import Pipeline, { PipelineInstance } from '../models/Pipeline'
import sequelize from '../../services/db/sequelize'
import BaseRepository from './BaseRepository'

export class PipelineRepository extends BaseRepository<PipelineInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(Pipeline, defaultOptions)
  }

  async getTotals(pipeline: PipelineInstance, cardDateFilter) {
    let cardDateSQL = ''
    if (cardDateFilter && (cardDateFilter.$gte || cardDateFilter.$lte)) {
      if (cardDateFilter.$gte) {
        cardDateSQL += ` AND c."createdAt" >= '${cardDateFilter.$gte}'`
      }
      if (cardDateFilter.$lte) {
        cardDateSQL += ` AND c."createdAt" <= '${cardDateFilter.$lte}'`
      }
    }

    const query = `
      select
      ps.id,
      ps.name,
      (
        select
        sum(cp.value * cp.ammount)	
        from pipeline.cards c 
        inner join pipeline.card_products cp on cp."cardId" = c.id
        inner join public.contacts co on c."contactId" = co.id 
        where c."pipelineStageId" = ps.id
        and c."isArchived" = false
        and co."deletedAt" is null
        ${cardDateSQL}
      ) as total,
      (
        select
        sum(cp.value * cp.ammount)	
        from pipeline.cards c
        inner join pipeline.card_products cp on cp."cardId" = c.id
        inner join public.contacts co on c."contactId" = co.id 
        where c."pipelineStageId" = ps.id
        and c."isArchived" = false and c.success = true
        and co."deletedAt" is null
        ${cardDateSQL}
      ) as "totalSuccess",
      (
        select
        sum(cp.value * cp.ammount)
        from pipeline.cards c
        inner join pipeline.card_products cp on cp."cardId" = c.id
        inner join pipeline.stage_status ss on c."statusId" = ss.id
        inner join public.contacts co on c."contactId" = co.id 
        where c."pipelineStageId" = ps.id
        and c."isArchived" = false and c.success = false
        and ss."name" = 'STATUS_LOOSE'
        and co."deletedAt" is null
        ${cardDateSQL}
      ) as "totalFailed",
      (
        select
        count(1)	
        from pipeline.cards c
        inner join public.contacts co on c."contactId" = co.id 
        where c."pipelineStageId" = ps.id
        and c."isArchived" = false
        and co."deletedAt" is null
        ${cardDateSQL}
      ) as "totalCards"
      from pipeline.pipeline_stages ps
      where ps."pipelineId" = :pipelineId
      and ps."deletedAt" IS NULL
      group by ps.id
      order by ps.position
    `
    const total = await sequelize.query(query, {
      replacements: { pipelineId: pipeline.id },
      type: sequelize.QueryTypes.SELECT,
    })

    return total
  }
}

export default new PipelineRepository()
