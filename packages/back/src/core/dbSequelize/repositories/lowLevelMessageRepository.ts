import { v4 as uuid } from 'uuid'
import BaseRepository from './BaseRepository'
import LowLevelMessage, { LowLevelMessageInstance } from '../models/LowLevelMessage'
import { LowLevelContactInstance } from '../models/LowLevelContact'
import sequelize from '../../services/db/sequelize'

class LowLevelMessageRepository extends BaseRepository<LowLevelMessageInstance> {
  constructor() {
    // @ts-ignore
    super(LowLevelMessage)
  }

  customUpsert(data, options = {}): PromiseLike<LowLevelContactInstance> {
    const replacements = {
      ...data,
      data: JSON.stringify(data.data),
      id: uuid(),
    }

    return sequelize.query(
      `INSERT INTO low_level_messages as l (id, "idFromService", data, "serviceId", "accountId", "createdAt", "updatedAt")
VALUES (:id, :idFromService, :data, :serviceId, :accountId, now(), now())
ON CONFLICT ("idFromService", "serviceId") DO UPDATE
SET data = (l.data || :data), "updatedAt" = now()`,
      { ...options, replacements, type: sequelize.QueryTypes.INSERT },
    )
  }
}

export default new LowLevelMessageRepository()
