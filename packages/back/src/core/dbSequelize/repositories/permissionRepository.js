import Permission from '../models/Permission'
import BaseRepository from './BaseRepository'

export class PermissionRepository extends BaseRepository {
  constructor(defaultQuery) {
    super(Permission, defaultQuery)
  }

  findOneByName(name) {
    return this.findOne({
      where: { name },
    })
  }

  findManyByNames(names) {
    return this.findMany({
      where: { name: { $in: names } },
    })
  }
}

export default new PermissionRepository()
