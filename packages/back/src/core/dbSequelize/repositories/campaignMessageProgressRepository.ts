import CampaignMessageProgress, {
  CampaignMessageProgressInstance,
  BLOCKED_BY_MESSAGE_RULE,
} from '../models/CampaignMessageProgress'
import { QueryTypes } from 'sequelize'
import BaseRepository from './BaseRepository'
import sequelize from '../../services/db/sequelize'

class CampaignMessageProgressRepository extends BaseRepository<CampaignMessageProgressInstance> {
  constructor() {
    //@ts-ignore
    super(CampaignMessageProgress)

    this.getTotalFiredCount = this.getTotalFiredCount.bind(this)
    this.getFiredFailedCount = this.getFiredFailedCount.bind(this)
    this.getFiredBlockedByMessageRuleCount = this.getFiredBlockedByMessageRuleCount.bind(this)
    this.getFiredSuccessCount = this.getFiredSuccessCount.bind(this)
  }

  // Total de mensagens disparadas na campanha
  async getTotalFiredCount(campaignId) {
    return this.count({
      where: {
        campaignId,
        sentAt: {
          $ne: null,
        },
      },
    })
  }

  // Total de mensagens disparadas com erro na campanha (onde a causa não foi BLOCKED_BY_MESSAGE_RULE)
  async getFiredFailedCount(campaignId) {
    return this.count({
      where: {
        campaignId,
        sentAt: {
          $ne: null,
        },
        failed: true,
        messageId: null,
        failReason: {
          $ne: BLOCKED_BY_MESSAGE_RULE,
        },
      },
    })
  }

  // Total de mensagens disparadas com erro na campanha (onde a causa foi BLOCKED_BY_MESSAGE_RULE)
  async getFiredBlockedByMessageRuleCount(campaignId) {
    return this.count({
      where: {
        campaignId,
        sentAt: {
          $ne: null,
        },
        failed: true,
        messageId: null,
        failReason: BLOCKED_BY_MESSAGE_RULE,
      },
    })
  }

  // Total de mensagens disparadas com sucesso na campanha
  async getFiredSuccessCount(campaignId) {
    return this.count({
      where: {
        campaignId,
        sentAt: {
          $ne: null,
        },
        failed: false,
        messageId: {
          $ne: null,
        },
      },
    })
  }

  async getFiredAnsweredCount(campaignId) {
    const records = await sequelize.query(
      `
      SELECT COUNT(DISTINCT m."contactId") as count
      FROM messages m 
      INNER JOIN (
        SELECT DISTINCT cmp."contactId", cmp."sentAt"
        FROM campaign_messages_progress cmp
        WHERE cmp."campaignId" = :campaignId
      ) cmp ON m."contactId" = cmp."contactId" AND m."createdAt" > cmp."sentAt"
      WHERE m."type" = 'chat'
      AND m."isFromMe" = false 
      `,
      {
        replacements: {
          campaignId: campaignId,
        },
        type: QueryTypes.SELECT,
      },
    )
    const { count = 0 } = records?.[0]
    return parseInt(count)
  }
}

export default new CampaignMessageProgressRepository()
