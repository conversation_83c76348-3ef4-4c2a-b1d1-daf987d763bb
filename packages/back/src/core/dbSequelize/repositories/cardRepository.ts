import Card, { CardInstance } from '../models/Card'
import BaseRepository from './BaseRepository'
import sequelize from '../../services/db/sequelize'

export class CardRepository extends BaseRepository<CardInstance> {
  constructor(defaultOptions?) {
    // @ts-ignore
    super(Card, defaultOptions)
  }

  async updateOrder(pipelineId: string, pipelineStageId: string, accountId: string, order: number, cardOrder: number) {
    const set = cardOrder > order ? '"order" + 1' : '"order" - 1'
    const where =
      cardOrder > order
        ? `AND "order" >= ${order} AND "order" <= ${cardOrder}`
        : `AND "order" <= ${order} AND "order" >= ${cardOrder}`

    const query = `
        UPDATE pipeline.cards
        SET "order" = ${set}
        WHERE "pipelineId" = :pipelineId
        AND "pipelineStageId" = :pipelineStageId
        AND "accountId" = :accountId
        ${where}
      `

    const replacements = {
      pipelineId,
      pipelineStageId,
      accountId,
    }

    await sequelize.query(query, { replacements, type: sequelize.QueryTypes.UPDATE })
  }
}

export default new CardRepository()
