// @ts-ignore
require('reflect-metadata')

if (process.env.NODE_ENV !== 'production') {
  require('@babel/register')({
    extensions: ['.js', '.ts'],
  })
}
const envMap = {
  test: '.env.test',
  cypress: '.env.test',
  default: '.env',
}
require('dotenv').config({
  path: envMap[process.env.NODE_ENV] || envMap.default,
})

const { Container } = require('typedi')
const { TracerToken } = require('../../services/tracer/Tracer')
const { GenericTracer } = require('../../services/tracer/GenericTracer')
const Config = require('../../services/config/Config').default
const configValues = require('../../configValues').default

Container.get(Config).setValues(configValues)
Container.set(TracerToken, Container.get(GenericTracer))

const config = {
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  dialect: 'postgres',
}

module.exports = {
  development: config,
  test: config,
  cypress: config,
  production: config,
}
