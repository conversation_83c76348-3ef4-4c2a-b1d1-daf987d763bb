import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type LinkInstance = {
  id: string
  messageId: string
  createdAt: Date
  updatedAt: Date
}

const Link = sequelize.define(
  'Link',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    messageId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'message',
        key: 'id',
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'links',
    timestamps: true,
  },
)

Link.associate = (models) => {
  Link.belongsTo(models.Message, {
    as: 'message',
    foreignKey: 'messageId',
  })
}

export default Link
