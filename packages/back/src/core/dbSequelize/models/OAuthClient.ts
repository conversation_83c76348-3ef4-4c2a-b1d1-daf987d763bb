import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type OAuthClientInstance = {
  id: string
  clientId: string
  clientSecret: string
  clientType: string
  grants: string[]
  redirectUri: string
  createdAt: Date
  updatedAt: Date
  userId: string
}

const OAuthClient = sequelize.define(
  'OAuthClient',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.STRING,
      unique: true,
    },
    clientSecret: DataTypes.STRING,
    clientType: {
      type: DataTypes.ENUM,
      values: ['public', 'confidential', 'web_application', 'native_application'],
      defaultValue: 'public',
    },
    grants: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      defaultValue: ['authorization_code', 'password', 'refresh_token', 'client_credentials'],
    },
    redirectUri: DataTypes.STRING,
  },
  { tableName: 'oauth_clients' },
)

OAuthClient.associate = (models) => {
  OAuthClient.belongsTo(models.User, { foreignKey: 'userId' })
}

export default OAuthClient
