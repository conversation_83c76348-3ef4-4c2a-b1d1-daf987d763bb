import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { CardInstance } from './Card'
import { UserInstance } from './User'

const { DataTypes } = Sequelize

export type CardCommentInstance = {
  id: string
  comment: string
  cardId: string
  card: CardInstance
  userId: string
  user: UserInstance
}

const CardComment = sequelize.define(
  'CardComment',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    cardId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUIDV4,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'card_comments',
    schema: 'pipeline',
  },
)

CardComment.associate = (models) => {
  CardComment.belongsTo(models.Card, {
    as: 'Card',
    foreignKey: 'cardId',
  })
  CardComment.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
}

export default CardComment
