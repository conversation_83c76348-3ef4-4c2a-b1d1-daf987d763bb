import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { PipelineInstance } from './Pipeline'
import { PipelineStageInstance } from './PipelineStage'

const { DataTypes } = Sequelize

export type PipelineStageReasonInstance = {
  id: string
  name: string
  position: number
  accountId: string
  account: AccountInstance
  pipelineId: string
  pipeline: PipelineInstance
  stageId: string
  stage: PipelineStageInstance
  isWon: boolean
  createdAt: Date
  updatedAt: Date
}

const PipelineStageReason = sequelize.define(
  'PipelineStageReason',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    pipelineId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    stageId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    isWon: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    tableName: 'stage_reasons',
    schema: 'pipeline',
  },
)

PipelineStageReason.associate = (models) => {
  PipelineStageReason.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  PipelineStageReason.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })
  PipelineStageReason.belongsTo(models.PipelineStage, {
    as: 'stage',
    foreignKey: 'stageId',
  })
}

export default PipelineStageReason
