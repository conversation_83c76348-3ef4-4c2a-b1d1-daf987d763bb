import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type ServerPodInstance = {
  id: string
  name: string
  address: string
  version?: string
  serviceIds: string[]
  serverPodTypeId: string
  settings: {
    browserLogsEnabled?: boolean
  }
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const ServerPod = sequelize.define(
  'ServerPod',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    address: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    version: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: true,
    },
    serviceIds: {
      type: DataTypes.JSONB,
      defaultValue: [],
      allowNull: false,
    },
    settings: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    lastPingAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'server_pods',
    paranoid: true,
  },
)

ServerPod.associate = (models) => {
  ServerPod.belongsTo(models.ServerPodType, {
    as: 'serverPodType',
    foreignKey: 'serverPodTypeId',
  })
}

export default ServerPod
