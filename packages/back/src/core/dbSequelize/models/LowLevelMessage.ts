import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { Message } from '../../../microServices/workers/jobs/whatsapp/adapterLowLevel/types/Message'

// @ts-ignore
const { DataTypes } = Sequelize

export type LowLevelMessageInstance = {
  id: string
  idFromService: string
  data: Message
  sent: boolean
  serviceId: string
  accountId: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const LowLevelMessage = sequelize.define(
  'LowLevelMessage',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    idFromService: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'low_level_messages',
    paranoid: true,
    indexes: [
      {
        name: 'low_level_messages_service_id_id_from_service',
        unique: true,
        fields: ['idFromService', 'serviceId'],
      },
    ],
  },
)

LowLevelMessage.associate = (models) => {
  LowLevelMessage.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
  LowLevelMessage.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default LowLevelMessage
