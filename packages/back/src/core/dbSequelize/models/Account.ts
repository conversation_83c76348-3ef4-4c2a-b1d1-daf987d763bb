import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { DepartmentInstance } from './Department'
import { ServiceInstance } from './Service'
import { UserInstance } from './User'
import { HolidayInstance } from './Holiday'
import config from '../../config'
import { isBefore } from 'date-fns'

// @ts-ignore
const { DataTypes } = Sequelize

export type WizardProgressType = 'notStarted' | 'started' | 'finished'

export type AccountInstance = {
  id: string
  name: string
  alias: string
  settings: {
    // Tornar o campo e-mail do usauário obrigatório
    isUserEmailRequired?: boolean
    // Toggle da funcionalidade de SAC
    ticketsEnabled?: boolean
    // toggle da funcionalidade para habilitar notificações na fila no aplicativo móvel
    isQueueNotificationActive?: boolean
    // Toggle da funcionalidade de notificaçãoes de abertura de chamado
    ticketOpenNotification?: boolean
    // Toggle da funcionalidade de notificaçãoes de transferência de chamado
    ticketTransferNotification?: boolean
    // Toggle da funcionalidade de notificaçãoes de novas mensagens
    newMessageNotification?: boolean
    // Toggle de exibição de informações do suporte
    showSupportInfo?: boolean
    // Toggle da bloqueio, para nomes duplicados
    allowDuplicateNames?: boolean
    changeUserPasswordOnFirstAccess?: Boolean
    userPasswordCreationMethod?: 'manual' | 'automatic' | 'link'
    // Toggle
    topicRequired?: boolean
    // Desabilita criptografica para habilitar funcionalidade de busca
    encryptionDisabled?: boolean
    disableDefaultTicketTransfer?: boolean
    // Tempo (da última mensagem) até um chamado ser considerado inativo (para uso no bot)
    ticketInactiveTime?: number
    // Tempo (em minutos) até um usuário ser considerado ausente (para uso no front)
    userAwayMinutesTime?: number
    // Habilita tipos de conexão para poder ser criadas
    drivers?: {
      whatsapp: boolean
      'whatsapp-business': boolean
      'whatsapp-remote': boolean
      'sms-wavy': boolean
      telegram: boolean
      webchat: boolean
      'facebook-messenger': boolean
      instagram: boolean
      'google-business-message': boolean
      'reclame-aqui': boolean
    }
    // Habilita tipos de conexões que permitem campanha
    campaign?: {
      whatsapp: boolean
      'sms-wavy': boolean
      'whatsapp-business': boolean
      'auto-pause-mode': 'disabled'
    }
    flags?: {
      'absence-management'?: boolean
      'bots-v2'?: boolean
      'bots-v3'?: boolean
      'enable-bots-v3-ai-node'?: boolean
      'by-user-and-by-department-tabs'?: boolean
      'use-block-message-rules-by-service'?: boolean
      'disable-hsm-limit'?: boolean
      distribution?: boolean
      'enable-audio-transcription'?: boolean
      'enable-smart-summary'?: boolean
      'enable-sales-funnel'?: boolean
      'internal-chat'?: boolean
      'invalid-webhooks-inactivator'?: boolean
      'search-messages'?: boolean
      'enable-magic-text'?: boolean
      'enable-smart-csat-score'?: boolean
      'enable-audio-waveform'?: boolean
      'enable-copilot'?: boolean
    }
    // Tabela de horas de funcionamento da empresa (para uso no bot)
    workPlan?: {
      start: string
      end: string
      weekDays: string[]
    }[]
    // Fuso horario da empresa
    timezone?: string
    // Toggle da funciondalidade do nome do antendente nas mensagens
    operatorName?: boolean
    // formato do protocol com interpolação de "{{date}}" e "{{count}}", default "{{date}}{{count}}"
    protocolFormat?: string

    ipRestriction?: boolean

    allowedIps?: string[]

    userNameInMessages?: boolean
    // Ativo quando a conta não está sendo usada.
    withoutPlatformAccess?: boolean

    // Habilitar estatisticas de HSMs
    enableHsmStats?: boolean

    idHub360?: string

    idClientHub360?: string

    //Habilita o controle de expiração de senha
    isPasswordExpirationActive?: boolean
    expirationPasswordTime?: number

    //Obriga todos os usuários a configurarem autenticação multi-fator
    twoFactorAuthMandatory?: boolean
    // Hora que será ativado autenticação multi-fator para todos os usuários
    twoFactorAuthMandatorySchedule?: Date

    //Variável setada quando o usuário faz a primeira sincronização com o chat interno para que os dados continuem sempre sendo
    //atualizados mesmo com a flag desabilitada e não haja divergencia entre chat interno e digisac com relação aos dados dos usuários.
    hasBeenSynchronizedWithInternalChat?: boolean

    // Data para controle de início IACSAT
    smartCsatScoreEnabledAt?: Date

    // Habilita a visualização de tags no chat
    showTagsInChat?: boolean
    // Permite que o usuário altere as tags de visibilidade no chat
    userCanChangeVisibilityTagsOnChat?: boolean

    // Habilita o envio de mensagens automáticas para o cliente quando o atendente transferir o atendimento
    autoGenerateSummaryOnTransfer?: boolean
    autoGenerateSummaryOnClosure?: boolean
  }
  data: {
    // Número de telefone do administrador da empresa
    managerNumber?: string
  }
  // Chave de criptografia das mensagens
  encryptionKey: string
  // Toggle de ativação da conta
  isActive: boolean
  // Estado do wizard
  wizardProgress: WizardProgressType
  plan: {
    [x: string]: any
    // Conta está em modo Trial
    isTrial?: boolean

    // Quantidade de usuarios que podem ser criados na conta
    users?: number
    services?: {
      [serviceName: string]: number
    }

    // Quantidade de HSM's que podem ser enviados pela conta
    hsmLimit?: number
    hsmUsedLimit?: number

    // Ao ser ativado a conta entra no modo de periodo de carência
    isOnGracePeriod?: boolean

    // Denota que a conta está trava e precisa uma ação para reativar por mais X tempo
    isGracePeriodOnHold?: boolean

    // Vezes restante para reativar o periodo de carência
    gracePeriodExtendTimesRemaining?: number

    // Tempo restante até o próximo "hold" do periodo de carência
    gracePeriodEndsAt?: Date

    // Controle de quais emails já foram enviados para os Administradores
    emailNotifications?: {
      expiredNotificationSent?: boolean
      expirationNotificationSent?: boolean
    }

    // Plano base contratado, por enquanto funcionamento apenas para o sistema de créditos
    planBaseUsers?: number
    planBaseServices?: {
      [serviceName: string]: number
    }
  }
  holidays?: HolidayInstance[]
  branch: string

  // Responsável por controlar toda a parte de créditos relacionados ao envio e recebimento de mensagens
  creditsControl?: {
    // Indicar se está habilitado o controle de créditos
    enabled: boolean
    // Data de habilitação do controle de créditos
    useCreditsSystemAt: string | null
    // Data da última alteração de contrato enviado pelo Agnus
    lastUpdateContractFromAgnus: string | null
    // Data da última renovação de crédito
    lastRenewalDate: string | null
    // Quantia da última renovação de crédito
    lastRenewalAmount: number
    // Data da próxima renovação de crédito
    nextRenewalDate: string | null
    // Quantia da próxima renovação de crédito
    nextRenewalAmount: number
    // Permitir ou não exceder o limite de crédito (ficar negativado)
    allowExceedLimit: boolean
    // Indicar se está sem crédito disponível pra uso
    withoutCreditsAvailable: boolean
    // ID do evento de contratação do plano (referência do ERP)
    contractedPlanEventId: string | null
    // ID do produto de plano contratado (referência do ERP)
    contractedPlanProductId: string | null
    // Nome do produto de plano contratado (referência do ERP)
    contractedPlanProductName: string | null
    // Dia de fechamento da fatura do contrato enviado pelo Agnus
    closingDay: number | null

    // Faixas de notificação de crédito utilizado
    // Para cada faixa é definido: um nome, uma porcentagem e um meio de envio
    // As faixas são verificadas e notificadas por um cron job
    notificationRanges: {
      name: string // Nome para ser exibido
      percent: number // Porcentagem para disparar a notificação
      modes: ('push' | 'email' | 'modal')[] // Modos de envio das notificações
    }[]

    // Registro de envio das notificações de créditos utilizados
    // Quando uma faixa é alcançada e não está registrada aqui, a notificação deve ser enviada para os usuários definidos
    // Quando a notificação é enviada, ela deve ser registrada aqui em seguida
    // Esse campo deve ser utilizado para evitar enviar diversas vezes a mesma notificação
    // O array deve ser resetado quando a renovação mensal acontecer ou quando inserir créditos adicionais
    // Exemplo: ao alcançar a porcentagem de 50%, uma notificação deve ser enviada e registrada aqui
    // Na próxima verificação do cron job, a notificação de 50% não deve ser mais enviada
    notificationsSent: {
      percent: number // Porcentagem da notificação enviada
      sentAt: string // Data de envio da notificação (é uma data salva como string)
    }[]

    creditsSystemType?: 'not-charge-credits' | 'charge-credits'
  }

  expiresAt: Date
  createdAt: Date
  updatedAt: Date
  defaultDepartmentId?: string
  defaultDepartment?: DepartmentInstance
  departments?: DepartmentInstance[]
  users?: UserInstance[]
  roles?: any
  services?: ServiceInstance[]
  correlationId?: string
  agnusSignatureKey?: string
  promptAiTransfer?: string
  promptAiFinalize?: string
  twoFactorAuthMandatoryActive: boolean
  promptAiCsat?: string
}

const Account = sequelize.define(
  'Account',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    alias: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {
        ticketsEnabled: false,
        isUserEmailRequired: true,
        isQueueNotificationActive: false,
        protocolsEnabled: false,
        encryptionDisabled: false,
        disableDefaultTicketTransfer: false,
        userAwayMinutesTime: 5,
        drivers: {
          whatsapp: true,
          'whatsapp-business': false,
          'whatsapp-remote': false,
          'sms-wavy': false,
          telegram: false,
          webchat: false,
          'reclame-aqui': false,
        },
        campaign: {
          whatsapp: false,
          'sms-wavy': true,
          'whatsapp-business': false,
          'auto-pause-mode': 'disabled',
        },
        ticketOpenNotification: false,
        ticketTransferNotification: false,
        newMessageNotification: true,
        showSupportInfo: true,
        allowDuplicateNames: false,
        changeUserPasswordOnFirstAccess: false,
        userPasswordCreationMethod: 'manual',
        topicRequired: false,
        idHub360: null,
        idClientHub360: null,
        isPasswordExpirationActive: false,
        expirationPasswordTime: 0,
        twoFactorAuthMandatory: false,
        hasBeenSynchronizedWithInternalChat: false,
        showTagsInChat: false,
        userCanChangeVisibilityTagsOnChat: false,
        autoGenerateSummaryOnTransfer: false,
        autoGenerateSummaryOnClosure: false,
        outsideTheKnowledgeBase: false,
      },
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {
        managerNumber: '',
      },
      allowNull: false,
    },
    encryptionKey: {
      type: DataTypes.STRING(544),
      allowNull: false,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isCampaignActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    wizardProgress: {
      type: DataTypes.STRING,
      defaultValue: 'notStarted',
      allowNull: false,
    },
    plan: {
      type: DataTypes.JSONB,
      defaultValue: {
        users: 3,
        services: {},
        hsmLimit: 0,
        hsmUsedLimit: 0,
        emailNotifications: {
          expiredNotificationSent: false,
          expirationNotificationSent: false,
        },
      },
      get() {
        const rawPlan = this.getDataValue('plan') || {}
        return {
          ...rawPlan,
          hsmLimit: (rawPlan.hsmLimit > 0 ? rawPlan.hsmLimit : config('defaultHsmLimit')) || 0,
          hsmUsedLimit: rawPlan.hsmUsedLimit || 0,
        }
      },
      allowNull: false,
    },
    agnusSignatureKey: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    correlationId: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '',
    },
    branch: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'default',
    },
    promptAiTransfer: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    promptAiFinalize: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    promptAiCsat: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    creditsControl: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {
        enabled: false,
        useCreditsSystemAt: null,
        lastUpdateContractFromAgnus: null,
        lastRenewalDate: null,
        lastRenewalAmount: 0,
        nextRenewalDate: null,
        nextRenewalAmount: 0,
        allowExceedLimit: false,
        withoutCreditsAvailable: false,
        contractedPlanEventId: null,
        contractedPlanProductId: null,
        contractedPlanProductName: null,
        notificationRanges: [
          { name: '50%', percent: 50, modes: ['push'] },
          { name: '75%', percent: 75, modes: ['push', 'email'] },
          { name: '90%', percent: 90, modes: ['email', 'modal'] },
          { name: '100%', percent: 100, modes: ['email', 'modal'] },
        ],
        notificationsSent: [],
        creditsSystemType: null,
        closingDay: null,
      },
    },
    twoFactorAuthMandatoryActive: {
      type: Sequelize.VIRTUAL,
      get(): boolean {
        if (!this.settings.twoFactorAuthMandatory) return false

        const scheduleDate = new Date(this.settings.twoFactorAuthMandatorySchedule || null)

        return isBefore(scheduleDate, new Date())
      },
    },
  },
  {
    tableName: 'accounts',
    paranoid: true,
  },
)

Account.associate = (models) => {
  Account.hasMany(models.User, { foreignKey: 'accountId', as: 'users' })
  Account.hasMany(models.Role, { foreignKey: 'accountId', as: 'roles' })
  Account.hasMany(models.Message, { foreignKey: 'accountId', as: 'messages' })
  Account.hasMany(models.AcceptanceTerm, {
    foreignKey: 'accountId',
    as: 'acceptanceTerms',
  })
  Account.belongsTo(models.Department, {
    foreignKey: 'defaultDepartmentId',
    as: 'defaultDepartment',
    constraints: false,
  })
  Account.hasMany(models.Department, {
    foreignKey: 'accountId',
    as: 'departments',
  })
  Account.hasMany(models.Holiday, {
    foreignKey: 'accountId',
    as: 'holidays',
  })
  Account.hasMany(models.Distribution, {
    foreignKey: 'accountId',
    as: 'distribution',
  })

  Account.hasMany(models.Service, {
    foreignKey: 'accountId',
    as: 'services',
  })
  Account.belongsTo(models.Cluster, { foreignKey: 'clusterId', as: 'cluster' })
  Account.hasMany(models.Integration, {
    foreignKey: 'accountId',
    as: 'integrations',
  })
  Account.hasMany(models.ServicesWebhookFail, {
    foreignKey: 'accountId',
    as: 'servicesWebhookFails',
  })
  Account.hasMany(models.ContactBlockList, {
    foreignKey: 'accountId',
    as: 'contactBlockLists',
  })
  Account.hasMany(models.ContactBlockListItem, {
    foreignKey: 'accountId',
    as: 'contactBlockListItems',
  })
  Account.hasMany(models.ContactBlockListsControl, {
    foreignKey: 'accountId',
    as: 'contactBlockListsControls',
  })
}

export default Account
