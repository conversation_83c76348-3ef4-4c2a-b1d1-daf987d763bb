import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type WhatsappBusinessHsmInstance = {
  id: string
  name: string
  namespace: string
  templateName: string
  parameters?: string
  accountId: string
  templateText?: string
  serviceId: string
  language: string
  templateType: string
  archivedAt?: Date
}

const WhatsappBusinessHSM = sequelize.define(
  'WhatsappBusinessHsm',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    namespace: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    templateName: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    templateText: {
      type: DataTypes.TEXT,
      allowNull: false,
    },

    parameters: {
      type: DataTypes.TEXT,
      defaultValue: {},
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    serviceId: {
      type: DataTypes.UUID,
      references: {
        model: 'services',
        key: 'id',
      },
    },
    language: {
      type: DataTypes.STRING,
      defaultValue: 'pt_BR',
      allowNull: false,
    },
    templateType: {
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: 'text',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'whatsapp_business_hsm',
    paranoid: true,
  },
)

WhatsappBusinessHSM.associate = (models) => {
  WhatsappBusinessHSM.belongsTo(models.Account, {
    as: 'accounts',
    foreignKey: 'accountId',
  })

  WhatsappBusinessHSM.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })

  WhatsappBusinessHSM.hasOne(models.File, {
    as: 'fileTemplate',
    foreignKey: 'attachedId',
    scope: { attachedType: 'hsm.file' },
    constraints: false,
  })
}

export default WhatsappBusinessHSM
