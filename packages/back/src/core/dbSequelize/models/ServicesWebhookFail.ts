import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ContactInstance } from './Contact'
import { ServiceInstance } from './Service'

// @ts-ignore
const { DataTypes } = Sequelize

export type ServicesWebhookFailInstance = {
  id: string
  payload?: {}
  serviceId: string
  contactId?: string
  accountId?: string
  account: AccountInstance
  contact?: ContactInstance
  service?: ServiceInstance
  createdAt: Date
  updatedAt: Date
}

const ServicesWebhookFail = sequelize.define(
  'ServicesWebhookFail',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    payload: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
  },
  {
    tableName: 'services_webhook_fails',
  },
)

ServicesWebhookFail.associate = (models) => {
  ServicesWebhookFail.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  ServicesWebhookFail.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })
  ServicesWebhookFail.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
}

export default ServicesWebhookFail
