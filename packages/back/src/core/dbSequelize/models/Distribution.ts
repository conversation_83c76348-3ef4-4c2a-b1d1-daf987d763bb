import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
// eslint-disable-next-line import/no-cycle
import { AccountInstance } from './Account'
import { RoleInstance } from './Role'

// @ts-ignore
const { DataTypes } = Sequelize

export type DistributionInstance = {
  id: string
  name: string
  maxNum: number
  timeToRedistribute: number
  departments?: any
  roles?: any
  account?: AccountInstance
  accountId: string
  redistributeAll: boolean
  redistributeAssignedTickets: boolean
  distributeQueue: boolean
  createdAt: Date
  updatedAt: Date
  archivedAt: Date
  getRoles(): Promise<RoleInstance[]>
  setRoles(roles: any[], options?: any): Promise<any>
}

const Distribution = sequelize.define(
  'Distribution',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    maxNum: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    timeToRedistribute: {
      defaultValue: 0,
      type: DataTypes.INTEGER,
    },
    redistributeAll: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    redistributeAssignedTickets: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    distributeQueue: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'distribution',
    paranoid: true,
  },
)

Distribution.associate = (models) => {
  Distribution.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Distribution.hasMany(models.Department, {
    foreignKey: 'distributionId',
    as: 'departments',
  })
  Distribution.belongsToMany(models.Role, {
    through: 'distribution_roles',
    as: 'roles',
    foreignKey: 'distributionId',
    otherKey: 'roleId',
  })
}

export default Distribution
