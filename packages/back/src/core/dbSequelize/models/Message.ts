import Sequelize, { Op } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { ServiceInstance } from './Service'
import { ContactInstance } from './Contact'
import { AccountInstance } from './Account'
import { UserInstance } from './User'
import { TicketInstance } from './Ticket'
import { DepartmentInstance } from './Department'
import { TicketTransferInstance } from './TicketTransfer'
import { WhatsappBusinessTemplateInstance } from './WhatsappBusinessTemplate'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

export type MessageInstance = {
  id: string
  idFromService: string
  text: string
  type: string
  timestamp: Date
  isFromMe: boolean
  isFromSync: boolean
  isFromBot: boolean
  sent: boolean
  visible: boolean
  data: {
    mentionedList?: Array<{ id: string }>
    hsmParameters?: any
    fileDownload?: {
      startedAt: Date
      endedAt?: Date
      isDownloading: boolean
    }
    ack: number | 'error'
    location?: {
      lat: number
      lng: number
      previewUrl: string
    }
    contact?: ContactInstance
    hsmDecremented?: boolean
    vcard?: string
    isFirst: boolean
    isNew: boolean
    isFromSync?: boolean
    isSurveyResponse?: boolean
    surveyExpired?: boolean
    isFromFirstSync?: boolean
    dontOpenTicket?: boolean
    dontTransferTicket?: boolean
    dontCallBot?: boolean
    softRevoked?: boolean
    mediaId?: string
    mediaFilehash?: string
    error?: any
    mail?: string
    RAInteractionTypeId?: number
    subject?: any
    resourceId?: string
    hasReaction?: boolean
    whatsappMessageId?: string
    startBlockedAt?: string
    endBlockedAt?: string
    unblockUntilAt?: string
    hasAudioMetadata?: boolean
    //Relação de atributos que um anúncio (Click To Whatsapp Ads) pode conter
    ctwaReferral?: {
      sourceUrl?: string
      sourceId?: string
      body?: string
      sourceType?: string
      headline?: string
      imageUrl?: string
      videoUrl?: string
      thumbnailUrl?: string
      ctwaClid?: string
    }
    showMessage?: boolean
  }
  accountId: string
  serviceId: string
  contactId: string
  campaignId: string
  fromId: string
  userId?: string
  ticketId?: string
  quotedMessageId?: string
  ticketDepartmentId?: string
  ticketUserId?: string
  account?: AccountInstance
  service?: ServiceInstance
  contact?: ContactInstance
  from?: ContactInstance
  user?: UserInstance
  ticket?: TicketInstance
  file?: any
  files?: any[]
  preview?: any
  thumbnail?: any
  quotedMessage?: MessageInstance
  ticketUser?: UserInstance
  ticketDepartment?: DepartmentInstance
  ticketTransfer?: TicketTransferInstance
  origin?: string
  createdAt: Date
  updatedAt: Date
  transferUserIds?: Array<string>
  transferDepartmentIds?: Array<string>
  hsmId?: string
  hsm?: WhatsappBusinessTemplateInstance
  hsmFile?: FileInstance
  isComment?: boolean
  reactionParentMessageId?: string
  reactions?: MessageInstance[]
  reactionParentMessage?: MessageInstance
  isTranscribing?: boolean
  transcribeError?: boolean
}

const Message = sequelize.define(
  'Message',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    idFromService: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    isFromMe: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isFromBot: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isFromSync: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    visible: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    origin: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    hsmId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isComment: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    isTranscribing: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    transcribeError: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
  },
  {
    tableName: 'messages',
    paranoid: true,
  },
)

Message.associate = (models) => {
  Message.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Message.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
  Message.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })
  Message.belongsTo(models.Contact, {
    as: 'from',
    foreignKey: 'fromId',
  })
  Message.belongsTo(models.Contact, {
    as: 'to',
    foreignKey: 'toId',
  })
  Message.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
    constraints: false,
  })
  Message.belongsTo(models.Ticket, {
    as: 'ticket',
    foreignKey: 'ticketId',
  })
  Message.belongsTo(models.Campaign, {
    as: 'campaign',
    foreignKey: 'campaignId',
  })
  Message.hasOne(models.File, {
    as: 'file',
    foreignKey: 'attachedId',
    // @ts-ignore
    scope: { attachedType: 'message.file' },
    constraints: false,
  })
  Message.belongsTo(models.File, {
    as: 'hsmFile',
    foreignKey: 'hsmFileId',
  })
  Message.hasMany(models.File, {
    as: 'files',
    foreignKey: 'attachedId',
    // @ts-ignore
    scope: { attachedType: 'message.files' },
    constraints: false,
  })
  Message.hasOne(models.File, {
    as: 'preview',
    foreignKey: 'attachedId',
    // @ts-ignore
    scope: { attachedType: 'message.preview' },
    constraints: false,
  })
  Message.hasOne(models.File, {
    as: 'thumbnail',
    foreignKey: 'attachedId',
    // @ts-ignore
    scope: { attachedType: 'message.thumbnail' },
    constraints: false,
  })
  Message.hasOne(models.CampaignMessageProgress, {
    as: 'campaignMessageProgress',
    foreignKey: 'messageId',
  })
  Message.belongsTo(models.Message, {
    as: 'quotedMessage',
    foreignKey: 'quotedMessageId',
  })
  Message.belongsTo(models.User, {
    as: 'ticketUser',
    foreignKey: 'ticketUserId',
  })
  Message.belongsTo(models.Department, {
    as: 'ticketDepartment',
    foreignKey: 'ticketDepartmentId',
  })
  Message.belongsTo(models.Bot, {
    as: 'bot',
    foreignKey: 'botId',
  })
  Message.hasOne(models.TicketTransfer, {
    as: 'ticketTransfer',
    foreignKey: 'transferredMessageId',
  })
  Message.belongsTo(models.WhatsappBusinessTemplate, {
    as: 'hsm',
    foreignKey: 'hsmId',
  })
  Message.hasMany(models.Message, {
    // Cada mensagem do chat pode ter várias reações
    as: 'reactions',
    foreignKey: 'reactionParentMessageId',
  })
  Message.belongsTo(models.Message, {
    // Cada reação pertence a uma mensagem do chat
    as: 'reactionParentMessage',
    foreignKey: 'reactionParentMessageId',
  })
  Message.hasMany(models.Link, {
    as: 'link',
    foreignKey: 'messageId',
  })
}

export default Message
