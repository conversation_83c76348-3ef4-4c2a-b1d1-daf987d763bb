import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { Contact } from '../../../microServices/workers/jobs/whatsapp/adapterLowLevel/types/Contact'

// @ts-ignore
const { DataTypes } = Sequelize

export type LowLevelContactInstance = {
  id: string
  idFromService: string
  data: Contact
  sent: boolean
  serviceId: string
  accountId: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const LowLevelContact = sequelize.define(
  'LowLevelContact',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    idFromService: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: false,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'low_level_contacts',
    paranoid: true,
  },
)

LowLevelContact.associate = (models) => {
  LowLevelContact.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
  LowLevelContact.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default LowLevelContact
