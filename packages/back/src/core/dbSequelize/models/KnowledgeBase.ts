import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { KnowledgeBaseItemInstance } from './KnowledgeBaseItem'

// @ts-ignore
const { DataTypes } = Sequelize

export type KnowledgeBaseInstance = {
  id: string
  name: string
  description: string
  accountId: string
  account?: AccountInstance
  items?: KnowledgeBaseItemInstance[]
  createdAt: Date
  updatedAt: Date
}

const KnowledgeBase = sequelize.define(
  'KnowledgeBase',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
    },
  },
  {
    tableName: 'knowledge_base',
    paranoid: true,
  },
)

KnowledgeBase.associate = (models) => {
  KnowledgeBase.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  KnowledgeBase.hasMany(models.KnowledgeBaseItem, {
    as: 'items',
    foreignKey: 'knowledgeBaseId',
  })
}

export default KnowledgeBase
