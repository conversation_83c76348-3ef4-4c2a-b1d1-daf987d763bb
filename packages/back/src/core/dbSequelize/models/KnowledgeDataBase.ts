import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

export type KnowledgeDataBaseInstance = {
  id: string
  name: string
  description: string
  accountId: string
  account?: AccountInstance
  files?: FileInstance[]
  createdAt: Date
  updatedAt: Date
}

const KnowledgeDataBase = sequelize.define(
  'KnowledgeDataBase',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
    },
  },
  {
    tableName: 'knowledge_base',
    paranoid: true,
  },
)

KnowledgeDataBase.associate = (models) => {
  KnowledgeDataBase.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  KnowledgeDataBase.hasMany(models.File, {
    as: 'files',
    foreignKey: 'attachedId',
    scope: { attachedType: 'knowledgebase' },
    constraints: false,
  })
}

export default KnowledgeDataBase
