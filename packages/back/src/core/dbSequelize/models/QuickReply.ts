import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

// @ts-ignore
const { DataTypes } = Sequelize

export type QuickReplyInstance = {
  id: string
  title: string
  text: string
  accountId: string
  account: AccountInstance
  createdAt: Date
  updatedAt: Date
}

const QuickReply = sequelize.define(
  'QuickReply',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
  },
  {
    tableName: 'quick_replies',
    paranoid: true,
  },
)

QuickReply.associate = (models) => {
  QuickReply.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  QuickReply.belongsToMany(models.Department, {
    as: 'departments',
    through: 'quick_reply_departments',
    foreignKey: 'quickReplyId',
    otherKey: 'departmentId',
  })
  QuickReply.belongsToMany(models.Category, {
    as: 'categories',
    through: 'quick_reply_categories',
    foreignKey: 'quickReplyId',
    otherKey: 'categoryId',
  })
  QuickReply.hasMany(models.File, {
    as: 'files',
    foreignKey: 'attachedId',
    scope: { attachedType: 'quickreply.file' },
    constraints: false,
  })
}

export default QuickReply
