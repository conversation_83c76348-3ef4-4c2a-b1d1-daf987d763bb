import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { PipelineInstance } from './Pipeline'
import { PipelineNotificationsInstance } from './PipelineNotifications'

const { DataTypes } = Sequelize

export interface ConfigInterface {
  notify: 'high' | 'medium' | 'medium-high'
  delay: 'daily' | 'weekly' | 'biweekly' | 'monthly'
  day: string
  from: {
    high: string
    medium: string
  }
}

export type PipelineAutomationsInstance = {
  id: string
  pipelineId: string
  pipeline: PipelineInstance
  notifications: PipelineNotificationsInstance[]
  config: ConfigInterface
  isArchived: boolean
  archivedAt: Date
  type: 'notification'
  createdAt: Date
  updatedAt: Date
}

const PipelineAutomations = sequelize.define(
  'PipelineAutomations',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    pipelineId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM,
      values: ['notification'],
      allowNull: false,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    config: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
  },
  {
    tableName: 'pipeline_automations',
    schema: 'pipeline',
  },
)

PipelineAutomations.associate = (models) => {
  PipelineAutomations.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })

  PipelineAutomations.hasMany(models.PipelineNotifications, {
    foreignKey: 'pipelineAutomationId',
    as: 'notifications',
  })
}

export default PipelineAutomations
