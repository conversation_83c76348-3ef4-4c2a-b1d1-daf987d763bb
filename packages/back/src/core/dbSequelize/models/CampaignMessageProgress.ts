import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { CampaignMessageInstance } from './CampaignMessage'
import { CampaignInstance } from './Campaign'
import { ContactInstance } from './Contact'
import { MessageInstance } from './Message'
import { ServiceInstance } from './Service'
import { AccountInstance } from './Account'
import { Options } from '../../resources/BaseResource'

// @ts-ignore
const { DataTypes } = Sequelize

export const BLOCKED_BY_MESSAGE_RULE = 'BLOCKED_BY_MESSAGE_RULE'

export type CampaignMessageProgressInstance = {
  id: string
  sentAt: Date
  failed: boolean
  failReason: string
  createdAt: Date
  updatedAt: Date
  campaignId: string
  parameters?: any
  account?: AccountInstance
  contactId: string
  serviceId: string
  messageId: string
  accountId: string
  contact?: ContactInstance
  campaign?: CampaignInstance
  campaignMessageId?: string
  campaignMessage?: CampaignMessageInstance
  message?: MessageInstance
  service?: ServiceInstance
  getCampaignMessage?: (options?: Options<any>) => CampaignMessageInstance
}

const CampaignMessageProgress = sequelize.define(
  'CampaignMessageProgress',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    failed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    failReason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    parameters: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'campaign_messages_progress',
  },
)

CampaignMessageProgress.associate = (models) => {
  CampaignMessageProgress.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  CampaignMessageProgress.belongsTo(models.Campaign, {
    as: 'campaign',
    foreignKey: 'campaignId',
  })

  CampaignMessageProgress.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })

  CampaignMessageProgress.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })

  CampaignMessageProgress.belongsTo(models.CampaignMessage, {
    as: 'campaignMessage',
    foreignKey: 'campaignMessageId',
  })

  CampaignMessageProgress.belongsTo(models.Message, {
    as: 'message',
    foreignKey: 'messageId',
  })
}

export default CampaignMessageProgress
