import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type AbsenceInstance = {
  id: string
  userId: string
  user?: UserInstance
  reason: string
  endedAt: Date
  createdAt: Date
  updatedAt: Date
}

const Absence = sequelize.define(
  'Absence',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    endedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
  },
  {
    tableName: 'absences',
  },
)

Absence.associate = (models) => {
  Absence.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  Absence.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default Absence
