import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { BotVersionInstance } from './BotVersion'
import { ServiceInstance } from './Service'
import { Condition } from '../../utils/createEvaluator'
import { Action, Event } from '../../../microServices/workers/jobs/bot/BaseBotService'

export interface BotContextTriggerEvent {
  title?: string
  conditions: Condition[]
  data?: Record<string, any>
  actions: (Action & { name?: string })[]
  fallbackActions: (Action & { name?: string })[]
}

export type BotInstance = {
  id: string
  name: string
  data: {}
  contexts: {
    [contextName: string]: {
      name?: string
      triggers: Partial<{
        [key in Event]: BotContextTriggerEvent[]
      }>
    }
  }
  settings: {
    sequentialRuleExecution?: boolean
    botCreatedVersion?: 'v1' | 'v2' | 'v3'
    limitations?: {
      hasInteractiveMessage?: boolean
      hasWebchatTrigger?: boolean
    }
  }
  accountId: string
  account: AccountInstance
  services?: ServiceInstance[]
  flowJson?: {
    edges?: any[]
    nodes?: any[]
  }
  sessions?: any[]
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  getSessions(options: any): Promise<any[]>
  currentBotVersionId?: string
  currentBotVersion?: BotVersionInstance
  botVersions?: BotVersionInstance[]
}

const Bot = sequelize.define(
  'Bot',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    contexts: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    flowJson: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    currentBotVersionId: {
      // Relacionamento 1:1 (1 Bot : 1 BotVersion)
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'bot_versions',
        key: 'id',
      },
    },
  },
  {
    tableName: 'bots',
  },
)

// @ts-ignore
Bot.associate = (models) => {
  Bot.belongsTo(models.Account, { as: 'account', foreignKey: 'accountId' })
  Bot.hasMany(models.Service, { as: 'services', foreignKey: 'botId' })
  Bot.hasMany(models.File, {
    as: 'files',
    foreignKey: 'attachedId',
    scope: { attachedType: 'bot' },
    constraints: false,
  })
  Bot.hasMany(models.BotsSession, { as: 'sessions', foreignKey: 'botId' })

  Bot.belongsTo(models.BotVersion, {
    // Relação de 1:1 -> 1 Bot : 1 BotVersion
    as: 'currentBotVersion',
    foreignKey: { name: 'currentBotVersionId', allowNull: true },
  })

  Bot.hasMany(models.BotVersion, {
    // Relação de 1:N -> 1 Bot : N BotVersion
    as: 'botVersions',
    foreignKey: 'botId',
  })
}

export default Bot
