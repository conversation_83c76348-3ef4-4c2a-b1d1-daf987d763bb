import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { ContactInstance } from './Contact'
import Organization, { OrganizationInstance } from './Organization'

// @ts-ignore
const { DataTypes } = Sequelize

export type PersonInstance = {
  id: string
  name: string
  document?: string
  createdAt: Date
  updatedAt: Date
  accountId: string
  contacts?: ContactInstance[]
  organizations?: OrganizationInstance[]
  setOrganizations(organizations: string[] | OrganizationInstance[]): Promise<void>
}

const Person = sequelize.define(
  'Person',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    document: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'people',
  },
)

Person.associate = (models) => {
  Person.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Person.belongsToMany(models.Organization, {
    as: 'organizations',
    through: 'people_organizations',
    foreignKey: 'personId',
    otherKey: 'organizationId',
  })
  Person.hasMany(models.Contact, {
    as: 'contacts',
    foreignKey: 'personId',
  })
}

export default Person
