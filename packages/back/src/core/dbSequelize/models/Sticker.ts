import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { FileInstance } from './File'
import { MessageInstance } from './Message'
import { UserInstance } from './User'

type Type = 'added' | 'created'

export type StickerInstance = {
  id: string
  type: Type
  originFileChecksum?: string
  originFilehash?: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  originMessageId?: string
  originMessage?: MessageInstance
  userId: string
  user?: UserInstance
  accountId: string
  account?: AccountInstance
  file?: FileInstance
}

const Sticker = sequelize.define(
  'Sticker',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    originFileChecksum: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    originFilehash: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    originMessageId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'messages',
        key: 'id',
      },
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
  },
  {
    tableName: 'stickers',
    paranoid: true,
  },
)

Sticker.associate = (models) => {
  Sticker.belongsTo(models.Message, {
    // Relação de 1:1 -> 1 Message : 1 Sticker
    as: 'originMessage',
    foreignKey: { name: 'originMessageId', allowNull: true },
  })

  Sticker.belongsTo(models.User, {
    // Relação de 1:N -> 1 User : N Sticker
    as: 'user',
    foreignKey: { name: 'userId', allowNull: false },
  })

  Sticker.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N Sticker
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })

  Sticker.hasOne(models.File, {
    // Relação de 1:1 -> 1 File : 1 Sticker
    as: 'file',
    foreignKey: 'attachedId',
    scope: { attachedType: 'sticker.file' },
    constraints: false,
  })
}

export default Sticker
