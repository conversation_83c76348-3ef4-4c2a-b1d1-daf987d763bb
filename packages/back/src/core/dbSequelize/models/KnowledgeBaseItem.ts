import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { KnowledgeBaseInstance } from './KnowledgeBase'
import { KnowledgeBaseItemDocInstance } from './KnowledgeBaseItemDoc'
import { AccountInstance } from './Account'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

export type ItemType = 'urls' | 'files' | 'messages'

export type KnowledgeBaseItemInstance = {
  id: string
  name: string
  type: ItemType
  accountId: string
  account?: AccountInstance
  knowledgeBaseId: string
  knowledgeBase: KnowledgeBaseInstance
  knowledgeBaseItemDocs: KnowledgeBaseItemDocInstance[]
  docs: [{ id: string; content: string; meta: string; blob: string }]
  sourceId?: string
  url?: string
  fileId?: string
  file?: FileInstance
  createdAt: Date
  updatedAt: Date
}

const KnowledgeBaseItem = sequelize.define(
  'KnowledgeBaseItem',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sourceId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    tableName: 'knowledge_base_item',
    paranoid: true,
  },
)

KnowledgeBaseItem.associate = (models) => {
  KnowledgeBaseItem.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  KnowledgeBaseItem.belongsTo(models.KnowledgeBase, {
    as: 'knowledgeBase',
    foreignKey: 'knowledgeBaseId',
  })

  KnowledgeBaseItem.hasOne(models.File, {
    as: 'file',
    foreignKey: 'attachedId',
    scope: { attachedType: 'knowledgebase.item' },
    constraints: false,
  })

  KnowledgeBaseItem.hasMany(models.KnowledgeBaseItemDoc, {
    as: 'docs',
    foreignKey: 'knowledgeBaseItemId',
  })
}

export default KnowledgeBaseItem
