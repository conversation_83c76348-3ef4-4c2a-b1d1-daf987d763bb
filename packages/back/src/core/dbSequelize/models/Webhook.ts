import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { UserInstance } from './User'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'

// @ts-ignore
const { DataTypes } = Sequelize

export type WebhookInstance = {
  id: string
  name: string
  errorAt?: Date
  errorNotifiedAt?: Date
  url: string
  type: string
  active: boolean
  events: string[]
  userId: string
  accountId: string
  user?: UserInstance
  account?: AccountInstance
  services?: ServiceInstance[]
}

const Webhook = sequelize.define(
  'Webhook',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
    },
    errorAt: {
      type: DataTypes.DATE,
    },
    errorNotifiedAt: {
      type: DataTypes.DATE,
    },
    url: {
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: '',
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
    },
    events: {
      type: DataTypes.JSONB,
      defaultValue: [],
      allowNull: false,
    },
  },
  {
    tableName: 'webhooks',
    paranoid: true,
  },
)

Webhook.associate = (models) => {
  Webhook.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Webhook.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  Webhook.belongsToMany(models.Service, {
    as: 'services',
    through: 'webhook_services',
    foreignKey: 'webhookId',
    otherKey: 'serviceId',
  })
}

export default Webhook
