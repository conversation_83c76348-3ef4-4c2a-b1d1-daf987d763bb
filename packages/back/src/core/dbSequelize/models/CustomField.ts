import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { CustomFieldValueInstance } from './CustomFieldValue'

// @ts-ignore
const { DataTypes } = Sequelize

type CustomFieldsAttributes = {
  id: string
  name: string
  type: string
  allowed: 'contacts'
  accountId: string
  createdAt: Date
  updatedAt: Date
  getAccount(): Promise<AccountInstance>
  showOnRegister: boolean
  required: boolean
  settings?: JSON
  values?: CustomFieldValueInstance[]
}

type CustomFieldsMethods = {
  toJSON(): any
}

export type CustomFieldInstance = CustomFieldsAttributes & CustomFieldsMethods

const CustomField = sequelize.define(
  'CustomField',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'text',
    },
    allowed: {
      type: DataTypes.ENUM,
      allowNull: false,
      values: ['contacts'],
      defaultValue: 'contacts',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    showOnRegister: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    required: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    tableName: 'custom_fields',
    paranoid: true,
  },
)

CustomField.associate = (models) => {
  CustomField.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  CustomField.hasMany(models.CustomFieldValue, {
    as: 'values',
    foreignKey: 'customFieldId',
  })
}

export default CustomField
