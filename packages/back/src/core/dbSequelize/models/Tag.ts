import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ContactInstance } from './Contact'

// @ts-ignore
const { DataTypes } = Sequelize

export type TagInstance = {
  id: string
  label: string
  backgroundColor: string
  contacts?: ContactInstance[]
  accountId: string
  account: AccountInstance
  createdAt: Date
  updatedAt: Date
}

const Tag = sequelize.define(
  'Tag',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    label: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    backgroundColor: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: 'tags',
    paranoid: true,
  },
)

Tag.associate = (models) => {
  Tag.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Tag.belongsToMany(models.Contact, {
    as: 'contacts',
    through: 'contact_tags',
    foreignKey: 'tagId',
  })
  Tag.belongsToMany(models.Department, {
    as: 'departments',
    through: 'tag_departments',
    foreignKey: 'tagId',
    otherKey: 'departmentId',
  })
}

export default Tag
