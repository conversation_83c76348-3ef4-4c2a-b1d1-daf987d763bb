import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { TicketInstance } from './Ticket'
import { UserInstance } from './User'

const { DataTypes } = Sequelize

export type CopilotTranscriptionInstance = {
  id: string
  accountId: string
  account: AccountInstance
  ticketId: string
  ticket: TicketInstance
  userId: string
  user: UserInstance
  finishedAt: Date
  createdAt: Date
  updatedAt: Date
}

const CopilotTranscription = sequelize.define(
  'CopilotTranscription',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    ticketId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    finishedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'copilot_transcriptions',
  },
)

CopilotTranscription.associate = (models) => {
  CopilotTranscription.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  CopilotTranscription.belongsTo(models.Ticket, {
    as: 'ticket',
    foreignKey: 'ticketId',
  })
  CopilotTranscription.belongsTo(models.Ticket, {
    as: 'user',
    foreignKey: 'userId',
  })
}

export default CopilotTranscription
