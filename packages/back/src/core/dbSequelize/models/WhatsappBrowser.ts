import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type WhatsappBrowserInstance = {
  id: string
  instanceId: string
  url: string
  status: string
  data: any
  createdAt: Date
  updatedAt: Date
}

const WhatsappBrowser = sequelize.define(
  'WhatsappBrowser',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    instanceId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'whatsapp_browsers',
  },
)

WhatsappBrowser.associate = (models) => {
  WhatsappBrowser.hasOne(models.Service, {
    as: 'whatsappBrowser',
    foreignKey: 'whatsappBrowserId',
  })
}

export default WhatsappBrowser
