import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'

// @ts-ignore
const { DataTypes } = Sequelize

export type FileInstance = {
  id: string
  name: string
  checksum: string
  mimetype: string
  extension: string
  attachedId: string
  storage: 's3' | 'oracle' | 'fs' | 'oracleFallback' | 'wpLvS3' | 'wpBrowserDataS3' | 'exportsS3' | string
  attachedType: string
  filename: string
  publicFilename: string
  filepath: string
  url: string
  accountId: string
  serviceId: string
  isEncrypted?: boolean
  iv?: string
  account?: AccountInstance
  service?: ServiceInstance
  createdAt: Date
  updatedAt: Date
  data?: {
    audioMetadata?: {
      duration?: number
      bitRate?: number
      sampleRate?: number
      channels?: number
      peaks?: number[]
    }
  }
}

/**
 * @typedef {Object} File
 * @property {string} id
 * @property {string} name
 * @property {string} checksum
 * @property {string} mimetype
 * @property {string} extension
 * @property {string} attachedId
 * @property {string} attachedType
 * @property {string} filename
 * @property {string} publicFilename
 * @property {string} filepath
 * @property {string} url
 * @property {Account} account
 */

/** @type {File} */
const File = sequelize.define(
  'File',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    checksum: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    mimetype: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    extension: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isEncrypted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    iv: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    attachedId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    attachedType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    storage: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 's3',
    },
    filename: {
      type: DataTypes.VIRTUAL,
      get() {
        return `${this.get('id')}.${this.get('extension')}`
      },
    },
    publicFilename: {
      type: DataTypes.VIRTUAL,
      get() {
        return this.get('mimetype') === 'image/*'
          ? `${this.get('id')}.jpeg`
          : this.get('name') !== null
          ? this.get('name')
          : this.get('filename')
      },
    },
    filepath: {
      type: DataTypes.VIRTUAL,
      get() {
        return `${this.get('accountId')}/${this.get('filename')}`
      },
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
  },
  {
    tableName: 'files',
    paranoid: true,
    indexes: [{ fields: ['attachedId', 'attachedType'] }],
  },
)

File.associate = (models) => {
  File.belongsTo(models.Account, { as: 'account', foreignKey: 'accountId' })
}

export default File
