import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type HolidayInstance = {
  id: string
  accountId: string
  name: string
  from: Date
  to: Date
  message: string
  createdAt: Date
  updatedAt: Date
}

const Holiday = sequelize.define(
  'Holiday',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    from: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    to: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    message: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    updatedAt: {
      type: DataTypes.VIRTUAL,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    expired: {
      type: DataTypes.VIRTUAL,
      get() {
        const from = this.get('from')
        const to = this.get('to')
        const expiresAt = to || from
        // @TODO:ver se precisa colocar date fns
        return expiresAt < new Date()
      },
    },
  },
  {
    tableName: 'holidays',
    paranoid: true,
  },
)

Holiday.associate = (models) => {
  Holiday.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default Holiday
