import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { PipelineInstance } from './Pipeline'
import { PipelineAutomationsInstance } from './PipelineAutomations'

const { DataTypes } = Sequelize

export type PipelineNotificationsInstance = {
  id: string
  pipelineId: string
  pipeline: PipelineInstance
  pipelineAutomationId: string
  pipelineAutomation: PipelineAutomationsInstance
  createdAt: Date
  updatedAt: Date
}

const PipelineNotifications = sequelize.define(
  'PipelineNotifications',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    pipelineId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    pipelineAutomationId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    tableName: 'pipeline_notifications',
    schema: 'pipeline',
  },
)

PipelineNotifications.associate = (models) => {
  PipelineNotifications.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })

  PipelineNotifications.belongsTo(models.PipelineAutomations, {
    as: 'automation',
    foreignKey: 'pipelineAutomationId',
  })
}

export default PipelineNotifications
