import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { MessageInstance } from './Message'
import { UserInstance } from './User'
import { DepartmentInstance } from './Department'

// @ts-ignore
const { DataTypes } = Sequelize

export type TicketTransferInstance = {
  id: string
  accountId: string
  action: string
  comments: string
  metrics: {
    waitingTime?: number
    messagingTime?: number
    ticketTime?: number
  }
  toUserId?: string
  fromUserId?: string
  fromDepartmentId?: string
  toDepartmentId?: string
  startedAt: Date
  endedAt?: Date
  transferredMessageId?: string
  firstMessageId?: string
  lastMessageId?: string
  transferredMessage?: MessageInstance
  firstMessage?: MessageInstance
  lastMessage?: MessageInstance
  byUserId: string
  createdAt: Date
  updatedAt: Date
  fromDistribution?: Boolean
}

const TicketTransfer = sequelize.define(
  'TicketTransfer',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    action: {
      type: DataTypes.ENUM,
      values: ['opened', 'transferred', 'closed'],
      allowNull: true,
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metrics: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    fromDistribution: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    endedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'ticket_transfers',
    paranoid: true,
  },
)

TicketTransfer.associate = (models) => {
  TicketTransfer.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  TicketTransfer.belongsTo(models.Department, {
    as: 'toDepartment',
    foreignKey: 'toDepartmentId',
  })

  TicketTransfer.belongsTo(models.Department, {
    as: 'fromDepartment',
    foreignKey: 'fromDepartmentId',
  })

  TicketTransfer.belongsTo(models.User, {
    as: 'toUser',
    foreignKey: 'toUserId',
  })

  TicketTransfer.belongsTo(models.User, {
    as: 'fromUser',
    foreignKey: 'fromUserId',
  })

  TicketTransfer.belongsTo(models.Message, {
    as: 'transferredMessage',
    foreignKey: 'transferredMessageId',
  })

  TicketTransfer.belongsTo(models.Ticket, {
    as: 'ticket',
    foreignKey: 'ticketId',
  })

  TicketTransfer.belongsTo(models.User, {
    as: 'byUser',
    foreignKey: 'byUserId',
  })

  TicketTransfer.belongsTo(models.Message, {
    as: 'firstMessage',
    foreignKey: 'firstMessageId',
  })

  TicketTransfer.belongsTo(models.Message, {
    as: 'lastMessage',
    foreignKey: 'lastMessageId',
  })
}

export default TicketTransfer
