import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

enum Status {
  ready = 'ready',
  processing = 'processing',
  done = 'done',
  error = 'error',
}

export type ContactBlockListInstance = {
  id: string
  name: string
  status: keyof typeof Status
  defaultDDI?: string
  saveCount?: number // salvos
  validCount?: number // apenas os válidos
  totalCount?: number // sem filtro
  userId: string
  accountId: string
  user?: UserInstance
  account?: AccountInstance
  createdAt?: Date
  updatedAt?: Date
  deletedAt?: Date
}

const ContactBlockList = sequelize.define(
  'ContactBlockList',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: ['ready', 'processing', 'done', 'error'],
      defaultValue: 'ready',
      allowNull: false,
    },
    defaultDDI: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    saveCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    validCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    userId: {
      type: DataTypes.UUID,
      references: {
        model: 'users',
        key: 'id',
      },
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
    deletedAt: DataTypes.DATE,
  },
  {
    tableName: 'contact_block_lists',
    paranoid: true,
  },
)

ContactBlockList.associate = (models) => {
  ContactBlockList.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  ContactBlockList.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  ContactBlockList.hasMany(models.ContactBlockListItem, {
    as: 'contactBlockListItems',
    foreignKey: 'contactBlockListId',
  })
}

export default ContactBlockList
