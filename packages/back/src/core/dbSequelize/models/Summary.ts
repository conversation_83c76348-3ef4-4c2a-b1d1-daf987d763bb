import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { TicketInstance } from './Ticket'
import { MessageInstance } from './Message'

const { DataTypes } = Sequelize

export type SummaryInstance = {
  id: string
  accountId: string
  account: AccountInstance
  ticketId: string
  ticket: TicketInstance
  messageId: string
  message: MessageInstance
  eventType: string
  finishedAt: Date
  isProcessing: boolean
  createdAt: Date
  updatedAt: Date
}

const Summary = sequelize.define(
  'Summary',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    ticketId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    messageId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    eventType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    finishedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    isProcessing: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'summaries',
  },
)

Summary.associate = (models) => {
  Summary.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Summary.belongsTo(models.Ticket, {
    as: 'ticket',
    foreignKey: 'ticketId',
  })
  Summary.belongsTo(models.Message, {
    as: 'message',
    foreignKey: 'messageId',
  })
}

export default Summary
