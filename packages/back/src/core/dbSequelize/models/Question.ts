import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

// @ts-ignore
const { DataTypes } = Sequelize

export type QuestionInstance = {
  id: string
  name: string
  type: string
  questionMessage: string
  duration: number
  tries: number
  successMessage?: string
  invalidMessage?: string
  reasonMessage?: string
  accountId: string
  account: AccountInstance
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const Question = sequelize.define(
  'Question',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM,
      values: ['nps', 'csat', 'custom'],
      allowNull: false,
    },
    questionMessage: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    tries: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    successMessage: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    invalidMessage: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    reasonMessage: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    tableName: 'questions',
    paranoid: true,
  },
)

Question.associate = (models) => {
  Question.hasMany(models.Answer, {
    as: 'answers',
    foreignKey: 'questionId',
  }),
    Question.belongsTo(models.Account, {
      as: 'account',
      foreignKey: 'accountId',
    })
}

export default Question
