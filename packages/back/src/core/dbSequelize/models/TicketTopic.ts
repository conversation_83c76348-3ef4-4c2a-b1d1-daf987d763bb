import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type TicketTopicInstance = {
  id: string
  name: string
  archivedAt?: Date
}

const TicketTopic = sequelize.define(
  'TicketTopic',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
  },
  {
    tableName: 'ticket_topics',
    paranoid: true,
  },
)

TicketTopic.associate = (models) => {
  TicketTopic.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  TicketTopic.belongsToMany(models.Ticket, {
    as: 'tickets',
    through: 'ticket_ticket_topics',
    foreignKey: 'ticketTopicId',
  })
}

export default TicketTopic
