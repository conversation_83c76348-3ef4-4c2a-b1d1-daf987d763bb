import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type WhatsappWorkerInstance = {
  id: string
  instanceId: string
  status: string
  data: any
  createdAt: Date
  updatedAt: Date
}

const WhatsappWorker = sequelize.define(
  'WhatsappWorker',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    instanceId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'whatsapp_workers',
  },
)

WhatsappWorker.associate = (models) => {
  WhatsappWorker.hasOne(models.Service, {
    as: 'service',
    foreignKey: 'whatsappWorkerId',
  })
}

export default WhatsappWorker
