import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { FileInstance } from './File'
import { UserInstance } from './User'
import { AccountInstance } from './Account'
import { WhatsappBusinessTemplateInstance } from './WhatsappBusinessTemplate'
import { MessageInstance } from './Message'

// @ts-ignore
const { DataTypes } = Sequelize

export type CampaignMessageInstance = {
  id: string
  text: string
  accountId: string
  userId: string
  campaignId: string
  extraOptions?: {
    hsm?: WhatsappBusinessTemplateInstance
    parameters?: MessageInstance['data']['hsmParameters']
    fileTemplate?: FileInstance
  }
  file?: FileInstance
  hsmFileId?: FileInstance['id']
  hsmFile?: FileInstance
  hsm?: WhatsappBusinessTemplateInstance
  hsmId?: WhatsappBusinessTemplateInstance['id']
  user?: UserInstance
  account?: AccountInstance
}

const CampaignMessage = sequelize.define(
  'CampaignMessage',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    extraOptions: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    hsmId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    hsmFileId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
  },
  {
    tableName: 'campaign_messages',
    paranoid: true,
  },
)

CampaignMessage.associate = (models) => {
  CampaignMessage.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  CampaignMessage.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })

  CampaignMessage.belongsTo(models.Campaign, {
    as: 'campaign',
    foreignKey: 'campaignId',
  })

  CampaignMessage.hasOne(models.File, {
    as: 'file',
    foreignKey: 'attachedId',
    scope: { attachedType: 'campaign_message' },
    constraints: false,
  })

  CampaignMessage.belongsTo(models.File, {
    as: 'hsmFile',
    foreignKey: 'hsmFileId',
  })

  CampaignMessage.belongsTo(models.WhatsappBusinessTemplate, {
    as: 'hsm',
    foreignKey: 'hsmId',
  })
}

export default CampaignMessage
