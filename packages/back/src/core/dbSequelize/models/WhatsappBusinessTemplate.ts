import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

type Component = any

export type WhatsappBusinessTemplateInstance = {
  id?: string
  internalName?: string
  category: string
  components: Component[]
  language: string
  name: string
  namespace: string
  rejectedReason?: string
  status: string
  messageType: string
  serviceId: string
  service: ServiceInstance
  accountId: string
  account: AccountInstance
  idGupshup?: string
  quality?: string
  archivedAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  fileExample?: FileInstance
}

const WhatsappBusinessTemplate = sequelize.define(
  'WhatsappBusinessTemplate',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    internalName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    components: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    language: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    namespace: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    rejectedReason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    messageType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    serviceId: {
      type: DataTypes.UUID,
      references: {
        model: 'services',
        key: 'id',
      },
      allowNull: false,
    },
    idGupshup: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    quality: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'whatsapp_business_templates',
    indexes: [
      {
        unique: true,
        fields: ['name', 'language', 'serviceId', 'accountId'],
      },
      {
        fields: ['serviceId', 'accountId'],
      },
    ],
    paranoid: true,
  },
)

WhatsappBusinessTemplate.associate = (models) => {
  WhatsappBusinessTemplate.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  WhatsappBusinessTemplate.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
  WhatsappBusinessTemplate.hasMany(models.File, {
    as: 'fileExample',
    foreignKey: 'attachedId',
    scope: { attachedType: 'hsm.file' },
    constraints: false,
  })
}

export default WhatsappBusinessTemplate
