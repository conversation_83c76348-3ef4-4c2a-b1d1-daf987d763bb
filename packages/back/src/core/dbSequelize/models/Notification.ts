import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

import Account from './Account'
import NotificationRead from './NotificationRead'
import Schedule from './Schedule'
import User from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type NotificationInstance = {
  id: string
  userId: string
  accountId: string
  scheduleId?: string
  contactId?: string
  attachedId?: string
  attachedType?: string
  text: string
  type?: 'schedule' | 'distribution' | 'info' | 'warn' | 'error' | 'announcement' | 'copilot' | 'undefined'
  read: boolean
  label?: string
  config?: {
    pipelineId: string
    pipelineAutomationId: string
  }
  createdAt: Date
  updatedAt: Date
}

const Notification = sequelize.define(
  'Notification',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    text: {
      type: DataTypes.STRING,
    },
    contactId: {
      type: DataTypes.STRING,
    },
    type: {
      type: DataTypes.STRING,
    },
    read: {
      type: DataTypes.BOOLEAN,
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    attachedType: {
      type: DataTypes.STRING,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    label: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    config: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    tableName: 'notifications',
    paranoid: true,
  },
)

Notification.belongsTo(Account, {
  as: 'account',
  foreignKey: 'accountId',
})

Notification.hasMany(NotificationRead, {
  as: 'notificationRead',
  foreignKey: 'notificationId',
})

Notification.belongsTo(User, {
  as: 'user',
  foreignKey: 'userId',
})

Notification.belongsTo(Schedule, {
  as: 'schedule',
  foreignKey: 'scheduleId',
})

export default Notification
