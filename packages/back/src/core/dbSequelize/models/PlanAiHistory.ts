import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

export type ActivityType = 'change' | 'renewal' | 'addition'

export type PlanAiHistoryInstance = {
  id: string
  accountId: string
  activity: ActivityType
  name: string
  magicText: boolean
  summary: boolean
  transcription: boolean
  copilot: boolean
  csat: boolean
  createdAt: Date
}

// @ts-ignore
const { DataTypes } = Sequelize

const PlanAiHistory = sequelize.define(
  'PlanAiHistory',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
    activity: {
      type: DataTypes.ENUM,
      values: ['change', 'renewal', 'addition'],
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    magicText: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    summary: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    transcription: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    copilot: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    csat: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'plan_ai_history',
  },
)

PlanAiHistory.associate = (models) => {
  PlanAiHistory.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default PlanAiHistory
