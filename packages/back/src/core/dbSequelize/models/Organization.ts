import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { PersonInstance } from './Person'

// @ts-ignore
const { DataTypes } = Sequelize

export type OrganizationInstance = {
  id: string
  name: string
  createdAt: Date
  updatedAt: Date
  accountId: string
  people?: PersonInstance[]
}

const Organization = sequelize.define(
  'Organization',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    document: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'organizations',
  },
)

Organization.associate = (models) => {
  Organization.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Organization.belongsToMany(models.Person, {
    as: 'people',
    through: 'people_organizations',
    foreignKey: 'organizationId',
    otherKey: 'personId',
  })
  Organization.belongsToMany(models.User, {
    as: 'users',
    through: 'users_organizations',
    foreignKey: 'organizationId',
    otherKey: 'userId',
  })
}

export default Organization
