import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { DistributionInstance } from './Distribution'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type DepartmentInstance = {
  id: string
  name: string
  accountId: string
  distributionId: string
  account?: AccountInstance
  createdAt: Date
  archivedAt: Date
  updatedAt: Date
  distribution?: DistributionInstance
  users?: UserInstance[]
}

const Department = sequelize.define(
  'Department',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'departments',
  },
)

Department.associate = (models) => {
  Department.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  Department.belongsTo(models.Distribution, {
    as: 'distribution',
    foreignKey: 'distributionId',
    onDelete: 'SET NULL',
    onUpdate: 'SET NULL',
  })

  Department.hasMany(models.Ticket, {
    as: 'tickets',
    foreignKey: 'departmentId',
  })
  Department.hasMany(models.Schedule, {
    foreignKey: 'departmentId',
    as: 'schedules',
  })
  Department.belongsToMany(models.Tag, {
    as: 'tags',
    through: 'tag_departments',
    foreignKey: 'departmentId',
    otherKey: 'tagId',
  })
  Department.belongsToMany(models.User, {
    as: 'users',
    through: 'user_departments',
    foreignKey: 'departmentId',
    otherKey: 'userId',
  })
}

export default Department
