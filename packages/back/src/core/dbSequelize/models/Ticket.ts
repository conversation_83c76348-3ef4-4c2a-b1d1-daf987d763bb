import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { UserInstance } from './User'
import { MessageInstance } from './Message'
import { ContactInstance } from './Contact'
import { TicketTransferInstance } from './TicketTransfer'
import { DepartmentInstance } from './Department'
import { TicketTopicInstance } from './TicketTopic'

// @ts-ignore
const { DataTypes } = Sequelize

export type TicketInstance = {
  id: string
  isOpen: boolean
  comments?: string
  protocol?: string
  count?: number
  origin: 'automatic' | 'manual' | 'widget'
  accountId: string
  departmentId: string
  contactId: string
  userId?: string
  firstMessageId?: string
  lastMessageId?: string
  currentTicketTransferId?: string
  startedAt: Date
  endedAt?: Date
  metrics: {
    waitingTime?: number
    waitingTimeAfterBot?: number
    messagingTime?: number
    ticketTime?: number
    waitingTimeTransfersSum?: number
    ticketTransferCount?: number
    waitingTimeTransfersAvg?: number
    isActiveTicket?: Boolean
  }
  account?: AccountInstance
  contact?: ContactInstance
  user?: UserInstance
  department?: DepartmentInstance
  messages?: MessageInstance[]
  firstMessage?: MessageInstance
  lastMessage?: MessageInstance
  ticketTransfers?: TicketTransferInstance[]
  ticketTopics?: TicketTopicInstance[]
  currentTicketTransfer?: TicketTransferInstance
  createdAt: Date
  updatedAt: Date
  isDistributing: boolean
  getCurrentTicketTransfer: () => Promise<TicketTransferInstance>
  getUser: () => Promise<UserInstance>
  addTicketTopics: (tickedTopics: TicketTopicInstance[] | string[], options: any) => Promise<void>
  get: (attribute: string) => Promise<any>
}

const Ticket = sequelize.define(
  'Ticket',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    isOpen: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    comments: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    protocol: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    origin: {
      type: DataTypes.ENUM,
      values: ['automatic', 'manual', 'widget'],
      allowNull: true,
    },
    metrics: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    endedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    isDistributing: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    count: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    tableName: 'tickets',
  },
)

Ticket.associate = (models) => {
  Ticket.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  Ticket.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })

  Ticket.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })

  Ticket.belongsTo(models.Department, {
    as: 'department',
    foreignKey: 'departmentId',
  })

  Ticket.hasMany(models.Message, {
    as: 'messages',
    foreignKey: 'ticketId',
  })

  Ticket.hasMany(models.TicketTransfer, {
    as: 'ticketTransfers',
    foreignKey: 'ticketId',
  })

  Ticket.belongsTo(models.TicketTransfer, {
    as: 'currentTicketTransfer',
    foreignKey: 'currentTicketTransferId',
  })

  Ticket.belongsTo(models.Message, {
    as: 'firstMessage',
    foreignKey: 'firstMessageId',
  })

  Ticket.belongsTo(models.Message, {
    as: 'lastMessage',
    foreignKey: 'lastMessageId',
  })

  Ticket.belongsToMany(models.TicketTopic, {
    as: 'ticketTopics',
    through: 'ticket_ticket_topics',
    foreignKey: 'ticketId',
  })

  Ticket.hasMany(models.Answer, {
    as: 'answers',
    foreignKey: 'ticketId',
  })
}

export default Ticket
