import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { FileInstance } from './File'
import { StickerInstance } from './Sticker'
import { UserInstance } from './User'

export type StickerUserInstance = {
  stickerId: string
  sticker?: StickerInstance
  userId: string
  user?: UserInstance
  accountId: string
  account?: AccountInstance
  lastSendAt: Date
  createdAt: Date
  updatedAt: Date
  file?: FileInstance
}

const StickerUser = sequelize.define(
  'StickerUser',
  {
    stickerId: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'stickers',
        key: 'id',
      },
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    lastSendAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'sticker_users',
  },
)

StickerUser.associate = (models) => {
  StickerUser.belongsTo(models.Sticker, {
    // Relação de 1:N -> 1 Sticker : N StickerUser
    as: 'sticker',
    foreignKey: { name: 'stickerId', allowNull: false },
  })

  StickerUser.belongsTo(models.User, {
    // Relação de 1:N -> 1 User : N StickerUser
    as: 'user',
    foreignKey: { name: 'userId', allowNull: false },
  })

  StickerUser.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N StickerUser
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })

  StickerUser.hasOne(models.File, {
    // Relação de 1:1 -> 1 File : 1 StickerUser
    as: 'file',
    sourceKey: 'stickerId',
    foreignKey: 'attachedId',
    scope: { attachedType: 'sticker.file' },
    constraints: false,
  })
}

export default StickerUser
