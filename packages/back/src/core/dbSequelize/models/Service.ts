import Sequelize from 'sequelize'
import { Auth } from 'googleapis'
import sequelize from '../../services/db/sequelize'
import { ContactInstance } from './Contact'
import { AccountInstance } from './Account'
import { DepartmentInstance } from './Department'
import { CustomFieldInstance } from './CustomField'
import { BotInstance } from './Bot'
import type { ServerPodInstance } from './ServerPod'
import { AcceptanceTermInstance } from './AcceptanceTerm'

export type WebchatFile = {
  base64Url: string
  mimetype: string
  fileName: string
}

export type WebchatCustom = {
  webchatBalloon: boolean
  webchatBalloonText: string
  webchatColor: string
  logo: string
  profilePhoto: string
  webchatIcon: string
  typeIcon: string
}

export type WebchatData = {
  id: string
  name: string
  phone: string
  telegram: string
  isOpenForm: boolean
  file: WebchatFile
  googleId: string
  desktopChatOpen: boolean
  mobileChatOpen: boolean
  getClientData: boolean
  closeTicketByClient: boolean
  phoneClient: boolean
  phoneClientRequired: boolean
  clientName: boolean
  clientNameRequired: boolean
  customField: {
    id: string
    name: string
  }
  customFieldRequired: boolean
  custom: WebchatCustom
}

export type ServiceInstance = {
  id: string
  name: string
  type:
    | 'whatsapp'
    | 'whatsapp-business'
    | 'whatsapp-remote-pod'
    | 'telegram'
    | 'sms-wavy'
    | 'email'
    | 'webchat'
    | 'facebook-messenger'
    | 'instagram'
    | 'google-business-message'
    | 'reclame-aqui'
  data: {
    appName?: string
    phone?: string
    error?: any
    syncFrom?: Date
    token: string
    tokens?: Auth.Credentials
    myId: string
    nameWebchat?: string
    phoneWhatsapp?: string
    nameTelegram?: string
    filePerfilWebchat?: {
      base64Url: string
      mimetype: string
      fileName: string
    }
    lastSyncAt?: string
    lastDisconnectedAt?: string
    lastMessageTimestamp?: string
    syncCount: number
    driverId?: string
    pageId?: string
    businessId?: string
    numberId?: string
    syncFlowDone?: boolean
    readReceipts: boolean
    markComposingBeforeSend: boolean
    status: {
      isSyncing?: boolean
      isConnected?: boolean
      isStarting?: boolean
      isStarted?: boolean
      batteryLevel?: number
      isCharging?: boolean
      isConflicted?: boolean
      isLoading?: boolean
      isOnChatPage?: boolean
      enteredQrCodePageAt?: Date | string
      disconnectedAt?: Date | string
      isOnQrPage?: boolean
      isPhoneAuthed?: boolean
      isPhoneConnected?: boolean
      isQrCodeExpired?: boolean
      isWaitingForPhoneInternet?: boolean
      isWebConnected?: boolean
      isWebSyncing?: boolean
      mode?: string
      myId?: string
      myName?: string
      myNumber?: string
      needsCharging?: boolean
      qrCodeExpiresAt?: number
      qrCodeUrl?: string | null
      state?: string
      timestamp?: string
    }
    apiURL?: string
    auth?: {
      user: string
      pass?: string
    }
    imap?: {
      host: string
      port: number
      syncFrom: number
    }
    smtp?: {
      host: string
      port: number
      ignoreTLS?: boolean
      requireTLS?: boolean
    }
    serviceType?: any
    service?: null | 'Gmail' | 'Outlook365' | 'Godaddy' | 'GSuite' | 'Hotmail' | 'other'
    providerType?: null | 'positus' | '360Dialog' | 'meta' | 'gupshup'
    webchat: WebchatData
    includeSpamTrash?: boolean
    microsoft?: {
      token_type: string
      scope: string
      expires_in: number
      ext_expires_in: number
      access_token: string
      refresh_token: string
      refreshedAt: Date
    }
    smsWavy: {
      sync: boolean
    }
    googleBusiness?: any
    hub360PartnerId?: any
    isManuallyDisconnected?: boolean
    notifiedServiceDisconnectionAt?: Date
  }
  internalData: {
    id: string
    appToken: string
    partnerToken: string
    apiKey?: string
    webchat?: {
      id: string
      token: string
      password: string
      username: string
    }
    user?: string
    pass?: string
    token?: {
      accessToken: string
      tokenType: string | 'bearer'
      expiresAt: number
      scope: string | 'trust read write'
      rateLimit: number | 10
      licenseStartAt: string
      licenseId: number
      organizationId: string
    }
    clientId?: string
    clientSecret?: string
    securityToken?: string
    envInfo?: {
      name: string
      version: string
      [key: string]: any
    }
  } | null
  settings: {
    widget?: {}
    readReceipts?: boolean
    markComposingBeforeSend?: boolean
    blockMessageRulesActive?: boolean
    unblockByReceiveMessage?: boolean
    shouldOpenTicketForGroups?: boolean
    reactionsEnabled?: boolean
    tokenSandBox360?: string
    lowLevel?: boolean
    cloudApi?: boolean
    keepOnline?: boolean
    allowScanAnotherNumber?: boolean
    contactsSyncDisabled?: boolean
    legacyMessageIds?: boolean
    ackChangeTimeout?: number
    fallbackMessagesCheckerInterval?: number
    disableMessagesChecker?: boolean
    disableFallbackMessagesChecker?: boolean
    syncDelay?: number
    lowLevelContactCheckerDelay?: number
    lowLevelStatusConnectedDelay?: number
  }
  health: {
    status: string
    info: string
    errors: [
      {
        description: string
        solution: string
      },
    ]
  }
  accountId: string
  defaultDepartmentId?: string
  account: AccountInstance
  contacts?: ContactInstance[]
  defaultDepartment?: DepartmentInstance
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  archivedAt?: Date
  botId?: string
  bot?: BotInstance
  acceptanceTerm?: AcceptanceTermInstance
  serverPod?: ServerPodInstance
  customField?: CustomFieldInstance
  logo?: string
  webchatIcon?: string
  profilePhoto?: string
}

// @ts-ignore
const { DataTypes } = Sequelize

const Service = sequelize.define(
  'Service',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    type: {
      type: DataTypes.ENUM,
      values: ['whatsapp'],
      defaultValue: 'whatsapp',
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    internalData: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {
        widget: {},
        readReceipts: false,
        markComposingBeforeSend: false,
        shouldOpenTicketForGroups: false,
        blockMessageRulesActive: false,
      },
    },
    health: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
    },
  },
  {
    tableName: 'services',
    paranoid: true,
  },
)

Service.associate = (models) => {
  Service.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  Service.belongsTo(models.Bot, {
    as: 'bot',
    foreignKey: 'botId',
  })
  Service.hasMany(models.Contact, {
    as: 'contacts',
    foreignKey: 'serviceId',
  })
  Service.hasMany(models.ContactBlockListsControl, {
    as: 'contactBlockListsControls',
    foreignKey: 'serviceId',
  })
  Service.hasMany(models.ServiceEvent, {
    as: 'serviceEvents',
    foreignKey: 'serviceId',
  })
  Service.hasMany(models.ServicesWebhookFail, {
    as: 'servicesWebhookFail',
    foreignKey: 'serviceId',
  })
  Service.belongsTo(models.Department, {
    as: 'defaultDepartment',
    foreignKey: 'defaultDepartmentId',
  })
  Service.belongsToMany(models.Webhook, {
    as: 'webhooks',
    through: 'webhook_services',
    foreignKey: 'serviceId',
    otherKey: 'webhookId',
  })
}

export default Service
