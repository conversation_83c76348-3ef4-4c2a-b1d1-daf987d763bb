import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { BotInstance } from './Bot'
import { UserInstance } from './User'

type Status = 'draft' | 'published'

export type BotVersionInstance = {
  id: string
  status: Status
  contexts: {}
  flowJson: {
    edges?: any[]
    nodes?: any[]
  }
  settings: {}
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  deletedAt?: Date
  botId: string
  bot?: BotInstance
  createdById?: string
  createdBy?: UserInstance
  publishedById?: string
  publishedBy?: UserInstance
  deletedById?: string
  deletedBy?: UserInstance
  accountId: string
  account?: AccountInstance
}

const BotVersion = sequelize.define(
  'BotVersion',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    status: {
      // Status da versão
      type: DataTypes.ENUM,
      values: ['draft', 'published'],
      allowNull: false,
    },
    contexts: {
      // Mesmo campo da tabela de bots (armazenar os contextos para percorrer no BotService)
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    flowJson: {
      // Mesmo campo da tabela de bots (armazenar os blocos da visualização em fluxograma)
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    settings: {
      // Mesmo campo da tabela de bots (armazenar as configurações do bot)
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    publishedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    deletedAt: {
      // Tabela com soft delete
      type: DataTypes.DATE,
      allowNull: true,
    },
    botId: {
      // Relacionamento 1:N (1 Bot : N BotVersion)
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'bots',
        key: 'id',
      },
    },
    createdById: {
      // Usuário que criou a versão
      // Relacionamento 1:N (1 User : N BotVersion)
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    publishedById: {
      // Usuário que publicou a versão
      // Relacionamento 1:N (1 User : N BotVersion)
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    deletedById: {
      // Usuário que deletou a versão
      // Relacionamento 1:N (1 User : N BotVersion)
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    accountId: {
      // Relacionamento 1:N (1 Account : N BotVersion)
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
  },
  {
    tableName: 'bot_versions',
    paranoid: true,
  },
)

// @ts-ignore
BotVersion.associate = (models) => {
  BotVersion.belongsTo(models.Bot, {
    // Relação de 1:N -> 1 Bot : N BotVersion
    as: 'bot',
    foreignKey: { name: 'botId', allowNull: false },
  })

  BotVersion.belongsTo(models.User, {
    // Relação de 1:N -> 1 User : N BotVersion
    as: 'createdBy',
    foreignKey: { name: 'createdById', allowNull: true },
  })

  BotVersion.belongsTo(models.User, {
    // Relação de 1:N -> 1 User : N BotVersion
    as: 'publishedBy',
    foreignKey: { name: 'publishedById', allowNull: true },
  })

  BotVersion.belongsTo(models.User, {
    // Relação de 1:N -> 1 User : N BotVersion
    as: 'deletedBy',
    foreignKey: { name: 'deletedById', allowNull: true },
  })

  BotVersion.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N BotVersion
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })
}

export default BotVersion
