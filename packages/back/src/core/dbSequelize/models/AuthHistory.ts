import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { OAuthAccessTokenInstance } from './OAuthAccessToken'
import { UserInstance } from './User'

export type AuthHistoryInstance = {
  id: string
  event: 'auth' | 'password_change' | 'logout'
  userId: string
  user: UserInstance
  accountId: string
  account: AccountInstance
  branch: string
  originIP: string
  originUA: string
  passwordHash: string
  accessTokenId: string
  accessToken: OAuthAccessTokenInstance
  createdAt: Date
  updatedAt: Date
}

const AuthHistory = sequelize.define(
  'AuthHistory',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    event: {
      type: DataTypes.ENUM,
      values: ['auth', 'password_change', 'logout'],
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    originIP: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    originUA: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    passwordHash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    accessTokenId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    tableName: 'auth_history',
  },
)

AuthHistory.associate = (models) => {
  AuthHistory.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  AuthHistory.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  AuthHistory.belongsTo(models.OAuthAccessToken, {
    as: 'accessToken',
    foreignKey: 'accessTokenId',
  })
}

// @ts-ignore
export default AuthHistory
