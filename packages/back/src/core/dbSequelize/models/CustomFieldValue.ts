import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { CustomFieldInstance } from './CustomField'

// @ts-ignore
const { DataTypes } = Sequelize

type CustomFieldValuesAttributes = {
  id: string
  customFieldId: string
  relatedId: string
  value: string
  customField: CustomFieldInstance
  createdAt: Date
  updatedAt: Date
}

type CustomFieldValuesMethods = {
  toJSON(): any
}

export type CustomFieldValueInstance = CustomFieldValuesAttributes & CustomFieldValuesMethods

const CustomFieldValue = sequelize.define(
  'CustomFieldValue',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customFieldId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'custom_fields',
        key: 'id',
      },
    },
    relatedId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    relatedType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'custom_field_values',
    paranoid: true,
  },
)

CustomFieldValue.associate = (models) => {
  CustomFieldValue.belongsTo(models.CustomField, {
    as: 'customField',
    foreignKey: 'customFieldId',
  })
}

export default CustomFieldValue
