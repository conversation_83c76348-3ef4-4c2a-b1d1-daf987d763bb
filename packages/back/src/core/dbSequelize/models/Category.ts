import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

// @ts-ignore
const { DataTypes } = Sequelize

export type CategoryInstance = {
  id: string
  title: string
  accountId: string
  account: AccountInstance
  createdAt: Date
  updatedAt: Date
  deletedAt: Date
}

const Category = sequelize.define(
  'Category',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    tableName: 'categories',
    paranoid: true,
  },
)

Category.associate = (models) => {
  Category.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default Category
