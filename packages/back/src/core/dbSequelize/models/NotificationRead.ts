import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import Notification from './Notification'
import User from './User'

export type NotificationReadInstance = {
  id: string
  userId: string
  notificationId: string
  readAt: Date
  createdAt: Date
  updatedAt: Date
}

const NotificationRead = sequelize.define(
  'NotificationRead',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    notificationId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'notifications',
        key: 'id',
      },
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'notification_reads',
    paranoid: false,
  },
)

NotificationRead.belongsTo(User, {
  as: 'user',
  foreignKey: 'userId',
})

export default NotificationRead
