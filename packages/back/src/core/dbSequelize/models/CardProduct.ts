import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { CardInstance } from './Card'

const { DataTypes } = Sequelize

export type CardProductInstance = {
  id: string
  name: string
  cardId: string
  card: CardInstance
  ammount: number
  value: number
}

const CardProduct = sequelize.define(
  'CardProduct',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ammount: {
      type: DataTypes.NUMBER,
      allowNull: false,
    },
    value: {
      type: DataTypes.NUMBER,
      allowNull: false,
    },
    cardId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'card_products',
    schema: 'pipeline',
  },
)

CardProduct.associate = (models) => {
  CardProduct.belongsTo(models.Card, {
    as: 'Card',
    foreignKey: 'cardId',
  })
}

export default CardProduct
