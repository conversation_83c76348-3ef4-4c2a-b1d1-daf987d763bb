import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { FileInstance } from './File'
// @ts-ignore
const { DataTypes } = Sequelize

type Media = {
  id?: string
  link?: string
  filename?: string
}
export type InteractiveMessageInstance = {
  id: string
  name: string
  type: 'list' | 'button'
  // https://developers.facebook.com/docs/whatsapp/on-premises/reference/messages#interactive-object
  interactive: {
    type: 'list' | 'button'
    header?: {
      document?: Media
      image?: Media
      video?: Media
      text?: string | { body: string }
      type: 'text' | 'document' | 'image' | 'video'
    }
    body: string | { text: string }
    footer?: string | { text: string }
    action: {
      button?: string // used in type list
      buttons?:
        | {
            type: string
            title: string
            id: string
          }
        | { type: 'reply'; reply: { id: string; title: string } }[]
      sections?: {
        title: string
        rows: {
          title: string
          id: string
          ID: string
          desctiption?: string
        }[]
      }
    }
  }
  archivedAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  accountId: string
  account?: AccountInstance
  file?: FileInstance
  setDepartments?(data: InteractiveMessageInstance[]): Promise<any>
}

const InteractiveMessage = sequelize.define(
  'InteractiveMessages',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    interactive: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
  },
  {
    tableName: 'interactive_messages',
    paranoid: true,
    indexes: [
      {
        unique: true,
        fields: ['name', 'accountId'],
        where: {
          deletedAt: null,
        },
      },
      {
        fields: ['accountId'],
      },
    ],
  },
)

InteractiveMessage.associate = (models) => {
  InteractiveMessage.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  InteractiveMessage.belongsToMany(models.Department, {
    as: 'departments',
    through: 'interactive_message_departments',
    foreignKey: 'interactiveMessageId',
    otherKey: 'departmentId',
  })
  InteractiveMessage.hasOne(models.File, {
    as: 'file',
    foreignKey: 'attachedId',
    scope: { attachedType: 'interactive.file' },
  })
}

export default InteractiveMessage
