import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type ClusterInstance = {
  id: string
  name: string
  address: string
  createdAt: Date
  updatedAt: Date
}

const Cluster = sequelize.define(
  'Cluster',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    address: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'clusters',
    paranoid: true,
  },
)

Cluster.associate = (models) => {
  Cluster.hasMany(models.Account, {
    as: 'accounts',
    foreignKey: 'clusterId',
  })
}

export default Cluster
