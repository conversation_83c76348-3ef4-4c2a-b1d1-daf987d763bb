import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { UserInstance } from './User'
import { ServiceInstance } from './Service'
import { ContactBlockListInstance } from './ContactBlockList'

// @ts-ignore
const { DataTypes } = Sequelize

enum BlockListStatus {
  'ready',
  'processing',
  'done',
  'error',
}

enum BlockListAction {
  'block',
  'unblock',
}

export type ContactBlockListsControlInstance = {
  id: string
  reason: string
  status: keyof typeof BlockListStatus
  action: keyof typeof BlockListAction
  revertExceptions: boolean
  updatedCount?: number // Blocked/unblocked
  processCount?: number // Total to block/unblock
  totalCount?: number // Blocklist count
  contactBlockListId: string
  serviceId: string
  userId: string
  accountId: string
  contactBlockList: ContactBlockListInstance
  user?: UserInstance
  account?: AccountInstance
  service?: ServiceInstance
  createdAt?: Date
  updatedAt?: Date
  deletedAt?: Date
}

const ContactBlockListsControl = sequelize.define(
  'ContactBlockListsControl',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: ['ready', 'processing', 'done', 'error'],
      defaultValue: 'ready',
      allowNull: false,
    },
    action: {
      type: DataTypes.ENUM,
      values: ['block', 'unblock'],
      allowNull: false,
    },
    revertExceptions: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    },
    updatedCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    processCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    contactBlockListId: {
      type: DataTypes.UUID,
      references: {
        model: 'contact_block_lists',
        key: 'id',
      },
    },
    serviceId: {
      type: DataTypes.UUID,
      references: {
        model: 'services',
        key: 'id',
      },
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      references: {
        model: 'users',
        key: 'id',
      },
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
    deletedAt: DataTypes.DATE,
  },
  {
    tableName: 'contact_block_lists_controls',
    paranoid: true,
  },
)

ContactBlockListsControl.associate = (models) => {
  ContactBlockListsControl.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  ContactBlockListsControl.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  ContactBlockListsControl.belongsTo(models.ContactBlockList, {
    as: 'contactBlockList',
    foreignKey: 'contactBlockListId',
  })
  ContactBlockListsControl.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
}

export default ContactBlockListsControl
