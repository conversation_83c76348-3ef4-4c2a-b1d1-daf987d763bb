import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
// @ts-ignore
const { DataTypes } = Sequelize

export type WhatsappBusinessTemplateHistoryInstance = {
  id: string
  accountId?: string
  wabaTemplateId: string
  statusFrom?: string
  statusTo?: string
  qualityFrom?: string
  qualityTo?: string
}

const WhatsappBusinessTemplateHistory = sequelize.define(
  'WhatsappBusinessTemplateHistory',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
    wabaTemplateId: {
      type: DataTypes.UUID,
      references: {
        model: 'whatsapp_business_templates',
        key: 'id',
      },
      allowNull: false,
    },
    statusFrom: {
      type: DataTypes.STRING,
    },
    statusTo: {
      type: DataTypes.STRING,
    },
    qualityFrom: {
      type: DataTypes.STRING,
    },
    qualityTo: {
      type: DataTypes.STRING,
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    tableName: 'whatsapp_business_templates_history',
  },
)

WhatsappBusinessTemplateHistory.associate = (models) => {
  WhatsappBusinessTemplateHistory.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  WhatsappBusinessTemplateHistory.belongsTo(models.WhatsappBusinessTemplate, {
    as: 'whatsappBusinessTemplates',
    foreignKey: 'wabaTemplateId',
  })
}

export default WhatsappBusinessTemplateHistory
