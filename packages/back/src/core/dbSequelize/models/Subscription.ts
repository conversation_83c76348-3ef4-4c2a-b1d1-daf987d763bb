import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type SubscriptionInstance = {
  id: string
  status: string
  transaction: string
  data: any
  createdAt: Date
  updatedat: Date
  expiresAt: Date
}

const Subscription = sequelize.define(
  'Subscription',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    transaction: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'subscriptions',
    paranoid: true,
  },
)

Subscription.associate = (models) => {
  Subscription.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Subscription.belongsTo(models.Plan, {
    as: 'plan',
    foreignKey: 'planId',
  })
}

export default Subscription
