import Account, { AccountInstance } from './Account'
import Contact, { ContactInstance } from './Contact'
import Message, { MessageInstance } from './Message'
import Service, { ServiceInstance } from './Service'
import User, { UserInstance } from './User'
import File, { FileInstance } from './File'
import Permission, { PermissionInstance } from './Permission'
import Role, { RoleInstance } from './Role'
import Tag, { TagInstance } from './Tag'
import OAuthAccessToken, { OAuthAccessTokenInstance } from './OAuthAccessToken'
import OAuthClient, { OAuthClientInstance } from './OAuthClient'
import OAuthRefreshToken, { OAuthRefreshTokenInstance } from './OAuthRefreshToken'
import Webhook, { WebhookInstance } from './Webhook'
import Campaign, { CampaignInstance } from './Campaign'
import CampaignMessage, { CampaignMessageInstance } from './CampaignMessage'
import CampaignMessageProgress, { CampaignMessageProgressInstance } from './CampaignMessageProgress'
import Cluster, { ClusterInstance } from './Cluster'
import BlockMessageRule, { BlockMessageRuleInstance } from './BlockMessageRule'
import Bot, { BotInstance } from './Bot'
import Department, { DepartmentInstance } from './Department'
import Ticket, { TicketInstance } from './Ticket'
import Plan, { PlanInstance } from './Plan'
import Subscription, { SubscriptionInstance } from './Subscription'
import TicketTransfer, { TicketTransferInstance } from './TicketTransfer'
import QuickReply, { QuickReplyInstance } from './QuickReply'
import BotsSession, { BotsSessionInstance } from './BotsSession'
import BotVersion, { BotVersionInstance } from './BotVersion'
import TicketTopic, { TicketTopicInstance } from './TicketTopic'
import Person, { PersonInstance } from './Person'
import Organization, { OrganizationInstance } from './Organization'
import ConsumedCredit, { ConsumedCreditInstance } from './ConsumedCredit'
import ContractedCredit, { ContractedCreditInstance } from './ContractedCredit'
import CreditMovement, { CreditMovementInstance } from './CreditMovement'
import Integration from './Integration'
import Question, { QuestionInstance } from './Question'
import WhatsappBusinessTemplate, { WhatsappBusinessTemplateInstance } from './WhatsappBusinessTemplate'
import ServicesHealthHistory, { ServicesHealthHistoryInstance } from './ServicesHealthHistory'
import Answer from './Answer'
import CustomField from './CustomField'
import CustomFieldValue from './CustomFieldValue'
import Schedule from './Schedule'
import Notification from './Notification'
import Timetable from './Timetable'
import Term, { TermInstance } from './Term'
import ServiceEvent from './ServiceEvent'
import ServicesWebhookFail from './ServicesWebhookFail'
import Sticker, { StickerInstance } from './Sticker'
import StickerUser, { StickerUserInstance } from './StickerUser'
import AcceptanceTerm from './AcceptanceTerm'
import Distribution from './Distribution'
import Holiday from './Holiday'
import Job, { JobInstance } from './Job'
import LowLevelContact, { LowLevelContactInstance } from './LowLevelContact'
import LowLevelMessage, { LowLevelMessageInstance } from './LowLevelMessage'
import ServerPodType, { ServerPodTypeInstance } from './ServerPodType'
import ServerPod, { ServerPodInstance } from './ServerPod'
import Absence, { AbsenceInstance } from './Absence'
import InteractiveMessage, { InteractiveMessageInstance } from './InteractiveMessage'
import ContactBlockList, { ContactBlockListInstance } from './ContactBlockList'
import ContactBlockListItem, { ContactBlockListItemInstance } from './ContactBlockListItem'
import ContactBlockListsControl, { ContactBlockListsControlInstance } from './ContactBlockListsControl'
import Pipeline, { PipelineInstance } from './Pipeline'
import PipelineAutomations, { PipelineAutomationsInstance } from './PipelineAutomations'
import PipelineStage, { PipelineStageInstance } from './PipelineStage'
import PipelineStageStatus, { PipelineStageStatusInstance } from './PipelineStageStatus'
import PipelineStageReason, { PipelineStageReasonInstance } from './PipelineStageReason'
import PipelineNotifications, { PipelineNotificationsInstance } from './PipelineNotifications'
import Card, { CardInstance } from './Card'
import CardMovement, { CardMovementInstance } from './CardMovement'
import CardProduct, { CardProductInstance } from './CardProduct'
import CardComment, { CardCommentInstance } from './CardComment'
import AuthHistory, { AuthHistoryInstance } from './AuthHistory'
import Summary, { SummaryInstance } from './Summary'
import Category, { CategoryInstance } from './Category'
import PlanAiHistory, { PlanAiHistoryInstance } from './PlanAiHistory'
import Link, { LinkInstance } from './Link'
import KnowledgeBase, { KnowledgeBaseInstance } from './KnowledgeBase'
import KnowledgeBaseItem, { KnowledgeBaseItemInstance } from './KnowledgeBaseItem'
import KnowledgeBaseItemDoc, { KnowledgeBaseItemDocInstance } from './KnowledgeBaseItemDoc'
import CopilotTranscription, { CopilotTranscriptionInstance } from './CopilotTranscription'

export type Instance =
  | ContactBlockListInstance
  | ContactBlockListItemInstance
  | ContactBlockListsControlInstance
  | PersonInstance
  | OrganizationInstance
  | AccountInstance
  | ContactInstance
  | MessageInstance
  | ServiceInstance
  | UserInstance
  | FileInstance
  | PermissionInstance
  | RoleInstance
  | TagInstance
  | OAuthAccessTokenInstance
  | OAuthClientInstance
  | OAuthRefreshTokenInstance
  | WebhookInstance
  | CampaignInstance
  | CampaignMessageInstance
  | CampaignMessageProgressInstance
  | ClusterInstance
  | BlockMessageRuleInstance
  | BotInstance
  | DepartmentInstance
  | TicketInstance
  | PlanInstance
  | StickerInstance
  | StickerUserInstance
  | SubscriptionInstance
  | TicketTransferInstance
  | QuickReplyInstance
  | BotsSessionInstance
  | BotVersionInstance
  | TicketTopicInstance
  | ConsumedCreditInstance
  | ContractedCreditInstance
  | CreditMovementInstance
  | QuestionInstance
  | JobInstance
  | LowLevelContactInstance
  | LowLevelMessageInstance
  | ServerPodTypeInstance
  | ServerPodInstance
  | InteractiveMessageInstance
  | TermInstance
  | PipelineInstance
  | PipelineAutomationsInstance
  | PipelineStageInstance
  | PipelineStageStatusInstance
  | PipelineStageReasonInstance
  | PipelineNotificationsInstance
  | CardInstance
  | CardMovementInstance
  | CardProductInstance
  | CardCommentInstance
  | WhatsappBusinessTemplateInstance
  | AbsenceInstance
  | AuthHistoryInstance
  | SummaryInstance
  | CategoryInstance
  | PlanAiHistoryInstance
  | ServicesHealthHistoryInstance
  | LinkInstance
  | KnowledgeBaseInstance
  | KnowledgeBaseItemInstance
  | KnowledgeBaseItemDocInstance
  | CopilotTranscriptionInstance

export default {
  Absence,
  Person,
  Organization,
  Account,
  Contact,
  Message,
  Service,
  User,
  File,
  Permission,
  Role,
  Tag,
  OAuthAccessToken,
  OAuthClient,
  OAuthRefreshToken,
  Webhook,
  Campaign,
  CampaignMessage,
  CampaignMessageProgress,
  Cluster,
  BlockMessageRule,
  Bot,
  Department,
  Ticket,
  Plan,
  Sticker,
  StickerUser,
  Subscription,
  TicketTransfer,
  QuickReply,
  BotsSession,
  BotVersion,
  TicketTopic,
  ConsumedCredit,
  ContractedCredit,
  CreditMovement,
  Integration,
  Question,
  WhatsappBusinessTemplate,
  Answer,
  CustomField,
  CustomFieldValue,
  Schedule,
  Notification,
  Timetable,
  Term,
  ServiceEvent,
  ServicesWebhookFail,
  AcceptanceTerm,
  Distribution,
  Holiday,
  Job,
  LowLevelContact,
  LowLevelMessage,
  ServerPodType,
  ServerPod,
  InteractiveMessage,
  Pipeline,
  PipelineAutomations,
  PipelineStage,
  PipelineStageStatus,
  PipelineStageReason,
  PipelineNotifications,
  Card,
  CardMovement,
  CardProduct,
  CardComment,
  ContactBlockList,
  ContactBlockListItem,
  ContactBlockListsControl,
  AuthHistory,
  Summary,
  Category,
  PlanAiHistory,
  ServicesHealthHistory,
  Link,
  KnowledgeBase,
  KnowledgeBaseItem,
  KnowledgeBaseItemDoc,
  CopilotTranscription,
}
