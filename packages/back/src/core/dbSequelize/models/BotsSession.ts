import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type BotsSessionInstance = {
  id: string
  accountId: string
  botId: string
  contactId: string
  store: {
    context: string
    contextPassCount: number
    variables?: {
      [key: string]: string
    }
    botInactiveSince?: Date | string
  }
  createdAt: Date
  updatedAt: Date
}

const BotsSession = sequelize.define(
  'BotsSession',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    store: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'bots_sessions',
    paranoid: false,
  },
)

BotsSession.associate = (models) => {
  BotsSession.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  BotsSession.belongsTo(models.Bot, {
    as: 'bot',
    foreignKey: 'botId',
    onDelete: 'CASCADE',
  })

  BotsSession.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })
}

export default BotsSession
