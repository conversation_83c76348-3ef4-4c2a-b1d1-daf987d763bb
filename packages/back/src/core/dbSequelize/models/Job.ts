import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type JobInstance = {
  id: string
  name: string
  key: string
  data: any
  attempts: number
  runsAt?: Date
  expiresAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const Job = sequelize.define(
  'Job',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: false,
    },
    key: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: false,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false,
    },
    runsAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'jobs',
    paranoid: true,
  },
)

Job.associate = (models) => {
  Job.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default Job
