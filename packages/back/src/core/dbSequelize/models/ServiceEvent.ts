import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'

export type ReasonServiceConnection =
  | 'waiting_qr_code'
  | 'connected_elsewhere'
  | 'device_offline'
  | 'archived'
  | 'shutdown'
  | 'connected'
  | 'unknown'

export type ReasonServiceBlockMessage =
  | 'enabled_block_message_rules_active'
  | 'disabled_block_message_rules_active'
  | 'enabled_unblock_by_receive_message'
  | 'disabled_unblock_by_receive_message'

export type ServiceEventInstance = {
  id: string
  accountId: string
  serviceId: string
  userId: string
  online: boolean
  reason: ReasonServiceConnection | ReasonServiceBlockMessage
  startedAt: Date
  endedAt: Date
  createdAt: Date
  updatedAt: Date
  reasonCategory: 'service_connection' | 'service_block_message'
}

const ServiceEvent = sequelize.define(
  'ServiceEvent',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    online: {
      type: DataTypes.BOOLEAN,
    },
    reason: {
      type: DataTypes.STRING,
    },
    startedAt: {
      type: DataTypes.DATE,
    },
    endedAt: {
      type: DataTypes.DATE,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    reasonCategory: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: 'service_events',
  },
)

// @ts-ignore
ServiceEvent.associate = (models) => {
  ServiceEvent.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  ServiceEvent.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  ServiceEvent.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
}

export default ServiceEvent
