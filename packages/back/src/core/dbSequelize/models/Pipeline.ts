import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { PipelineStageInstance } from './PipelineStage'
import { DepartmentInstance } from './Department'
import { PipelineAutomationsInstance } from './PipelineAutomations'
import { PipelineNotificationsInstance } from './PipelineNotifications'

const { DataTypes } = Sequelize

export type PipelineInstance = {
  id: string
  name: string
  goBack: boolean
  accountId: string
  account: AccountInstance
  stages: PipelineStageInstance[]
  automations: PipelineAutomationsInstance[]
  notifications: PipelineNotificationsInstance[]
  createdAt: Date
  updatedAt: Date
  deletedAt: Date
  archivedAt: Date
  setDepartments?(data: DepartmentInstance[], options: any): Promise<any>
}

const Pipeline = sequelize.define(
  'Pipeline',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    goBack: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
  },
  {
    tableName: 'pipelines',
    schema: 'pipeline',
    paranoid: true,
  },
)

Pipeline.associate = (models) => {
  Pipeline.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Pipeline.belongsToMany(models.Department, {
    as: 'departments',
    through: 'pipeline_departments',
    foreignKey: 'pipelineId',
    otherKey: 'departmentId',
  })
  Pipeline.hasMany(models.PipelineStage, {
    as: 'stages',
    foreignKey: 'pipelineId',
  })
  Pipeline.hasMany(models.PipelineAutomations, {
    as: 'automations',
    foreignKey: 'pipelineId',
  })
  Pipeline.hasMany(models.PipelineNotifications, {
    as: 'notifications',
    foreignKey: 'pipelineId',
  })
}

export default Pipeline
