import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'
import { ConsumedServiceType, ContractedCreditInstance } from './ContractedCredit'

export type ConsumedCreditInstance = {
  id: string
  consumed: number
  serviceType: ConsumedServiceType
  messageIds: string[]
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  accountId: string
  account?: AccountInstance
  serviceId: string
  service?: ServiceInstance
  contractedCreditId: string
  contractedCredit?: ContractedCreditInstance
}

const ConsumedCredit = sequelize.define(
  'ConsumedCredit',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    consumed: {
      // Quantidade consumida
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    serviceType: {
      // Tipo da conexão utilizada no consumo
      type: DataTypes.STRING,
      allowNull: false,
    },
    messageIds: {
      // Array com os IDs das mensagens que foram enviadas neste respectivo consumo de créditos
      type: DataTypes.ARRAY(DataTypes.UUID),
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      // Tabela com soft delete
      type: DataTypes.DATE,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    serviceId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'services',
        key: 'id',
      },
    },
    contractedCreditId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'contracted_credits',
        key: 'id',
      },
    },
  },
  {
    tableName: 'consumed_credits',
    paranoid: true,
  },
)

ConsumedCredit.prototype.associate = (models) => {
  ConsumedCredit.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N ConsumedCredit
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })

  ConsumedCredit.belongsTo(models.Service, {
    // Relação de 1:N -> 1 Service : N ConsumedCredit
    as: 'services',
    foreignKey: { name: 'serviceId', allowNull: false },
  })

  ConsumedCredit.belongsTo(models.ContractedCredit, {
    // Relação de 1:N -> 1 ContractedCredit : N ConsumedCredit
    as: 'contractedCredit',
    foreignKey: { name: 'contractedCreditId', allowNull: false },
  })
}

export default ConsumedCredit
