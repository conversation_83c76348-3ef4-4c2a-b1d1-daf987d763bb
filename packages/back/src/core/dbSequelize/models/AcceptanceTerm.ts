import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

export type AcceptanceTermInstance = {
  id: string
  name: string
  textField: string
  accountId: string
  account?: AccountInstance
  fileId: string
  file?: FileInstance
  createdAt: Date
  updatedAt: Date
  deletedAt: Date
}

const AcceptanceTerm = sequelize.define(
  'AcceptanceTerm',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    textField: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    tableName: 'acceptance_terms',
    paranoid: true,
  },
)

AcceptanceTerm.associate = (models) => {
  AcceptanceTerm.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  AcceptanceTerm.belongsTo(models.File, {
    as: 'file',
    foreignKey: 'fileId',
  })

  AcceptanceTerm.hasOne(models.Contact, {
    as: 'contact',
    foreignKey: 'acceptanceTermId',
    onDelete: 'SET NULL',
  })
}

export default AcceptanceTerm
