import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { PipelineInstance } from './Pipeline'
import { PipelineStageStatusInstance } from './PipelineStageStatus'
import { PipelineStageReasonInstance } from './PipelineStageReason'
import { CardInstance } from './Card'

const { DataTypes } = Sequelize

export type PipelineStageInstance = {
  id: string
  name: string
  position: number
  accountId: string
  account: AccountInstance
  pipelineId: string
  pipeline: PipelineInstance
  statuses: PipelineStageStatusInstance[]
  reasons: PipelineStageReasonInstance[]
  cards: CardInstance[]
  createdAt: Date
  updatedAt: Date
  deletedAt: Date
}

const PipelineStage = sequelize.define(
  'PipelineStage',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    position: {
      type: DataTypes.NUMBER,
      allowNull: false,
    },
    pipelineId: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
  },
  {
    tableName: 'pipeline_stages',
    schema: 'pipeline',
    paranoid: true,
  },
)

PipelineStage.associate = (models) => {
  PipelineStage.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  PipelineStage.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })
  PipelineStage.hasMany(models.PipelineStageStatus, {
    as: 'statuses',
    foreignKey: 'stageId',
  })
  PipelineStage.hasMany(models.PipelineStageReason, {
    as: 'reasons',
    foreignKey: 'stageId',
  })
  PipelineStage.hasMany(models.Card, {
    as: 'cards',
    foreignKey: 'pipelineStageId',
  })
}

export default PipelineStage
