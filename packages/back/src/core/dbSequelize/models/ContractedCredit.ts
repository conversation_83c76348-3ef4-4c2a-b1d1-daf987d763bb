import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

export type ConsumedServiceType =
  | 'email'
  | 'facebook-messenger'
  | 'google-business-message'
  | 'instagram'
  | 'reclame-aqui'
  | 'sms-wavy'
  | 'telegram'
  | 'webchat'
  | 'whatsapp'
  | 'whatsapp-business'

type ContractedCreditType = 'credit-system-plan' | 'credit-system-additional'

type Status = 'active' | 'expired' | 'revoked'

export type ContractedCreditInstance = {
  id: string
  type: ContractedCreditType
  startedAt: Date
  expiredAt: Date | null
  status: Status
  contractedQuantity: number
  consumedQuantity: number
  consumedPercent: number
  isRevoked: boolean
  consumedServiceTypes: {
    [key in ConsumedServiceType]?: number
  }
  contractedEventId: string | null
  contractedProductId: string | null
  contractedProductName: string | null
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  accountId: string
  account?: AccountInstance
}

const ContractedCredit = sequelize.define(
  'ContractedCredit',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    type: {
      // Tipo de crédito contratado
      type: DataTypes.ENUM,
      values: ['credit-system-plan', 'credit-system-additional'],
      allowNull: false,
    },
    startedAt: {
      // Data de início da vigência do crédito contratado
      type: DataTypes.DATE,
      allowNull: false,
    },
    expiredAt: {
      // Data de expiração da vigência do crédito contratado
      // Crédito do tipo plan não tem data de expiração predefinida
      type: DataTypes.DATE,
      allowNull: true,
    },
    status: {
      type: DataTypes.VIRTUAL,
      get(): Status {
        if (this.get('isRevoked')) {
          return 'revoked'
        }

        const currentTime = new Date()
        const startedAt = this.get('startedAt')
        const expiredAt = this.get('expiredAt')

        if (startedAt < currentTime && (currentTime < expiredAt || expiredAt === null)) {
          return 'active'
        }

        return 'expired'
      },
    },
    contractedQuantity: {
      // Quantidade total contratada
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    consumedQuantity: {
      // Quantidade total consumida
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false,
    },
    consumedPercent: {
      type: DataTypes.VIRTUAL,
      get(): number {
        const contracted = this.get('contractedQuantity')
        const consumed = this.get('consumedQuantity')

        if (typeof contracted !== 'number' || typeof consumed !== 'number') {
          return 0
        }

        return contracted > 0 ? Math.floor((consumed / contracted) * 100) : 0
      },
    },
    isRevoked: {
      // Flag para indicar se a contratação foi revogada
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    consumedServiceTypes: {
      // Quantidade consumida para cada tipo de conexão
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    contractedEventId: {
      // ID do evento de contratação (referência do ERP)
      type: DataTypes.STRING,
      allowNull: true,
    },
    contractedProductId: {
      // ID do produto contratado (referência do ERP)
      type: DataTypes.STRING,
      allowNull: true,
    },
    contractedProductName: {
      // Nome do produto contratado (referência do ERP)
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      // Tabela com soft delete
      type: DataTypes.DATE,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
  },
  {
    tableName: 'contracted_credits',
    paranoid: true,
  },
)

ContractedCredit.prototype.associate = (models) => {
  ContractedCredit.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N ContractedCredit
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })
}

export default ContractedCredit
