import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type TermInstance = {
  id?: string
  title: any
  text: any
  expirationDate: Date
  version: string
  agnusId: string
  accountId?: string
  account?: AccountInstance
  userId?: string
  user?: UserInstance
  agreementDate?: Date
  createdAt?: Date
  updatedAt?: Date
}

const Term = sequelize.define(
  'Term',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    text: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    version: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    agnusId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    expirationDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    agreementDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'terms',
    paranoid: false,
  },
)

Term.associate = (models) => {
  Term.belongsTo(models.Account, { foreignKey: 'accountId', as: 'account' })
  Term.belongsTo(models.User, { foreignKey: 'userId', as: 'user' })
}

export default Term
