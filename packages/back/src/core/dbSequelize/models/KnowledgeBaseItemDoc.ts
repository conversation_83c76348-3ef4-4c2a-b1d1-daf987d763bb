import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { KnowledgeBaseItemInstance } from './KnowledgeBaseItem'
import { AccountInstance } from './Account'

// @ts-ignore
const { DataTypes } = Sequelize

export type KnowledgeBaseItemDocInstance = {
  id: string
  accountId: string
  account?: AccountInstance
  knowledgeBaseItemId: string
  knowledgeBaseItem: KnowledgeBaseItemInstance
  docId: string
  createdAt: Date
  updatedAt: Date
}

const KnowledgeBaseItemDoc = sequelize.define(
  'KnowledgeBaseItemDoc',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    knowledgeBaseItemId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    docId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    tableName: 'knowledge_base_item_doc',
  },
)

KnowledgeBaseItemDoc.associate = (models) => {
  KnowledgeBaseItemDoc.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  KnowledgeBaseItemDoc.belongsTo(models.KnowledgeBaseItem, {
    as: 'knowledgeBaseItem',
    foreignKey: 'knowledgeBaseItemId',
  })
}

export default KnowledgeBaseItemDoc
