import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type IntegrationInstance = {
  id: string
  type: string
  url: string
  icon: string
  text: string
  accountId: string
  userId?: string
  user?: UserInstance
  size?: string
  createdAt: Date
  updatedAt: Date
}

const Integration = sequelize.define(
  'Integration',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    type: {
      type: DataTypes.STRING,
      defaultValue: true,
      allowNull: false,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    icon: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    text: {
      type: DataTypes.STRING,
      values: ['automatic', 'manual', 'widget'],
      allowNull: true,
    },
    size: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'integrations',
  },
)

Integration.associate = (models) => {
  Integration.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
}

export default Integration
