import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

// @ts-ignore
const { DataTypes } = Sequelize

export type TimetableInstance = {
  id: string
  name: string
  previousTimeToNotification: number
  workPlan: {
    start: string
    end: string
    weekDays: string[]
  }[]
  accountId: string
  account: AccountInstance
  createdAt: Date
  updatedAt: Date
}

const Timetable = sequelize.define(
  'Timetable',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    previousTimeToNotification: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    workPlan: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'timetables',
  },
)

Timetable.associate = (models) => {
  Timetable.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Timetable.hasMany(models.User, {
    as: 'users',
    foreignKey: 'timetableId',
  })
}

export default Timetable
