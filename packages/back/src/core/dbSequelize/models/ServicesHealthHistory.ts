import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
const { DataTypes } = Sequelize

export type ServicesHealthHistoryInstance = {
  id: string
  serviceId?: string
  healthFrom?: string
  healthTo?: string
}

const ServicesHealthHistory = sequelize.define(
  'ServicesHealthHistory',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    serviceId: {
      type: DataTypes.UUID,
      references: {
        model: 'services',
        key: 'id',
      },
      allowNull: false,
    },
    healthFrom: {
      type: DataTypes.STRING,
    },
    healthTo: {
      type: DataTypes.STRING,
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    tableName: 'services_health_history',
  },
)

ServicesHealthHistory.associate = (models) => {
  ServicesHealthHistory.belongsTo(models.Service, {
    as: 'services',
    foreignKey: 'serviceId',
  })
}

export default ServicesHealthHistory
