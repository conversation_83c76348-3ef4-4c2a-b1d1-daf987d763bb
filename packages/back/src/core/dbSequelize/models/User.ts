import Sequelize from 'sequelize'
import uniqBy from 'lodash/uniqBy'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { TimetableInstance } from './Timetable'
import { PermissionInstance } from './Permission'
import { RoleInstance } from './Role'
import { DepartmentInstance } from './Department'
import { Options } from '../../resources/BaseResource'
import { AuthHistoryInstance } from './AuthHistory'

// @ts-ignore
const { DataTypes } = Sequelize

export type UserInstance = {
  id: string
  name: string
  email: string
  phoneNumber: string
  branch: string
  password: string
  activationToken: string
  resetPasswordToken: string
  isSuperAdmin: boolean
  isClientUser: boolean
  active: boolean
  isFirstLogin: boolean
  accountId: string
  account: AccountInstance
  roles?: RoleInstance[]
  departments?: DepartmentInstance[]
  departmentsDefault?: DepartmentInstance[]
  timetableId?: string
  timetable?: TimetableInstance
  data?: {
    sentResetPasswordAt?: Date
    oneSignalToken?: string
    expoPushToken?: string
    domain?: string
  }
  internalChatToken: string
  archivedAt: Date
  createdAt: Date
  updatedAt: Date
  status: 'online' | 'offline' | 'away'
  clientsStatus?: {
    web?: 'online' | 'offline' | 'away'
    app?: 'online' | 'offline' | 'away'
  }
  offlineAt: Date
  hasPermission(permission: string): boolean
  getAccount(options?: Options<AccountInstance>): Promise<AccountInstance>
  getRoles(options?: Options<RoleInstance>): Promise<RoleInstance[]>
  getDepartments(options?: Options<DepartmentInstance>): Promise<DepartmentInstance[]>
  permissions: PermissionInstance[]
  language: string
  countTickets?: number
  isActiveInternalChat: boolean
  passwordExpiresAt?: Date
  hasPasswordExpired: boolean
  authHistory?: AuthHistoryInstance[]
  otpSecretKey?: string
  otpAuthActive?: boolean
  preferences?: {
    audioSpeed?: number
    isShowTagsChat?: boolean
  }
}

const User = sequelize.define(
  'User',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    branch: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isSuperAdmin: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isClientUser: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    activationToken: {
      type: DataTypes.STRING,
      defaultValue: false,
    },
    resetPasswordToken: {
      type: DataTypes.STRING,
      defaultValue: false,
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
    permissions: {
      type: Sequelize.VIRTUAL,
      get() {
        if (!this.roles || (this.roles.length && !this.roles[0].permissions)) {
          return undefined
        }

        if (this._permissions) return this._permissions

        let rolePermissions = []

        for (const role of this.roles) {
          rolePermissions = [...rolePermissions, ...role.permissions]
        }

        this._permissions = uniqBy(rolePermissions, 'id')

        return this._permissions
      },
    },
    language: {
      type: DataTypes.STRING,
      defaultValue: 'pt-BR',
    },
    isFirstLogin: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    status: {
      type: DataTypes.ENUM,
      values: ['online', 'offline', 'away'],
      defaultValue: 'offline',
      allowNull: false,
    },
    clientsStatus: {
      type: DataTypes.JSONB,
      defaultValue: {
        web: 'offline',
        app: 'offline',
      },
      allowNull: true,
    },
    offlineAt: DataTypes.DATE,
    isActiveInternalChat: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    internalChatToken: {
      type: DataTypes.TEXT,
    },
    passwordExpiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    hasPasswordExpired: {
      type: Sequelize.VIRTUAL,
      get() {
        return this.passwordExpiresAt ? this.passwordExpiresAt < new Date() : false
      },
    },
    otpSecretKey: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    otpAuthActive: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    preferences: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
  },
  {
    tableName: 'users',
    paranoid: true,
  },
)

User.associate = (models) => {
  User.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  User.belongsTo(models.Timetable, {
    as: 'timetable',
    foreignKey: 'timetableId',
  })
  User.belongsToMany(models.Role, {
    as: 'roles',
    through: 'user_roles',
    foreignKey: 'userId',
    otherKey: 'roleId',
  })
  User.hasMany(models.Ticket, {
    as: 'tickets',
    foreignKey: 'userId',
  })
  User.hasMany(models.ContactBlockList, {
    as: 'contactBlockLists',
    foreignKey: 'userId',
  })
  User.hasMany(models.ContactBlockListsControl, {
    as: 'contactBlockListsControls',
    foreignKey: 'userId',
  })
  User.belongsToMany(models.Department, {
    as: 'departments',
    through: 'user_departments',
    foreignKey: 'userId',
    otherKey: 'departmentId',
  })
  User.belongsToMany(models.Department, {
    as: 'departments1',
    through: 'user_departments',
    foreignKey: 'userId',
    otherKey: 'departmentId',
  })
  User.belongsToMany(models.Organization, {
    as: 'organizations',
    through: 'users_organizations',
    foreignKey: 'userId',
    otherKey: 'organizationId',
  })
  User.hasMany(models.Schedule, { foreignKey: 'userId', as: 'schedules' })
  User.hasMany(models.AuthHistory, { foreignKey: 'userId', as: 'authHistory' })

  User.belongsToMany(models.Contact, {
    as: 'contacts',
    through: 'user_contacts',
    foreignKey: 'userId',
  })

  User.hasMany(models.Notification, {
    as: 'notifications',
    foreignKey: 'userId',
  })
}

// @ts-ignore
User.prototype.hasPermission = function (permission) {
  const permissions = this.permissions

  for (const p of permissions) {
    if (p.name === permission) {
      return true
    }
  }

  return false
}

// @ts-ignore
export default User
