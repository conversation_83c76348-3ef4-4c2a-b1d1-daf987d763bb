import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type ServerPodTypeInstance = {
  id: string
  name: string
  autoscalable: boolean
  cloud: string
  serverSize: string
  maxPerPod: number
  min: number
  desiredAvailable: number
  accountIdWhitelist: string[]
  serviceIdWhitelist: string[]
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

const ServerPodType = sequelize.define(
  'ServerPodType',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    autoscalable: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    cloud: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: false,
    },
    serverSize: {
      type: DataTypes.STRING,
      unique: false,
      allowNull: false,
    },
    maxPerPod: {
      type: DataTypes.INTEGER,
      unique: false,
      allowNull: false,
      defaultValue: 100,
    },
    min: {
      type: DataTypes.INTEGER,
      unique: false,
      allowNull: false,
      defaultValue: 0,
    },
    desiredAvailable: {
      type: DataTypes.INTEGER,
      unique: false,
      allowNull: false,
      defaultValue: 0,
    },
    startPriority: {
      type: DataTypes.INTEGER,
      unique: false,
      allowNull: false,
      defaultValue: 0,
    },
    accountIdWhitelist: {
      type: DataTypes.JSONB,
      defaultValue: [],
      allowNull: false,
    },
    serviceIdWhitelist: {
      type: DataTypes.JSONB,
      defaultValue: [],
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'server_pod_types',
    paranoid: true,
  },
)

ServerPodType.associate = (models) => {}

export default ServerPodType
