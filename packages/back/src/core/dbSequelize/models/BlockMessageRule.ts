import { DataTypes } from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'

export type Type = 'phone-number'

export type WorkPlanDay = { start: string; end: string }[]

export type ConditionType = 'regex' | 'string' | 'boolean' | 'number'

export type Condition = string | boolean | number

export type BlockMessageRuleInstance = {
  id: string
  name: string
  type: Type
  priority: number
  data: {
    workPlan?: {
      // Valor null ou vazio representa sem horário de atendimento
      sun?: WorkPlanDay
      mon?: WorkPlanDay
      tue?: WorkPlanDay
      wed?: WorkPlanDay
      thu?: WorkPlanDay
      fri?: WorkPlanDay
      sat?: WorkPlanDay
    }
    options?: {
      timezone?: number
      dayLight?: number
    }
    conditionType?: ConditionType
    condition?: Condition
  }
  createdAt: Date
  updatedAt: Date
  accountId: string
  account?: AccountInstance
}

const BlockMessageRule = sequelize.define(
  'BlockMessageRule',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    priority: {
      // Prioridade (zero é a maior prioridade)
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false,
    },
    data: {
      // Informações sobre a regra de bloqueio
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'accounts',
        key: 'id',
      },
    },
  },
  {
    tableName: 'block_message_rules',
  },
)

BlockMessageRule.associate = (models) => {
  BlockMessageRule.belongsTo(models.Account, {
    // Relação de 1:N -> 1 Account : N BlockMessageRule
    as: 'account',
    foreignKey: { name: 'accountId', allowNull: false },
  })
}

export default BlockMessageRule
