import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ContactBlockListInstance } from './ContactBlockList'

// @ts-ignore
const { DataTypes } = Sequelize

export type ContactBlockListItemInstance = {
  id: string
  idFromService: string
  contactBlockListId: string
  accountId: string
  contactBlockList: ContactBlockListInstance
  account?: AccountInstance
  createdAt?: Date
  updatedAt?: Date
}

const ContactBlockListItem = sequelize.define(
  'ContactBlockListItem',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    idFromService: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    contactBlockListId: {
      type: DataTypes.UUID,
      references: {
        model: 'contact_block_lists',
        key: 'id',
      },
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      references: {
        model: 'accounts',
        key: 'id',
      },
      allowNull: false,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  },
  {
    tableName: 'contact_block_list_items',
    paranoid: false,
    indexes: [
      {
        unique: true,
        fields: ['idFromService', 'contactBlockListId', 'accountId'],
      },
      {
        fields: ['accountId'],
      },
    ],
  },
)

ContactBlockListItem.associate = (models) => {
  ContactBlockListItem.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  ContactBlockListItem.belongsTo(models.ContactBlockList, {
    as: 'contactBlockList',
    foreignKey: 'contactBlockListId',
  })
}

export default ContactBlockListItem
