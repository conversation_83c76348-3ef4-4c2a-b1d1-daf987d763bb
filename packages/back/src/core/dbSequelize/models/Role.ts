import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { PermissionInstance } from './Permission'
import { Options } from '../../resources/BaseResource'

// @ts-ignore
const { DataTypes } = Sequelize

export type RoleInstance = {
  id: string
  displayName: string
  isAdmin: boolean
  permissions: PermissionInstance[]
  getPermissions(options: Options<PermissionInstance>): PermissionInstance[]
}

const Role = sequelize.define(
  'Role',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    displayName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isAdmin: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
  },
  {
    tableName: 'roles',
    paranoid: false,
    indexes: [
      {
        unique: true,
        fields: ['displayName', 'accountId'],
      },
    ],
  },
)

Role.associate = (models) => {
  Role.belongsTo(models.Account, { foreignKey: 'accountId' })
  Role.belongsToMany(models.Permission, {
    through: 'role_permissions',
    as: 'permissions',
    foreignKey: 'roleId',
    otherKey: 'permissionId',
  })
  Role.belongsToMany(models.Distribution, {
    through: 'distribution_roles',
    as: 'distributions',
    foreignKey: 'roleId',
    otherKey: 'distributionId',
  })
  Role.belongsToMany(models.User, {
    as: 'users',
    through: 'user_roles',
    foreignKey: 'roleId',
    otherKey: 'userId',
  })
}

export default Role
