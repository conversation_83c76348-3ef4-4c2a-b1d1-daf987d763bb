import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type ScheduleInstance = {
  id: string
  contactId: string
  userId: string
  departmentId: string
  accountId: string
  status: 'scheduled' | 'done'
  message: string
  notes: string
  openTicket: boolean
  notificateUser: boolean
  scheduledAt: Date
  createdAt: Date
  updatedAt: Date
}

const Schedule = sequelize.define(
  'Schedule',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: 'scheduled',
    },
    message: {
      type: DataTypes.STRING,
    },
    notes: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    openTicket: {
      type: DataTypes.BOOLEAN,
    },
    notificateUser: {
      type: DataTypes.BOOLEAN,
    },
    scheduledAt: DataTypes.DATE,
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'schedules',
    paranoid: true,
  },
)

Schedule.associate = (models) => {
  Schedule.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  }),
    Schedule.belongsTo(models.Contact, {
      as: 'contact',
      foreignKey: 'contactId',
    }),
    Schedule.belongsTo(models.User, {
      as: 'user',
      foreignKey: 'userId',
    }),
    Schedule.belongsTo(models.Department, {
      as: 'department',
      foreignKey: 'departmentId',
    }),
    Schedule.hasOne(models.Notification, {
      as: 'notification',
      foreignKey: 'scheduleId',
    }),
    Schedule.hasMany(models.File, {
      as: 'files',
      foreignKey: 'attachedId',
      // @ts-ignore
      scope: { attachedType: 'schedule.files' },
      constraints: false,
    })
}

export default Schedule
