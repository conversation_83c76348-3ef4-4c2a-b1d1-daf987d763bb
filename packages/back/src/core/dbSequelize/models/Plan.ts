import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type PlanInstance = {
  id: string
  name: string
  durations: number
  data: any
  securityToken: string
  gatewayId: string
  createdAt: Date
  updatedAt: Date
}

const Plan = sequelize.define(
  'Plan',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: false,
    },
    securityToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    gatewayId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    tableName: 'plans',
    paranoid: true,
  },
)

Plan.associate = (models) => {}

export default Plan
