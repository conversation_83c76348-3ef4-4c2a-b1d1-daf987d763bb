import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { QuestionInstance } from './Question'

// @ts-ignore
const { DataTypes } = Sequelize

export type AnswerInstance = {
  id: string
  questionId: string
  ticketId: string
  text: string
  aiGenerated: boolean
  aiText?: string
  reason?: string
  createdAt?: Date
  updatedAt?: Date
  deletedAt?: Date
  question?: QuestionInstance
}

const Answer = sequelize.define(
  'Answer',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    text: {
      type: DataTypes.STRING,
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    aiGenerated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    aiText: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    tableName: 'answers',
    paranoid: true,
  },
)

Answer.associate = (models) => {
  Answer.belongsTo(models.Question, {
    as: 'question',
    foreignKey: 'questionId',
  })

  Answer.belongsTo(models.Ticket, {
    as: 'ticket',
    foreignKey: 'ticketId',
  })
}

export default Answer
