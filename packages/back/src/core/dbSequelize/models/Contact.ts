import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { MessageInstance } from './Message'
import { ServiceInstance } from './Service'
import { AccountInstance } from './Account'
import { UserInstance } from './User'
import { TicketInstance } from './Ticket'
import { TagInstance } from './Tag'
import { CardInstance } from './Card'
import { DepartmentInstance } from './Department'
import { PersonInstance } from './Person'
import { CustomFieldValueInstance } from './CustomFieldValue'
import { AcceptanceTermInstance } from './AcceptanceTerm'

// @ts-ignore
const { DataTypes } = Sequelize

export enum ContactOrigin {
  API = 'api',
  App = 'app',
  Campaign = 'campaign',
  Driver = 'driver',
  Import = 'import',
  Web = 'web',
}

type ContactAttributes = {
  id: string
  idFromService: string
  name: string
  internalName: string
  alternativeName: string
  isGroup: boolean
  isBroadcast: boolean
  isMe: boolean
  isMyContact: boolean
  hadChat: boolean
  visible: boolean
  isSilenced: boolean
  data: {
    lastSyncAt?: Date
    unread?: boolean
    hugmeStatus?: 1 | 2 | 3 | 4 | 15 | 16 | 17 | 21 | 22
    raStatus?: 5 | 6 | 7 | 8 | 9 | 10 | 12 | 13 | 18 | 19 | 20
    answered?: boolean
    info?: {
      birthday: Date
      cpf: string
      rg: string
      gender: string
      email: string
      phoneNumbers: string
      city: string
      state: string
      company: string
    }
    number?: string
    valid?: boolean
    isOriginal?: boolean
    validNumber?: string
    email?: string
    survey?: {
      expiresAt?: Date
      ticketId?: string
      questionId?: string
      tries?: number
    }
    webchat?: {
      userId: string
      roomId?: string
      idleStages?: {
        1?: Date
        2?: Date
        3?: Date
      }
      sentFollowUpMail?: {
        sentAt: Date
        origin: 'auto' | 'manual'
      }
      sentTicketCloseMail?: {
        sentAt: Date
        origin: 'auto' | 'manual'
      }
      originAccessURL?: string
      followUpToken?: string
      followUpRedirectLink?: string
      contactUnread?: number
      continueService?: boolean
    }
    lastChargedMessage?: Date
    botIsRunning?: boolean
    botFinishedAt?: Date
    blockMessageRules?: {
      unblockUntilAt?: string
      alertToBlockUntilAt?: string
      nextUnblockAt?: string
      unreadAlertToBlock?: boolean
    }
    canSend?: boolean
    lastMessageIdOnInactiveTicket?: string
  }
  note: string
  unread: number
  lastMessageAt: Date
  lastContactMessageAt?: Date
  unsubscribed: boolean
  status: string
  setParticipants: (contacts: ContactInstance[]) => Promise<ContactInstance[]>
  accountId: string
  serviceId: string
  defaultDepartmentId?: string
  defaultUserId?: string
  currentTicketId?: string
  lastMessageId?: string
  personId?: string
  account: AccountInstance
  service: ServiceInstance
  defaultDepartment?: DepartmentInstance
  defaultUser?: UserInstance
  lastMessage?: MessageInstance
  participants?: (ContactInstance & { isAdmin?: boolean; isSuperAdmin?: boolean })[]
  group_participants?: { isAdmin?: boolean; isSuperAdmin?: boolean }
  groups?: ContactInstance[]
  tags?: TagInstance[]
  cards?: CardInstance[]
  avatar?: any
  thumbAvatar?: any
  currentTicket?: TicketInstance
  tickets?: TicketInstance[]
  person?: PersonInstance
  createdAt: Date
  updatedAt: Date
  getAccount(options?: any): Promise<AccountInstance>
  customFields: any[]
  customFieldValues: CustomFieldValueInstance[]
  selectedIds?: {}
  allContactsSelected?: boolean
  filters?: any
  acceptedTermAt?: Date
  acceptanceTermId?: string
  acceptanceTerm?: AcceptanceTermInstance
  messages?: MessageInstance[]
  hsmExpirationTime?: Date
  block?: boolean
  dataBlock?: {
    level: 0 | 1 | 2 | 3 // 0 e 1 = desbloqueado/bloqueado via lista. 2 e 3 desbloqueado/bloqueado manual
    byUserId: string
    date: string | Date
    description: string
  }
  contactBlockListControlId: string
  origin?: ContactOrigin
  archivedAt: Date
}

type ContactMethods = {
  getAvatar(options?: any): Promise<any>
  getThumbAvatar(options?: any): Promise<any>
  addTags(tags: any[], options?: any): Promise<any>
  setTags(tags: any[], options?: any): Promise<any>
  removeTags(tags: any[], options?: any): Promise<any>
  toJSON(): any
  getCurrentTicket(options: any): Promise<TicketInstance>
  getService(): Promise<ServiceInstance>
}

export type ContactInstance = ContactAttributes & ContactMethods

const Contact = sequelize.define(
  'Contact',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    idFromService: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    internalName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    alternativeName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isGroup: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isBroadcast: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    isMe: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isMyContact: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    hadChat: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    visible: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: ['online', 'offline', 'absent'],
      defaultValue: 'offline',
      allowNull: false,
    },
    isSilenced: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: false,
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    unread: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    lastMessageAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    unsubscribed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    lastContactMessageAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    acceptedTermAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    hsmExpirationTime: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    block: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    },
    dataBlock: {
      type: DataTypes.JSONB,
      defaultValue: {},
      allowNull: true,
    },
    origin: {
      type: DataTypes.ENUM,
      values: Object.values(ContactOrigin),
      allowNull: true,
    },
    archivedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
    },
  },
  {
    tableName: 'contacts',
    paranoid: true,
  },
)

Contact.associate = (models) => {
  Contact.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  Contact.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })

  Contact.belongsTo(models.Department, {
    as: 'defaultDepartment',
    foreignKey: 'defaultDepartmentId',
  })

  Contact.belongsTo(models.User, {
    as: 'defaultUser',
    foreignKey: 'defaultUserId',
  })

  Contact.belongsTo(models.Message, {
    as: 'lastMessage',
    foreignKey: 'lastMessageId',
    constraints: false,
  })

  Contact.belongsToMany(Contact, {
    as: 'participants',
    through: 'group_participants',
    foreignKey: 'groupId',
  })

  Contact.belongsToMany(Contact, {
    as: 'groups',
    through: 'group_participants',
    foreignKey: 'contactId',
  })

  Contact.belongsToMany(models.Tag, {
    as: 'tags',
    through: 'contact_tags',
    foreignKey: 'contactId',
    otherKey: 'tagId',
  })

  Contact.belongsToMany(models.Tag, {
    as: 'tagsFilter',
    through: 'contact_tags',
    foreignKey: 'contactId',
    otherKey: 'tagId',
  })

  Contact.hasOne(models.File, {
    as: 'avatar',
    foreignKey: 'attachedId',
    scope: { attachedType: 'contact' },
    constraints: false,
  })

  Contact.hasOne(models.File, {
    as: 'thumbAvatar',
    foreignKey: 'attachedId',
    scope: { attachedType: 'contact.thumbnail' },
    constraints: false,
  })

  Contact.belongsTo(models.Ticket, {
    as: 'currentTicket',
    foreignKey: 'currentTicketId',
    constraints: false,
  })

  Contact.belongsToMany(models.Ticket, {
    as: 'tickets',
    through: 'contact_tickets',
    foreignKey: 'contactId',
    otherKey: 'ticketId',
  })

  Contact.hasMany(models.CustomFieldValue, {
    as: 'customFieldValues',
    foreignKey: 'relatedId',
    scope: { relatedType: 'contact' },
  })

  Contact.belongsTo(models.Person, {
    as: 'person',
    foreignKey: 'personId',
  })

  Contact.hasMany(models.Schedule, {
    foreignKey: 'contactId',
    as: 'schedules',
  })

  Contact.belongsTo(models.AcceptanceTerm, {
    as: 'acceptanceTerm',
    foreignKey: 'acceptanceTermId',
  })

  Contact.hasMany(models.ServicesWebhookFail, {
    as: 'servicesWebhookFails',
    foreignKey: 'contactId',
  })

  Contact.belongsToMany(models.User, {
    as: 'user',
    through: 'user_contacts',
    foreignKey: 'contactId',
  })

  Contact.hasMany(models.Card, {
    as: 'cards',
    foreignKey: 'contactId',
  })
}

export default Contact
