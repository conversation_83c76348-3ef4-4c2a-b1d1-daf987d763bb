import { v4 as uuid } from 'uuid'
import env from './utils/env'
// @ts-ignore
import packageJson from '../../package.json'

if (!env('ENCRYPTION_KEY')) {
  console.error('No ENCRYPTION_KEY env variable found. (Esqueceu de copiar o .env né mané?)')
  process.exit()
}

export default {
  product: 'digisac',

  env: env('NODE_ENV', 'development'),

  get deployment() {
    return env('DEPLOYMENT', this.env)
  },

  // text or ecs
  logSecretKey: env('LOG_SECRET_KEY', 'CHANGEME-lCu7m1RPCT47nZ7QqsXKohEklIHThiRQ'),
  logFormat: env('LOG_FORMAT', 'text') as 'text' | 'ecs',
  tracer: env('TRACER', 'generic') as 'generic' | 'elastic',
  silentLogs: false,
  version: packageJson.version,
  get dbSchemaVersion() {
    // TODO has some problems, read CachedBaseRepository comment
    return env('DB_SCHEMA_VERSION', this.version)
  },
  appName: env('APP_NAME', 'core'),
  instanceId: env('INSTANCE_ID', uuid()),
  branch: env('BRANCH', 'default'),

  host: env('HOST', 'localhost'),
  port: env.number('PORT', 4002),

  publicUrl: env('PUBLIC_URL', 'http://127.0.0.1:8080/v1'),
  frontUrl: env('FRONT_URL', 'http://127.0.0.1:1337'),
  gracePeriodWebhookUrl: env(
    'GRACE_PERIOD_WEBHOOK_URL',
    'https://n8n.digisac.io/webhook/422c0893-d77f-4615-b178-7c1c668ea3d4',
  ),
  gracePeriodWebhookToken: env('GRACE_PERIOD_WEBHOOK_TOKEN', null),

  gracePeriodExtendTimes: env('GRACE_PERIOD_EXTEND_TIMES', 1),

  amountLoadSearchMessages: env.number('AMOUNT_LOAD_SEARCH_MESSAGES', 500),

  webchatUrl: env('WEBCHAT_URL', 'http://127.0.0.1:5959'),
  internalChatUrl: env('INTERNAL_CHAT_URL', 'http://127.0.0.1:3333'),

  workersPort: env.number('WORKERS_PORT', 8000),
  redisUrl: env('REDIS_URL', 'redis://127.0.0.1:6379'),

  podGatewayPort: env.number('POD_GATEWAY_PORT', 5000),
  taskQueueManagerPort: env.number('TASK_QUEUE_MANAGER_PORT', 8003),

  socketGatewayPort: env.number('SOCKET_GATEWAY_PORT', 7000),

  privateIpRange: env('PRIVATE_IP_RANGE', '172.'),

  get workersUrl() {
    return env('WORKERS_URL', `http://127.0.0.1:${this.workersPort}`)
  },
  get taskQueueManagerUrl() {
    return env('TASK_QUEUE_MANAGER_URL', `http://127.0.0.1:${this.taskQueueManagerPort}`)
  },
  get podGatewayAddress() {
    return env('POD_GATEWAY_ADDRESS', `http://127.0.0.1:${this.podGatewayPort}`)
  },
  get podGatewayPublicAddress() {
    return env('POD_GATEWAY_PUBLIC_ADDRESS', this.podGatewayAddress)
  },
  get apiPublicBaseUrl() {
    return this.publicUrl.replace('/v1', '')
  },

  get internalApiUrl() {
    return env('INTERNAL_API_URL', this.apiPublicBaseUrl)
  },

  driversGatewayUrl: env('DRIVERS_GATEWAY_URL'),

  facebookApiUrl: env('FACEBOOK_API_URL', 'https://graph.facebook.com/v21.0'),
  facebookAppId: env('FACEBOOK_APP_ID', '139638411265417'),
  facebookClientSecret: env('FACEBOOK_CLIENT_SECRET', '********************************'),

  apiKeyOpenIA: env('API_KEY_ONPENAI'),
  apiTranscriberUrl: env('API_TRANSCRIBER_URL'),
  apiTranscriberToken: env('API_TRANSCRIBER_TOKEN'),

  allowMultiTabNavigation: env.boolean('ALLOW_MULTI_TAB_NAVIGATION', true),

  n8nWebhook: env('N8N_WEBHOOK', 'https://n8n.digisac.io/webhook/0a713f22-27d6-428b-a3b6-4948b40ee255'),

  dbDialect: env('DB_DIALECT', 'postgres'),

  dbDatabase: env('DB_DATABASE'),
  dbLogSql: env.boolean('DB_LOG_SQL', false),
  dbMaxConnections: env.number('DB_MAX_CONNECTIONS', 90),
  dbPort: env('DB_PORT', 5432),
  dbSSL: env.boolean('DB_SSL', false),
  dbSSLCA: env('DB_SSL_CA', null),

  mongoDBHost: env('MONGODB_HOST'),
  mongoDBPort: env('MONGODB_PORT'),
  mongoDBDatabase: env('MONGODB_DATABASE'),
  mongoDBUsername: env('MONGODB_USERNAME'),
  mongoDBPassword: env('MONGODB_PASSWORD'),
  mongoDBMaxPoolSize: env.number('MONGODB_MAX_POOL_SIZE', 1),
  mongoDBAuthSource: env('MONGODB_AUTH_SOURCE'),

  dbWriteHost: env('DB_WRITE_HOST', env('DB_HOST', 'localhost')),
  dbWriteUsername: env('DB_WRITE_USERNAME', env('DB_USERNAME')),
  dbWritePassword: env('DB_WRITE_PASSWORD', env('DB_PASSWORD')),

  dbReadHost: env('DB_READ_HOST', env('DB_HOST', 'localhost')),
  dbReadUsername: env('DB_READ_USERNAME', env('DB_USERNAME')),
  dbReadPassword: env('DB_READ_PASSWORD', env('DB_PASSWORD')),

  dbRead2Host: env('DB_READ2_HOST'),
  dbRead2Username: env('DB_READ2_USERNAME', env('DB_USERNAME')),
  dbRead2Password: env('DB_READ2_PASSWORD', env('DB_PASSWORD')),

  dbRead3Host: env('DB_READ3_HOST'),
  dbRead3Username: env('DB_READ3_USERNAME', env('DB_USERNAME')),
  dbRead3Password: env('DB_READ3_PASSWORD', env('DB_PASSWORD')),

  dbRead4Host: env('DB_READ4_HOST'),
  dbRead4Username: env('DB_READ4_USERNAME', env('DB_USERNAME')),
  dbRead4Password: env('DB_READ4_PASSWORD', env('DB_PASSWORD')),

  encryptionKey: env('ENCRYPTION_KEY'),

  sentryDsn: env('SENTRY_DSN'),

  emailSmtpServer: env('EMAIL_SMTP_SERVER'),
  emailServerPort: env('EMAIL_SERVER_PORT'),
  emailFrom: env('EMAIL_FROM', '<EMAIL>'),
  emailPassword: env('EMAIL_PASSWORD'),
  oneSignalAppId: env('ONE_SIGNAL_APP_ID'),
  oneSignalUrl: env('ONE_SIGNAL_URL'),
  minutesToAlertWebhookInactivation: env.number('MINUTES_TO_ALERT_WEBHOOK_INACTIVATION', 60),
  minutesForWebhookInactivation: env.number('MINUTES_FOR_WEBHOOK_INACTIVATION', 1440),

  expoAccessToken: env('EXPO_ACCESS_TOKEN'),
  awsSesAccessKeyId: env('AWS_ACCESS_KEY_ID'),
  awsSesSecretAccessKey: env('AWS_SECRET_ACCESS_KEY'),

  awsRegion: env('AWS_REGION'),
  awsBucketName: env('AWS_BUCKET_NAME'),
  awsS3AccessKeyId: env('AWS_ACCESS_KEY_ID'),
  awsS3SecretAccessKey: env('AWS_SECRET_ACCESS_KEY'),
  awsS3Endpoint: env('AWS_S3_ENDPOINT'),

  oracleRegion: env('ORACLE_REGION'),
  oracleBucketName: env('ORACLE_NAME'),
  oracleBucketNameFallback: env('ORACLE_BUCKET_FALLBACK'),
  oracleAccessKeyId: env('ORACLE_ACCESS_KEY_ID'),
  oracleSecretAccessKey: env('ORACLE_SECRET_ACCESS_KEY'),
  oracleEndpoint: env('ORACLE_ENDPOINT'),
  oracleEndpointReader: env('ORACLE_ENDPOINT_READER'),

  awsExportsS3Endpoint: env('AWS_EXPORTS_ENDPOINT') || env('AWS_S3_ENDPOINT'),
  awsExportsS3Region: env('AWS_EXPORTS_REGION') || env('AWS_REGION'),
  awsExportsS3BucketName: env('AWS_EXPORTS_BUCKET_NAME', 'mandeumzap-exports'),
  awsExportsS3AccessKeyId: env('AWS_EXPORTS_ACCESS_KEY_ID') || env('AWS_ACCESS_KEY_ID'),
  awsExportsS3SecretAccessKey: env('AWS_EXPORTS_SECRET_ACCESS_KEY') || env('AWS_SECRET_ACCESS_KEY'),

  wpLvS3Endpoint: env('WPLV_S3_ENDPOINT'),
  wpLvS3Region: env('WPLV_S3_REGION'),
  wpLvS3BucketName: env('WPLV_S3_BUCKET_NAME'),
  wpLvS3AccessKeyId: env('WPLV_S3_ACCESS_KEY_ID'),
  wpLvS3SecretAccessKey: env('WPLV_S3_SECRET_ACCESS_KEY'),

  wpBrowserDataS3Endpoint: env('WP_BROWSER_DATA_S3_ENDPOINT'),
  wpBrowserDataS3Region: env('WP_BROWSER_DATA_S3_REGION'),
  wpBrowserDataS3BucketName: env('WP_BROWSER_DATA_S3_BUCKET_NAME'),
  wpBrowserDataS3AccessKeyId: env('WP_BROWSER_DATA_S3_ACCESS_KEY_ID'),
  wpBrowserDataS3SecretAccessKey: env('WP_BROWSER_DATA_S3_SECRET_ACCESS_KEY'),

  sendGridApiKey: env('SENDGRID_API_KEY'),
  domain: env('DOMAIN'),

  invalidWebhooksInactivator: {
    // Aviso da desativação do webbhook inválido
    'mandeumzap.com.br': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'mandeumzap.app': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.app': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.chat': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.co': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.io': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.me': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'digisac.biz': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
    'hublx.app': '16453b60-38bb-11ec-a464-b5ea24e94bf7',
  },

  registerEmailTemplateIdMap: {
    // Ativação de conta DigiSac ?
    'mandeumzap.com.br': '0a8887c0-d1da-11ea-a902-fd7dd74222e6',
    'mandeumzap.app': '0a8887c0-d1da-11ea-a902-fd7dd74222e6',
    'digisac.app': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'digisac.chat': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'digisac.co': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'digisac.io': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'digisac.me': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'digisac.biz': '0c4c4940-d0c8-11ea-a129-c568b7cfeddb',
    'hublx.app': '0875c500-11ff-11ee-9af4-0b625bbba4b7',
  },

  expirationEmailTemplateIdMap: {
    // Seu plano irá expirar em breve
    'mandeumzap.com.br': 'a980e940-d1da-11ea-8087-43c9b9dee51f',
    'mandeumzap.app': 'a980e940-d1da-11ea-8087-43c9b9dee51f',
    'digisac.app': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
    'digisac.chat': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
    'digisac.co': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
    'digisac.io': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
    'digisac.me': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
    'digisac.biz': 'c8148cd0-d1d7-11ea-bc05-b96d7b8108ed',
  },

  desactivationEmailTemplateIdMap: {
    // Seu plano expirou
    'mandeumzap.com.br': '730f9b20-d1da-11ea-91dc-1f7476319aba',
    'mandeumzap.app': '730f9b20-d1da-11ea-91dc-1f7476319aba',
    'digisac.app': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
    'digisac.chat': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
    'digisac.co': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
    'digisac.io': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
    'digisac.me': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
    'digisac.biz': 'f5e41e70-d1d7-11ea-bf7f-05797e5af82f',
  },

  sendResetPasswordEmail: {
    // Bem vindo ao digisac! (Redeninição de senha)
    'mandeumzap.com.br': '42bb62d0-d1da-11ea-bd8c-0d3ec8b7e46f',
    'mandeumzap.app': '42bb62d0-d1da-11ea-bd8c-0d3ec8b7e46f',
    'digisac.app': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'digisac.chat': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'digisac.co': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'digisac.io': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'digisac.me': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'digisac.biz': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
    'hublx.app': '1df1d510-d1d8-11ea-9cc9-4f0198bc64ed',
  },

  sendCreatePasswordEmail: {
    // Bem vindo ao digisac! (Criação de senha)
    'mandeumzap.com.br': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'mandeumzap.app': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.app': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.chat': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.co': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.io': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.me': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
    'digisac.biz': 'a232c830-5d58-11ee-937f-f9c70853e0b5',
  },

  gracePeriodEmailTemplateIdMap: {
    // Aviso de Bloqueio de Conta
    'mandeumzap.com.br': 'd-7622e1fc3df545b78bfcfcc46f1bae7c',
    'mandeumzap.app': 'd-7622e1fc3df545b78bfcfcc46f1bae7c',
    'digisac.app': '11ce6060-d1d9-11ea-adab-7d5005986c08',
    'digisac.chat': '11ce6060-d1d9-11ea-adab-7d5005986c08',
    'digisac.co': '11ce6060-d1d9-11ea-adab-7d5005986c08',
    'digisac.io': '11ce6060-d1d9-11ea-adab-7d5005986c08',
    'digisac.me': '11ce6060-d1d9-11ea-adab-7d5005986c08',
    'digisac.biz': '11ce6060-d1d9-11ea-adab-7d5005986c08',
  },

  checkPlatformAccess: {
    // Aviso de Acesso a Plataforma
    'mandeumzap.com.br': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'mandeumzap.app': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.app': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.chat': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.co': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.io': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.me': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
    'digisac.biz': '57b8aa00-fdd2-11ea-965f-6b6df0cebdd9',
  },

  sendEmailToFollowUpUsersInWebchat: {
    // Envio de email de follow up para contatos do webchat
    'mandeumzap.com.br': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'mandeumzap.app': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.app': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.chat': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.co': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.io': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.me': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
    'digisac.biz': 'c32299f0-098d-11ec-bd28-9f24ffd00da2',
  },

  emailTermsTemplate: '13ca7ee2-5a24-4497-ac30-728a411b9361',

  serviceEmailIds: {
    'mandeumzap.com.br': 'cda05520-d101-11ea-86f9-43c3f633db54',
    'mandeumzap.app': 'cda05520-d101-11ea-86f9-43c3f633db54',
    'digisac.app': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'digisac.chat': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'digisac.co': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'digisac.io': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'digisac.me': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'digisac.biz': 'b0890260-d104-11ea-be62-7d8beb5ce828',
    'hublx.app': '94cf41b0-eed1-11eb-b2e8-01ab3b1343a5',
  },

  sendMFANotification: {
    'mandeumzap.com.br': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'mandeumzap.app': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.app': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.chat': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.co': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.io': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.me': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'digisac.biz': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
    'hublx.app': 'cfd27494-b4b5-4332-8f52-1b473af9adc5',
  },

  defaultCluster: env('DEFAULT_CLUSTER'),

  agnusCloudToken: env(
    'AGNUS_CLOUD_TOKEN',
    'Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  ),

  haystackIa: {
    url: env('HAYSTACK_IA_URL'),
    username: env('HAYSTACK_IA_USERNAME'),
    password: env('HAYSTACK_IA_PASSWORD'),
  },

  agnusUrl: env('AGNUS_URL', 'https://qa.agnus.app/api/v1'),
  agnusUrlv2: env('AGNUS_URL_V2', 'https://qa.agnus.app/api/v2'),

  gmailApi: {
    clientId: env('GMAIL_APP_CLIENT_ID'),
    clientSecret: env('GMAIL_APP_CLIENT_SECRET'),
    redirectURL: env('GMAIL_APP_REDIRECT_URL'),
  },

  hub360Api: {
    baseUrl: env('HUB360_API_URL', 'https://hub.360dialog.io/api/v2'),
    username: env('HUB360_API_USERNAME'),
    password: env('HUB360_API_PASSWORD'),
    partnerId: env('HUB360_API_PARTNER_ID'),
    usernameExternalBilling: env('HUB360_API_USERNAME_EXTERNAL_BILLING'),
    passwordExternalBilling: env('HUB360_API_PASSWORD_EXTERNAL_BILLING'),
    partnerIdExternalBilling: env('HUB360_API_PARTNER_ID_EXTERNAL_BILLING'),
  },

  microsoftClientId: env('MICROSOFT_CLIENT_ID'),
  microsoftClientSecret: env('MICROSOFT_CLIENT_SECRET'),
  microsoftBaseUrl: env('MICROSOFT_BASE_URL'),
  microsoftLoginBaseUrl: env('MICROSOFT_LOGIN_BASE_URL'),

  googleBusinessBaseURL: env('GOOGLE_BUSINESS_BASE_URL', 'https://www.googleapis.com/auth/businessmessages'),

  googleRecaptchaSecretKey: env('GOOGLE_RECAPTCHA_SECRET_KEY'),

  bccAddresses: [],

  newInfraClusterName: env('NEW_INFRA_CLUSTER_NAME', 'app2'),

  get storageDriver() {
    return env('STORAGE_DRIVER', this.env === 'production' ? 's3' : 'fs')
  },

  get buckets() {
    return env('STORAGE_BUCKET', 'digisac-storage,digisac-storage-2,digisac-storage-3')
  },

  get resourceCacheEnabled() {
    return env.boolean('RESOURCE_CACHE_ENABLED', true)
  },

  get enableSentry() {
    return this.env === 'production'
  },

  get isFrontTest() {
    return this.env === 'cypress'
  },

  useCacheForAllQueries: env.boolean('USE_CACHE_FOR_ALL_QUERIES', false),

  whatsappScriptsPath: env('WHATSAPP_SCRIPTS_PATH', 'node_modules/whatsapp-scripts-dist/index.js'),
  reclameAquiURL: env('RECLAME_AQUI_URL', 'https://app.hugme.com.br/api'),

  waSendingQueueConcurrency: env('WA_SENDING_QUEUE_CONCURRENCY', 10),
  waReceivingQueueConcurrency: env('WA_RECEIVING_QUEUE_CONCURRENCY', 10),

  apmUrl: env('APM_URL', null),
  apmSecret: env('APM_SECRET', null),

  add9ToMessageIdAfterTimestamp: env('ADD_9_TO_MESSAGE_ID_AFTER_TIMESTAMP', null),

  resourceCacheLogsEnabled: env('RESOURCE_CACHE_LOGS_ENABLED', false),

  // @TODO remove no futuro
  debugPaginationCount: env('DEBUG_PAGINATION_COUNT', false),

  get fileSignatureSecret() {
    return env('FILE_JWT_SECRET', this.encryptionKey)
  },

  whatsappBusinessWebhookProcessReceivedFileQueueConcurrency: env(
    'WHATSAPP_BUSINESS_WEBHOOK_PROCESS_RECEIVED_FILE_QUEUE_CONCURRENCY',
    10,
  ),
  whatsappBusinessWebhookProcessReceivedFileQueueTimeout: env(
    'WHATSAPP_BUSINESS_WEBHOOK_PROCESS_RECEIVED_FILE_QUEUE_TIMEOUT',
    10 * 60 * 1000,
  ),

  defaultHsmLimit: env('DEFAULT_HSM_LIMIT', 3000),
  useCachedTicketsCount: env.boolean('USE_CACHED_TICKETS_COUNT', true),
  refreshTicketCountCronExpression: env('REFRESH_TICKET_COUNT_CRON_EXPRESSION', '*/3 * * * * *'), // A cada 3 segundos

  // Só definir esse valor no arquivo .env do cliente, se estiver com use-block-message-rules-by-service habilitado na conta
  // Caso contrário, vai gerar processamento de cron sem necessidade
  blockMessageRuleCronExpression: env('BLOCK_MESSAGE_RULE_CRON_EXPRESSION', null),
  blockMessageRuleMinutesBeforeToAlert: env.number('BLOCK_MESSAGE_RULE_MINUTES_BEFORE_TO_ALERT', 30),

  consolidateConsumedCreditsCronExpression: env('CONSOLIDATE_CONSUMED_CREDITS_CRON_EXPRESSION', '*/5 * * * *'), // A cada 5 minutos

  fileCacheTTL: env('FILE_CACHE_TTL', 5 * 60 * 1000),
  // Trata warning exibido em console ao iniciar o socket:
  // `MaxListenersExceededWarning: Possible EventEmitter memory leak detected`
  emitterMaxListeners: env.number('EMITTER_MAX_LISTENERS', 50),

  feedbackEmail: env('FEEDBACK_EMAIL'),
  feedbackEmailUser: env('FEEDBACK_EMAIL_USER'),
  feedbackEmailPassword: env('FEEDBACK_EMAIL_PASSWORD'),
  feedbackEmailPort: env('FEEDBACK_EMAIL_PORT'),
  feedbackEmailHost: env('FEEDBACK_EMAIL_HOST'),

  gupshupEmail: env('GUPSHUP_EMAIL'),
  gupshupPassword: env('GUPSHUP_PASSWORD'),
  gupshupClientUrl: env('GUPSHUP_CLIENT_URL', 'https://api.gupshup.io/wa/api/v1'),

  resetPasswordJwtSecret: env('RESET_PASSWORD_JWT_SECRET', 'ae23390d6097f39941defa773ecff045'),
  allowIgnoreStrengthPassword: env('ALLOW_IGNORE_STRENGTH_PASSOWRD', true),

  minImageSizeToGenerateThumbnail: env.number('MIN_IMAGE_SIZE_TO_GENERATE_THUMBNAIL', 500000), // em bytes

  importContactsCampaignConcurrency: env.number('IMPORT_CONTACTS_CAMPAIGN_CONCURRENCY', 5), // Max 1000

  sendCampaignConcurrency: env.number('SEND_CAMPAIGN_CONCURRENCY', 5), // Max 10000

  initPrepareCampaignConcurrency: env.number('INIT_PREPARE_CAMPAIGN_CONCURRENCY', 5),

  initSendCampaignConcurrency: env.number('INIT_SEND_CAMPAIGN_CONCURRENCY', 5),

  limitJobsDistributionInQueue: env.number('LIMIT_JOBS_DISTRIBUTION_IN_QUEUE', 500),

  disableWabaWebhookUrlSet: env.boolean('DISABLE_WABA_WEBHOOK_URL_SET', false),

  queueManagerDispatchUrl: env('QUEUE_MANAGER_DISPATCHER_URL', 'http://message-broker-producer:2000/dispatch'),

  mockDriverUrl: env('MOCK_DRIVER_URL', 'http://localhost:3838'),
  useMockDriver: env.boolean('USE_MOCK_DRIVER', false),
  blockAiConsumption: env.boolean('BLOCK_AI_CONSUMPTION', false),
}
