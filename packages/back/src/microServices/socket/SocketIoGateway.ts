import 'reflect-metadata'
import { v4 as uuid } from 'uuid'
import http, { createServer } from 'http'
import createIo, { Server, Socket } from 'socket.io'
import wildcardMiddleware from 'socketio-wildcard'
import express, { Request, Response } from 'express'
import bodyParser from 'body-parser'
import reportError from '../../core/services/logs/reportError'
import { promiseHandler } from '../../core/utils/routing/resourceRouter'
import httpErrorHandler from '../../core/middlewares/httpErrorHandler'
import { Container } from 'typedi'
import { HttpClient } from '../../core/services/httpClient/HttpClient'

type Handler = {
  id: string
  url: string
  method: 'post' | 'get' | 'put' | 'patch' | 'delete'
}

type Options = {
  socketPort: number
  httpPort: number
  extraMetadata?: { [key: string]: any }
}

export default class SocketIoGateway {
  protected socketServer: http.Server

  protected httpServer: http.Server

  protected io: Server

  protected sockets = new Map<string, Socket>()

  protected handlers = new Map<string, Handler[]>()

  protected handlersIdMap = new Map<string, [string, number]>()

  constructor(protected options: Options) {}

  async start() {
    // express
    const app = express()
    app.use(bodyParser.json({ limit: '100mb' }))
    app.use(bodyParser.text({ limit: '100mb' }))
    app.get('/health', this.healthHandler.bind(this))
    app.post('/send/:id/:event', this.sendHandler.bind(this))
    app.post('/disconnect/:id', this.disconnectHandler.bind(this))
    app.post('/bulk-disconnect', this.disconnectBulkHandler.bind(this))
    app.post('/send-bulk', this.sendBulkHandler.bind(this))
    app.post('/handlers', this.createHandlersHandler.bind(this))
    app.delete('/handlers/:id', this.deleteHandlersHandler.bind(this))
    app.use(httpErrorHandler)

    this.httpServer = createServer(app)
    await new Promise((resolve) =>
      // @ts-ignore
      this.httpServer.listen(this.options.httpPort, resolve),
    )

    // socket io
    this.socketServer = createServer()

    this.io = createIo(this.socketServer)
    this.io.use(wildcardMiddleware())
    this.io.use((socket, next) => {
      this.handleHandshake(socket)
        .then(() => next())
        .catch((error) => next(error))
    })

    this.io.on('connection', this.handleConnection.bind(this))

    await new Promise((resolve) => this.socketServer.listen(this.options.socketPort, resolve))
  }

  async close() {
    await new Promise((resolve) => this.io.close(() => resolve()))
    await new Promise((resolve) => this.httpServer.close(() => resolve()))
    await new Promise((resolve) => this.socketServer.close(() => resolve()))
  }

  protected async handleHandshake(socket: Socket) {
    const socketId = socket.id
    this.sockets.set(socketId, socket)

    const handshakeData = socket.request
    const data = {
      headers: handshakeData.headers,
      // eslint-disable-next-line no-underscore-dangle
      query: handshakeData._query,
      method: handshakeData.method,
    }
    const results = await this.getHandlersAndSend(socket.id, '$handshake', data)

    if (results.some((r) => r instanceof Error)) {
      throw new Error('Handshake failed.')
    }

    return results
  }

  addHandler({ event, url, method = 'post' }: { event: string; url: string; method?: Handler['method'] }) {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, [])
    }

    const id = uuid()

    const handler = { id, url, method }
    const index = this.handlers.get(event).push(handler)

    this.handlersIdMap.set(id, [event, index])

    return handler
  }

  protected createHandlersHandler(req: Request, res: Response<Handler>) {
    const { event, url, method = 'post' } = req.body

    const handler = this.addHandler({ event, url, method })

    // @ts-ignore
    res.json(handler)
  }

  protected deleteHandlersHandler(req: Request, res: Response) {
    const { id } = req.params

    const [event, number] = this.handlersIdMap.get(id)

    this.handlers.get(event).splice(number, 1)
    this.handlersIdMap.delete(id)

    // @ts-ignore
    res.sendStatus(200)
  }

  protected sendHandler = promiseHandler(async (req: Request, res: Response) => {
    const { id, event } = req.params
    const data = req.body

    await this.sendEvent(id, event, data)
  })

  protected disconnectHandler = promiseHandler(async (req: Request, res: Response) => {
    const { id } = req.params
    const socket = this.sockets.get(id)
    if (socket?.disconnect) socket.disconnect()
  })

  protected disconnectBulkHandler = promiseHandler(async (req: Request, res: Response) => {
    const {
      socketIds,
    }: {
      socketIds: string[]
    } = req.body

    socketIds.forEach((socketId) => {
      const socket = this.sockets.get(socketId)
      if (socket?.disconnect) socket.disconnect()
    })
  })

  protected sendBulkHandler = promiseHandler(async (req: Request, res: Response) => {
    const {
      socketIds = [],
      event,
      data,
    }: {
      socketIds: string[]
      event: string
      data: any
    } = req.body

    const results = await Promise.all(
      socketIds.map(async (socketId) => ({
        socketId,
        sent: await this.sendEvent(socketId, event, data)
          .then(() => true)
          .catch(() => false),
      })),
    )

    return results.reduce(
      (obj, { socketId, sent }) => ({
        ...obj,
        [socketId]: sent,
      }),
      {},
    )
  })

  protected async sendEvent(socketId: string, event: string, data: any) {
    const socket = this.sockets.get(socketId)

    if (!socket) {
      throw new Error(`Not socket with id #${socketId} found.`)
    }

    socket.emit(event, data)
  }

  protected async handleConnection(socket: Socket) {
    const socketId = socket.id

    await this.getHandlersAndSend(socketId, '$connect', null)

    socket.on('*', async (payload = {}) => {
      const [event, data] = payload.data
      await this.getHandlersAndSend(socketId, event, data)
    })

    socket.on('disconnect', async () => {
      this.handleDisconnection(socketId, socket)
      await this.getHandlersAndSend(socketId, '$disconnect', null)
    })
  }

  protected handleDisconnection(id: string, socket: Socket) {
    this.sockets.delete(id)
  }

  protected async getHandlersAndSend(socketId: string, event: string, data: any) {
    const handlers = this.handlers.get(event) || []
    return this.sendToHandlers(handlers, socketId, event, data)
  }

  protected sendToHandlers(handlers: Handler[], socketId: string, event: string, data: any) {
    return Promise.all(
      handlers.map((handler: Handler) =>
        this.getHttpClient()
          .request({
            method: handler.method,
            url: handler.url,
            data: {
              ...this.options.extraMetadata,
              socketId,
              event,
              data,
            },
          })
          .catch(reportError),
      ),
    )
  }

  protected healthHandler(req: Request, res: Response) {
    res.sendStatus(200)
  }

  protected getHttpClient() {
    return Container.get(HttpClient)
  }
}
