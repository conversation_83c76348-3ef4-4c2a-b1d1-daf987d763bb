import { Inject, Service } from 'typedi'
import OAuthServer from '@node-oauth/oauth2-server'
import Job from '../../../../core/services/jobs/interfaces/Job'
import oAuthServer from '../../../../core/services/auth/oAuthServer'
import { UserInstance } from '../../../../core/dbSequelize/models/User'
import RedisSessionStorage from './RedisSessionStorage'
import userResource from '../../../../core/resources/userResource'
import SocketSender from './SocketSender'
import HttpJobsDispatcher from '../../../../core/services/jobs/http/HttpJobsDispatcher'
import Logger from '../../../../core/services/logs/Logger'
import RequestContextCls from '../../../../core/services/cls/RequestContextCls'
import config from '../../../../core/config'

export type BasePayload = {
  gatewayAddress: string
  socketId: string
}
export type HandshakePayload = BasePayload & {
  event: '$handshake'
  data: {
    headers: { [key: string]: any }
    method: 'GET' | 'POST'
    query: { [key: string]: any }
  }
}
export type MePayload = BasePayload & {
  event: 'me.state'
  data: {
    chat: { view?: 'MINE' | 'QUEUE' | 'ALL'; currentContactId?: string }
  }
}
export type ConnectPayload = BasePayload & {
  event: '$connect'
}
export type DisconnectPayload = BasePayload & {
  event: '$disconnect'
}
export type AnyPayload = BasePayload & {
  event: string
  data: any
}
export type UserTakeoverPayload = {
  event: string
  data: UserInstance
}
export type Payload = HandshakePayload | ConnectPayload | DisconnectPayload | AnyPayload | UserTakeoverPayload

@Service()
export default class SocketReceiverJob implements Job {
  static jobName = 'socket-receive'

  @Inject()
  protected jobsDispatcher: HttpJobsDispatcher

  @Inject()
  protected sessionStorage: RedisSessionStorage

  @Inject()
  protected socketSender: SocketSender

  @Inject()
  protected logger: Logger

  @Inject()
  protected requestContextCls: RequestContextCls

  async handle(payload: Payload): Promise<void> {
    if ((payload as HandshakePayload).event === '$handshake') {
      return this.handleHandshake(payload as HandshakePayload)
    }

    if ((payload as DisconnectPayload).event === '$disconnect') {
      return this.handleDisconnect(payload as DisconnectPayload)
    }

    if ((payload as MePayload).event === 'me.state') {
      return this.handleMe(payload as MePayload)
    }
  }

  protected async handleMe(payload: MePayload) {
    const socketTuple = await this.sessionStorage.getSocketUser(payload.socketId, payload.gatewayAddress)

    await this.sessionStorage.addSocketToUser({
      ...socketTuple,
      ...payload,
      ...payload.data.chat,
    })
  }

  protected async handleHandshake(payload: HandshakePayload) {
    const { Request, Response } = OAuthServer
    const request = new Request(payload.data)
    const response = new Response({})
    const options = { allowBearerTokensInQueryString: true }

    const token = await oAuthServer.authenticate(request, response, options)

    const { impersonate = false, client = 'web', platform, version = null } = payload.data.query

    const user = (!impersonate && (await userResource.updateStatus(token.user.id, 'online', client))) || token.user

    const multiTabFlag = config('allowMultiTabNavigation')

    if (!user.isSuperAdmin && !impersonate) {
      const socketTuples = (await this.sessionStorage.getUserSockets(user.id)).filter(
        (socketTuple) =>
          socketTuple.impersonate === !!impersonate && (!multiTabFlag || socketTuple.accessToken !== token.accessToken),
      )

      if (socketTuples.length) {
        await Promise.all(
          socketTuples.map((st) =>
            this.socketSender.sendToManySocket({
              event: 'me.took_over',
              socketTuples: [st],
              data: {
                client,
              },
            }),
          ),
        )

        await this.socketSender.disconnectManySocket({
          socketTuples,
        })
        await Promise.all(
          socketTuples.map((socketTuple) =>
            this.sessionStorage.removeSocketFromUser({
              ...socketTuple,
              userId: user.id,
              accountId: user.accountId,
            }),
          ),
        )
      }
    }

    await this.sessionStorage.addSocketToUser({
      ...payload,
      userId: user.id,
      accountId: user.accountId,
      impersonate: !!impersonate,
      accessToken: token.accessToken,
      client,
      ...((client !== 'app' || (client === 'app' && version === '3')) && {
        view: 'MINE',
        currentContactId: null,
      }),
      platform,
    })
    const accountSockets = await this.sessionStorage.getAccountSockets(user.accountId)

    this.requestContextCls.run(() => {
      this.requestContextCls.setContext(
        {
          userId: user.id,
          accountId: user.accountId,
          impersonate: !!impersonate,
          client,
          platform,
        },
        true,
      )

      if (!impersonate) userResource.emitLogin(user)
    })

    this.logger.log(
      `\nUser #${user?.id} (${user?.name}) connected${(!!impersonate && ' as impersonate') || ''}. ${
        (accountSockets || []).length
      } connected users for account #${user?.accountId} from ${client}.`,
      'debug',
    )
  }

  protected async handleDisconnect(payload: DisconnectPayload) {
    const { socketId, gatewayAddress } = payload

    const socketTuple = await this.sessionStorage.getSocketUser(socketId, gatewayAddress)

    if (!socketTuple) return

    const { userId, accountId, impersonate = false, client = 'web' } = socketTuple

    const accountSockets = await this.sessionStorage.getAccountSockets(accountId)
    const user = !impersonate
      ? await userResource.updateStatus(userId, 'offline', client)
      : await userResource.findById(userId)

    await this.sessionStorage.removeSocketFromUser(payload)

    if (!impersonate) userResource.emitLogout(user)

    this.logger.log(
      `\nUser #${user?.id} (${user?.name}) disconnected${(!!impersonate && ' as impersonate') || ''}. ${
        ((accountSockets || []).length || 1) - 1
      } connected users for account #${user?.accountId} from ${client}.`,
      'debug',
    )
  }
}
