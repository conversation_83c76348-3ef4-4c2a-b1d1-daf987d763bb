import { Service } from 'typedi'
import accountResource from '../../../../../core/resources/accountResource'
import userResource from '../../../../../core/resources/userResource'
import i18next from '../../../../../core/i18n'
import { sendMFANotification } from '../../../../../core/services/email'
import Job from '../../../../../core/services/jobs/interfaces/Job'
import queuedAsyncMap from '../../../../../core/utils/array/queuedAsyncMap'

const getMFAAccounts = async () =>
  accountResource.findMany({
    attributes: ['id', 'name', 'settings'],
    where: {
      isActive: true,
    },
  })

@Service()
export default class MFAUserEmailJob implements Job {
  static jobName = 'MFAUserEmailJob'

  async handle() {
    const accounts = await getMFAAccounts()

    await queuedAsyncMap(accounts, async (account) => {
      if (!account.twoFactorAuthMandatoryActive) return

      const users = await userResource.findAllUsers(account.id)

      await queuedAsyncMap(
        users,
        async (user) => {
          await i18next.changeLanguage(user.language)

          const mfaData = {
            to: user.email,
            titulo: i18next.t('account:MFA_TITULO_LOGOUT'),
            texto: i18next.t('account:MFA_TEXTO_LOGOUT'),
            texto_final: i18next.t('account:MFA_TEXTO_FINAL_LOGOUT'),
            texto_rodape: i18next.t('account:MFA_TEXTO_RODAPE_DISABLE'),
          }

          // Envia e-mail para usuário informado o logout
          await sendMFANotification(mfaData)
        },
        1,
      )
    })
  }
}
