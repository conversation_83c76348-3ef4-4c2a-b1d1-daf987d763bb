import express from 'express'
import bodyParser from 'body-parser'
import helmet from 'helmet'
import * as Sentry from '@sentry/node'
import cors from 'cors'
import routes from './routes'
import config from '../../core/config'
import errorHandler from '../../core/middlewares/httpErrorHandler'
import notFound from '../../core/middlewares/notFound'
import parseQuery from '../../core/middlewares/parseQuery'
import tracing from '../../core/middlewares/tracing'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import apiVersion from '../../core/middlewares/apiVersion'

const app = express()

const logger = Container.get(Logger)

const corsConfig = {
  origin: config('origins') || true,
  credentials: true,
  exposedHeaders: ['X-Ratelimit-Remaining', 'X-Ratelimit-Reset'],
}

if (config('oasGenerator')) {
  require('express-oas-generator').init(app, {})
}

app.use(Sentry.Handlers.requestHandler())

app.use(
  helmet({
    crossOriginResourcePolicy: false,
  }),
)

//  apply to all requests
app.use(bodyParser.json({ extended: true, limit: '1000mb' }))
app.use(bodyParser.urlencoded({ extended: false, limit: '1000mb' }))
app.use(parseQuery())
app.use(cors(corsConfig))
app.use(tracing())
app.options('*', cors(corsConfig))

app.use((req, res, next) => {
  logger.log(`${req.method} ${req.url}`, 'debug')
  next()
})

const appV1 = express.Router()

const whitelistVersions = ['v1', 'v2']

appV1.use('/:version', apiVersion(whitelistVersions), routes)

if (!config('oasGenerator')) {
  appV1.use(notFound)
}

// app.use(Sentry.Handlers.errorHandler())

appV1.use(errorHandler)

app.use(appV1)

export default app
