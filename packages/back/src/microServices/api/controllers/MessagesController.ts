import { pick, uniqBy } from 'lodash'
import Container from 'typedi'
import { Response, Request } from 'express'
import BaseResourceController from './BaseResourceController'
import { MessageInstance } from '../../../core/dbSequelize/models/Message'
import stringToBoolean from '../../../core/utils/stringToBoolean'
import messageResource from '../../../core/resources/messageResource'
import messageTransformer from '../../../core/transformers/messageTransformer'
import contactResource from '../../../core/resources/contactResource'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import getIsDownloadable from '../../../core/utils/whatsapp/getIsDownloadable'
import driverService, { getServiceRpc } from '../../../core/services/driverService'
import reportError from '../../../core/services/logs/reportError'
import fileResource from '../../../core/resources/fileResource'
import { transformMany } from '../../../core/utils/resource/transformerHelpers'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'
import ForbiddenHttpError from '../../../core/utils/error/ForbiddenHttpError'
import Logger from '../../../core/services/logs/Logger'
import HttpError from '../../../core/utils/error/HttpError'
import userResource from '../../../core/resources/userResource'
import { differenceInMinutes } from 'date-fns'
import NotFoundHttpError from '../../../core/utils/error/NotFoundHttpError'

const logger = Container.get(Logger)
export default class MessagesController extends BaseResourceController<MessageInstance> {
  static LIMIT_MINUTES_TO_EDIT_MESSAGE = 15

  protected resource = messageResource

  protected transformer = messageTransformer

  protected includeWhitelist = [
    'hsm',
    'file',
    'files',
    'hsmFile',
    'preview',
    'thumbnail',
    'service',
    'from',
    'to',
    'user',
    'ticket',
    // TODO support this
    'ticket.department',
    'ticket.ticketTransfers',
    'quotedMessage',
    'ticketTransfer',
    'contact',
    'contact.avatar',
    'contact.thumbAvatar',
    'reactions',
    'reactionParentMessage',
  ]

  async getMessages(query) {
    const paginate = query.paginate !== false && query.paginate !== 'false'
    const withTotal = !!query.withTotal
    query = this.filterQuery(query, this.includeWhitelist)

    if (paginate) {
      return this.resource.findManyPaginated(query)
    }

    if (withTotal) {
      return this.resource.findManyWithTotal(query)
    }

    return this.resource.findMany(query)
  }

  async index(req: Request, res: Response): Promise<any> {
    if (req.query.includeTicketTransfer) {
      return this.resource.includeTicketTransfer(await this.resource.findMany(req.query), {
        account: res.locals.account,
        user: res.locals.user,
        impersonate: res.locals.impersonate,
      })
    }

    const response = await this.getMessages(req.query)
    const userHasPermission = this.resource.userHasPermissionToOpenTickets(res.locals.user.permissions)
    const openTickets = userHasPermission
      ? await contactResource.getOpenTicketsByContactsIds(
          (response.data || response).map((m) => m.contactId),
          res.locals.accountId,
        )
      : []
    const context = {
      account: res.locals.account,
      user: res.locals.user,
      openTickets,
      impersonate: res.locals.impersonate,
    }

    if (response.data) {
      return {
        ...response,
        data: await transformMany(this.transformer, context)(response.data),
      }
    }

    return await transformMany(this.transformer, context)(response)
  }

  async editMessage(req: Request, res: Response) {
    const { id } = req.params
    const { text, contactId } = req.body
    const { id: userId } = res.locals.user

    const findUser = await userResource.findById(userId)
    if (!findUser) throw new HttpError(404, 'User not found')

    const findContact = await contactResource.findById(contactId)
    if (!findContact) throw new HttpError(404, 'Contact not found')

    const findMessage = await messageResource.findById(id)
    if (!findMessage) throw new HttpError(404, 'Message not found')
    if (!findMessage.isFromMe) throw new HttpError(403, 'Cannot edit message')

    const isMoreThan15Minutes = differenceInMinutes(new Date(), new Date(findMessage.timestamp))
    if (isMoreThan15Minutes > MessagesController.LIMIT_MINUTES_TO_EDIT_MESSAGE)
      throw new HttpError(403, 'Cannot edit message')

    if (!text?.trim()) throw new HttpError(400, 'Text cannot is be empty')

    if (req.body.mentionedList && !Array.isArray(req.body.mentionedList)) {
      throw new HttpError(400, 'Invalid mentionedList format, must be array')
    }

    if (req.body.mentionedList && req.body.mentionedList.length) {
      const mentionedListProperty: Array<string> = req.body.mentionedList
      mentionedListProperty.filter((mention: string) => {
        const regexp = /\d+@c\.us/g
        const matchValue = mention.match(regexp)
        if (!matchValue)
          throw new HttpError(400, 'Invalid mentionedList format, must be array of contacts idFromService')
        return matchValue[0]
      })
    }

    const messageOptions = {
      ...(req.body.mentionedList && { mentionedList: req.body.mentionedList }),
    }

    try {
      await driverService.editMessage(findContact.serviceId, findMessage.idFromService, text, messageOptions)
      logger.log(`Success on update message [driverService]: \n\n { messageId: ${id}, userRequest: ${userId} }`)
    } catch (error) {
      logger.log(`Error on update message [driverService] \n\n${error}`)
      throw new HttpError(500, 'Internal Server Error')
    }
  }

  async create(req: Request, res: Response) {
    const data = {
      origin: 'user',
      ...pick(req.body, [
        'text',
        'number',
        'name',
        'type',
        'isPtt',
        'contactId',
        'serviceId',
        'file',
        'fileId',
        'filesIds',
        'hsmFileId',
        'itemIndex',
        'quotedMessageId',
        'attachments',
        'subject',
        'ccs',
        'hsm',
        'hsmId',
        'fileTemplate',
        'parameters',
        'origin',
        'isComment',
        'vcard',
        'interactiveMessage',
        'actions',
        'stickerId',
        'mentionedList',
      ]),
      userId: req.body.userId || res.locals.user.id,
      accountId: req.body.accountId,
      dontOpenTicket: stringToBoolean(req.body.dontOpenTicket),
      dontTransferTicket: !!res.locals?.oauth?.token?.name,
      dontSendUserName: stringToBoolean(req.body.dontSendUserName),
    }

    if (data.type === 'sticker' && !this.resource.userHasPermissionToSendStickers(res.locals.user.permissions)) {
      // Não tem permissão para enviar figurinha, deve emitir erro
      throw new ForbiddenHttpError()
    }

    if (data.mentionedList && !Array.isArray(data.mentionedList)) {
      throw new HttpError(400, 'Invalid mentionedList format, must be array')
    }

    if (data.mentionedList && data.mentionedList.length) {
      const mentionedListProperty: Array<string> = data.mentionedList

      mentionedListProperty.filter((mention: string) => {
        const regexp = /\d+@c\.us/g
        const matchValue = mention.match(regexp)
        if (!matchValue)
          throw new HttpError(400, 'Invalid mentionedList format, must be array of contacts idFromService')
        return matchValue[0]
      })
    }

    const options = { ...pick(req.query, ['include']) }

    const message = await this.resource.send(data, options)

    if (!message.data || message?.data?.error) {
      logger.logEvent(
        'msg_send_fail',
        { ...(message?.id && { messageId: message.id }) },
        `${message?.data?.error}`,
        'error',
      )
      if (message?.data?.error === 'HSM_WINDOW_ERROR') {
        const messageError = 'Túnel de mensagem encontra-se fechado'
        return res.status(400).send(messageError)
      }
      return res.status(400).send(message?.data?.error || 'Unidentified error')
    }

    const userHasPermission = this.resource.userHasPermissionToOpenTickets(res.locals.user.permissions)
    const openTickets = userHasPermission
      ? await contactResource.getOpenTicketsByContactsIds([(message.data || message).contactId], res.locals.accountId)
      : []

    return messageTransformer(message, {
      account: res.locals.account,
      user: res.locals.user,
      openTickets,
      impersonate: res.locals.impersonate,
    })
  }

  async forward(req: Request, res: Response) {
    const { contactsIds, messagesIds } = req.body

    if (contactsIds.length > 5) throw new Error('You can select at most 5 contacts.')
    if (messagesIds.length > 20) throw new Error('You can select at most 20 messages.')

    const contacts = await contactResource.findMany({
      where: { id: contactsIds },
    })

    const messages = await this.resource.findMany({
      where: { id: messagesIds },
      include: ['account', 'file'],
      order: [['timestamp', 'asc']],
    })

    const messagesDatas = (
      await queuedAsyncMap(messages, async (message) => {
        const isDownloadable = getIsDownloadable(message.type)

        const isLocation = message && message.data && message.data.location

        const { lat, lng } = isLocation || {}

        const locationLink = isLocation ? `https://www.google.com/maps?q=${lat},${lng}` : ''

        const file =
          message.file ||
          (isDownloadable
            ? await driverService
                .syncMessageFileById(message.serviceId, message.id)
                .then(async () => {
                  const updatedMessage = await messageResource.findById(message.id, {
                    include: ['file'],
                  })

                  return updatedMessage.file
                })
                .catch((error) => {
                  reportError(error)
                  return null
                })
            : null)

        if (isDownloadable && !file) return null

        return {
          type: message.type,
          serviceId: message.serviceId,
          quotedMessageId: message.quotedMessageId,
          userId: res.locals.user.id,
          origin: 'forward',
          accountId: message.accountId,
          dontOpenTicket: false,
          text: (await messageResource.decryptMessageText(message)) || locationLink || ' ',
          ...(file && {
            file: {
              base64: await fileResource.getBase64(file),
              mimetype: file.mimetype,
              name: file.name,
            },
          }),
          ...(message?.data?.vcard && {
            vcard: message.data.vcard,
          }),
        }
      })
    ).filter(Boolean)

    return queuedAsyncMap(
      contacts,
      async (contact) => ({
        id: contact.id,
        messagesIds: await queuedAsyncMap(
          messagesDatas,
          async (messagesData) => {
            const data = {
              ...messagesData,
              contactId: contact.id,
            }

            if (data.type === 'vcard') {
              const vcards = Array.isArray(data.vcard) ? data.vcard : [{ vcard: data.vcard }]

              const contactsIdsVcard = await contactResource.getOrInsertContactByVcard(
                data.serviceId,
                data.accountId,
                vcards,
              )

              if (contactsIdsVcard.length === 0) {
                throw new Error('No contacts found')
              }

              const req = {
                body: {
                  contactsIds: contactsIdsVcard,
                  serviceId: messagesData.serviceId,
                  contactId: contact.id,
                },
              }
              const sentVcard = await this.sendVcards(req, res)
              return sentVcard.id
            }
            const sentMessage = await this.resource.send(data)

            return sentMessage.id
          },
          1,
        ),
      }),
      1,
    )
  }

  async revoke(req: Request, res: Response) {
    const { id } = req.params

    try {
      const message = await this.resource.findById(id)

      const revokedMessage = await this.resource.revoke(message)

      logger.logEvent('msg_deleted_success', { messageId: message.id })

      return revokedMessage
    } catch (error) {
      logger.logEvent('msg_deleted_failed', { messageId: id }, error, 'error')
      throw error
    }
  }

  async summarize(req: Request) {
    const { id } = req.params
    return this.resource.summarize(id)
  }

  async sendReaction(req: Request, res: Response) {
    const { id } = req.params
    const { accountId } = res.locals
    const { reactionEmojiRendered, reactionCode } = req.body

    if (!id || typeof id !== 'string') {
      throw new BadRequestHttpError('Message id is required and must be a string.')
    }

    if (!reactionEmojiRendered || typeof reactionEmojiRendered !== 'string') {
      throw new BadRequestHttpError('Reaction emoji rendered is required and must be a string.')
    }

    const result = await this.resource.sendReactionByMessageId(id, accountId, reactionEmojiRendered, reactionCode)

    return result
  }

  async revokeReaction(req: Request, res: Response) {
    const { id } = req.params
    const { accountId } = res.locals

    if (!id || typeof id !== 'string') {
      throw new BadRequestHttpError('Message id is required and must be a string.')
    }

    const result = await this.resource.revokeReactionByMessageId(id, accountId)

    return result
  }

  async syncFile(req: Request, res: Response) {
    const { id } = req.params

    const message = await this.resource.findById(id)

    return this.resource.syncFile(message)
  }

  destroyMany(req: Request, res: Response) {
    const { accountId } = res.locals
    const { where } = req.body
    where.accountId = accountId

    return this.resource.bulkDestroy({ where }).then(Boolean)
  }

  async magicText(req: Request, res: Response) {
    const { message, tone, serviceId } = req.body
    if (!message || typeof message !== 'string') {
      throw new BadRequestHttpError('Message is required and must be a string.')
    }

    if (!tone || typeof tone !== 'string') {
      throw new BadRequestHttpError('Tone is required and must be a string.')
    }

    return this.resource.magicText(message, tone, res.locals.account, serviceId)
  }

  async outOfRange(req: Request) {
    const { id } = req.params
    const { limit } = req.query

    return this.resource.outOfRange(id, String(limit) ?? String(500))
  }

  async sendVcards(req: Request, res: Response) {
    const { serviceId, contactId, contactsIds } = req.body

    if (!serviceId || typeof serviceId !== 'string') {
      throw new BadRequestHttpError('ServiceId is required and must be a string.')
    }

    if (!contactId || typeof contactId !== 'string') {
      throw new BadRequestHttpError('ContactId is required and must be a string.')
    }

    if (!contactsIds || !Array.isArray(contactsIds)) {
      throw new BadRequestHttpError('contactsIds is required and must be an array.')
    }

    const contact = await contactResource.findById(contactId)

    if (!contact) {
      throw new BadRequestHttpError('ContactId not found.')
    }

    const contactsToSend = await contactResource.findMany({
      where: {
        id: { $in: contactsIds },
      },
    })

    if (contactsToSend.length <= 0) {
      throw new BadRequestHttpError('ContactsIds not found.')
    }
    const whatsappService = await getServiceRpc('whatsapp-remote')
    const contactsSent = await whatsappService.wplvSendVCardContactMessage(
      serviceId,
      contact.idFromService,
      contactsToSend.map((contactToSend) => contactToSend.idFromService),
    )

    if (!contactsSent) {
      throw new BadRequestHttpError('Error sending contacts.')
    }

    const contactsSentTreated = await this.treatVcardMessage(contactsSent, contactsToSend)
    const data = await this.createVCardMessageData(contact, contactsSentTreated, serviceId, res.locals)
    const messageSent = await messageResource.create(data)
    return messageSent
  }

  async treatVcardMessage(contactsSent, contactsToSend) {
    const vcards = contactsSent?.type === 'vcard' ? [{ vcard: contactsSent?.vCardString }] : contactsSent?.vcardList
    const vcardsTreated = vcards.map((vcard) => {
      const match = vcard.vcard.match(/waid=(\d+)/)
      const waid = match ? match[1] : null
      const contact = contactsToSend.find((item) => item?.data?.number === waid)
      const vcardTreated = vcard.vcard.replace(/^FN:.*/m, `FN:${contact?.alternativeName || contact?.name}`)
      return {
        vcard: vcardTreated,
      }
    })

    if (vcardsTreated.length === 1) {
      contactsSent.vCardString = vcardsTreated
    }

    if (vcardsTreated.length > 1) {
      contactsSent.vcardList = vcardsTreated
    }

    return contactsSent
  }

  async createVCardMessageData(contact, contactsSent, serviceId, locals) {
    return {
      contactId: contact.id,
      idFromService: contactsSent.id,
      type: 'vcard',
      accountId: locals.accountId,
      serviceId,
      data: {
        vcard: contactsSent?.type === 'vcard' ? contactsSent?.vCardString : contactsSent?.vcardList,
        isNew: false,
        isFirst: !contactsSent.lastMessageId,
        ack: contactsSent.ack,
      },
      origin: 'user',
      isFromMe: true,
      userId: locals.user.id,
      sent: true,
      fromId: contact.id,
      timestamp: new Date(),
      isTranscribing: false,
    }
  }

  async loadAround(req: Request, res: Response) {
    const { id } = req.params
    const limit = parseInt(req.query.limit as string, 10) || 10
    const accountId = res.locals.user.accountId

    const query = this.filterQuery(req.query, this.includeWhitelist)

    const targetMessage = await messageResource.findOne({
      ...query,
      where: { ...query.where, id, accountId },
    })
    if (!targetMessage) throw new NotFoundHttpError('Message not found')

    const baseQuery = {
      ...query,
      where: {
        ...query.where,
        contactId: targetMessage.contactId,
        accountId,
        id: { $ne: id },
      },
    }

    const beforeQuery = {
      ...baseQuery,
      where: {
        ...baseQuery.where,
        timestamp: { $lte: targetMessage.timestamp },
      },
      order: [['timestamp', 'DESC']],
      limit,
    }

    const afterQuery = {
      ...baseQuery,
      where: {
        ...baseQuery.where,
        timestamp: { $gte: targetMessage.timestamp },
      },
      order: [['timestamp', 'ASC']],
      limit,
    }

    const [messagesBefore, messagesAfter] = await Promise.all([
      messageResource.findMany(beforeQuery),
      messageResource.findMany(afterQuery),
    ])

    const messages = uniqBy([...messagesBefore.reverse(), targetMessage, ...messagesAfter], 'id')

    const context = {
      account: res.locals.account,
      user: res.locals.user,
      impersonate: res.locals.impersonate,
    }

    return transformMany(this.transformer, context)(messages)
  }

  async checkSummaryMessage(req: Request) {
    const { ticketId } = req.body

    if (!ticketId) {
      throw new BadRequestHttpError('TicketId is required.')
    }

    const hasSummary = await this.resource.hasSummaryMessage(ticketId)

    return { hasSummary }
  }
}
