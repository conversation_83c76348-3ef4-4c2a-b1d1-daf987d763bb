import { Router } from 'express'
import { HeadObjectOutput } from 'aws-sdk/clients/s3'
import fileResource from '../../../core/resources/fileResource'
import { getStorage } from '../../../core/services/storage'
import stringToBoolean from '../../../core/utils/stringToBoolean'
import { convertAudioStreamToMp3, mustConvertAudio, mimetypeIsAudio } from '../../../core/utils/convertAudio'
import fileSignature from '../../../core/services/fileSignature'
import userSetter from '../../../core/middlewares/userSetter'
import ipRestriction from '../../../core/middlewares/ipRestriction'
import validate from '../../../core/middlewares/validate'
import { string } from '../../../core/utils/validator/rules'
import getRandomBucket from '../../../core/utils/getRandomBucket'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import accountContext from '../../../core/middlewares/accountContext'
import ValidationError from '../../../core/utils/error/ValidationError'
import authenticate from '../../../core/middlewares/authenticate'

export const validateCreate = validate({
  body: {
    serviceId: [string],
  },
})

const auth = [authenticate, userSetter(), ipRestriction()]

const router = Router()

router.get('/:accountId/:filename', async (req, res) => {
  const { accountId } = req.params
  const query = req.query
  const [id] = req.params.filename.split('.')

  const sig = <string>query[fileSignature.sigParameter]

  if (sig) {
    const valid = fileSignature.verifyUrlSignature({ accountId, id }, sig)
    if (!valid) {
      res.status(400).send('Invalid or expired signature query parameter.')
      return
    }
  }

  const file = await fileResource.findOne({
    where: { id, accountId },
    include: ['account'],
  })

  if (file && !sig) {
    res.status(400).send('Missing signature query parameter.')
    return
  }

  if (!file || !(await fileResource.fileExistOnDisk(file))) {
    res.sendStatus(404)
    return
  }

  const range = req.headers.range

  const asMp3 = stringToBoolean(req.query.mp3)
  const shouldConvertToMp3 = asMp3 && mimetypeIsAudio(file.mimetype)

  const mimetype = shouldConvertToMp3 ? 'audio/mp3' : file.mimetype
  const fileName = shouldConvertToMp3 ? `${file.id}.mp3` : file.publicFilename

  res.setHeader('Content-Type', mimetype)
  res.setHeader('Cache-Control', 'public, max-age=********')
  res.setHeader('Content-Disposition', `inline; filename=${encodeURI(fileName)}`)

  const data = <HeadObjectOutput>await fileResource.getMetadata(file)

  if (range && !asMp3) {
    res.status(206)
    const bytes = range?.replace(/bytes=/, '').split('-')
    const start = parseInt(bytes?.[0])

    const total = data.ContentLength
    const end = bytes[1] ? parseInt(bytes[1]) : total - 1
    const chunkSize = end - start + 1

    res.setHeader('Content-Length', chunkSize)
    res.setHeader('Accept-Ranges', 'bytes')
    res.setHeader('Content-Range', `bytes ${start}-${end}/${total}`)
  } else if (data.ContentLength && !asMp3) {
    res.setHeader('Content-Length', data.ContentLength)
  }

  const options = {
    ...(!asMp3 && range && { Range: range }),
    ResponseContentType: mimetype,
  }
  const readStream = await fileResource.getReadStream(file, options)

  const outputStream = shouldConvertToMp3 ? convertAudioStreamToMp3(readStream) : readStream

  return outputStream.pipe(res)
})

router.post(
  '/create-to-upload',
  auth,
  accountContext(),
  validateCreate,
  promiseHandler(async (req, res) => {
    const { base64, mimetype, name, attachedType = 'message.file', serviceId, isPtt = true } = req.body

    const [type, extension] = mimetype.split('/')

    const newName = (type === 'audio' && !name ? `recording.${extension}` : name).replace(/,/g, ' ')

    if (await mustConvertAudio(mimetype, isPtt, serviceId)) {
      if (!req.body.base64) throw new ValidationError(`Base64 is required to convert audio`)
      const createdFile = await fileResource.create({
        base64,
        name: newName,
        mimetype,
        attachedType,
        accountId: res.locals.accountId,
        serviceId,
        isPtt,
      })

      return {
        id: createdFile.id,
        name: createdFile.name,
        mimetype: createdFile.mimetype,
      }
    }

    const driver = await getRandomBucket()

    const createdFile = await fileResource.getRepository().create({
      name: newName,
      mimetype,
      extension,
      attachedType,
      accountId: res.locals.accountId,
      storage: driver,
    })

    try {
      const s3Params = {
        Key: createdFile.filepath,
        Expires: 30 * 60, // 30m
        ContentType: createdFile.mimetype,
      }

      const storage = await getStorage(driver)

      const urlToUpload = await storage.getSignedUrl('putObject', s3Params)

      return {
        urlToUpload,
        id: createdFile.id,
        name: createdFile.name,
        mimetype: createdFile.mimetype,
      }
    } catch (error) {}
  }),
)

router.post(
  '/',
  auth,
  accountContext(),
  promiseHandler(async (req) => fileResource.getRepository().create(req.body)),
)

router.post(
  '/duplicate-file',
  auth,
  accountContext(),
  promiseHandler(async (req) => {
    const { fileId, urlToUpload } = req.body
    const file = await fileResource.findById(fileId)
    const fileBase64 = await fileResource.getBuffer(file)
    await fetch(urlToUpload, {
      method: 'PUT',
      body: fileBase64,
      headers: {
        'Content-Type': file?.mimetype,
      },
    })
  }),
)

export default router
