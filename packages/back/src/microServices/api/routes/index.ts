import { Router } from 'express'
import { Container } from 'typedi'
import userSetter from '../../../core/middlewares/userSetter'
import onlyNormalUser from '../../../core/middlewares/onlyNormalUser'
import messagesRoutes from './messages'
import contactsRoutes from './contacts'
import tagsRoutes from './tags'
import knowledgeBaseRoutes from './knowledgeBase'
import knowledgeBaseItemRoutes from './knowledgeBaseItem'
import usersRoutes from './users'
import servicesRoutes from './services'
import rolesRoutes from './roles'
import permissionsRoutes from './permissions'
import profileRoutes from './profile'
import filesRoutes from './files'
import myplanRoutes from './myplan'
import planAiHistory from './planAiHistory'
import superAdminRoutes from './superAdmin'
import clientUserRoutes from './clientUser'
import personalAccessTokensRoutes from './personalAccessTokens'
import webhooksRoutes from './webhooks'
import campaignRoutes from './campaign'
import botsRoutes from './bots'
import botSimulatorsRoutes from './botSimulators'
import botVersionsRoutes from './botVersions'
import blockMessageRulesRoutes from './blockMessageRules'
import departmentRoutes from './departments'
import quickRepliesRoutes from './quickReplies'
import categoriesRoutes from './categories'
import plansRoutes from './plans'
import widgetRoutes from './widget'
import ticketsRoutes from './tickets'
import registerRoutes from './register'
import resetPasswordRoutes from './resetPassword'
import paymentRoutes from './payment'
import dashboardRoutes from './dashboard'
import nowRoutes from './now'
import telegramRoutes from './telegram'
import whatsappBusinessWebhooksRoutes from './whatsappBusinessWebhooks'
import facebookMessengerWebhooksRoutes from './facebookMessengerWebhooks'
import googleBusinessMessageWebhookRoutes from './googleBusinessMessagesWebhooks'
import whatsappServerPodWebhooksRoutes from './whatsappServerPodWebhooks'
import ticketTopicsRoutes from './ticketTopic'
import browserRoutes from './browser'
import organizationsRoutes from './organizations'
import peopleRoutes from './people'
import pipelineRoutes from './pipeline'
import cardRoutes from './card'
import questionRoutes from './question'
import feedbackRoutes from './feedback'
import answersRoutes from './answers'
import absenceRoutes from './absence'
import customFieldsRoutes from './customFields'
import timetableRoutes from './timetable'
import termRoutes from './term'
import activityLogRoutes from './activityLog'
import acceptanceTermsRoutes from './acceptanceTerms'
import authHistoryRoutes from './authHistory'
import webhookSms from './smsWavy'
import webchat from './webchat'
import outlook from './outlook'
import creditMovements from './creditMovements'
import generatePdfRoutes from './generatePdf'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import config from '../../../core/config'
import onlyClientUser from '../../../core/middlewares/onlyClientUser'
import integrationsRoutes from './integrations'
import groupsRoutes from './groups'
import whatsappBusinessTemplatesRoutes from './whatsappBusinessTemplates'
import interactiveMessagesRoutes from './interactiveMessages'
import contactBlockListsRoutes from './contactBlockLists'
import contactBlockListsControlsRoutes from './contactBlockListsControls'
import whatsappRoutes from './whatsapp'
import serviceEventsRoutes from './serviceEvents'
import ipRestriction, { getIpFromRequest } from '../../../core/middlewares/ipRestriction'
import timeRestriction from '../../../core/middlewares/timeRestriction'
import validateWebhookRoute from '../../../core/middlewares/validateWebhookRoute'
import authChatInterno from '../../../core/middlewares/authChatInterno'
import scheduleRoutes from './schedule'
import stickersRoutes from './stickers'
import stickerUsersRoutes from './stickerUsers'
import notificationRoutes from './notification'
import distributionRoutes from './distribution'
import servicesController from '../controllers/services/servicesController'
import holidayRoutes from './holiday'
import internalChat from './internalChat'
import serverPodsRoutes from './serverPods'
import apiCanaryProxy from '../../../core/middlewares/apiCanaryProxy'
import Logger from '../../../core/services/logs/Logger'
import oAuth2AddAccount from '../../../core/middlewares/oAuth2AddAccount'
import passwordExpirationDetect from '../../../core/middlewares/passwordExpirationDetect'
import throttleLogin from '../../../core/middlewares/throttleLogin'
import oAuth2AccountBlock from '../../../core/middlewares/oAuth2AccountBlock'
import otpAuthentication from '../../../core/middlewares/otpAuthentication'
import authHistoryRegistry from '../../../core/middlewares/authHistoryRegistry'
import transcriptsRoutes from './transcripts'
import oAuth2Authentication from '../../../core/middlewares/oAuth2Authentication'
import authenticate from '../../../core/middlewares/authenticate'
import clientFeedbackRoutes from './clientFeedback'

const logger = Container.get(Logger)

const router = Router()

const basicAuth = [authenticate, userSetter(), ipRestriction(), timeRestriction(), apiCanaryProxy()]
const auth = [...basicAuth, passwordExpirationDetect()]
const normalAuth = [...auth, onlyNormalUser()]

router.get('/', (req, res) => {
  res.send('MandeUmZap API v1')
})

router.get(
  '/versions',
  promiseHandler(async (req, res) => {
    return {
      api: config('version') || '',
    }
  }),
)

router.get('/services/gmail/oauth2callback', servicesController.gmailCallback)

router.get(
  '/my-ip',
  promiseHandler(async (req, res) => getIpFromRequest(req)),
)

router.post('/webhook', (req, res) => {
  logger.log(`Post webhook: %o`, 'info', [req.body])
  res.json(req.body)
})

router.get('/outlook-callback', servicesController.hotmailCallback)
router.get('/360dialog-callback/:id', servicesController.hub360DialogCallback)

router.post(
  '/oauth/token',
  throttleLogin.handleThrottle,
  oAuth2AddAccount,
  oAuth2Authentication.generateToken,
  oAuth2AccountBlock,
  otpAuthentication,
  authHistoryRegistry,
  oAuth2Authentication.sendSuccess,
  throttleLogin.throttleErrorHandler,
)

router.get(
  '/configs',
  ...normalAuth,
  promiseHandler(async (req, res) => {
    return {
      useMockDriver: config('useMockDriver'),
    }
  }),
)

router.use('/register', registerRoutes)
router.use('/reset-password', resetPasswordRoutes)
router.use('/payment', paymentRoutes)
router.use('/files', filesRoutes)
router.use('/widget', widgetRoutes)
router.use('/generate-pdf', generatePdfRoutes)

router.use('/me', ...basicAuth, profileRoutes)
router.use('/me/tokens', ...normalAuth, personalAccessTokensRoutes)
router.use('/me/webhooks', ...normalAuth, webhooksRoutes)

router.use('/terms', termRoutes)

router.use('/myplan', ...normalAuth, myplanRoutes)
router.use('/plan-ai-history', ...normalAuth, planAiHistory)
router.use('/admin', ...normalAuth, superAdminRoutes)
router.use('/client', ...auth, onlyClientUser(), clientUserRoutes)

router.use('/users', ...normalAuth, usersRoutes)
router.use('/contacts', ...normalAuth, contactsRoutes)
router.use('/tags', ...normalAuth, tagsRoutes)
router.use('/knowledge-base', ...normalAuth, knowledgeBaseRoutes)
router.use('/knowledge-base-item', ...normalAuth, knowledgeBaseItemRoutes)
router.use('/messages', ...normalAuth, messagesRoutes)
router.use('/services', ...normalAuth, servicesRoutes)
router.use('/roles', ...normalAuth, rolesRoutes)
router.use('/permissions', ...normalAuth, permissionsRoutes)
router.use('/campaigns', ...normalAuth, campaignRoutes)
router.use('/acceptance-terms', ...normalAuth, acceptanceTermsRoutes)
router.use('/bots', ...normalAuth, botsRoutes)
router.use('/bot-simulators', ...normalAuth, botSimulatorsRoutes)
router.use('/bot-versions', ...normalAuth, botVersionsRoutes)
router.use('/block-message-rules', ...normalAuth, blockMessageRulesRoutes)
router.use('/departments', ...normalAuth, departmentRoutes)
router.use('/quick-replies', ...normalAuth, quickRepliesRoutes)
router.use('/categories', ...normalAuth, categoriesRoutes)
router.use('/plans', ...normalAuth, plansRoutes)
router.use('/tickets', ...normalAuth, ticketsRoutes)
router.use('/dashboard', ...normalAuth, dashboardRoutes)
router.use('/holiday', ...normalAuth, holidayRoutes)
router.use('/now', ...normalAuth, nowRoutes)
router.use('/ticket-topics', ...normalAuth, ticketTopicsRoutes)
router.use('/people', ...normalAuth, peopleRoutes)
router.use('/pipelines', ...normalAuth, pipelineRoutes)
router.use('/cards', ...normalAuth, cardRoutes)
router.use('/organizations', ...normalAuth, organizationsRoutes)
router.use('/credit-movements', ...normalAuth, creditMovements)
router.use('/integrations', ...normalAuth, integrationsRoutes)
router.use('/distribution', ...normalAuth, distributionRoutes)
router.use('/question', ...normalAuth, questionRoutes) // Mantendo o singular para ser retrocompatível
router.use('/questions', ...normalAuth, questionRoutes)
router.use('/feedback', ...normalAuth, feedbackRoutes)
router.use('/answers', ...normalAuth, answersRoutes)
router.use('/custom-fields', ...normalAuth, customFieldsRoutes)
router.use('/schedule', ...normalAuth, scheduleRoutes)
router.use('/stickers', ...normalAuth, stickersRoutes)
router.use('/sticker-users', ...normalAuth, stickerUsersRoutes)
router.use('/notifications', ...normalAuth, notificationRoutes)
router.use('/timetable', ...normalAuth, timetableRoutes)
router.use('/log', ...normalAuth, activityLogRoutes)
router.use('/service-events', ...normalAuth, serviceEventsRoutes)
router.use('/browsers', ...normalAuth, browserRoutes)
// @deprecated
router.use('/whatsapp/groups', ...normalAuth, groupsRoutes)
router.use('/whatsapp', ...normalAuth, whatsappRoutes)
router.use('/whatsapp-business-webhook', whatsappBusinessWebhooksRoutes)
router.use('/messenger-webhook', facebookMessengerWebhooksRoutes)
router.use('/google-business-webhook', validateWebhookRoute, googleBusinessMessageWebhookRoutes)
router.use('/whatsapp-server-pod-webhook', whatsappServerPodWebhooksRoutes)
router.use('/telegram/webhook', telegramRoutes)
router.use('/sms-wavy-webhook', webhookSms)
router.use('/webchat-webhook', webchat)
router.use('/whatsapp-business-hsm', ...normalAuth, whatsappBusinessTemplatesRoutes) // manter para retrocontabilidade
router.use('/whatsapp-business-templates', ...normalAuth, whatsappBusinessTemplatesRoutes)
router.use('/interactive-messages', ...normalAuth, interactiveMessagesRoutes)
router.use('/internalchat', authChatInterno, internalChat)
router.use('/email-webhook', outlook)
router.use('/server-pods', serverPodsRoutes)
router.use('/absence', ...normalAuth, absenceRoutes)
router.use('/contact-block-lists', ...normalAuth, contactBlockListsRoutes)
router.use('/contact-block-lists-controls', ...normalAuth, contactBlockListsControlsRoutes)
router.use('/auth-history', ...normalAuth, authHistoryRoutes)
router.use('/transcripts', ...normalAuth, transcriptsRoutes)
router.use('/client-feedback', clientFeedbackRoutes)

export default router
