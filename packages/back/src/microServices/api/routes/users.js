import { Router } from 'express'
import jwt from 'jsonwebtoken'
import { omit } from 'lodash'
import Container from 'typedi'
import pick from 'lodash/pick'
import { subYears } from 'date-fns'
import validate, { validateWithConditionalRules } from '../../../core/middlewares/validate'
import userResource from '../../../core/resources/userResource'
import contactResource from '../../../core/resources/contactResource'
import webhookResource from '../../../core/resources/webhookResource'
import validateUserArchive from '../../../core/middlewares/validateUserArchive'
import { fromReqRes } from '../../../core/utils/resource/transformerHelpers'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import accountContext from '../../../core/middlewares/accountContext'
import resourceController from '../../../core/utils/routing/resourceController'
import transformer from '../../../core/transformers/userTransformer'
import accountResource from '../../../core/resources/accountResource'
import roleResource from '../../../core/resources/roleResource'
import departmentResource from '../../../core/resources/departmentResource'
import timetableResource from '../../../core/resources/timetableResource'
import { loadModelsWithResources } from '../../../core/utils/resource/relationsLoader'
import HttpError from '../../../core/utils/error/HttpError'
import hasPermission from '../../../core/middlewares/hasPermission'
import { sendCreatePasswordEmail, sendResetPasswordEmail } from '../../../core/services/email'

import {
  required,
  string,
  hasLengthLesserThanOrEqual,
  hasLengthGreaterThanOrEqual,
  boolean,
  hasUpperCaseCharacter,
  hasLowerCaseCharacter,
  hasDigitCharacter,
  hasSpecialCharacter,
  notContainKnownPatternsInZxcvbnResult,
} from '../../../core/utils/validator/rules'
import unique from '../../../core/utils/validator/unique'
import { currentAccountOwnsOrganizations } from '../../../core/utils/validator/accountOwns'
import ContactService from '../../../core/services/contacts/ContactService'
import { removeAccessToken } from '../../../core/services/auth/authService'
import hasher from '../../../core/utils/crypt/hasher'
import authHistoryResource from '../../../core/resources/authHistoryResource'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import ValidationError from '../../../core/utils/error/ValidationError'
import config from '../../../core/config'
import { getIpFromRequest, getUserAgentFromRequest } from '../../../core/middlewares/ipRestriction'
import userTransformer from '../../../core/transformers/userTransformer'
import redisTaskQueue from '../../../core/services/queue/redisTaskQueue'

const conditionedRules = (request, rules) => {
  const ignoreStrengthPassword = (request.headers['ignore-strength-password'] || 'false') === 'true'

  if (config('allowIgnoreStrengthPassword') && ignoreStrengthPassword) return rules

  const strengthPasswordRules = [
    hasLengthGreaterThanOrEqual(8),
    hasUpperCaseCharacter(),
    hasLowerCaseCharacter(),
    hasDigitCharacter(),
    hasSpecialCharacter(),
    notContainKnownPatternsInZxcvbnResult(),
  ]
  return {
    ...rules,
    body: {
      ...rules?.body,
      password: [...(rules?.body?.password || []), ...strengthPasswordRules],
    },
  }
}

export const validateCreate = validateWithConditionalRules(conditionedRules, {
  body: {
    name: [required, string, hasLengthLesserThanOrEqual(255)],
    email: [required, string, hasLengthLesserThanOrEqual(255), unique(userResource, 'email')],
    password: [required, string, hasLengthLesserThanOrEqual(255)],
    isSuperAdmin: [boolean],
    isClientUser: [boolean],
    organizationIds: [currentAccountOwnsOrganizations],
  },
})

export const validateUpdate = validateWithConditionalRules(conditionedRules, {
  body: {
    name: [string, hasLengthLesserThanOrEqual(255)],
    email: [string, hasLengthLesserThanOrEqual(255), unique(userResource, 'email', true)],
    password: [string, hasLengthLesserThanOrEqual(255)],
    isSuperAdmin: [boolean],
    isClientUser: [boolean],
    organizationIds: [currentAccountOwnsOrganizations],
  },
})

export const validatePin = validate({
  body: {
    contactId: [required, string, hasLengthLesserThanOrEqual(255)],
  },
})

const checkForPlanLimit = async (req, res, next) => {
  const { account } = res.locals

  const userCount = await userResource.getRepository().count({
    where: {
      accountId: account.id,
      archivedAt: null,
    },
  })

  const planUsers = parseInt(account.plan.users, 10) || 3

  if (userCount >= planUsers) {
    next(new HttpError(402, 'Plan limit reached.'))
    return
  }
  next()
}

const loadRelations = (data) =>
  loadModelsWithResources(data, {
    account: accountResource,
    roles: roleResource,
    departments: departmentResource,
    timetable: timetableResource,
  })

const queryIncludeWhitelist = [
  'roles',
  'permissions',
  'account',
  'departments',
  'departments1',
  'organizations',
  'timetable',
  'tickets',
]

const baseController = resourceController(userResource, transformer, queryIncludeWhitelist)

const contactService = Container.get(ContactService)

const controller = {
  ...baseController,
  create: promiseHandler(async (req, res) => {
    const { userPasswordCreationMethod, changeUserPasswordOnFirstAccess } = res.locals.user.account.settings

    if (!req.body.password && userPasswordCreationMethod !== 'link')
      throw new HttpError(400, 'Parameter password is required.')

    const data = {
      ...(await loadRelations(omit(req.body, ['isSuperAdmin']))),
      active: true,
    }
    const query = pick(req.query, ['include'])

    const createdUser = await userResource.create(data, query)

    if (userPasswordCreationMethod === 'link' || changeUserPasswordOnFirstAccess) {
      const secret = config('resetPasswordJwtSecret')

      const token = jwt.sign({ id: createdUser.id }, secret, { expiresIn: '24h' })

      await userResource.update(
        createdUser,
        {
          resetPasswordToken: token,
          data: {
            sentResetPasswordAt: new Date(),
          },
        },
        {
          mergeJson: ['data'],
        },
      )

      await sendCreatePasswordEmail({
        to: createdUser.email,
        token,
        userName: createdUser.name,
      })
    }

    return transformer(createdUser)
  }),
  update: promiseHandler(async (req, res) => {
    const { id } = req.params

    const data = await loadRelations(omit(req.body, ['isSuperAdmin']))

    const changedPassword = !!data.password

    let user = null
    if (changedPassword) {
      user = await userResource.findById(id, {
        include: [
          {
            model: 'authHistory',
            where: {
              event: 'password_change',
              createdAt: {
                $gte: subYears(new Date(), 1),
              },
            },
            limit: 6,
            order: [['createdAt', 'desc']],
            required: false,
          },
        ],
      })

      await queuedAsyncMap(
        user.authHistory,
        async (history) => {
          if (await hasher.compare(data.password, history.passwordHash)) {
            throw new ValidationError(null, 'Recently used password')
          }
        },
        1,
        false,
        true,
      )
    }

    const query = pick(req.query, ['include'])

    user = user || (await userResource.findById(id))

    const updatedUser = await userResource.update(user, data, query)

    if (changedPassword) {
      const {
        account: {
          settings: { userPasswordCreationMethod },
        },
      } = res.locals.user

      if (userPasswordCreationMethod === 'link') {
        const secret = config('resetPasswordJwtSecret')

        const token = jwt.sign({ id: updatedUser.id, isResetPassword: true }, secret, { expiresIn: '24h' })

        await userResource.update(
          updatedUser,
          {
            resetPasswordToken: token,
            data: {
              sentResetPasswordAt: new Date(),
            },
          },
          {
            mergeJson: ['data'],
          },
        )

        await sendResetPasswordEmail({
          to: updatedUser.email,
          token: encodeURIComponent(token),
          userName: updatedUser.name,
          accountId: updatedUser.accountId,
        })
      }

      await authHistoryResource.create({
        userId: updatedUser.id,
        accountId: updatedUser.accountId,
        event: 'password_change',
        accessTokenId: res.locals.oauth.token.accessTokenId,
        passwordHash: updatedUser.password,
        branch: config('branch'),
        originIP: getIpFromRequest(req),
        originUA: getUserAgentFromRequest(req),
      })

      await removeAccessToken(id)
    }

    return transformer(updatedUser)
  }),

  bulkUpdate: promiseHandler(async (req, res) => {
    const { ids, timetableId, accountId } = req.body
    return userResource
      .bulkUpdate(
        {
          timetableId: timetableId || null,
        },
        {
          where: {
            accountId,
            id: {
              $in: ids,
            },
          },
        },
      )
      .then(fromReqRes(req, res).transform(transformer))
  }),

  archive: promiseHandler(async (req, res) => {
    const queue = redisTaskQueue(`user:archive:${req.params.id}`)

    return queue.run(async () =>
      userResource
        .archive({ userId: req.params.id, archive: req.body.archive })
        .then(fromReqRes(req, res).transform(transformer)),
    )
  }),

  destroy: promiseHandler(async (req, res) => {
    const { id: userId } = req.params
    const { account } = res.locals

    return userResource.getRepository().transaction(async (transaction) => {
      await contactService.removeDefaultUser(account.id, userId, transaction)

      await webhookResource.bulkDestroy({
        where: {
          userId,
          accountId: account.id,
        },
        transaction,
      })

      await userResource.updateById(
        userId,
        {
          archivedAt: new Date(),
          accountId: account.id,
          status: 'offline',
          offlineAt: new Date(),
          clientsStatus: { web: 'offline', app: 'offline' },
        },
        { transaction },
      )

      return userResource.destroyById(userId, { transaction }).then(Boolean)
    })
  }),

  togglePinContacts: promiseHandler(async (req, res) => {
    const {
      accountId,
      user: { id: userId },
    } = res.locals
    const { contactId } = req.body

    const user = await userResource.findById(userId, {
      attributes: ['id', 'name'],
      where: {
        accountId,
      },
      include: [
        {
          model: 'contacts',
          paranoid: false,
        },
      ],
    })

    const pinnedContacts = user?.contacts

    const validContacts = await contactResource.findMany({
      where: {
        accountId,
        id: {
          $in: [...(pinnedContacts || []).map((pinnedContact) => pinnedContact.id), contactId],
        },
      },
      attributes: ['id'],
      raw: true,
    })

    const contactsToRemove = (pinnedContacts || []).filter((pc) => !validContacts.some((vc) => vc.id === pc.id))

    if (contactsToRemove) contactsToRemove.map((contact) => user.removeContacts(contact.id))

    if (!validContacts.some((contact) => contact.id === contactId)) throw new HttpError(400, 'Contact not found')

    const contactIsPinned = (pinnedContacts || []).some((contact) => contact.id === contactId)

    if (validContacts.length > 3) throw new HttpError(400, 'Limit of pinned contacts reached.')

    if (!contactIsPinned) return await user.addContacts(contactId)

    return await user.removeContacts(contactId)
  }),

  contactsPinned: promiseHandler(async (req, res) => {
    const {
      accountId,
      user: { id: userId },
    } = res.locals

    const user = await userResource.findById(userId, {
      where: {
        accountId,
      },
    })

    return user.getContacts()
  }),
  disableOTPAuth: promiseHandler(async (req, res) => {
    const { id: userId } = req.params
    const data = {
      otpAuthActive: false,
    }

    return userResource.updateById(userId, data).then(fromReqRes(req, res).transform(userTransformer))
  }),
  enableAccess: promiseHandler(async (req, res) => {
    if (!res.locals.user.roles.find((role) => role.isAdmin)) {
      return res.send(401)
    }
    const { id: userId } = req.params
    return userResource.enableAccess(userId)
  }),
  gererateTokenInternalChat: promiseHandler(async (req, res) => {
    const users = await userResource.findMany({
      attributes: ['id', 'email', 'accountId', 'password'],
      include: ['roles'],
    })

    await queuedAsyncMap(users, async (user) => {
      await userResource.generateTokenFromInternalChat({
        user,
        roles: user.roles,
      })
    })
  }),
}

const router = Router()

router.use(accountContext())

// contacts pinned
router.get('/pin', hasPermission('contacts.pin'), controller.contactsPinned)
router.patch('/pin', hasPermission('contacts.pin'), validatePin, controller.togglePinContacts)

router.get('/', controller.index)
router.get('/:id', controller.show)
router.post('/', checkForPlanLimit, validateCreate, controller.create)
router.put('/bulk-update', controller.bulkUpdate)
router.post('/:id/archive', hasPermission('users.archive'), validateUserArchive, controller.archive)
router.put('/:id', validateUpdate, controller.update)
router.post('/:id/disable-otp-auth', hasPermission(['users.update', 'myAccount.update']), controller.disableOTPAuth)
router.post('/:id/enable-access', controller.enableAccess)
router.post('/generate-token-internalchat', controller.gererateTokenInternalChat)
router.delete('/:id', controller.destroy)

export default router
