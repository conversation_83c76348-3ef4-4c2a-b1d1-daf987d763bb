/* eslint-disable prefer-destructuring */
import { Router } from 'express'
import jwt from 'jsonwebtoken'
import { promise<PERSON>and<PERSON> } from '../../../core/utils/routing/resourceRouter'
import { sendResetPasswordEmail, sendCreatePasswordEmail } from '../../../core/services/email'
import { removeAccessToken } from '../../../core/services/auth/authService'
import userResource from '../../../core/resources/userResource'
import HttpError from '../../../core/utils/error/HttpError'
import { validateWithConditionalRules } from '../../../core/middlewares/validate'
import { required, string, hasLengthLesserThanOrEqual } from '../../../core/utils/validator/rules'
import accountResource from '../../../core/resources/accountResource'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import hasher from '../../../core/utils/crypt/hasher'
import ValidationError from '../../../core/utils/error/ValidationError'
import authHistoryResource from '../../../core/resources/authHistoryResource'
import { getIpFromRequest, getUserAgentFromRequest } from '../../../core/middlewares/ipRestriction'
import config from '../../../core/config'
import { subYears } from 'date-fns'
import { conditionedRules } from './profile'
import reportError from '../../../core/services/logs/reportError'

export const validateNewPassword = validateWithConditionalRules(conditionedRules, {
  body: {
    password: [required, string, hasLengthLesserThanOrEqual(255)],
    token: [required, string],
  },
})

const getAccountId = async (accountAlias: string) => {
  if (!accountAlias) return null

  const account = await accountResource.findOne({
    attributes: ['id'],
    where: {
      alias: { $eq: accountAlias },
    },
  })

  return account?.id || null
}

const controller = {
  request: promiseHandler(async (req) => {
    try {
      const { email, account: accountAlias, creating = false } = req.body

      const accountId = await getAccountId(accountAlias)

      const user = await userResource.findOne({
        where: {
          email: { $eq: email },
          // not a requirement because of standalone envs
          ...(!!accountId && {
            accountId: { $eq: accountId },
          }),
        },
        include: [
          {
            model: 'account',
            attributes: ['id', 'isActive'],
          },
        ],
      })

      // if user was not found just reply success
      if (!user) return true

      if (!user.account.isActive) throw new HttpError(403, 'ACCOUNT_INACTIVE')

      const secret = config('resetPasswordJwtSecret')

      const token = jwt.sign({ id: user.id }, secret, { expiresIn: '24h' })

      const updatedUser = await userResource.updateById(
        user.id,
        {
          resetPasswordToken: token,
          data: { sentResetPasswordAt: new Date() },
        },
        { mergeJson: ['data'] },
      )

      if (!creating) {
        await sendResetPasswordEmail({
          to: updatedUser.email,
          token,
          userName: updatedUser.name,
        }).catch((error) => {
          throw new Error('Cannot send email to reset password', { cause: error })
        })
      } else {
        await sendCreatePasswordEmail({
          to: updatedUser.email,
          token,
          userName: updatedUser.name,
        }).catch((error) => {
          throw new Error('Cannot send email to create password', { cause: error })
        })
      }
    } catch (error) {
      reportError(error, { req })
    }
    return true
  }),

  save: promiseHandler(async (req) => {
    const { password, token } = req.body

    const secret = config('resetPasswordJwtSecret')

    try {
      jwt.verify(token, secret)
    } catch (e) {
      if (e.message === 'jwt expired') {
        throw new HttpError(400, 'Expired token.')
      }
      throw new HttpError(400, 'Invalid token')
    }

    const query = {
      where: {
        resetPasswordToken: { $eq: token },
      },
      include: [
        {
          model: 'account',
          attributes: ['id', 'settings'],
        },
        {
          model: 'authHistory',
          where: {
            event: 'password_change',
            createdAt: {
              $gte: subYears(new Date(), 1),
            },
          },
          limit: 6,
          order: [['createdAt', 'desc']],
          required: false,
        },
      ],
    }

    const user = await userResource.findOne(query)

    if (!user) throw new HttpError(400, 'Invalid token.')

    await queuedAsyncMap(
      user.authHistory,
      async (history) => {
        const isSamePassword = await hasher.compare(password, history.passwordHash)
        if (isSamePassword) {
          throw new ValidationError(null, 'Recently used password')
        }
      },
      1,
    )

    const newPasswordExpiresAt = await accountResource.generateNewExpirationDate(user.account)

    const updatedUser = await userResource.update(user, {
      password,
      resetPasswordToken: null,
      passwordExpiresAt: newPasswordExpiresAt,
    })

    await authHistoryResource.create({
      userId: updatedUser.id,
      accountId: updatedUser.accountId,
      event: 'password_change',
      passwordHash: updatedUser.password,
      branch: config('branch'),
      originIP: getIpFromRequest(req),
      originUA: getUserAgentFromRequest(req),
    })

    await removeAccessToken(user.id)

    return true
  }),
}

const router = Router()

router.post('/request', controller.request)
router.post('/save', validateNewPassword, controller.save)

export default router
