import { Router } from 'express'
import { Op } from 'sequelize'
import { promise<PERSON><PERSON><PERSON> } from '../../../core/utils/routing/resourceRouter'
import userResource from '../../../core/resources/userResource'
import accountResource from '../../../core/resources/accountResource'
import reportError from '../../../core/services/logs/reportError'

const router = Router()

router.post(
  '/active-internalchat',
  promiseHandler(async (req, res) => {
    const { email, accountId, value: isActiveInternalChat } = req.body

    try {
      const searchUser = await userResource.findByEmail(email, accountId)

      await userResource.updateById(searchUser.id, {
        isActiveInternalChat,
      })

      return true
    } catch (error) {
      reportError(error)
    }
  }),
)

router.post(
  '/webhook-users',
  promiseHandler(async (req, res) => {
    const { accountId } = req.body.params

    try {
      const users = await userResource.findMany({
        where: {
          deletedAt: null,
          accountId,
          archivedAt: null,
          isSuperAdmin: {
            [Op.ne]: true,
          },
        },
        include: ['roles'],
      })

      await accountResource.updateById(
        String(accountId),
        {
          settings: {
            hasBeenSynchronizedWithInternalChat: true,
          },
        },
        { mergeJson: ['settings'] },
      )

      return users
    } catch (error) {
      reportError(error)
    }
  }),
)

router.get(
  '/resync-users',
  promiseHandler(async (req, res) => {
    const { accountId } = req.query

    try {
      const users = await userResource.findMany({
        attributes: ['id', 'name', 'email', 'accountId', 'password', 'isActiveInternalChat'],
        where: {
          deletedAt: null,
          accountId,
          archivedAt: null,
          isSuperAdmin: {
            [Op.ne]: true,
          },
        },
        include: ['roles'],
      })

      return users
    } catch (error) {
      reportError(error)
    }
  }),
)

export default router
