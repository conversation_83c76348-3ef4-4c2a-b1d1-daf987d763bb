import { Router } from 'express'
import moment from 'moment'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import termResource from '../../../core/resources/termResource'
import resourceController from '../../../core/utils/routing/resourceController'
import userSetter from '../../../core/middlewares/userSetter'
import onlyNormalUser from '../../../core/middlewares/onlyNormalUser'
import ipRestriction from '../../../core/middlewares/ipRestriction'
import authenticate from '../../../core/middlewares/authenticate'

const auth = [authenticate, userSetter(), ipRestriction(), onlyNormalUser()]

const controller = {
  ...resourceController(termResource),

  getTermsNotAgreed: promiseHandler(async (req, res) => {
    return termResource.findMany({
      where: {
        agreementDate: { $eq: null },
        accountId: res.locals.user.accountId,
      },
    })
  }),

  agree: promiseHandler(async (req, res) => {
    const { user } = res.locals
    const { terms } = req.body
    return termResource.agree(user, terms)
  }),

  downloadTerm: promiseHandler(async (req, res) => {
    const { termId, accountId } = req.params
    const { fileBuffer, fileName } = await termResource.downloadTerm(accountId, termId)

    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURI(fileName)}`)
    res.send(fileBuffer)
  }),

  webhook: promiseHandler(async (req) => {
    const { terms, signatureId } = req.body
    const expirationDate = moment(terms.expiration_date, 'DD/MM/YYYY').format('YYYY-MM-DD')
    const data = {
      agnusId: terms.id,
      title: terms.title_term,
      text: terms.text_term,
      expirationDate,
      version: terms.version,
    }

    return termResource.createOrUpdateTerm(data, signatureId)
  }),
}

const router = Router()

router.get('/terms-not-agreed', auth, controller.getTermsNotAgreed)
router.get('/:id', auth, controller.show)
router.patch('/agree', auth, controller.agree)
router.post('/webhook', controller.webhook)
router.get('/download/:accountId/:termId', controller.downloadTerm)

export default router
