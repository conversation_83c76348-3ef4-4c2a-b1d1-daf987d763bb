#!/bin/bash

# Script para atualizar as definições de filas
# Uso: ./scripts/update-queues.sh

echo "🔄 Atualizando definições de filas..."

# Verifica se o arquivo existe
if [ ! -f "packages/back/src/core/queues-definition.yaml" ]; then
    echo "❌ Arquivo queues-definition.yaml não encontrado!"
    exit 1
fi

# Valida se o YAML está correto
echo "📋 Validando sintaxe do YAML..."
if command -v yamllint &> /dev/null; then
    yamllint packages/back/src/core/queues-definition.yaml
    if [ $? -ne 0 ]; then
        echo "❌ Erro na sintaxe do YAML!"
        exit 1
    fi
else
    echo "⚠️  yamllint não encontrado, pulando validação"
fi

# Reinicia o consumer
echo "🔄 Reiniciando message-broker-consumer..."
docker-compose restart message-broker-consumer

# Aguarda o serviço ficar saudável
echo "⏳ Aguardando serviço ficar online..."
sleep 5

# Verifica se o container está rodando
if docker-compose ps message-broker-consumer | grep -q "Up"; then
    echo "✅ Consumer reiniciado com sucesso!"
    echo "🌐 Verifique as filas em: http://localhost:15672"
else
    echo "❌ Erro ao reiniciar o consumer!"
    docker-compose logs message-broker-consumer
    exit 1
fi
